# Health Check

Please perform a comprehensive health check of: $ARGUMENTS.

Follow these steps:
1. Check application health endpoints
2. Verify database connectivity
3. Test Redis and cache systems
4. Check external service integrations
5. Monitor resource usage
6. Review recent logs for errors
7. Test critical API endpoints
8. Validate monitoring systems

Commands to use:
- `curl http://localhost:9077/health/live`
- `curl http://localhost:9077/health/ready`
- `curl http://localhost:9077/health/status`

Focus on:
- System availability
- Performance metrics
- Resource utilization
- Error rates
- Service dependencies

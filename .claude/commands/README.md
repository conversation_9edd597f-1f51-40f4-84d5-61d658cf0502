# Claude Code Commands

This directory contains project-specific commands for Claude Code. These commands are designed to streamline common development workflows and tasks.

## Available Commands

### Development Commands
- `/debug-issue` - Debug and fix GitHub issues
- `/analyze-logs` - Analyze application logs
- `/code-review` - Perform comprehensive code reviews
- `/create-migration` - Create database migrations
- `/run-tests` - Run and analyze tests
- `/test-api` - Test API endpoints with Bruno

### Deployment Commands
- `/deploy` - Deploy application to environments
- `/health-check` - Perform system health checks

### Monitoring Commands
- `/monitor-performance` - Monitor and analyze performance
- `/security-audit` - Perform security audits

### Documentation Commands
- `/update-docs` - Update project documentation
- `/generate-api-docs` - Generate API documentation

### Project-Specific Commands
- `/project-setup-database` - Setup project database
- `/project-develop-module` - Develop or enhance modules
- `/project-optimize-performance` - Optimize application performance

### Utility Commands
- `/clean-project` - Clean up project files
- `/backup-data` - Backup project data

## Usage

To use these commands in Claude Code:
1. Type `/` to open the slash commands menu
2. Select the desired command
3. Provide any required arguments
4. The command will execute with the specified parameters

## Command Structure

Commands are stored as Markdown files and can include:
- Step-by-step instructions
- Relevant commands to execute
- Focus areas for attention
- Project-specific context

## Adding New Commands

To add new commands:
1. Create a new `.md` file in this directory
2. Follow the existing command structure
3. Include clear instructions and context
4. Test the command before committing

## Best Practices

- Keep commands focused and specific
- Include error handling considerations
- Document any prerequisites
- Use the `$ARGUMENTS` placeholder for parameters
- Follow the project's coding and documentation standards

## Maintenance

These commands should be regularly updated to:
- Reflect changes in project structure
- Include new tools and processes
- Remove obsolete procedures
- Improve clarity and effectiveness

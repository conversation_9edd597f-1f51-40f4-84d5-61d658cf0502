# Security Audit

Please perform a security audit of: $ARGUMENTS.

Follow these steps:
1. Review authentication and authorization
2. Check for SQL injection vulnerabilities
3. Analyze input validation and sanitization
4. Review CORS and security headers
5. Check for sensitive data exposure
6. Analyze dependency vulnerabilities
7. Review API security practices
8. Check multi-tenant isolation

Commands to use:
- `go mod audit` - Check dependencies
- `gosec ./...` - Security scanner
- Review authentication flows

Focus on:
- Security best practices
- Vulnerability identification
- Data protection
- Access control
- Input validation

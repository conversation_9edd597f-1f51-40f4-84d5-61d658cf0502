# Monitor Performance

Please monitor and analyze performance for: $ARGUMENTS.

Follow these steps:
1. Check application metrics and logs
2. Monitor database performance
3. Analyze API response times
4. Review resource usage (CPU, memory)
5. Check for bottlenecks
6. Monitor error rates
7. Analyze traffic patterns
8. Provide optimization recommendations

Tools to use:
- Grafana dashboards (if configured)
- Application logs
- Database metrics
- System monitoring tools

Focus on:
- Response time optimization
- Resource utilization
- Error rate reduction
- Scalability improvements

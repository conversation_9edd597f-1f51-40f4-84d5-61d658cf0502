package pagination

import (
	"fmt"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// PaginationType represents the type of pagination being used
type PaginationType int

const (
	// PagePagination represents traditional page-based pagination
	PagePagination PaginationType = iota
	// CursorPaginationType represents cursor-based pagination
	CursorPaginationType
)

// FlexiblePaginationRequest supports both page and cursor pagination
type FlexiblePaginationRequest struct {
	// Page-based pagination (deprecated)
	Page     *int `form:"page" json:"page,omitempty"`
	PageSize *int `form:"page_size" json:"page_size,omitempty"`
	Limit    *int `form:"limit" json:"limit,omitempty"` // Alternative to page_size

	// Cursor-based pagination (preferred)
	Cursor      string `form:"cursor" json:"cursor,omitempty"`
	CursorLimit int    `form:"cursor_limit" json:"cursor_limit,omitempty"`

	// Common sorting
	SortBy    string `form:"sort_by" json:"sort_by,omitempty"`
	SortOrder string `form:"sort_order" json:"sort_order,omitempty"`
}

// PaginationMigrationHelper helps with migrating from page to cursor pagination
type PaginationMigrationHelper struct {
	cursorBuilder *CursorBuilder
}

// NewPaginationMigrationHelper creates a new migration helper
func NewPaginationMigrationHelper(cursorBuilder *CursorBuilder) *PaginationMigrationHelper {
	if cursorBuilder == nil {
		cursorBuilder = DefaultCursorBuilder()
	}

	return &PaginationMigrationHelper{
		cursorBuilder: cursorBuilder,
	}
}

// DetectPaginationType determines which pagination type is being used
func (pmh *PaginationMigrationHelper) DetectPaginationType(req *FlexiblePaginationRequest) PaginationType {
	// If cursor is provided, use cursor pagination
	if req.Cursor != "" {
		return CursorPaginationType
	}

	// If only cursor-specific fields are set, use cursor pagination
	if req.CursorLimit > 0 && req.Page == nil && req.PageSize == nil && req.Limit == nil {
		return CursorPaginationType
	}

	// If page-based fields are set, use page pagination
	if req.Page != nil || req.PageSize != nil || req.Limit != nil {
		return PagePagination
	}

	// Default to cursor pagination for new implementations
	return CursorPaginationType
}

// ParseFromGinContext parses pagination parameters from Gin context
func (pmh *PaginationMigrationHelper) ParseFromGinContext(c *gin.Context) *FlexiblePaginationRequest {
	req := &FlexiblePaginationRequest{}

	// Parse page-based parameters
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			req.Page = &page
		}
	}

	if pageSizeStr := c.Query("page_size"); pageSizeStr != "" {
		if pageSize, err := strconv.Atoi(pageSizeStr); err == nil && pageSize > 0 {
			req.PageSize = &pageSize
		}
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 {
			req.Limit = &limit
		}
	}

	// Parse cursor-based parameters
	req.Cursor = c.Query("cursor")

	if cursorLimitStr := c.Query("cursor_limit"); cursorLimitStr != "" {
		if cursorLimit, err := strconv.Atoi(cursorLimitStr); err == nil && cursorLimit > 0 {
			req.CursorLimit = cursorLimit
		}
	}

	// Parse sorting parameters
	req.SortBy = c.Query("sort_by")
	req.SortOrder = c.Query("sort_order")

	return req
}

// ConvertPageToCursorRequest converts page-based request to cursor request
func (pmh *PaginationMigrationHelper) ConvertPageToCursorRequest(req *FlexiblePaginationRequest) *CursorRequest {
	var limit int

	// Determine limit from various sources
	if req.CursorLimit > 0 {
		limit = req.CursorLimit
	} else if req.Limit != nil {
		limit = *req.Limit
	} else if req.PageSize != nil {
		limit = *req.PageSize
	} else {
		limit = DefaultLimit
	}

	// Validate limit
	limit = pmh.cursorBuilder.ValidateLimit(limit)

	return &CursorRequest{
		Cursor: req.Cursor,
		Limit:  limit,
	}
}

// ConvertPageToCursorResponse converts page-based metadata to cursor response
func (pmh *PaginationMigrationHelper) ConvertPageToCursorResponse(
	page int,
	pageSize int,
	total int64,
	hasMore bool,
	lastID uint,
	lastCreatedAt time.Time,
) (*CursorResponse, error) {

	response := &CursorResponse{
		HasNext: hasMore,
		HasMore: hasMore,
		Count:   pageSize,
		Limit:   pageSize,
	}

	// Generate cursor if there are more items
	if hasMore && lastID > 0 {
		nextCursor, err := EncodeCursor(lastID, lastCreatedAt)
		if err != nil {
			return nil, fmt.Errorf("failed to encode cursor: %w", err)
		}
		response.NextCursor = nextCursor
	}

	return response, nil
}

// BackwardCompatibleResponse creates a response that supports both pagination types
type BackwardCompatibleResponse struct {
	// Data
	Data interface{} `json:"data"`

	// Cursor pagination (preferred)
	Pagination *CursorResponse `json:"pagination,omitempty"`

	// Page pagination (deprecated)
	Meta *PageMeta `json:"meta,omitempty"`
}

// PageMeta represents page-based pagination metadata
type PageMeta struct {
	Total      int64 `json:"total"`
	Page       int   `json:"page"`
	PageSize   int   `json:"page_size"`
	TotalPages int   `json:"total_pages"`
	HasNext    bool  `json:"has_next"`
	HasPrev    bool  `json:"has_prev"`
}

// CreateBackwardCompatibleResponse creates a response supporting both pagination types
func (pmh *PaginationMigrationHelper) CreateBackwardCompatibleResponse(
	data interface{},
	paginationType PaginationType,
	cursorResp *CursorResponse,
	pageMeta *PageMeta,
) *BackwardCompatibleResponse {

	response := &BackwardCompatibleResponse{
		Data: data,
	}

	switch paginationType {
	case CursorPaginationType:
		response.Pagination = cursorResp
	case PagePagination:
		response.Meta = pageMeta
		// Also include cursor pagination for forward compatibility
		if cursorResp != nil {
			response.Pagination = cursorResp
		}
	}

	return response
}

// EstimatePageFromCursor estimates page number from cursor (approximate)
// Note: This is not precise but can help with backward compatibility
func (pmh *PaginationMigrationHelper) EstimatePageFromCursor(cursor string, pageSize int) (int, error) {
	if cursor == "" {
		return 1, nil
	}

	cursorData, err := ParseCursor(cursor)
	if err != nil {
		return 1, err
	}

	// This is a rough estimate - in practice, this would require
	// additional database queries to be accurate
	_ = cursorData

	// For now, return 1 as we can't accurately estimate without more context
	return 1, nil
}

// MigrationConfig contains configuration for pagination migration
type MigrationConfig struct {
	// Whether to support backward compatibility
	SupportLegacyPagePagination bool `json:"support_legacy_page_pagination"`

	// Whether to include both pagination formats in response
	IncludeBothFormats bool `json:"include_both_formats"`

	// Default pagination type for new requests
	DefaultPaginationType PaginationType `json:"default_pagination_type"`

	// Cursor builder configuration
	CursorConfig CursorConfig `json:"cursor_config"`
}

// DefaultMigrationConfig returns default migration configuration
func DefaultMigrationConfig() MigrationConfig {
	return MigrationConfig{
		SupportLegacyPagePagination: true,
		IncludeBothFormats:          false,
		DefaultPaginationType:       CursorPaginationType,
		CursorConfig: CursorConfig{
			DefaultSortField:  "created_at",
			AllowedSortFields: []string{"created_at", "updated_at", "id"},
			DefaultLimit:      DefaultLimit,
			MaxLimit:          MaxLimit,
			DefaultSortOrder:  "DESC",
		},
	}
}

// PaginationManager manages the migration process
type PaginationManager struct {
	config MigrationConfig
	helper *PaginationMigrationHelper
}

// NewPaginationManager creates a new pagination manager
func NewPaginationManager(config MigrationConfig) *PaginationManager {
	cursorBuilder := NewCursorBuilder(config.CursorConfig)
	helper := NewPaginationMigrationHelper(cursorBuilder)

	return &PaginationManager{
		config: config,
		helper: helper,
	}
}

// HandleRequest processes pagination parameters from request
func (pm *PaginationManager) HandleRequest(c *gin.Context) (*FlexiblePaginationRequest, PaginationType, *CursorRequest) {
	// Parse request
	flexReq := pm.helper.ParseFromGinContext(c)

	// Detect pagination type
	paginationType := pm.helper.DetectPaginationType(flexReq)

	// If legacy page pagination not supported, force cursor pagination
	if !pm.config.SupportLegacyPagePagination && paginationType == PagePagination {
		paginationType = CursorPaginationType
	}

	// Convert to cursor request
	cursorReq := pm.helper.ConvertPageToCursorRequest(flexReq)

	return flexReq, paginationType, cursorReq
}

// BuildResponse builds appropriate response based on configuration
func (pm *PaginationManager) BuildResponse(
	data interface{},
	paginationType PaginationType,
	cursorResp *CursorResponse,
	pageMeta *PageMeta,
) interface{} {

	if pm.config.IncludeBothFormats {
		return pm.helper.CreateBackwardCompatibleResponse(data, paginationType, cursorResp, pageMeta)
	}

	// Return appropriate format based on request type
	switch paginationType {
	case CursorPaginationType:
		return map[string]interface{}{
			"data":       data,
			"pagination": cursorResp,
		}
	case PagePagination:
		return map[string]interface{}{
			"data": data,
			"meta": pageMeta,
		}
	}

	// Default to cursor format
	return map[string]interface{}{
		"data":       data,
		"pagination": cursorResp,
	}
}

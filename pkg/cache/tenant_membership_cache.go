package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	userModels "github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

const (
	// Cache key patterns for tenant membership
	TenantMembershipKey = "tenant:membership:%d:%d" // user_id:tenant_id
	UserRoleKey         = "user:role:%d:%d"         // user_id:tenant_id
	UserActiveStatusKey = "user:active:%d:%d"       // user_id:tenant_id
	UserTenantsKey      = "user:tenants:%d"         // user_id

	// Cache TTL settings
	MembershipCacheTTL   = 15 * time.Minute
	RoleCacheTTL         = 10 * time.Minute
	ActiveStatusCacheTTL = 5 * time.Minute
	UserTenantsCacheTTL  = 20 * time.Minute
)

// TenantMembershipCache provides caching for tenant membership operations
type TenantMembershipCache interface {
	// Membership caching
	GetMembership(ctx context.Context, userID, tenantID uint) (*userModels.TenantMembership, error)
	SetMembership(ctx context.Context, userID, tenantID uint, membership *userModels.TenantMembership) error
	InvalidateMembership(ctx context.Context, userID, tenantID uint) error

	// Role caching
	GetUserRole(ctx context.Context, userID, tenantID uint) (string, error)
	SetUserRole(ctx context.Context, userID, tenantID uint, role string) error
	InvalidateUserRole(ctx context.Context, userID, tenantID uint) error

	// Active status caching
	GetUserActiveStatus(ctx context.Context, userID, tenantID uint) (bool, error)
	SetUserActiveStatus(ctx context.Context, userID, tenantID uint, isActive bool) error
	InvalidateUserActiveStatus(ctx context.Context, userID, tenantID uint) error

	// User tenants caching
	GetUserTenants(ctx context.Context, userID uint) ([]userModels.TenantMembership, error)
	SetUserTenants(ctx context.Context, userID uint, memberships []userModels.TenantMembership) error
	InvalidateUserTenants(ctx context.Context, userID uint) error

	// Bulk invalidation
	InvalidateUserCache(ctx context.Context, userID uint) error
	InvalidateTenantCache(ctx context.Context, tenantID uint) error
}

type tenantMembershipCache struct {
	redis  *redis.Client
	logger utils.Logger
}

// NewTenantMembershipCache creates a new tenant membership cache
func NewTenantMembershipCache(redis *redis.Client, logger utils.Logger) TenantMembershipCache {
	return &tenantMembershipCache{
		redis:  redis,
		logger: logger,
	}
}

// GetMembership retrieves cached membership
func (c *tenantMembershipCache) GetMembership(ctx context.Context, userID, tenantID uint) (*userModels.TenantMembership, error) {
	key := fmt.Sprintf(TenantMembershipKey, userID, tenantID)

	result, err := c.redis.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // Cache miss
		}
		return nil, fmt.Errorf("failed to get membership from cache: %w", err)
	}

	var membership userModels.TenantMembership
	if err := json.Unmarshal([]byte(result), &membership); err != nil {
		return nil, fmt.Errorf("failed to unmarshal membership: %w", err)
	}

	return &membership, nil
}

// SetMembership caches membership
func (c *tenantMembershipCache) SetMembership(ctx context.Context, userID, tenantID uint, membership *userModels.TenantMembership) error {
	key := fmt.Sprintf(TenantMembershipKey, userID, tenantID)

	data, err := json.Marshal(membership)
	if err != nil {
		return fmt.Errorf("failed to marshal membership: %w", err)
	}

	if err := c.redis.Set(ctx, key, data, MembershipCacheTTL).Err(); err != nil {
		return fmt.Errorf("failed to set membership in cache: %w", err)
	}

	return nil
}

// InvalidateMembership removes membership from cache
func (c *tenantMembershipCache) InvalidateMembership(ctx context.Context, userID, tenantID uint) error {
	key := fmt.Sprintf(TenantMembershipKey, userID, tenantID)
	return c.redis.Del(ctx, key).Err()
}

// GetUserRole retrieves cached user role
func (c *tenantMembershipCache) GetUserRole(ctx context.Context, userID, tenantID uint) (string, error) {
	key := fmt.Sprintf(UserRoleKey, userID, tenantID)

	result, err := c.redis.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return "", nil // Cache miss
		}
		return "", fmt.Errorf("failed to get role from cache: %w", err)
	}

	return result, nil
}

// SetUserRole caches user role
func (c *tenantMembershipCache) SetUserRole(ctx context.Context, userID, tenantID uint, role string) error {
	key := fmt.Sprintf(UserRoleKey, userID, tenantID)
	return c.redis.Set(ctx, key, role, RoleCacheTTL).Err()
}

// InvalidateUserRole removes role from cache
func (c *tenantMembershipCache) InvalidateUserRole(ctx context.Context, userID, tenantID uint) error {
	key := fmt.Sprintf(UserRoleKey, userID, tenantID)
	return c.redis.Del(ctx, key).Err()
}

// GetUserActiveStatus retrieves cached active status
func (c *tenantMembershipCache) GetUserActiveStatus(ctx context.Context, userID, tenantID uint) (bool, error) {
	key := fmt.Sprintf(UserActiveStatusKey, userID, tenantID)

	result, err := c.redis.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return false, nil // Cache miss, default to false
		}
		return false, fmt.Errorf("failed to get active status from cache: %w", err)
	}

	return result == "true", nil
}

// SetUserActiveStatus caches user active status
func (c *tenantMembershipCache) SetUserActiveStatus(ctx context.Context, userID, tenantID uint, isActive bool) error {
	key := fmt.Sprintf(UserActiveStatusKey, userID, tenantID)
	value := "false"
	if isActive {
		value = "true"
	}
	return c.redis.Set(ctx, key, value, ActiveStatusCacheTTL).Err()
}

// InvalidateUserActiveStatus removes active status from cache
func (c *tenantMembershipCache) InvalidateUserActiveStatus(ctx context.Context, userID, tenantID uint) error {
	key := fmt.Sprintf(UserActiveStatusKey, userID, tenantID)
	return c.redis.Del(ctx, key).Err()
}

// GetUserTenants retrieves cached user tenants
func (c *tenantMembershipCache) GetUserTenants(ctx context.Context, userID uint) ([]userModels.TenantMembership, error) {
	key := fmt.Sprintf(UserTenantsKey, userID)

	result, err := c.redis.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // Cache miss
		}
		return nil, fmt.Errorf("failed to get user tenants from cache: %w", err)
	}

	var memberships []userModels.TenantMembership
	if err := json.Unmarshal([]byte(result), &memberships); err != nil {
		return nil, fmt.Errorf("failed to unmarshal user tenants: %w", err)
	}

	return memberships, nil
}

// SetUserTenants caches user tenants
func (c *tenantMembershipCache) SetUserTenants(ctx context.Context, userID uint, memberships []userModels.TenantMembership) error {
	key := fmt.Sprintf(UserTenantsKey, userID)

	data, err := json.Marshal(memberships)
	if err != nil {
		return fmt.Errorf("failed to marshal user tenants: %w", err)
	}

	return c.redis.Set(ctx, key, data, UserTenantsCacheTTL).Err()
}

// InvalidateUserTenants removes user tenants from cache
func (c *tenantMembershipCache) InvalidateUserTenants(ctx context.Context, userID uint) error {
	key := fmt.Sprintf(UserTenantsKey, userID)
	return c.redis.Del(ctx, key).Err()
}

// InvalidateUserCache removes all cached data for a user
func (c *tenantMembershipCache) InvalidateUserCache(ctx context.Context, userID uint) error {
	// Pattern for all user-related keys
	patterns := []string{
		fmt.Sprintf("user:*:%d:*", userID),
		fmt.Sprintf("user:*:%d", userID),
		fmt.Sprintf("tenant:membership:%d:*", userID),
	}

	for _, pattern := range patterns {
		keys, err := c.redis.Keys(ctx, pattern).Result()
		if err != nil {
			c.logger.WithError(err).Warnf("Failed to get keys for pattern %s", pattern)
			continue
		}

		if len(keys) > 0 {
			if err := c.redis.Del(ctx, keys...).Err(); err != nil {
				c.logger.WithError(err).Warnf("Failed to delete keys for pattern %s", pattern)
			}
		}
	}

	return nil
}

// InvalidateTenantCache removes all cached data for a tenant
func (c *tenantMembershipCache) InvalidateTenantCache(ctx context.Context, tenantID uint) error {
	// Pattern for all tenant-related keys
	patterns := []string{
		fmt.Sprintf("tenant:membership:*:%d", tenantID),
		fmt.Sprintf("user:role:*:%d", tenantID),
		fmt.Sprintf("user:active:*:%d", tenantID),
	}

	for _, pattern := range patterns {
		keys, err := c.redis.Keys(ctx, pattern).Result()
		if err != nil {
			c.logger.WithError(err).Warnf("Failed to get keys for pattern %s", pattern)
			continue
		}

		if len(keys) > 0 {
			if err := c.redis.Del(ctx, keys...).Err(); err != nil {
				c.logger.WithError(err).Warnf("Failed to delete keys for pattern %s", pattern)
			}
		}
	}

	return nil
}

package utils

import (
	"bytes"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// ConsoleFormatter formats logs for console output with better readability
type ConsoleFormatter struct {
	// TimestampFormat to use for display when a full timestamp is printed
	TimestampFormat string

	// The fields are sorted by default for a consistent output. For applications
	// that log extremely frequently and don't use the JSON formatter this may not
	// be desired.
	DisableSorting bool

	// Force color output
	ForceColors bool

	// Disable color output
	DisableColors bool

	// Show full timestamp
	FullTimestamp bool

	// Which fields to exclude from output
	ExcludeFields []string
}

// Format renders a single log entry
func (f *ConsoleFormatter) Format(entry *logrus.Entry) ([]byte, error) {
	var b *bytes.Buffer
	if entry.Buffer != nil {
		b = entry.Buffer
	} else {
		b = &bytes.Buffer{}
	}

	// Get colors based on level
	levelColor := f.getLevelColor(entry.Level)
	resetColor := "\033[0m"

	if f.DisableColors {
		levelColor = ""
		resetColor = ""
	}

	// Format timestamp
	timestamp := ""
	if f.FullTimestamp {
		if f.TimestampFormat == "" {
			f.TimestampFormat = "15:04:05"
		}
		timestamp = fmt.Sprintf("\033[90m%s\033[0m ", entry.Time.Format(f.TimestampFormat))
	}

	// Format level - make it fixed width for alignment
	level := strings.ToUpper(entry.Level.String())
	levelText := fmt.Sprintf("%s%-5s%s", levelColor, level, resetColor)

	// Format message
	message := entry.Message

	// Write base log
	fmt.Fprintf(b, "%s%s %s", timestamp, levelText, message)

	// Format fields
	if len(entry.Data) > 0 {
		fields := f.formatFields(entry)
		if fields != "" {
			fmt.Fprintf(b, " %s", fields)
		}
	}

	b.WriteByte('\n')
	return b.Bytes(), nil
}

func (f *ConsoleFormatter) formatFields(entry *logrus.Entry) string {

	// Sort fields for consistent output
	keys := make([]string, 0, len(entry.Data))
	for k := range entry.Data {
		// Skip excluded fields
		if f.isExcluded(k) {
			continue
		}
		keys = append(keys, k)
	}

	if !f.DisableSorting {
		sort.Strings(keys)
	}

	// Special handling for common fields to make them more readable
	importantFields := []string{}
	regularFields := []string{}

	for _, key := range keys {
		value := entry.Data[key]

		// Format based on key type
		switch key {
		case "method", "path", "status", "duration", "remote_ip":
			// Important fields - highlight them
			formatted := f.formatField(key, value)
			if formatted != "" {
				importantFields = append(importantFields, formatted)
			}
		case "error":
			// Error in red
			formatted := fmt.Sprintf("\033[31m%s=%v\033[0m", key, value)
			importantFields = append(importantFields, formatted)
		default:
			// Regular fields in gray
			formatted := f.formatField(key, value)
			if formatted != "" {
				regularFields = append(regularFields, fmt.Sprintf("\033[90m%s\033[0m", formatted))
			}
		}
	}

	// Combine fields
	allFields := append(importantFields, regularFields...)

	if len(allFields) > 0 {
		return strings.Join(allFields, " ")
	}

	return ""
}

func (f *ConsoleFormatter) formatField(key string, value interface{}) string {
	// Special formatting for certain fields
	switch key {
	case "duration":
		// Format duration nicely
		switch v := value.(type) {
		case time.Duration:
			return fmt.Sprintf("%s=%v", key, v)
		case string:
			// Sometimes duration comes as string
			return fmt.Sprintf("%s=%s", key, v)
		case float64:
			// Sometimes duration comes as float64 microseconds
			if v < 1000 {
				return fmt.Sprintf("%s=%.1fµs", key, v)
			} else if v < 1000000 {
				return fmt.Sprintf("%s=%.1fms", key, v/1000)
			} else {
				return fmt.Sprintf("%s=%.1fs", key, v/1000000)
			}
		default:
			return fmt.Sprintf("%s=%v", key, value)
		}
	case "status":
		// Color code status
		if status, ok := value.(int); ok {
			color := f.getStatusColor(status)
			if !f.DisableColors && color != "" {
				return fmt.Sprintf("%s%s=%d%s", color, key, status, "\033[0m")
			}
			return fmt.Sprintf("%s=%d", key, status)
		}
	case "method":
		// Color code HTTP methods
		if method, ok := value.(string); ok {
			color := f.getMethodColor(method)
			if !f.DisableColors && color != "" {
				return fmt.Sprintf("%s%s=%s%s", color, key, method, "\033[0m")
			}
			return fmt.Sprintf("%s=%s", key, method)
		}
	}

	// Default formatting
	return fmt.Sprintf("%s=%v", key, value)
}

func (f *ConsoleFormatter) getLevelColor(level logrus.Level) string {
	if f.DisableColors {
		return ""
	}

	switch level {
	case logrus.DebugLevel:
		return "\033[36m" // Cyan
	case logrus.InfoLevel:
		return "\033[32m" // Green
	case logrus.WarnLevel:
		return "\033[33m" // Yellow
	case logrus.ErrorLevel:
		return "\033[31m" // Red
	case logrus.FatalLevel, logrus.PanicLevel:
		return "\033[35m" // Magenta
	default:
		return ""
	}
}

func (f *ConsoleFormatter) getStatusColor(status int) string {
	switch {
	case status >= 200 && status < 300:
		return "\033[32m" // Green for success
	case status >= 300 && status < 400:
		return "\033[36m" // Cyan for redirect
	case status >= 400 && status < 500:
		return "\033[33m" // Yellow for client error
	case status >= 500:
		return "\033[31m" // Red for server error
	default:
		return ""
	}
}

func (f *ConsoleFormatter) getMethodColor(method string) string {
	switch method {
	case "GET":
		return "\033[36m" // Cyan
	case "POST":
		return "\033[32m" // Green
	case "PUT", "PATCH":
		return "\033[33m" // Yellow
	case "DELETE":
		return "\033[31m" // Red
	default:
		return ""
	}
}

func (f *ConsoleFormatter) isExcluded(field string) bool {
	for _, excluded := range f.ExcludeFields {
		if field == excluded {
			return true
		}
	}
	return false
}

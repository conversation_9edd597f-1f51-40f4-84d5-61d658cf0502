package events

import (
	"context"
	"errors"
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestEventBus_EmailVerified(t *testing.T) {
	// Reset global event bus for testing
	globalEventBus = nil
	once = sync.Once{}

	eventBus := GetEventBus()
	ctx := context.Background()

	t.Run("Subscribe and Publish Email Verified Event", func(t *testing.T) {
		var receivedEvent EmailVerifiedEvent
		handlerCalled := false

		// Subscribe to event
		eventBus.SubscribeEmailVerified("email_verified", func(ctx context.Context, event EmailVerifiedEvent) error {
			handlerCalled = true
			receivedEvent = event
			return nil
		})

		// Publish event
		err := eventBus.PublishEmailVerified(ctx, "email_verified", EmailVerifiedEvent{
			UserID: 123,
		})

		assert.NoError(t, err)
		assert.True(t, handlerCalled)
		assert.Equal(t, uint(123), receivedEvent.UserID)
	})

	t.Run("Multiple Handlers", func(t *testing.T) {
		// Reset
		globalEventBus = nil
		once = sync.Once{}
		eventBus = GetEventBus()

		callCount := 0
		
		// Subscribe multiple handlers
		for i := 0; i < 3; i++ {
			eventBus.SubscribeEmailVerified("email_verified", func(ctx context.Context, event EmailVerifiedEvent) error {
				callCount++
				return nil
			})
		}

		// Publish event
		err := eventBus.PublishEmailVerified(ctx, "email_verified", EmailVerifiedEvent{
			UserID: 456,
		})

		assert.NoError(t, err)
		assert.Equal(t, 3, callCount)
	})

	t.Run("Handler Error Does Not Stop Other Handlers", func(t *testing.T) {
		// Reset
		globalEventBus = nil
		once = sync.Once{}
		eventBus = GetEventBus()

		handler1Called := false
		handler2Called := false
		handler3Called := false

		// First handler returns error
		eventBus.SubscribeEmailVerified("email_verified", func(ctx context.Context, event EmailVerifiedEvent) error {
			handler1Called = true
			return errors.New("handler 1 error")
		})

		// Second handler succeeds
		eventBus.SubscribeEmailVerified("email_verified", func(ctx context.Context, event EmailVerifiedEvent) error {
			handler2Called = true
			return nil
		})

		// Third handler succeeds
		eventBus.SubscribeEmailVerified("email_verified", func(ctx context.Context, event EmailVerifiedEvent) error {
			handler3Called = true
			return nil
		})

		// Publish event
		err := eventBus.PublishEmailVerified(ctx, "email_verified", EmailVerifiedEvent{
			UserID: 789,
		})

		assert.NoError(t, err) // No error returned even if handler fails
		assert.True(t, handler1Called)
		assert.True(t, handler2Called)
		assert.True(t, handler3Called)
	})
}

func TestEventBus_TenantCreated(t *testing.T) {
	// Reset global event bus for testing
	globalEventBus = nil
	once = sync.Once{}

	eventBus := GetEventBus()
	ctx := context.Background()

	t.Run("Subscribe and Publish Tenant Created Event", func(t *testing.T) {
		var receivedEvent TenantCreatedEvent
		handlerCalled := false

		// Subscribe to event
		eventBus.SubscribeTenantCreated("tenant_created", func(ctx context.Context, event TenantCreatedEvent) error {
			handlerCalled = true
			receivedEvent = event
			return nil
		})

		// Publish event
		err := eventBus.PublishTenantCreated(ctx, "tenant_created", TenantCreatedEvent{
			TenantID: 1,
			OwnerID:  100,
			Domain:   "example.com",
		})

		assert.NoError(t, err)
		assert.True(t, handlerCalled)
		assert.Equal(t, uint(1), receivedEvent.TenantID)
		assert.Equal(t, uint(100), receivedEvent.OwnerID)
		assert.Equal(t, "example.com", receivedEvent.Domain)
	})

	t.Run("Multiple Handlers", func(t *testing.T) {
		// Reset
		globalEventBus = nil
		once = sync.Once{}
		eventBus = GetEventBus()

		callCount := 0
		var events []TenantCreatedEvent

		// Subscribe multiple handlers
		for i := 0; i < 3; i++ {
			eventBus.SubscribeTenantCreated("tenant_created", func(ctx context.Context, event TenantCreatedEvent) error {
				callCount++
				events = append(events, event)
				return nil
			})
		}

		// Publish event
		err := eventBus.PublishTenantCreated(ctx, "tenant_created", TenantCreatedEvent{
			TenantID: 2,
			OwnerID:  200,
			Domain:   "test.com",
		})

		assert.NoError(t, err)
		assert.Equal(t, 3, callCount)
		assert.Len(t, events, 3)
		for _, event := range events {
			assert.Equal(t, uint(2), event.TenantID)
			assert.Equal(t, uint(200), event.OwnerID)
			assert.Equal(t, "test.com", event.Domain)
		}
	})
}

func TestEventBus_Concurrency(t *testing.T) {
	// Reset global event bus for testing
	globalEventBus = nil
	once = sync.Once{}

	eventBus := GetEventBus()
	ctx := context.Background()

	t.Run("Concurrent Subscribe and Publish", func(t *testing.T) {
		var wg sync.WaitGroup
		callCount := 0
		var mu sync.Mutex

		// Subscribe handlers concurrently
		for i := 0; i < 10; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()
				eventBus.SubscribeEmailVerified("concurrent_test", func(ctx context.Context, event EmailVerifiedEvent) error {
					mu.Lock()
					callCount++
					mu.Unlock()
					return nil
				})
			}(i)
		}

		// Wait for all subscriptions
		wg.Wait()

		// Publish events concurrently
		for i := 0; i < 5; i++ {
			wg.Add(1)
			go func(userID uint) {
				defer wg.Done()
				err := eventBus.PublishEmailVerified(ctx, "concurrent_test", EmailVerifiedEvent{
					UserID: userID,
				})
				assert.NoError(t, err)
			}(uint(i))
		}

		// Wait for all publishes
		wg.Wait()

		// Each of 5 events should be handled by 10 handlers
		assert.Equal(t, 50, callCount)
	})
}

func TestPublishEmailVerifiedEvent(t *testing.T) {
	// Reset global event bus for testing
	globalEventBus = nil
	once = sync.Once{}

	ctx := context.Background()
	
	t.Run("Publish Using Helper Function", func(t *testing.T) {
		handlerCalled := false
		var receivedUserID uint

		// Subscribe to event
		GetEventBus().SubscribeEmailVerified("email_verified", func(ctx context.Context, event EmailVerifiedEvent) error {
			handlerCalled = true
			receivedUserID = event.UserID
			return nil
		})

		// Use helper function
		err := PublishEmailVerifiedEvent(ctx, 999)

		assert.NoError(t, err)
		assert.True(t, handlerCalled)
		assert.Equal(t, uint(999), receivedUserID)
	})
}

func TestPublishTenantCreatedEvent(t *testing.T) {
	// Reset global event bus for testing
	globalEventBus = nil
	once = sync.Once{}

	ctx := context.Background()
	
	t.Run("Publish Using Helper Function", func(t *testing.T) {
		handlerCalled := false
		var receivedEvent TenantCreatedEvent

		// Subscribe to event
		GetEventBus().SubscribeTenantCreated("tenant_created", func(ctx context.Context, event TenantCreatedEvent) error {
			handlerCalled = true
			receivedEvent = event
			return nil
		})

		// Use helper function
		err := PublishTenantCreatedEvent(ctx, 10, 1000, "helper.test.com")

		assert.NoError(t, err)
		assert.True(t, handlerCalled)
		assert.Equal(t, uint(10), receivedEvent.TenantID)
		assert.Equal(t, uint(1000), receivedEvent.OwnerID)
		assert.Equal(t, "helper.test.com", receivedEvent.Domain)
	})
}

func TestEventBus_NoHandlers(t *testing.T) {
	// Reset global event bus for testing
	globalEventBus = nil
	once = sync.Once{}

	eventBus := GetEventBus()
	ctx := context.Background()

	t.Run("Publish With No Handlers Should Not Error", func(t *testing.T) {
		// Publish email verified event with no handlers
		err := eventBus.PublishEmailVerified(ctx, "no_handlers", EmailVerifiedEvent{
			UserID: 123,
		})
		assert.NoError(t, err)

		// Publish tenant created event with no handlers
		err = eventBus.PublishTenantCreated(ctx, "no_handlers", TenantCreatedEvent{
			TenantID: 1,
			OwnerID:  100,
			Domain:   "test.com",
		})
		assert.NoError(t, err)
	})
}

func TestEventBus_RaceCondition(t *testing.T) {
	// Reset global event bus for testing
	globalEventBus = nil
	once = sync.Once{}

	eventBus := GetEventBus()
	ctx := context.Background()

	// Use -race flag when running: go test -race
	t.Run("No Race Condition", func(t *testing.T) {
		done := make(chan bool)
		
		// Concurrent subscribes
		go func() {
			for i := 0; i < 100; i++ {
				eventBus.SubscribeEmailVerified("race_test", func(ctx context.Context, event EmailVerifiedEvent) error {
					return nil
				})
			}
			done <- true
		}()

		// Concurrent publishes
		go func() {
			for i := 0; i < 100; i++ {
				eventBus.PublishEmailVerified(ctx, "race_test", EmailVerifiedEvent{
					UserID: uint(i),
				})
			}
			done <- true
		}()

		// Wait for both goroutines
		<-done
		<-done
	})
}

func BenchmarkEventBus_Publish(b *testing.B) {
	// Reset global event bus for testing
	globalEventBus = nil
	once = sync.Once{}

	eventBus := GetEventBus()
	ctx := context.Background()

	// Subscribe a simple handler
	eventBus.SubscribeEmailVerified("benchmark", func(ctx context.Context, event EmailVerifiedEvent) error {
		return nil
	})

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		eventBus.PublishEmailVerified(ctx, "benchmark", EmailVerifiedEvent{
			UserID: uint(i),
		})
	}
}
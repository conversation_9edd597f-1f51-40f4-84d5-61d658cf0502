package events

import (
	"context"
	"sync"
)

// EmailVerifiedEvent represents an email verification event
type EmailVerifiedEvent struct {
	UserID uint
}

// EmailVerifiedHandler handles email verification events
type EmailVerifiedHandler func(ctx context.Context, event EmailVerifiedEvent) error

// TenantCreatedEvent represents a tenant creation event
type TenantCreatedEvent struct {
	TenantID uint
	OwnerID  uint
	Domain   string
}

// TenantCreatedHandler handles tenant creation events
type TenantCreatedHandler func(ctx context.Context, event TenantCreatedEvent) error

// EventHandler is a generic event handler interface
type EventHandler interface{}

// EventBus manages event handlers
type EventBus struct {
	emailHandlers  map[string][]EmailVerifiedHandler
	tenantHandlers map[string][]TenantCreatedHandler
	mu             sync.RWMutex
}

var (
	globalEventBus *EventBus
	once           sync.Once
)

// GetEventBus returns the global event bus instance
func GetEventBus() *EventBus {
	once.Do(func() {
		globalEventBus = &EventBus{
			emailHandlers:  make(map[string][]EmailVerifiedHandler),
			tenantHandlers: make(map[string][]TenantCreatedHandler),
		}
	})
	return globalEventBus
}

// ResetEventBusForTesting resets the event bus for testing purposes
// This should only be used in tests
func ResetEventBusForTesting() {
	globalEventBus = nil
	once = sync.Once{}
}

// SubscribeEmailVerified adds a handler for email verified events
func (eb *EventBus) SubscribeEmailVerified(eventType string, handler EmailVerifiedHandler) {
	eb.mu.Lock()
	defer eb.mu.Unlock()
	eb.emailHandlers[eventType] = append(eb.emailHandlers[eventType], handler)
}

// SubscribeTenantCreated adds a handler for tenant created events
func (eb *EventBus) SubscribeTenantCreated(eventType string, handler TenantCreatedHandler) {
	eb.mu.Lock()
	defer eb.mu.Unlock()
	eb.tenantHandlers[eventType] = append(eb.tenantHandlers[eventType], handler)
}

// PublishEmailVerified publishes an email verified event
func (eb *EventBus) PublishEmailVerified(ctx context.Context, eventType string, event EmailVerifiedEvent) error {
	eb.mu.RLock()
	handlers := eb.emailHandlers[eventType]
	eb.mu.RUnlock()

	for _, handler := range handlers {
		if err := handler(ctx, event); err != nil {
			// Log error but continue with other handlers
			continue
		}
	}
	return nil
}

// PublishTenantCreated publishes a tenant created event
func (eb *EventBus) PublishTenantCreated(ctx context.Context, eventType string, event TenantCreatedEvent) error {
	eb.mu.RLock()
	handlers := eb.tenantHandlers[eventType]
	eb.mu.RUnlock()

	for _, handler := range handlers {
		if err := handler(ctx, event); err != nil {
			// Log error but continue with other handlers
			continue
		}
	}
	return nil
}

// PublishEmailVerifiedEvent publishes an email verified event
func PublishEmailVerifiedEvent(ctx context.Context, userID uint) error {
	eventBus := GetEventBus()
	return eventBus.PublishEmailVerified(ctx, "email_verified", EmailVerifiedEvent{
		UserID: userID,
	})
}

// PublishTenantCreatedEvent publishes a tenant created event
func PublishTenantCreatedEvent(ctx context.Context, tenantID, ownerID uint, domain string) error {
	eventBus := GetEventBus()
	return eventBus.PublishTenantCreated(ctx, "tenant_created", TenantCreatedEvent{
		TenantID: tenantID,
		OwnerID:  ownerID,
		Domain:   domain,
	})
}

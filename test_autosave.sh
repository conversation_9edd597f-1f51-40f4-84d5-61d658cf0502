#!/bin/bash

# Test script for blog post auto-save functionality

API_BASE="http://localhost:9077/api/cms/v1"
TENANT_ID="1"
WEBSITE_ID="1"

echo "Testing Blog Post Auto-save Functionality"
echo "========================================="

# First, we need to login to get a token
echo "1. Login to get authentication token..."
LOGIN_RESPONSE=$(curl -s -X POST "$API_BASE/auth/login" \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }')

TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.data.access_token' 2>/dev/null)

if [ -z "$TOKEN" ] || [ "$TOKEN" = "null" ]; then
  echo "❌ Failed to get authentication token"
  echo "Response: $LOGIN_RESPONSE"
  exit 1
fi

echo "✅ Got authentication token"

# Create a test blog post first
echo -e "\n2. Creating a test blog post..."
POST_RESPONSE=$(curl -s -X POST "$API_BASE/blog/posts" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "X-Website-ID: $WEBSITE_ID" \
  -d '{
    "title": "Test Post for Auto-save",
    "content": "Initial content",
    "excerpt": "Test excerpt",
    "category_id": 1,
    "status": "draft"
  }')

POST_ID=$(echo $POST_RESPONSE | jq -r '.data.id' 2>/dev/null)

if [ -z "$POST_ID" ] || [ "$POST_ID" = "null" ]; then
  echo "❌ Failed to create blog post"
  echo "Response: $POST_RESPONSE"
  exit 1
fi

echo "✅ Created blog post with ID: $POST_ID"

# Test auto-save functionality
echo -e "\n3. Testing auto-save..."
AUTOSAVE_RESPONSE=$(curl -s -X POST "$API_BASE/blog/posts/$POST_ID/autosave" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "X-Website-ID: $WEBSITE_ID" \
  -d '{
    "title": "Test Post for Auto-save - Updated",
    "content": "This is the auto-saved content that has been updated",
    "excerpt": "Updated excerpt",
    "featured_image": "https://example.com/image.jpg"
  }')

AUTOSAVE_SUCCESS=$(echo $AUTOSAVE_RESPONSE | jq -r '.status.success' 2>/dev/null)

if [ "$AUTOSAVE_SUCCESS" != "true" ]; then
  echo "❌ Failed to auto-save"
  echo "Response: $AUTOSAVE_RESPONSE"
else
  echo "✅ Auto-save successful"
  echo "Response: $AUTOSAVE_RESPONSE" | jq .
fi

# Get auto-save status
echo -e "\n4. Getting auto-save status..."
STATUS_RESPONSE=$(curl -s -X GET "$API_BASE/blog/posts/$POST_ID/autosave/status" \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "X-Website-ID: $WEBSITE_ID")

echo "Auto-save status:"
echo "$STATUS_RESPONSE" | jq .

# Retrieve auto-save
echo -e "\n5. Retrieving auto-save..."
GET_RESPONSE=$(curl -s -X GET "$API_BASE/blog/posts/$POST_ID/autosave" \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "X-Website-ID: $WEBSITE_ID")

echo "Retrieved auto-save:"
echo "$GET_RESPONSE" | jq .

# Test restore from auto-save
echo -e "\n6. Testing restore from auto-save..."
RESTORE_RESPONSE=$(curl -s -X POST "$API_BASE/blog/posts/$POST_ID/autosave/restore" \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "X-Website-ID: $WEBSITE_ID")

echo "Restore response:"
echo "$RESTORE_RESPONSE" | jq .

# Clean up - delete auto-save
echo -e "\n7. Cleaning up - deleting auto-save..."
DELETE_RESPONSE=$(curl -s -X DELETE "$API_BASE/blog/posts/$POST_ID/autosave" \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: $TENANT_ID" \
  -H "X-Website-ID: $WEBSITE_ID")

echo "Delete response:"
echo "$DELETE_RESPONSE" | jq .

echo -e "\n✅ Auto-save functionality test completed!"
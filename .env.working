# Working Onboarding Test Environment
# Source this file: source .env.working

export API_BASE_URL="http://localhost:9077"

# Working Verified User
export TEST_EMAIL="<EMAIL>"
export TEST_PASSWORD="TestPassword123"
export USER_ID="35"

# Working Access Token (expires: 2025-07-22T17:35:13Z)
export ACCESS_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************.F-Jln77O1i_cDYGxIC2TjrdAaIepAYitfoGSD5Xqc_Y"

# Organization Info
export TENANT_ID="21"
export ORG_NAME="Flow Test Organization"

# Onboarding State
export CURRENT_JOURNEY="user_onboarding"
export CURRENT_STEP="organization_setup"
export PROGRESS_PERCENTAGE="45.5"

# ============================================================================
# READY-TO-USE CURL COMMANDS
# ============================================================================

alias onboarding-status='curl -s -H "Authorization: Bearer $ACCESS_TOKEN" $API_BASE_URL/api/cms/v1/onboarding/my/status | jq .'
alias onboarding-current='curl -s -H "Authorization: Bearer $ACCESS_TOKEN" $API_BASE_URL/api/cms/v1/onboarding/my/current-step | jq .'
alias onboarding-next='curl -s -H "Authorization: Bearer $ACCESS_TOKEN" $API_BASE_URL/api/cms/v1/onboarding/my/next-step | jq .'
alias onboarding-org='curl -s -H "Authorization: Bearer $ACCESS_TOKEN" $API_BASE_URL/api/cms/v1/onboarding/my/organization-status | jq .'
alias onboarding-progress='curl -s -H "Authorization: Bearer $ACCESS_TOKEN" $API_BASE_URL/api/cms/v1/onboarding/my/progress | jq .'
alias onboarding-templates='curl -s -H "Authorization: Bearer $ACCESS_TOKEN" $API_BASE_URL/api/cms/v1/onboarding/my/templates | jq .'

echo "✅ Onboarding test environment loaded!"
echo ""
echo "🚀 Available commands:"
echo "  onboarding-status     # Check onboarding status"  
echo "  onboarding-current    # Get current step"
echo "  onboarding-next       # Get next step"
echo "  onboarding-org        # Check organization status"
echo "  onboarding-progress   # Get detailed progress"
echo "  onboarding-templates  # List available templates"
echo ""
echo "📝 Manual curl examples:"
echo "  curl -H \"Authorization: Bearer \$ACCESS_TOKEN\" \$API_BASE_URL/api/cms/v1/onboarding/my/status | jq ."
echo ""
echo "🔧 Complete a step:"
echo "  curl -X POST -H \"Authorization: Bearer \$ACCESS_TOKEN\" -H \"Content-Type: application/json\" \\"
echo "    -d '{\"step_id\":3,\"result\":\"completed\",\"data\":{\"method\":\"manual\"}}' \\"
echo "    \$API_BASE_URL/api/cms/v1/onboarding/my/complete-step | jq ."
echo ""
echo "🏢 Create new organization:"
echo "  curl -X POST -H \"Authorization: Bearer \$ACCESS_TOKEN\" -H \"Content-Type: application/json\" \\"
echo "    -d '{\"organization_name\":\"New Org\",\"slug\":\"neworg\",\"admin_email\":\"<EMAIL>\",\"admin_name\":\"Admin\"}' \\"
echo "    \$API_BASE_URL/api/cms/v1/onboarding/my/organization | jq ."
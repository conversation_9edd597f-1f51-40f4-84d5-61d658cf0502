meta {
  name: Media API
  type: collection
}

auth {
  mode: bearer
  bearer: {{token}}
}

headers {
  X-Tenant-ID: {{tenantId}}
  Content-Type: application/json
}

docs {
  # Media API Test Collection

  Tests for Media Files API with both cursor and page pagination support.

  ## Prerequisites
  - Valid authentication token in `{{token}}`
  - Valid tenant ID in `{{tenantId}}`
  - At least one website with ID 1
  - Some media files uploaded to test pagination

  ## Test Sequence
  1. Test page-based pagination (backward compatibility)
  2. Test cursor-based pagination (recommended)
  3. Test cursor navigation with next_cursor
  4. Test default behavior
}
meta {
  name: List Files - Cursor with Next
  type: http
  seq: 3
}

get {
  url: {{baseUrl}}/api/cms/v1/media/files?website_id=1&cursor_limit=5&cursor={{nextCursor}}
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{token}}
  X-Tenant-ID: {{tenantId}}
  Content-Type: application/json
}

assert {
  res.status: eq 200
  res.body.status.success: eq true
  res.body.data.files: isDefined
  res.body.data.pagination: isDefined
}

tests {
  test("should handle cursor navigation correctly", function() {
    const data = res.getBody().data;
    expect(data).to.have.property('files');
    expect(data).to.have.property('pagination');
    
    // Save next cursor for subsequent requests
    if (data.pagination.next_cursor) {
      bru.setEnvVar("nextCursor", data.pagination.next_cursor);
    }
  });
  
  test("should maintain correct sort order", function() {
    const files = res.getBody().data.files;
    if (files.length > 1) {
      for (let i = 0; i < files.length - 1; i++) {
        const current = new Date(files[i].created_at);
        const next = new Date(files[i + 1].created_at);
        expect(current.getTime()).to.be.greaterThanOrEqual(next.getTime());
      }
    }
  });
}
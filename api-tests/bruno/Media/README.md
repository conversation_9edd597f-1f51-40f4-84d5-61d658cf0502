# Media API Tests

This collection tests the Media Files API with both cursor and page pagination support.

## Test Coverage

### Pagination Tests
1. **List Files - Page Pagination**: Tests traditional page-based pagination with `page` and `page_size` parameters
2. **List Files - Cursor Pagination**: Tests cursor-based pagination with `cursor_limit` parameter
3. **List Files - Cursor with Next**: Tests cursor navigation using `next_cursor` values
4. **List Files - Default Behavior**: Tests default pagination behavior when no parameters are specified

### Key Assertions

#### Page Pagination (Backward Compatibility)
- Response contains `meta` object with pagination info
- Meta includes: `page`, `page_size`, `total`, `has_next`, `has_prev`
- No `pagination` object in response

#### Cursor Pagination (Preferred)
- Response contains `pagination` object with cursor info
- Pagination includes: `has_next`, `has_more`, `count`, `limit`, `next_cursor`
- No `meta` object in response
- Files sorted by `created_at DESC, id DESC`

#### Default Behavior
- Defaults to cursor pagination when no explicit pagination parameters provided
- Uses default limit of 20 items

## Migration Strategy

The API supports both pagination types during the migration period:
- **Legacy clients**: Continue using `page` and `page_size` parameters
- **New clients**: Use `cursor_limit` and `cursor` parameters for better performance
- **Auto-detection**: API automatically detects pagination type from request parameters

## Performance Benefits

Cursor pagination provides:
- ✅ Consistent results during concurrent modifications
- ✅ Better performance for large datasets
- ✅ No "lost" or duplicate items when data changes
- ✅ Efficient database queries using indexes

## Usage Examples

### Page Pagination (Legacy)
```
GET /media/files?website_id=1&page=1&page_size=20
```

### Cursor Pagination (Recommended)
```
GET /media/files?website_id=1&cursor_limit=20
GET /media/files?website_id=1&cursor_limit=20&cursor=eyJpZCI6MTIzLCJjcmVhdGVkX2F0IjoiMjAyNC0wMS0xNVQxMDozMDowMFoifQ==
```
meta {
  name: List Files - Cursor Pagination
  type: http
  seq: 2
}

get {
  url: {{baseUrl}}/api/cms/v1/media/files?website_id=1&cursor_limit=10
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{token}}
  X-Tenant-ID: {{tenantId}}
  Content-Type: application/json
}

assert {
  res.status: eq 200
  res.body.status.success: eq true
  res.body.data.files: isDefined
  res.body.data.pagination: isDefined
  res.body.data.pagination.limit: eq 10
  res.body.data.pagination.count: isNumber
}

tests {
  test("should return files with cursor-based pagination", function() {
    const data = res.getBody().data;
    expect(data).to.have.property('files');
    expect(data).to.have.property('pagination');
    expect(data.pagination).to.have.property('has_next');
    expect(data.pagination).to.have.property('has_more');
    expect(data.pagination).to.have.property('count');
    expect(data.pagination).to.have.property('limit');
  });
  
  test("should not have page pagination fields", function() {
    const data = res.getBody().data;
    expect(data).to.not.have.property('meta');
  });
  
  test("files should be sorted by created_at DESC", function() {
    const files = res.getBody().data.files;
    if (files.length > 1) {
      for (let i = 0; i < files.length - 1; i++) {
        const current = new Date(files[i].created_at);
        const next = new Date(files[i + 1].created_at);
        expect(current.getTime()).to.be.greaterThanOrEqual(next.getTime());
      }
    }
  });
}
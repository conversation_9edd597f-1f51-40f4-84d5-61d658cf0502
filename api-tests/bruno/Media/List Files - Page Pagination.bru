meta {
  name: List Files - Page Pagination
  type: http
  seq: 1
}

get {
  url: {{baseUrl}}/api/cms/v1/media/files?website_id=1&page=1&page_size=10
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{token}}
  X-Tenant-ID: {{tenantId}}
  Content-Type: application/json
}

assert {
  res.status: eq 200
  res.body.status.success: eq true
  res.body.data.files: isDefined
  res.body.data.meta: isDefined
  res.body.data.meta.page: eq 1
  res.body.data.meta.page_size: eq 10
  res.body.data.meta.total: isNumber
}

tests {
  test("should return files with page-based pagination", function() {
    const data = res.getBody().data;
    expect(data).to.have.property('files');
    expect(data).to.have.property('meta');
    expect(data.meta).to.have.property('page');
    expect(data.meta).to.have.property('page_size');
    expect(data.meta).to.have.property('total');
    expect(data.meta).to.have.property('has_next');
    expect(data.meta).to.have.property('has_prev');
  });
  
  test("should not have cursor pagination fields", function() {
    const data = res.getBody().data;
    expect(data).to.not.have.property('pagination');
  });
}
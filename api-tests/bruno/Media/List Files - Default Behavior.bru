meta {
  name: List Files - Default Behavior
  type: http
  seq: 4
}

get {
  url: {{baseUrl}}/api/cms/v1/media/files?website_id=1
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{token}}
  X-Tenant-ID: {{tenantId}}
  Content-Type: application/json
}

assert {
  res.status: eq 200
  res.body.status.success: eq true
  res.body.data.files: isDefined
}

tests {
  test("should default to cursor pagination when no parameters specified", function() {
    const data = res.getBody().data;
    expect(data).to.have.property('files');
    
    // According to our configuration, default should be cursor pagination
    expect(data).to.have.property('pagination');
    expect(data.pagination).to.have.property('limit');
    expect(data.pagination).to.have.property('count');
  });
  
  test("should use default limit of 20 when no cursor_limit specified", function() {
    const data = res.getBody().data;
    expect(data.pagination.limit).to.equal(20);
  });
}
meta {
  name: Categories - With Filters
  type: http
  seq: 6
}

get {
  url: {{base_url}}/api/cms/v1/blog/categories?website_id=1&limit=5&is_active=true&sort_by=name&sort_order=asc
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{auth_token}}
  X-Tenant-ID: {{tenant_id}}
  Content-Type: application/json
}

tests {
  test("should return 200 status", function() {
    expect(res.status).to.equal(200);
  });
  
  test("should filter by is_active", function() {
    expect(res.body.data).to.be.an('array');
    if (res.body.data.length > 0) {
      res.body.data.forEach(category => {
        expect(category.is_active).to.equal(true);
      });
    }
  });
  
  test("should have cursor pagination with limit", function() {
    expect(res.body.pagination.limit).to.equal(5);
  });
}
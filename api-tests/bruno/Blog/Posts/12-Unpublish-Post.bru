meta {
  name: Unpublish Post
  type: http
  seq: 12
}

post {
  url: {{base_url}}/api/cms/v1/blog/posts/{{created_post_id}}/unpublish
  body: json
  auth: bearer
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

auth:bearer {
  token: {{bearer_token}}
}

body:json {
  {
    "reason": "Testing unpublish functionality",
    "new_status": "draft"
  }
}

tests {
  test("should return 200 status", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("should return unpublished post", function() {
    expect(res.getBody()).to.have.property('data');
    expect(res.getBody().data).to.have.property('post');
    expect(res.getBody().data.post.status).to.equal('draft');
  });

  test("should have success message", function() {
    expect(res.getBody()).to.have.property('message');
    expect(res.getBody().message).to.contain('unpublished');
  });

  test("should still have published_at timestamp", function() {
    const post = res.getBody().data.post;
    expect(post).to.have.property('published_at');
    // published_at should still exist but post is now draft
  });
}
meta {
  name: Delete Post
  type: http
  seq: 14
}

delete {
  url: {{base_url}}/api/cms/v1/blog/posts/{{created_post_id}}
  body: none
  auth: bearer
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

auth:bearer {
  token: {{bearer_token}}
}

tests {
  test("should return 200 status", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("should return success message", function() {
    expect(res.getBody()).to.have.property('message');
    expect(res.getBody().message).to.contain('deleted');
  });

  test("should confirm post deletion", function() {
    expect(res.getBody()).to.have.property('data');
    expect(res.getBody().data).to.have.property('deleted');
    expect(res.getBody().data.deleted).to.be.true;
  });
}
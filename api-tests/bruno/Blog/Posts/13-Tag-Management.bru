meta {
  name: Tag Management
  type: http
  seq: 13
}

post {
  url: {{base_url}}/api/cms/v1/blog/posts/{{created_post_id}}/tags/sync
  body: json
  auth: bearer
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

auth:bearer {
  token: {{bearer_token}}
}

body:json {
  {
    "tags": [
      {
        "name": "API Testing",
        "slug": "api-testing"
      },
      {
        "name": "<PERSON>",
        "slug": "bruno"
      },
      {
        "name": "Blog Management",
        "slug": "blog-management"
      }
    ]
  }
}

tests {
  test("should return 200 status", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("should return updated post with tags", function() {
    expect(res.getBody()).to.have.property('data');
    expect(res.getBody().data).to.have.property('post');
    expect(res.getBody().data.post).to.have.property('tags');
    expect(res.getBody().data.post.tags).to.be.an('array');
  });

  test("should have synced tags", function() {
    const post = res.getBody().data.post;
    expect(post.tags.length).to.equal(3);
    const tagNames = post.tags.map(tag => tag.name);
    expect(tagNames).to.include('API Testing');
    expect(tagNames).to.include('Bruno');
    expect(tagNames).to.include('Blog Management');
  });

  test("should have success message", function() {
    expect(res.getBody()).to.have.property('message');
    expect(res.getBody().message).to.contain('tags');
  });
}
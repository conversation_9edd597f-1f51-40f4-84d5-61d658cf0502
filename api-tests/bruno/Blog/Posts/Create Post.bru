meta {
  name: Create Post
  type: http
  seq: 2
}

post {
  url: {{baseUrl}}/api/cms/v1/blog/posts
  body: json
  auth: bearer
}

auth:bearer {
  token: {{accessToken}}
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "website_id": 1,
    "category_id": 1,
    "title": "Getting Started with Go",
    "slug": "getting-started-with-go",
    "content": "# Getting Started with Go\n\nGo is a statically typed, compiled programming language...",
    "excerpt": "Learn the basics of Go programming language",
    "type": "post",
    "status": "draft",
    "is_featured": false,
    "allow_comments": true,
    "meta_title": "Getting Started with Go - Tutorial",
    "meta_description": "Complete guide to getting started with Go programming",
    "meta_keywords": "go, golang, programming, tutorial"
  }
}

docs {
  ## Create Blog Post
  
  Creates a new blog post.
  
  ### Request Body
  - `website_id` (required) - Website ID
  - `category_id` - Category ID
  - `title` (required) - Post title
  - `slug` (required) - URL-friendly slug
  - `content` (required) - Post content (Markdown supported)
  - `excerpt` - Short description
  - `type` - Post type: post or page (default: post)
  - `status` - Status: draft, published, archived, scheduled (default: draft)
  - `is_featured` - Whether post is featured
  - `allow_comments` - Whether comments are allowed
  - `meta_title` - SEO meta title
  - `meta_description` - SEO meta description
  - `meta_keywords` - SEO keywords
}
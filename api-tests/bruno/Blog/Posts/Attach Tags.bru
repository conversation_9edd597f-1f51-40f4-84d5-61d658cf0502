meta {
  name: Attach Tags
  type: http
  seq: 4
}

post {
  url: {{baseUrl}}/api/cms/v1/blog/posts/1/tags/attach
  body: json
  auth: bearer
}

auth:bearer {
  token: {{accessToken}}
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "tag_ids": [1, 2, 3]
  }
}

docs {
  ## Attach Tags to Post
  
  Attaches one or more tags to a blog post.
  
  ### URL Parameters
  - `id` - Post ID
  
  ### Request Body
  - `tag_ids` (required) - Array of tag IDs to attach
}
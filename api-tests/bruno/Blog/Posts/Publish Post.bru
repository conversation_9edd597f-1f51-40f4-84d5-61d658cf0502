meta {
  name: Publish Post
  type: http
  seq: 3
}

post {
  url: {{baseUrl}}/api/cms/v1/blog/posts/1/publish
  body: none
  auth: bearer
}

auth:bearer {
  token: {{accessToken}}
}

headers {
  Content-Type: application/json
}

docs {
  ## Publish Blog Post
  
  Publishes a draft blog post, making it publicly visible.
  
  ### URL Parameters
  - `id` - Post ID
  
  **Note**: The post must be in 'draft' or 'scheduled' status to be published.
}
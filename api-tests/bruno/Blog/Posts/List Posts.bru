meta {
  name: List Posts
  type: http
  seq: 1
}

get {
  url: {{baseUrl}}/api/cms/v1/blog/posts?page=1&page_size=20
  body: none
  auth: bearer
}

auth:bearer {
  token: {{accessToken}}
}

headers {
  Content-Type: application/json
}

docs {
  ## List Blog Posts
  
  This endpoint retrieves a paginated list of blog posts.
  
  ### Query Parameters
  - `page` - Page number (default: 1)
  - `page_size` - Items per page (default: 20)
  - `website_id` - Filter by website ID
  - `author_id` - Filter by author ID
  - `category_id` - Filter by category ID
  - `type` - Filter by post type (post, page)
  - `status` - Filter by status (draft, published, archived, scheduled, deleted)
  - `is_featured` - Filter by featured status
  - `search` - Search by title or content
  - `sort_by` - Sort field (default: created_at)
  - `sort_order` - Sort order: asc/desc (default: desc)
}
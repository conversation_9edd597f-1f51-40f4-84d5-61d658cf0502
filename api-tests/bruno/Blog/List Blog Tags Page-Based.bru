meta {
  name: List Blog Tags Page-Based
  type: http
  seq: 32
}

get {
  url: {{baseUrl}}/blog/tags?website_id=1&use_page=true&page=1&page_size=10
  body: none
  auth: bearer
}

params:query {
  website_id: 1
  use_page: true
  page: 1
  page_size: 10
  ~search: 
  ~is_active: true
  ~status: active
  ~sort_by: name
  ~sort_order: asc
}

auth:bearer {
  token: {{authToken}}
}

headers {
  X-Tenant-ID: 1
}

script:post-response {
  // Extract token from response for use in other requests
  if (res.status === 200) {
    const responseData = res.getBody();
    
    // Check response structure
    bru.assertTrue(responseData.status, "Response should have status");
    bru.assertTrue(responseData.status.success, "Request should be successful");
    bru.assertTrue(responseData.data, "Response should have data");
    bru.assertTrue(responseData.data.tags, "Data should have tags array");
    bru.assertTrue(responseData.data.meta, "Data should have meta for page-based pagination");
    
    // Check meta structure for page-based pagination
    const meta = responseData.data.meta;
    bru.assertTrue(meta.hasOwnProperty('page'), "Meta should have page");
    bru.assertTrue(meta.hasOwnProperty('page_size'), "Meta should have page_size");
    bru.assertTrue(meta.hasOwnProperty('total'), "Meta should have total");
    bru.assertTrue(meta.hasOwnProperty('total_pages'), "Meta should have total_pages");
    bru.assertTrue(meta.hasOwnProperty('has_next'), "Meta should have has_next");
    bru.assertTrue(meta.hasOwnProperty('has_prev'), "Meta should have has_prev");
    
    console.log(`Page ${meta.page} of ${meta.total_pages}, showing ${responseData.data.tags.length} tags out of ${meta.total}`);
  }
}

docs {
  # List Blog Tags - Page-Based (Backward Compatibility)
  
  This endpoint lists blog tags using traditional page-based pagination for backward compatibility.
  
  ## Query Parameters
  - `website_id` (required): The website ID
  - `use_page` (required): Set to `true` to use page-based pagination
  - `page`: Page number (default: 1)
  - `page_size`: Items per page (default: 20)
  - `search`: Search filter
  - `is_active`: Filter by active status
  - `status`: Filter by status
  - `sort_by`: Sort field
  - `sort_order`: Sort order
  
  ## Response
  Returns a paginated list of tags with traditional meta information.
}
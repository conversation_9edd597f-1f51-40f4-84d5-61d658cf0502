meta {
  name: Categories - Cursor Pagination
  type: http
  seq: 5
}

get {
  url: {{base_url}}/api/cms/v1/blog/categories?website_id=1&limit=10
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{auth_token}}
  X-Tenant-ID: {{tenant_id}}
  Content-Type: application/json
}

tests {
  test("should return 200 status", function() {
    expect(res.status).to.equal(200);
  });
  
  test("should return cursor-based pagination response", function() {
    expect(res.body.data).to.be.an('array');
    expect(res.body.pagination).to.be.an('object');
    expect(res.body.pagination.next_cursor).to.be.a('string');
    expect(res.body.pagination.has_next).to.be.a('boolean');
  });
  
  test("should have cursor pagination fields", function() {
    expect(res.body.pagination.limit).to.be.a('number');
    expect(res.body.pagination.limit).to.equal(10);
  });
}
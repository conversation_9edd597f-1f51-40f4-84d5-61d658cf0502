meta {
  name: List Blog Tags Next Page
  type: http
  seq: 31
}

get {
  url: {{baseUrl}}/blog/tags?website_id=1&cursor={{nextTagCursor}}&limit=10
  body: none
  auth: bearer
}

params:query {
  website_id: 1
  cursor: {{nextTagCursor}}
  limit: 10
  ~search: 
  ~is_active: true
  ~status: active
  ~sort_by: name
  ~sort_order: asc
}

auth:bearer {
  token: {{authToken}}
}

headers {
  X-Tenant-ID: 1
}

script:pre-request {
  // Check if we have a cursor from previous request
  const cursor = bru.getEnvVar("nextTagCursor");
  if (!cursor) {
    console.log("No cursor available. Run 'List Blog Tags Cursor' first.");
  }
}

script:post-response {
  // Extract token from response for use in other requests
  if (res.status === 200) {
    const responseData = res.getBody();
    
    // Check response structure
    bru.assertTrue(responseData.status, "Response should have status");
    bru.assertTrue(responseData.status.success, "Request should be successful");
    bru.assertTrue(responseData.data, "Response should have data");
    bru.assertTrue(responseData.data.tags, "Data should have tags array");
    bru.assertTrue(responseData.data.pagination, "Data should have pagination");
    
    console.log(`Found ${responseData.data.tags.length} tags on next page`);
    
    // Update cursor if there are more pages
    const pagination = responseData.data.pagination;
    if (pagination.has_next && pagination.next_cursor) {
      bru.setEnvVar("nextTagCursor", pagination.next_cursor);
      console.log(`Updated cursor for next page: ${pagination.next_cursor}`);
    } else {
      console.log("No more pages available");
    }
  }
}

docs {
  # List Blog Tags - Next Page
  
  This endpoint retrieves the next page of blog tags using the cursor from the previous response.
  
  **Note:** Run "List Blog Tags Cursor" first to get the initial cursor.
  
  ## Query Parameters
  - `website_id` (required): The website ID
  - `cursor` (required): Pagination cursor from previous response
  - `limit`: Number of items per page
  
  ## Response
  Returns the next page of tags with updated cursor information.
}
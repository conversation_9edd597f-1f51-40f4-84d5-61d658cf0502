# Blog Newsletter Subscription API Tests

This folder contains Bruno tests for the blog newsletter subscription feature.

## Test Sequence

1. **01-Subscribe.bru** - Subscribe to newsletter with email, categories, tags, and frequency preference
2. **02-Subscribe-Duplicate.bru** - Test duplicate subscription handling
3. **03-Confirm-Subscription.bru** - Confirm subscription using token from email
4. **04-List-Subscriptions.bru** - List subscriptions with cursor pagination and filters
5. **05-Get-Subscription.bru** - Get individual subscription by ID
6. **06-Update-Subscription.bru** - Update subscription preferences
7. **07-Get-Stats.bru** - Get subscription statistics
8. **08-Get-Growth-Data.bru** - Get subscription growth data over time
9. **09-Export-Subscribers.bru** - Export subscribers (admin only)
10. **10-Unsubscribe.bru** - Unsubscribe using token
11. **11-Delete-Subscription.bru** - Delete subscription (admin only)

## Features Tested

- Newsletter subscription with email verification
- Category and tag preferences for targeted newsletters
- Frequency options (instant, daily, weekly, monthly)
- Duplicate subscription handling
- Token-based confirmation and unsubscribe
- Cursor-based pagination for listing subscriptions
- Statistics and growth analytics
- Admin features for export and management

## Prerequisites

- Valid tenant ID and website ID in headers
- Authentication token for protected endpoints
- Admin role for export and delete operations

## Notes

- Subscription tokens are unique and generated automatically
- Email confirmation is required before subscription becomes active
- Unsubscribed emails can re-subscribe
- Soft delete is used (status-based, not deleted_at)
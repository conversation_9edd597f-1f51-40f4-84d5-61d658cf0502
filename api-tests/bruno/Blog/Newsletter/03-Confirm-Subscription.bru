meta {
  name: 03-Confirm-Subscription
  type: http
  seq: 3
}

post {
  url: {{baseUrl}}/blog/newsletter/confirm
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "token": "test-confirmation-token-replace-with-actual"
  }
}

tests {
  test("Status code is 200 or 400", function() {
    // 200 for success, 400 for invalid token
    const status = res.getStatus();
    expect([200, 400]).to.include(status);
  });

  test("Response has correct structure", function() {
    const json = res.getBody();
    expect(json).to.have.property('status');
    
    if (res.getStatus() === 200) {
      expect(json).to.have.property('data');
      const data = json.data;
      expect(data.status).to.equal('confirmed');
    }
  });
}
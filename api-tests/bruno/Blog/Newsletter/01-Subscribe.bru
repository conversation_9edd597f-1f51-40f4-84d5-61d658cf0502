meta {
  name: 01-Subscribe
  type: http
  seq: 1
}

post {
  url: {{baseUrl}}/blog/newsletter/subscribe
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenantId}}
  X-Website-ID: {{websiteId}}
}

body:json {
  {
    "email": "<EMAIL>",
    "name": "Newsletter Subscriber",
    "categories": [1, 2],
    "tags": [1, 3, 5],
    "frequency": "weekly"
  }
}

tests {
  test("Status code is 201", function() {
    expect(res.getStatus()).to.equal(201);
  });

  test("Response has correct structure", function() {
    const json = res.getBody();
    expect(json).to.have.property('status');
    expect(json).to.have.property('data');
    expect(json.status.success).to.be.true;
  });

  test("Subscription data is correct", function() {
    const data = res.getBody().data;
    expect(data).to.have.property('id');
    expect(data).to.have.property('email');
    expect(data).to.have.property('status');
    expect(data.email).to.equal('<EMAIL>');
    expect(data.status).to.equal('pending');
    expect(data.frequency).to.equal('weekly');
  });
}
meta {
  name: 10-Unsubscribe
  type: http
  seq: 10
}

post {
  url: {{baseUrl}}/blog/newsletter/unsubscribe
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "token": "test-unsubscribe-token-replace-with-actual"
  }
}

tests {
  test("Status code is 200 or 400", function() {
    // 200 for success, 400 for invalid token
    const status = res.getStatus();
    expect([200, 400]).to.include(status);
  });

  test("Response has correct structure", function() {
    const json = res.getBody();
    expect(json).to.have.property('status');
    
    if (res.getStatus() === 200) {
      expect(json.message).to.include('unsubscribed');
    }
  });
}
meta {
  name: 06-Update-Subscription
  type: http
  seq: 6
}

put {
  url: {{baseUrl}}/blog/newsletter/subscriptions/1
  body: json
  auth: bearer
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenantId}}
}

auth:bearer {
  token: {{accessToken}}
}

body:json {
  {
    "name": "Updated Name",
    "frequency": "daily",
    "categories": [1, 3, 4],
    "tags": [2, 4]
  }
}

tests {
  test("Status code is 200 or 404", function() {
    const status = res.getStatus();
    expect([200, 404]).to.include(status);
  });

  test("Response has correct structure", function() {
    const json = res.getBody();
    expect(json).to.have.property('status');
    
    if (res.getStatus() === 200) {
      expect(json).to.have.property('data');
      const data = json.data;
      expect(data.frequency).to.equal('daily');
    }
  });
}
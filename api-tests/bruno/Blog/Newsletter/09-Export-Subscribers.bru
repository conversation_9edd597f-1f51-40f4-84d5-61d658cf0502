meta {
  name: 09-Export-Subscribers
  type: http
  seq: 9
}

get {
  url: {{baseUrl}}/blog/newsletter/export?status=confirmed
  body: none
  auth: bearer
}

headers {
  X-Tenant-ID: {{tenantId}}
  X-Website-ID: {{websiteId}}
}

auth:bearer {
  token: {{accessToken}}
}

tests {
  test("Status code is 200 or 403", function() {
    // 200 for admin, 403 for non-admin users
    const status = res.getStatus();
    expect([200, 403]).to.include(status);
  });

  test("Response has correct structure", function() {
    const json = res.getBody();
    expect(json).to.have.property('status');
    
    if (res.getStatus() === 200) {
      expect(json).to.have.property('data');
      const data = json.data;
      expect(data).to.be.an('array');
    }
  });
}
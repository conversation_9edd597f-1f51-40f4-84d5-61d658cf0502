meta {
  name: 05-Get-Subscription
  type: http
  seq: 5
}

get {
  url: {{baseUrl}}/blog/newsletter/subscriptions/1
  body: none
  auth: bearer
}

headers {
  X-Tenant-ID: {{tenantId}}
}

auth:bearer {
  token: {{accessToken}}
}

tests {
  test("Status code is 200 or 404", function() {
    const status = res.getStatus();
    expect([200, 404]).to.include(status);
  });

  test("Response has correct structure", function() {
    const json = res.getBody();
    expect(json).to.have.property('status');
    
    if (res.getStatus() === 200) {
      expect(json).to.have.property('data');
      const data = json.data;
      expect(data).to.have.property('id');
      expect(data).to.have.property('email');
      expect(data).to.have.property('status');
      expect(data).to.have.property('frequency');
    }
  });
}
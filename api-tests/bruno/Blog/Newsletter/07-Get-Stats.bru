meta {
  name: 07-Get-Stats
  type: http
  seq: 7
}

get {
  url: {{baseUrl}}/blog/newsletter/stats
  body: none
  auth: bearer
}

headers {
  X-Tenant-ID: {{tenantId}}
  X-Website-ID: {{websiteId}}
}

auth:bearer {
  token: {{accessToken}}
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("Response has correct structure", function() {
    const json = res.getBody();
    expect(json).to.have.property('status');
    expect(json).to.have.property('data');
    expect(json.status.success).to.be.true;
  });

  test("Stats data is correct", function() {
    const data = res.getBody().data;
    expect(data).to.have.property('total_subscribers');
    expect(data).to.have.property('confirmed_subscribers');
    expect(data).to.have.property('pending_subscribers');
    expect(data).to.have.property('unsubscribed_count');
    expect(data).to.have.property('bounced_count');
    expect(data).to.have.property('by_frequency');
    expect(data).to.have.property('by_status');
  });
}
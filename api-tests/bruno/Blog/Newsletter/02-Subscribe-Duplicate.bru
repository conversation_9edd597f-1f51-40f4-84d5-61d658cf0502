meta {
  name: 02-Subscribe-Duplicate
  type: http
  seq: 2
}

post {
  url: {{baseUrl}}/blog/newsletter/subscribe
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenantId}}
  X-Website-ID: {{websiteId}}
}

body:json {
  {
    "email": "<EMAIL>",
    "frequency": "daily"
  }
}

tests {
  test("Should handle duplicate subscription", function() {
    // Could be 201 (resend confirmation) or 409 (already subscribed)
    const status = res.getStatus();
    expect([201, 409]).to.include(status);
  });

  test("Response has correct structure", function() {
    const json = res.getBody();
    expect(json).to.have.property('status');
    
    if (res.getStatus() === 409) {
      expect(json.message).to.include('already subscribed');
    }
  });
}
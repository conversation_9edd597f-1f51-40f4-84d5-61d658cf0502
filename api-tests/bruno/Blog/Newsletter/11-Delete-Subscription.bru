meta {
  name: 11-Delete-Subscription
  type: http
  seq: 11
}

delete {
  url: {{baseUrl}}/blog/newsletter/subscriptions/1
  body: none
  auth: bearer
}

headers {
  X-Tenant-ID: {{tenantId}}
}

auth:bearer {
  token: {{accessToken}}
}

tests {
  test("Status code is 200 or 404", function() {
    const status = res.getStatus();
    expect([200, 404]).to.include(status);
  });

  test("Response has correct structure", function() {
    const json = res.getBody();
    expect(json).to.have.property('status');
    
    if (res.getStatus() === 200) {
      expect(json.message).to.include('deleted');
    }
  });
}
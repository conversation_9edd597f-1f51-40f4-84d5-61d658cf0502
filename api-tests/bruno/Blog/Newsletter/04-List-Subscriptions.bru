meta {
  name: 04-List-Subscriptions
  type: http
  seq: 4
}

get {
  url: {{baseUrl}}/blog/newsletter/subscriptions?limit=20&status=confirmed
  body: none
  auth: bearer
}

headers {
  X-Tenant-ID: {{tenantId}}
  X-Website-ID: {{websiteId}}
}

auth:bearer {
  token: {{accessToken}}
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("Response has correct structure", function() {
    const json = res.getBody();
    expect(json).to.have.property('status');
    expect(json).to.have.property('data');
    expect(json).to.have.property('meta');
    expect(json.status.success).to.be.true;
  });

  test("Data is array", function() {
    const data = res.getBody().data;
    expect(data).to.be.an('array');
  });

  test("Meta has cursor pagination info", function() {
    const meta = res.getBody().meta;
    expect(meta).to.have.property('has_more');
    if (meta.has_more) {
      expect(meta).to.have.property('next_cursor');
    }
  });
}
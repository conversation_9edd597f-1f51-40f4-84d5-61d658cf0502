meta {
  name: 08-Get-Growth-Data
  type: http
  seq: 8
}

get {
  url: {{baseUrl}}/blog/newsletter/growth?days=30
  body: none
  auth: bearer
}

headers {
  X-Tenant-ID: {{tenantId}}
  X-Website-ID: {{websiteId}}
}

auth:bearer {
  token: {{accessToken}}
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("Response has correct structure", function() {
    const json = res.getBody();
    expect(json).to.have.property('status');
    expect(json).to.have.property('data');
    expect(json.status.success).to.be.true;
  });

  test("Growth data is array", function() {
    const data = res.getBody().data;
    expect(data).to.be.an('array');
    
    if (data.length > 0) {
      const item = data[0];
      expect(item).to.have.property('date');
      expect(item).to.have.property('subscribed');
      expect(item).to.have.property('unsubscribed');
      expect(item).to.have.property('net_growth');
      expect(item).to.have.property('total');
    }
  });
}
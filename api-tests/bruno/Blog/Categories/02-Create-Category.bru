meta {
  name: Create Category
  type: http
  seq: 2
}

post {
  url: {{base_url}}/api/cms/v1/blog/categories
  body: json
  auth: bearer
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

auth:bearer {
  token: {{bearer_token}}
}

body:json {
  {
    "name": "Test Category",
    "slug": "test-category",
    "description": "This is a test category for API testing",
    "parent_id": null,
    "sort_order": 1,
    "color": "#3498db",
    "is_active": true,
    "seo": {
      "meta_title": "Test Category - Blog",
      "meta_description": "Test category for blog posts",
      "meta_keywords": "test, category, blog"
    }
  }
}

tests {
  test("should return 201 status", function() {
    expect(res.getStatus()).to.equal(201);
  });

  test("should return created category", function() {
    expect(res.getBody()).to.have.property('data');
    expect(res.getBody().data).to.have.property('category');
    expect(res.getBody().data.category).to.have.property('id');
    expect(res.getBody().data.category).to.have.property('name');
    expect(res.getBody().data.category.name).to.equal('Test Category');
  });

  test("should store category ID for later use", function() {
    const category = res.getBody().data.category;
    bru.setVar('created_category_id', category.id);
    bru.setVar('created_category_slug', category.slug);
  });

  test("should have correct properties", function() {
    const category = res.getBody().data.category;
    expect(category.slug).to.equal('test-category');
    expect(category.description).to.equal('This is a test category for API testing');
    expect(category.parent_id).to.be.null;
    expect(category.sort_order).to.equal(1);
    expect(category.color).to.equal('#3498db');
    expect(category.is_active).to.be.true;
  });

  test("should have hierarchical fields", function() {
    const category = res.getBody().data.category;
    expect(category).to.have.property('level');
    expect(category).to.have.property('lft');
    expect(category).to.have.property('rgt');
    expect(category.level).to.equal(1); // Root level
  });
}
meta {
  name: Move Category
  type: http
  seq: 8
}

post {
  url: {{base_url}}/api/cms/v1/blog/categories/{{created_subcategory_id}}/move
  body: json
  auth: bearer
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

auth:bearer {
  token: {{bearer_token}}
}

body:json {
  {
    "new_parent_id": null,
    "position": "last"
  }
}

tests {
  test("should return 200 status", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("should return moved category", function() {
    expect(res.getBody()).to.have.property('data');
    expect(res.getBody().data).to.have.property('category');
    expect(res.getBody().data.category).to.have.property('id');
    expect(res.getBody().data.category.id).to.equal(parseInt(bru.getVar('created_subcategory_id')));
  });

  test("should have new parent (null = root)", function() {
    expect(res.getBody().data.category.parent_id).to.be.null;
  });

  test("should have updated level", function() {
    expect(res.getBody().data.category.level).to.equal(1); // Now root level
  });

  test("should have success message", function() {
    expect(res.getBody()).to.have.property('message');
    expect(res.getBody().message).to.contain('moved');
  });

  test("should have updated hierarchical values", function() {
    const category = res.getBody().data.category;
    expect(category).to.have.property('lft');
    expect(category).to.have.property('rgt');
    expect(category.lft).to.be.a('number');
    expect(category.rgt).to.be.a('number');
    expect(category.lft).to.be.lessThan(category.rgt);
  });
}
meta {
  name: Create Subcategory
  type: http
  seq: 3
}

post {
  url: {{base_url}}/api/cms/v1/blog/categories
  body: json
  auth: bearer
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

auth:bearer {
  token: {{bearer_token}}
}

body:json {
  {
    "name": "Test Subcategory",
    "slug": "test-subcategory",
    "description": "This is a test subcategory under test category",
    "parent_id": {{created_category_id}},
    "sort_order": 1,
    "color": "#e74c3c",
    "is_active": true,
    "seo": {
      "meta_title": "Test Subcategory - Blog",
      "meta_description": "Test subcategory for blog posts",
      "meta_keywords": "test, subcategory, blog"
    }
  }
}

tests {
  test("should return 201 status", function() {
    expect(res.getStatus()).to.equal(201);
  });

  test("should return created subcategory", function() {
    expect(res.getBody()).to.have.property('data');
    expect(res.getBody().data).to.have.property('category');
    expect(res.getBody().data.category).to.have.property('id');
    expect(res.getBody().data.category.name).to.equal('Test Subcategory');
  });

  test("should store subcategory ID for later use", function() {
    const category = res.getBody().data.category;
    bru.setVar('created_subcategory_id', category.id);
    bru.setVar('created_subcategory_slug', category.slug);
  });

  test("should have correct parent", function() {
    const category = res.getBody().data.category;
    expect(category.parent_id).to.equal(parseInt(bru.getVar('created_category_id')));
  });

  test("should have correct level", function() {
    const category = res.getBody().data.category;
    expect(category.level).to.equal(2); // Second level
  });

  test("should have hierarchical structure", function() {
    const category = res.getBody().data.category;
    expect(category).to.have.property('lft');
    expect(category).to.have.property('rgt');
    expect(category.lft).to.be.a('number');
    expect(category.rgt).to.be.a('number');
    expect(category.lft).to.be.lessThan(category.rgt);
  });
}
meta {
  name: Get Category by Slug
  type: http
  seq: 4
}

get {
  url: {{baseUrl}}/api/cms/v1/blog/categories/slug/technology?website_id=1
  body: none
  auth: bearer
}

auth:bearer {
  token: {{accessToken}}
}

headers {
  Content-Type: application/json
}

docs {
  ## Get Blog Category by Slug
  
  Retrieves a specific blog category by its slug.
  
  ### URL Parameters
  - `slug` - Category slug
  
  ### Query Parameters
  - `website_id` (required) - Website ID
}
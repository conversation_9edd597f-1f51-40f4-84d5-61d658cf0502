meta {
  name: Get Category By ID
  type: http
  seq: 5
}

get {
  url: {{base_url}}/api/cms/v1/blog/categories/{{created_category_id}}
  body: none
  auth: bearer
}

params:query {
  include_posts: true
  include_children: true
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

auth:bearer {
  token: {{bearer_token}}
}

tests {
  test("should return 200 status", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("should return category data", function() {
    expect(res.getBody()).to.have.property('data');
    expect(res.getBody().data).to.have.property('category');
    expect(res.getBody().data.category).to.have.property('id');
    expect(res.getBody().data.category.id).to.equal(parseInt(bru.getVar('created_category_id')));
  });

  test("should include all category fields", function() {
    const category = res.getBody().data.category;
    expect(category).to.have.property('name');
    expect(category).to.have.property('slug');
    expect(category).to.have.property('description');
    expect(category).to.have.property('parent_id');
    expect(category).to.have.property('sort_order');
    expect(category).to.have.property('color');
    expect(category).to.have.property('is_active');
    expect(category).to.have.property('level');
    expect(category).to.have.property('lft');
    expect(category).to.have.property('rgt');
    expect(category).to.have.property('created_at');
    expect(category).to.have.property('updated_at');
  });

  test("should include children when requested", function() {
    const category = res.getBody().data.category;
    expect(category).to.have.property('children');
    expect(category.children).to.be.an('array');
    expect(category.children.length).to.be.greaterThan(0);
    
    const subcategory = category.children.find(c => c.id === parseInt(bru.getVar('created_subcategory_id')));
    expect(subcategory).to.not.be.undefined;
    expect(subcategory.name).to.equal('Test Subcategory');
  });

  test("should include posts when requested", function() {
    const category = res.getBody().data.category;
    expect(category).to.have.property('posts');
    expect(category.posts).to.be.an('array');
    expect(category).to.have.property('posts_count');
    expect(category.posts_count).to.be.a('number');
  });
}
meta {
  name: List Categories
  type: http
  seq: 1
}

get {
  url: {{base_url}}/api/cms/v1/blog/categories
  body: none
  auth: bearer
}

params:query {
  page: 1
  limit: 20
  sort: name
  order: asc
  include_posts_count: true
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

auth:bearer {
  token: {{bearer_token}}
}

tests {
  test("should return 200 status", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("should return array of categories", function() {
    expect(res.getBody()).to.have.property('data');
    expect(res.getBody().data).to.have.property('categories');
    expect(res.getBody().data.categories).to.be.an('array');
  });

  test("should have pagination metadata", function() {
    expect(res.getBody().data).to.have.property('pagination');
    expect(res.getBody().data.pagination).to.have.property('page');
    expect(res.getBody().data.pagination).to.have.property('limit');
    expect(res.getBody().data.pagination).to.have.property('total');
  });

  test("should have required category fields", function() {
    const categories = res.getBody().data.categories;
    if (categories.length > 0) {
      const category = categories[0];
      expect(category).to.have.property('id');
      expect(category).to.have.property('name');
      expect(category).to.have.property('slug');
      expect(category).to.have.property('description');
      expect(category).to.have.property('parent_id');
      expect(category).to.have.property('sort_order');
      expect(category).to.have.property('created_at');
    }
  });

  test("should include posts count when requested", function() {
    const categories = res.getBody().data.categories;
    if (categories.length > 0) {
      categories.forEach(category => {
        expect(category).to.have.property('posts_count');
        expect(category.posts_count).to.be.a('number');
      });
    }
  });
}
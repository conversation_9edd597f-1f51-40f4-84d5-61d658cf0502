meta {
  name: Get Category By Slug
  type: http
  seq: 6
}

get {
  url: {{base_url}}/api/cms/v1/blog/categories/slug/{{created_category_slug}}
  body: none
  auth: bearer
}

params:query {
  include_posts: false
  include_children: false
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

auth:bearer {
  token: {{bearer_token}}
}

tests {
  test("should return 200 status", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("should return category data", function() {
    expect(res.getBody()).to.have.property('data');
    expect(res.getBody().data).to.have.property('category');
    expect(res.getBody().data.category).to.have.property('slug');
    expect(res.getBody().data.category.slug).to.equal(bru.getVar('created_category_slug'));
  });

  test("should have same ID as created category", function() {
    const category = res.getBody().data.category;
    expect(category.id).to.equal(parseInt(bru.getVar('created_category_id')));
  });

  test("should have correct data", function() {
    const category = res.getBody().data.category;
    expect(category.name).to.equal('Test Category');
    expect(category.description).to.equal('This is a test category for API testing');
    expect(category.color).to.equal('#3498db');
    expect(category.is_active).to.be.true;
  });
}
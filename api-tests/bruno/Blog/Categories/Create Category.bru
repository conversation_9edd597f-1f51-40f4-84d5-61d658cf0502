meta {
  name: Create Category
  type: http
  seq: 2
}

post {
  url: {{baseUrl}}/api/cms/v1/blog/categories
  body: json
  auth: bearer
}

auth:bearer {
  token: {{accessToken}}
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "website_id": 1,
    "name": "Technology",
    "slug": "technology",
    "description": "Technology related articles",
    "is_active": true,
    "parent_id": null,
    "meta_title": "Technology Blog",
    "meta_description": "Latest technology news and tutorials",
    "display_order": 1
  }
}

docs {
  ## Create Blog Category
  
  Creates a new blog category.
  
  ### Request Body
  - `website_id` (required) - Website ID
  - `name` (required) - Category name
  - `slug` (required) - URL-friendly slug
  - `description` - Category description
  - `is_active` - Whether category is active (default: true)
  - `parent_id` - Parent category ID for nested categories
  - `meta_title` - SEO meta title
  - `meta_description` - SEO meta description
  - `display_order` - Display order
}
meta {
  name: Update Category
  type: http
  seq: 7
}

put {
  url: {{base_url}}/api/cms/v1/blog/categories/{{created_category_id}}
  body: json
  auth: bearer
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

auth:bearer {
  token: {{bearer_token}}
}

body:json {
  {
    "name": "Updated Test Category",
    "description": "This is an updated test category description",
    "color": "#2ecc71",
    "sort_order": 2,
    "is_active": true,
    "seo": {
      "meta_title": "Updated Test Category - Blog",
      "meta_description": "Updated test category for blog posts",
      "meta_keywords": "updated, test, category, blog"
    }
  }
}

tests {
  test("should return 200 status", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("should return updated category", function() {
    expect(res.getBody()).to.have.property('data');
    expect(res.getBody().data).to.have.property('category');
    expect(res.getBody().data.category).to.have.property('id');
    expect(res.getBody().data.category.id).to.equal(parseInt(bru.getVar('created_category_id')));
  });

  test("should have updated name", function() {
    expect(res.getBody().data.category.name).to.equal('Updated Test Category');
  });

  test("should have updated description", function() {
    expect(res.getBody().data.category.description).to.equal('This is an updated test category description');
  });

  test("should have updated color", function() {
    expect(res.getBody().data.category.color).to.equal('#2ecc71');
  });

  test("should have updated sort order", function() {
    expect(res.getBody().data.category.sort_order).to.equal(2);
  });

  test("should have updated timestamp", function() {
    const category = res.getBody().data.category;
    expect(category).to.have.property('updated_at');
    expect(new Date(category.updated_at)).to.be.greaterThan(new Date(category.created_at));
  });

  test("should maintain hierarchical structure", function() {
    const category = res.getBody().data.category;
    expect(category).to.have.property('level');
    expect(category).to.have.property('lft');
    expect(category).to.have.property('rgt');
    expect(category.level).to.equal(1);
  });
}
meta {
  name: Get Category Hierarchy
  type: http
  seq: 4
}

get {
  url: {{base_url}}/api/cms/v1/blog/categories/hierarchy
  body: none
  auth: bearer
}

params:query {
  include_posts_count: true
  max_depth: 3
  root_id: 
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

auth:bearer {
  token: {{bearer_token}}
}

tests {
  test("should return 200 status", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("should return hierarchical structure", function() {
    expect(res.getBody()).to.have.property('data');
    expect(res.getBody().data).to.have.property('categories');
    expect(res.getBody().data.categories).to.be.an('array');
  });

  test("should have nested structure", function() {
    const categories = res.getBody().data.categories;
    if (categories.length > 0) {
      categories.forEach(category => {
        expect(category).to.have.property('children');
        expect(category.children).to.be.an('array');
        expect(category).to.have.property('level');
        expect(category.level).to.be.a('number');
      });
    }
  });

  test("should include our created categories", function() {
    const categories = res.getBody().data.categories;
    const findCategoryById = (cats, id) => {
      for (const cat of cats) {
        if (cat.id === id) return cat;
        const found = findCategoryById(cat.children, id);
        if (found) return found;
      }
      return null;
    };
    
    const parentCategory = findCategoryById(categories, parseInt(bru.getVar('created_category_id')));
    expect(parentCategory).to.not.be.null;
    expect(parentCategory.name).to.equal('Test Category');
    
    const childCategory = findCategoryById(categories, parseInt(bru.getVar('created_subcategory_id')));
    expect(childCategory).to.not.be.null;
    expect(childCategory.name).to.equal('Test Subcategory');
  });

  test("should have posts count when requested", function() {
    const categories = res.getBody().data.categories;
    const checkPostsCount = (cats) => {
      cats.forEach(cat => {
        expect(cat).to.have.property('posts_count');
        expect(cat.posts_count).to.be.a('number');
        if (cat.children.length > 0) {
          checkPostsCount(cat.children);
        }
      });
    };
    
    if (categories.length > 0) {
      checkPostsCount(categories);
    }
  });
}
meta {
  name: List Categories
  type: http
  seq: 1
}

get {
  url: {{baseUrl}}/api/cms/v1/blog/categories?page=1&page_size=20
  body: none
  auth: bearer
}

auth:bearer {
  token: {{accessToken}}
}

headers {
  Content-Type: application/json
}

docs {
  ## List Blog Categories
  
  This endpoint retrieves a paginated list of blog categories.
  
  ### Query Parameters
  - `page` - Page number (default: 1)
  - `page_size` - Items per page (default: 20)
  - `website_id` - Filter by website ID
  - `parent_id` - Filter by parent category ID
  - `is_active` - Filter by active status
  - `status` - Filter by status
  - `search` - Search by name or slug
  - `sort_by` - Sort field (default: name)
  - `sort_order` - Sort order: asc/desc (default: asc)
}
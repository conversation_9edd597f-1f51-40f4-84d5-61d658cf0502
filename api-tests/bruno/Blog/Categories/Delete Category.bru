meta {
  name: Delete Category
  type: http
  seq: 6
}

delete {
  url: {{baseUrl}}/api/cms/v1/blog/categories/1
  body: none
  auth: bearer
}

auth:bearer {
  token: {{accessToken}}
}

headers {
  Content-Type: application/json
}

docs {
  ## Delete Blog Category
  
  Soft deletes a blog category by setting its status to 'deleted'.
  
  ### URL Parameters
  - `id` - Category ID
  
  **Note**: This is a soft delete operation. The category will be marked as deleted but not removed from the database.
}
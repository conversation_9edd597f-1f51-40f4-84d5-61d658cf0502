meta {
  name: Update Category
  type: http
  seq: 5
}

put {
  url: {{baseUrl}}/api/cms/v1/blog/categories/1
  body: json
  auth: bearer
}

auth:bearer {
  token: {{accessToken}}
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "name": "Technology & Innovation",
    "description": "Latest technology news, tutorials and innovation insights",
    "meta_title": "Technology & Innovation Blog",
    "meta_description": "Stay updated with the latest technology trends and innovations"
  }
}

docs {
  ## Update Blog Category
  
  Updates an existing blog category.
  
  ### URL Parameters
  - `id` - Category ID
  
  ### Request Body
  All fields are optional:
  - `name` - Category name
  - `slug` - URL-friendly slug
  - `description` - Category description
  - `is_active` - Whether category is active
  - `parent_id` - Parent category ID
  - `meta_title` - SEO meta title
  - `meta_description` - SEO meta description
  - `display_order` - Display order
}
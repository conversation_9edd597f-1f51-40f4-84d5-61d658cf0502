meta {
  name: Posts - With Filters
  type: http
  seq: 3
}

get {
  url: {{base_url}}/api/cms/v1/blog/posts?website_id=1&limit=5&status=published&sort_by=created_at&sort_order=desc
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{auth_token}}
  X-Tenant-ID: {{tenant_id}}
  Content-Type: application/json
}

tests {
  test("should return 200 status", function() {
    expect(res.status).to.equal(200);
  });
  
  test("should filter by status", function() {
    expect(res.body.data).to.be.an('array');
    if (res.body.data.length > 0) {
      res.body.data.forEach(post => {
        expect(post.status).to.equal('published');
      });
    }
  });
  
  test("should have cursor pagination with limit", function() {
    expect(res.body.pagination.limit).to.equal(5);
  });
}
meta {
  name: Posts - Page Pagination
  type: http
  seq: 1
}

get {
  url: {{base_url}}/api/cms/v1/blog/posts?website_id=1&page=1&page_size=10
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{auth_token}}
  X-Tenant-ID: {{tenant_id}}
  Content-Type: application/json
}

tests {
  test("should return 200 status", function() {
    expect(res.status).to.equal(200);
  });
  
  test("should return page-based pagination meta", function() {
    expect(res.body.posts).to.be.an('array');
    expect(res.body.meta).to.be.an('object');
    expect(res.body.meta.page).to.be.a('number');
    expect(res.body.meta.page_size).to.be.a('number');
    expect(res.body.meta.total).to.be.a('number');
    expect(res.body.meta.total_pages).to.be.a('number');
  });
  
  test("should have pagination flags", function() {
    expect(res.body.meta.has_next).to.be.a('boolean');
    expect(res.body.meta.has_prev).to.be.a('boolean');
  });
}
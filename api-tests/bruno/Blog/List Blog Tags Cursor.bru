meta {
  name: List Blog Tags Cursor
  type: http
  seq: 30
}

get {
  url: {{baseUrl}}/blog/tags?website_id=1&limit=10
  body: none
  auth: bearer
}

params:query {
  website_id: 1
  limit: 10
  ~cursor: 
  ~search: 
  ~is_active: true
  ~status: active
  ~sort_by: name
  ~sort_order: asc
}

auth:bearer {
  token: {{authToken}}
}

headers {
  X-Tenant-ID: 1
}

script:post-response {
  // Extract token from response for use in other requests
  if (res.status === 200) {
    const responseData = res.getBody();
    
    // Check response structure
    bru.assertTrue(responseData.status, "Response should have status");
    bru.assertTrue(responseData.status.success, "Request should be successful");
    bru.assertTrue(responseData.data, "Response should have data");
    bru.assertTrue(responseData.data.tags, "Data should have tags array");
    bru.assertTrue(responseData.data.pagination, "Data should have pagination");
    
    // Check pagination structure
    const pagination = responseData.data.pagination;
    bru.assertTrue(pagination.hasOwnProperty('has_next'), "Pagination should have has_next");
    bru.assertTrue(pagination.hasOwnProperty('has_more'), "Pagination should have has_more");
    bru.assertTrue(pagination.hasOwnProperty('count'), "Pagination should have count");
    bru.assertTrue(pagination.hasOwnProperty('limit'), "Pagination should have limit");
    
    // If there are more results, save the cursor
    if (pagination.has_next && pagination.next_cursor) {
      bru.setEnvVar("nextTagCursor", pagination.next_cursor);
    }
    
    console.log(`Found ${responseData.data.tags.length} tags`);
    if (pagination.has_next) {
      console.log(`Next cursor: ${pagination.next_cursor}`);
    }
  }
}

docs {
  # List Blog Tags with Cursor Pagination
  
  This endpoint lists blog tags using cursor-based pagination.
  
  ## Query Parameters
  - `website_id` (required): The website ID
  - `cursor`: Pagination cursor from previous response
  - `limit`: Number of items per page (default: 20, max: 100)
  - `search`: Search filter for tag names/descriptions
  - `is_active`: Filter by active status
  - `status`: Filter by status (active, inactive, deleted)
  - `sort_by`: Sort field (id, name, created_at, updated_at, usage_count)
  - `sort_order`: Sort order (asc, desc)
  
  ## Response
  Returns a paginated list of tags with cursor information.
}
meta {
  name: Update Device <PERSON>ken
  type: http
  seq: 2
}

post {
  url: {{base_url}}/push/webhooks/token-update
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "old_token": "{{old_device_token}}",
    "new_token": "{{new_device_token}}",
    "platform": "ios"
  }
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should indicate token updated", function() {
    expect(res.getBody().data.updated).to.be.true;
  });
  
  test("Response should include success message", function() {
    expect(res.getBody().status.message).to.include("updated");
  });
}
meta {
  name: Test Push Notification
  type: http
  seq: 1
}

post {
  url: {{base_url}}/push/test
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "device_token": "{{test_device_token}}",
    "platform": "android",
    "title": "Test Notification",
    "body": "This is a test push notification from <PERSON>",
    "data": {
      "test": true,
      "timestamp": "{{timestamp}}"
    },
    "sound": "default",
    "badge": 1
  }
}

vars:pre-request {
  timestamp: new Date().toISOString()
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should indicate success", function() {
    expect(res.getBody().status.success).to.be.true;
  });
  
  test("Response should include device token", function() {
    expect(res.getBody().data.device_token).to.equal(req.getBody().device_token);
  });
}
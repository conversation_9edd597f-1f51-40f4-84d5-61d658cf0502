meta {
  name: Send Push via Notification
  type: http
  seq: 3
}

post {
  url: {{base_url}}/notifications
  body: json
  auth: bearer
}

auth:bearer {
  token: {{auth_token}}
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

body:json {
  {
    "type": "push_test",
    "channel": "push",
    "priority": "high",
    "subject": "Test Push Notification",
    "recipients": [
      {
        "recipient_type": "device",
        "recipient_address": "{{test_device_token}}",
        "device_token": "{{test_device_token}}"
      }
    ],
    "metadata": {
      "platform": "android",
      "sound": "default",
      "badge": 1,
      "test_mode": true
    }
  }
}

tests {
  test("Status should be 201", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("Response should include notification ID", function() {
    expect(res.getBody().data.id).to.be.a('number');
  });
  
  test("Channel should be push", function() {
    expect(res.getBody().data.channel).to.equal('push');
  });
  
  test("Recipients should be included", function() {
    expect(res.getBody().data.recipients).to.be.an('array');
    expect(res.getBody().data.recipients.length).to.equal(1);
  });
}

vars:post-response {
  notification_id: res.getBody().data.id
}
meta {
  name: Get User Notifications
  type: http
  seq: 6
}

get {
  url: {{base_url}}/api/cms/v1/notifications/me?page=1&limit=10&sort_by=created_at&sort_order=desc
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{auth_token}}
  X-Tenant-ID: {{tenant_id}}
  Content-Type: application/json
}

params:query {
  page: 1
  limit: 10
  sort_by: created_at
  sort_order: desc
  ~type: 
  ~channel: 
  ~status: 
  ~priority: 
  ~date_from: 
  ~date_to: 
}

tests {
  test("Status is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("Response has data array", function() {
    expect(res.getBody()).to.have.property('data');
    expect(res.getBody().data).to.be.an('array');
  });

  test("Response has pagination meta", function() {
    expect(res.getBody()).to.have.property('meta');
    expect(res.getBody().meta).to.have.property('total');
    expect(res.getBody().meta).to.have.property('page');
    expect(res.getBody().meta).to.have.property('limit');
    expect(res.getBody().meta).to.have.property('total_pages');
  });

  test("Notifications contain user-specific data", function() {
    const notifications = res.getBody().data;
    if (notifications.length > 0) {
      const notification = notifications[0];
      expect(notification).to.have.property('id');
      expect(notification).to.have.property('type');
      expect(notification).to.have.property('channel');
      expect(notification).to.have.property('status');
      expect(notification).to.have.property('priority');
      expect(notification).to.have.property('created_at');
    }
  });
}

docs {
  # Get User Notifications

  Retrieves notifications for the currently authenticated user within their tenant.

  ## Authentication
  - Requires valid JWT token in Authorization header
  - Requires X-Tenant-ID header

  ## Query Parameters
  - `page` (optional): Page number for pagination (default: 1)
  - `limit` (optional): Number of items per page (default: 20, max: 100)
  - `sort_by` (optional): Sort field (default: created_at)
  - `sort_order` (optional): Sort order - asc/desc (default: desc)
  - `type` (optional): Filter by notification type
  - `channel` (optional): Filter by channel (email, socket, push, sms)
  - `status` (optional): Filter by status (pending, queued, sent, delivered, failed, cancelled)
  - `priority` (optional): Filter by priority (low, normal, high, urgent)
  - `date_from` (optional): Filter from date (YYYY-MM-DD)
  - `date_to` (optional): Filter to date (YYYY-MM-DD)

  ## Response
  Returns paginated list of notifications specifically sent to the current user.

  ## Use Cases
  - User notification center/inbox
  - Mobile app notification history
  - Email notification dashboard
}
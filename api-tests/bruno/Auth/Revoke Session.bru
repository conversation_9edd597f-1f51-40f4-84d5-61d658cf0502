meta {
  name: Revoke Session
  type: http
  seq: 9
}

delete {
  url: {{api_url}}/auth/sessions/{{session_id}}
  body: none
  auth: bearer
}

headers {
  X-Tenant-ID: {{tenant_id}}
}

auth:bearer {
  token: {{bearer_token}}
}

tests {
  test("Revoke session returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has success structure", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
    expect(body).to.have.property('data');
  });
  
  test("Response contains revocation message", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('message');
    expect(body.data.message).to.contain('revoked');
  });
  
  test("Response contains revoked session ID", function() {
    const body = res.getBody();
    if (body.data.revoked_session_id) {
      expect(body.data.revoked_session_id).to.equal(bru.getVar('session_id'));
    }
  });
}
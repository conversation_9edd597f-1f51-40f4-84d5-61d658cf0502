# DEPRECATED FILE
# This endpoint no longer exists in the new user-only JWT architecture
# Tenant switching is now handled via X-Tenant-ID headers, not JWT token switches
# Use onboarding flow to create tenants and tenant membership endpoints to manage access

meta {
  name: Switch Tenant [DEPRECATED]
  type: http
  seq: 10
}

post {
  url: {{api_url}}/auth/switch-tenant
  body: json
  auth: bearer
}

headers {
  X-Tenant-ID: {{tenant_id}}
}

auth:bearer {
  token: {{bearer_token}}
}

body:json {
  {
    "tenant_id": "{{second_tenant_id}}"
  }
}

script:post-response {
  if (res.getStatus() === 200) {
    const body = res.getBody();
    if (body.success && body.data) {
      // Update tokens after successful tenant switch
      bru.setEnvVar("access_token", body.data.access_token);
      bru.setEnvVar("bearer_token", body.data.access_token);
      bru.setEnvVar("refresh_token", body.data.refresh_token);
      
      // Update current tenant ID
      bru.setEnvVar("tenant_id", body.data.current_tenant.id);
      
      console.log("Tenant switched successfully to:", body.data.current_tenant.name);
    }
  }
}

tests {
  test("This endpoint is deprecated", function() {
    expect(res.getStatus()).to.equal(404);
  });
}
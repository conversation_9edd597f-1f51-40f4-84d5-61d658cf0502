meta {
  name: Login
  type: http
  seq: 2
}

post {
  url: {{api_url}}/auth/login
  body: json
  auth: none
}

body:json {
  {
    "email": "<EMAIL>",
    "password": "MySecure895"
  }
}

script:pre-request {
  // Use test_email from registration or fallback to a default
  const testEmail = bru.getVar("test_email") || "<EMAIL>";
  bru.setVar("login_email", testEmail);
  console.log("Attempting login with email:", testEmail);
}

script:post-response {
  if (res.getStatus() === 200) {
    const body = res.getBody();
    if (body.success && body.data) {
      bru.setEnvVar("access_token", body.data.access_token);
      bru.setEnvVar("bearer_token", body.data.access_token);
      bru.setEnvVar("refresh_token", body.data.refresh_token);
      
      if (body.data.user) {
        bru.setEnvVar("user_id", body.data.user.id);
      }
      
      // Note: tenant information is no longer returned in login response
      // Tenant selection is now handled separately via onboarding or tenant switching
      
      console.log("Login successful - tokens saved");
    }
  }
}

tests {
  test("Login returns expected status", function() {
    const status = res.getStatus();
    // Should be 200 for successful login, 403 for email not verified, or 401 for invalid credentials
    expect([200, 401, 403]).to.include(status);
  });
  
  test("Handle login response based on email verification status", function() {
    const body = res.getBody();
    const status = res.getStatus();
    
    if (status === 200) {
      // Successful login
      expect(body).to.have.property('success', true);
      expect(body.data).to.have.property('access_token');
      expect(body.data).to.have.property('refresh_token');
      console.log("Login successful for verified user");
    } else if (status === 403) {
      // Email not verified
      expect(body).to.have.property('success', false);
      expect(body.error).to.have.property('message', 'Email not verified');
      console.log("Login blocked - email verification required");
    } else if (status === 401) {
      // Invalid credentials
      expect(body).to.have.property('success', false);
      expect(body.error).to.have.property('message', 'Invalid email or password');
      console.log("Login failed - invalid credentials");
    }
  });
  
  test("Access token is saved when login is successful", function() {
    const status = res.getStatus();
    if (status === 200) {
      expect(bru.getEnvVar("access_token")).to.not.be.empty;
    }
  });
}

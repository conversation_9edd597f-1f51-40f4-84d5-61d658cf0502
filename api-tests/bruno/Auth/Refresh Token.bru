meta {
  name: Refresh Token
  type: http
  seq: 3
}

post {
  url: {{api_url}}/auth/refresh
  body: json
  auth: none
}

body:json {
  {
    "refresh_token": "{{refresh_token}}"
  }
}

script:post-response {
  if (res.getStatus() === 200) {
    const body = res.getBody();
    if (body.success && body.data) {
      bru.setEnvVar("access_token", body.data.access_token);
      bru.setEnvVar("bearer_token", body.data.access_token);
      if (body.data.refresh_token) {
        bru.setEnvVar("refresh_token", body.data.refresh_token);
      }
      console.log("Token refreshed successfully");
    }
  }
}

tests {
  test("Refresh token returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has success structure", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
    expect(body).to.have.property('data');
  });
  
  test("Response contains new access token", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('access_token');
    expect(body.data).to.have.property('token_type', 'Bearer');
    expect(body.data).to.have.property('expires_in');
  });
  
  test("Access token is saved to environment", function() {
    const status = res.getStatus();
    if (status === 200) {
      expect(bru.getEnvVar("access_token")).to.not.be.empty;
      expect(bru.getEnvVar("bearer_token")).to.not.be.empty;
    }
  });
}
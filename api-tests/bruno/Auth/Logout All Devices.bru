meta {
  name: Logout All Devices
  type: http
  seq: 7
}

post {
  url: {{api_url}}/auth/logout-all
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

headers {
  Content-Type: application/json
}

script:post-response {
  if (res.status === 200) {
    console.log("✅ Logout from all devices successful");
    
    if (res.body.data) {
      const data = res.body.data;
      
      if (data.sessions_terminated !== undefined) {
        console.log("🔒 Sessions terminated:", data.sessions_terminated);
      }
    }
    
    // Clear all auth tokens after successful logout
    bru.setEnvVar("access_token", "");
    bru.setEnvVar("bearer_token", "");
    bru.setEnvVar("refresh_token", "");
    bru.setEnvVar("session_id", "");
    
    console.log("🧹 All authentication tokens cleared");
    console.log("⚠️  User must login again to access API");
    
  } else {
    console.log("❌ Logout all devices failed:", res.status);
    if (res.body && res.body.status) {
      console.log("Error:", res.body.status.message);
    }
  }
}

tests {
  test("Logout all devices successful", function() {
    expect(res.status).to.equal(200);
  });
  
  test("Response has success status", function() {
    expect(res.body.status.success).to.equal(true);
  });
  
  test("Response contains sessions terminated count", function() {
    expect(res.body.data).to.have.property('sessions_terminated');
    expect(res.body.data.sessions_terminated).to.be.a('number');
    expect(res.body.data.sessions_terminated).to.be.greaterThanOrEqual(1);
  });
  
  test("Response structure is correct", function() {
    expect(res.body).to.have.property('status');
    expect(res.body).to.have.property('data');
    expect(res.body.status).to.have.property('code');
    expect(res.body.status).to.have.property('message');
  });
  
  test("Response time is reasonable", function() {
    expect(res.responseTime).to.be.lessThan(3000);
  });
}
# Authentication API Tests

This directory contains comprehensive Bruno tests for the authentication module, covering all major authentication flows and endpoints.

## Test Collection Overview

### Core Authentication (8 tests)
- **Register.bru** - User registration with email verification
- **Login.bru** - User authentication with JWT tokens
- **Logout.bru** - Single session logout
- **Logout All Devices.bru** - Multi-device logout
- **Refresh Token.bru** - Token refresh functionality
- **Get Profile.bru** - User profile retrieval
- **Get Active Sessions.bru** - Session management
- **Switch Tenant.bru** - Multi-tenant context switching

### Email Verification (5 tests)
- **Verify Email.bru** - Email verification with token
- **Resend Verification.bru** - Resend verification email
- **Get Verification Status.bru** - Check verification status
- **Get Token Stats.bru** - Verification token statistics
- **Complete Flow Demo.bru** - Full verification flow demonstration

### Password Management (2 tests)
- **Forgot Password.bru** - Password reset request
- **Reset Password.bru** - Password reset completion

### Two-Factor Authentication (4 tests)
- **Enable Two Factor.bru** - Enable 2FA for user
- **Disable Two Factor.bru** - Disable 2FA for user
- **Verify Two Factor.bru** - Verify 2FA code
- **Complete Login.bru** - Complete 2FA login flow

### Additional Features (3 tests)
- **Register with Invitation.bru** - Invitation-based registration
- **Revoke Session.bru** - Individual session revocation
- **Change Password.bru** - Password change for authenticated users

## Recent Updates (Task-48)

### Fixed Issues:
1. **Bearer Token Consistency** - Updated all tests to use `bearer_token` variable
2. **Tenant Headers** - Added `X-Tenant-ID` header to all protected routes
3. **Environment Variables** - Consistent token management across tests
4. **Response Validation** - Enhanced test assertions for API responses
5. **Error Handling** - Better error scenario coverage

### Key Improvements:
1. **Consistent Token Management**
   - All tokens saved to both `access_token` and `bearer_token` variables
   - Proper token cleanup on logout operations
   - Environment variable synchronization

2. **Multi-Tenant Support**
   - All protected routes include `X-Tenant-ID` header
   - Tenant switching functionality tested
   - Tenant context validation

3. **Enhanced Error Handling**
   - Rate limiting scenarios (429 responses)
   - Email verification states (verified/unverified)
   - Token expiration and refresh scenarios

4. **Comprehensive Flow Testing**
   - Complete registration → verification → login flow
   - Multi-device session management
   - Tenant switching with token updates

## Usage

### Run All Auth Tests
```bash
# Run entire auth collection
bru run api-tests/bruno/Auth --env local

# Run specific auth modules
bru run api-tests/bruno/Auth/Email\ Verification --env local
bru run api-tests/bruno/Auth/Two\ Factor --env local
bru run api-tests/bruno/Auth/Password\ Reset --env local
```

### Run Individual Tests
```bash
# Core authentication flow
bru run api-tests/bruno/Auth/Register.bru --env local
bru run api-tests/bruno/Auth/Login.bru --env local
bru run api-tests/bruno/Auth/Get\ Profile.bru --env local

# Email verification flow
bru run api-tests/bruno/Auth/Email\ Verification/Complete\ Flow\ Demo.bru --env local

# Multi-tenant operations
bru run api-tests/bruno/Auth/Switch\ Tenant.bru --env local
```

## Test Flow Sequence

### Standard Authentication Flow
1. **Register** → Creates user with pending verification
2. **Get Verification Status** → Check verification requirements
3. **Verify Email** → Complete email verification (if required)
4. **Login** → Get access tokens
5. **Get Profile** → Access protected resources
6. **Get Active Sessions** → Manage user sessions
7. **Logout** → Clean up tokens

### Multi-Tenant Flow
1. **Login** → Get initial tenant context
2. **Switch Tenant** → Change tenant context
3. **Get Profile** → Verify tenant switch
4. **Use tenant-specific resources** → Access tenant data

## Environment Variables

Required environment variables in `local.bru`:
```
base_url: http://localhost:9077
api_prefix: /api/cms/v1
api_url: {{base_url}}{{api_prefix}}
bearer_token: (auto-populated by tests)
access_token: (auto-populated by tests)
refresh_token: (auto-populated by tests)
tenant_id: 1
user_id: (auto-populated by tests)
```

## API Endpoints Covered

### Public Endpoints (No Authentication)
- `POST /auth/register` - User registration
- `POST /auth/login` - User authentication
- `POST /auth/refresh` - Token refresh
- `POST /auth/verify-email` - Email verification
- `POST /auth/resend-verification` - Resend verification
- `GET /auth/verification-status` - Check verification status
- `POST /auth/forgot-password` - Password reset request
- `POST /auth/reset-password` - Password reset completion
- `POST /auth/2fa/complete-login` - Complete 2FA login
- `GET /auth/health` - Health check

### Protected Endpoints (Require Authentication)
- `GET /auth/profile` - User profile
- `POST /auth/logout` - Single session logout
- `POST /auth/logout-all` - Multi-device logout
- `GET /auth/sessions` - Active sessions
- `DELETE /auth/sessions/:id` - Revoke session
- `POST /auth/switch-tenant` - Switch tenant context
- `GET /auth/verification-token-stats` - Token statistics
- `POST /auth/2fa/enable` - Enable 2FA
- `POST /auth/2fa/disable` - Disable 2FA
- `POST /auth/2fa/verify` - Verify 2FA code

## Features Tested

### Core Features
- ✅ User registration with email verification
- ✅ JWT token-based authentication
- ✅ Session management (single/multi-device)
- ✅ Token refresh mechanism
- ✅ Profile management
- ✅ Multi-tenant context switching

### Security Features
- ✅ Email verification with rate limiting
- ✅ Password reset flow
- ✅ Two-factor authentication
- ✅ Session revocation
- ✅ Token blacklisting (via logout)

### Error Handling
- ✅ Rate limiting (429 responses)
- ✅ Invalid credentials (401 responses)
- ✅ Email verification required (403 responses)
- ✅ Token expiration handling
- ✅ Malformed request validation (400 responses)

### Multi-Tenant Support
- ✅ Tenant-scoped authentication
- ✅ Tenant switching with token updates
- ✅ Tenant context validation
- ✅ Cross-tenant access prevention

## Debugging

### Console Output
Tests provide detailed console logging for:
- Request parameters and timing
- Response status and data
- Token management operations
- Error scenarios and debugging info

### Common Issues
1. **Token Mismatch** - Ensure `bearer_token` variable is populated
2. **Tenant Headers** - Verify `X-Tenant-ID` header is included
3. **Email Verification** - Check if verification is required/enabled
4. **Rate Limiting** - Wait between verification email requests

### Health Check
Use the auth health endpoint to verify module status:
```bash
bru run api-tests/bruno/Auth/Email\ Verification/Complete\ Flow\ Demo.bru --env local
```
meta {
  name: Register
  type: http
  seq: 1
}

post {
  url: {{api_url}}/auth/register
  body: json
  auth: none
}

script:pre-request {
  console.log("Sending registration request to:", bru.getEnvVar("api_url") + "/auth/register");
  console.log("Request body will contain email:", "test-" + Date.now() + "-" + Math.floor(Math.random() * 1000000) + "@example.com");
}

body:json {
  {
    "email": "test-{{$timestamp}}-{{$randomInt}}@example.com",
    "password": "MySecure895",
    "first_name": "Test",
    "last_name": "User"
  }
}

tests {
  test("API responds with expected status", function() {
    const status = res.getStatus();
    console.log("Registration response status:", status);
    
    // For debugging: log the full response when there's an error
    if (status >= 400) {
      console.log("Error response body:", JSON.stringify(res.getBody(), null, 2));
    }
    
    // Registration should return 201 for success
    // Handle common error cases for better debugging
    if (status === 500) {
      console.log("Server error occurred - check server logs");
    } else if (status === 409) {
      console.log("Conflict - email or username already exists");
    } else if (status === 400) {
      console.log("Bad request - validation error");
    }
    
    expect(status).to.equal(201);
  });
  
  test("Response has correct structure", function() {
    const body = res.getBody();
    const status = res.getStatus();
    
    if (status === 201) {
      expect(body).to.have.property('success', true);
      expect(body).to.have.property('data');
      expect(body.data).to.have.property('user');
      
      // User should have required fields
      expect(body.data.user).to.have.property('id');
      expect(body.data.user).to.have.property('email');
      expect(body.data.user).to.have.property('first_name');
      expect(body.data.user).to.have.property('last_name');
    }
  });
  
  test("Registration creates user only (no tenant)", function() {
    const body = res.getBody();
    const status = res.getStatus();
    
    if (status === 201) {
      // Registration only creates user, tenant creation is handled in onboarding
      expect(body.data).to.not.have.property('tenant');
      expect(body.data).to.not.have.property('current_tenant_id');
      expect(body.data).to.not.have.property('tenant_memberships');
      console.log("User created successfully without tenant assignment");
    }
  });
  
  test("Handle email verification flow", function() {
    const body = res.getBody();
    const status = res.getStatus();
    
    if (status === 201 && body.data) {
      if (body.data.requires_email_verification) {
        // Email verification required - no tokens should be present
        expect(body.data).to.have.property('requires_email_verification', true);
        expect(body.data).to.have.property('message');
        expect(body.data).to.not.have.property('access_token');
        expect(body.data).to.not.have.property('refresh_token');
        console.log("Email verification required for:", body.data.user.email);
      } else {
        // Email verification not required - tokens should be present
        expect(body.data).to.have.property('access_token');
        expect(body.data).to.have.property('refresh_token');
        expect(body.data).to.have.property('token_type', 'Bearer');
        expect(body.data).to.have.property('expires_in');
        console.log("Registration completed with tokens for:", body.data.user.email);
      }
    }
  });
  
  test("Save user data for further testing", function() {
    const body = res.getBody();
    const status = res.getStatus();
    
    if (status === 201 && body.data) {
      bru.setVar('test_user_id', body.data.user.id);
      bru.setVar('test_email', body.data.user.email);
      
      // Note: tenant data is no longer returned in registration response
      
      if (body.data.access_token) {
        bru.setEnvVar('auth_token', body.data.access_token);
        bru.setEnvVar('access_token', body.data.access_token);
        bru.setEnvVar('bearer_token', body.data.access_token);
      }
      
      if (body.data.refresh_token) {
        bru.setEnvVar('refresh_token', body.data.refresh_token);
      }
      
      console.log("Saved test data for user:", body.data.user.email);
    }
  });
}

meta {
  name: Logout
  type: http
  seq: 5
}

post {
  url: {{api_url}}/auth/logout
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

headers {
  Content-Type: application/json
}

script:post-response {
  if (res.status === 200) {
    console.log("✅ Logout successful");
    
    if (res.body.data && res.body.data.message) {
      console.log("📝 Message:", res.body.data.message);
    }
    
    // Clear current session tokens only (not all devices)
    bru.setEnvVar("access_token", "");
    bru.setEnvVar("bearer_token", "");
    bru.setEnvVar("refresh_token", "");
    bru.setEnvVar("session_id", "");
    
    console.log("🧹 Current session tokens cleared");
    console.log("⚠️  User must login again to access API");
    
  } else {
    console.log("❌ Logout failed:", res.status);
    if (res.body && res.body.status) {
      console.log("Error:", res.body.status.message);
    }
  }
}

tests {
  test("Logout successful", function() {
    expect(res.status).to.equal(200);
  });
  
  test("Response has success status", function() {
    expect(res.body.status.success).to.equal(true);
  });
  
  test("Response contains logout message", function() {
    expect(res.body.data).to.have.property('message');
    expect(res.body.data.message).to.be.a('string');
  });
  
  test("Response structure is correct", function() {
    expect(res.body).to.have.property('status');
    expect(res.body).to.have.property('data');
    expect(res.body.status).to.have.property('code');
    expect(res.body.status).to.have.property('message');
  });
  
  test("Response time is reasonable", function() {
    expect(res.responseTime).to.be.lessThan(3000);
  });
}
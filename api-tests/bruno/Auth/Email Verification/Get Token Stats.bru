meta {
  name: Get Token Stats
  type: http
  seq: 4
}

get {
  url: {{api_url}}/auth/verification-token-stats
  body: none
  auth: bearer
}

headers {
  X-Tenant-ID: {{tenant_id}}
}

auth:bearer {
  token: {{bearer_token}}
}

tests {
  test("Get token stats returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has success status", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
  });
  
  test("Response contains statistics data", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('statistics');
    expect(body.data).to.have.property('recent_tokens');
  });
  
  test("Statistics object has expected properties", function() {
    const body = res.getBody();
    const stats = body.data.statistics;
    expect(stats).to.have.property('total_created');
    expect(stats).to.have.property('total_used');
    expect(stats).to.have.property('total_expired');
    expect(stats).to.have.property('total_active');
    expect(stats).to.have.property('total_resends');
    
    // All stats should be numbers
    expect(stats.total_created).to.be.a('number');
    expect(stats.total_used).to.be.a('number');
    expect(stats.total_expired).to.be.a('number');
    expect(stats.total_active).to.be.a('number');
    expect(stats.total_resends).to.be.a('number');
  });
  
  test("Recent tokens is an array", function() {
    const body = res.getBody();
    expect(body.data.recent_tokens).to.be.an('array');
  });
}
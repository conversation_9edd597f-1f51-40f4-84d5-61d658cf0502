meta {
  name: Complete Flow Demo
  type: http
  seq: 5
}

post {
  url: {{api_url}}/auth/register
  body: json
  auth: none
}

body:json {
  {
    "email": "verification-test-{{$randomInt}}@example.com",
    "password": "MySecure895",
    "first_name": "Verification",
    "last_name": "Test"
  }
}

script:post-response {
  if (res.getStatus() === 201) {
    const body = res.getBody();
    if (body.success && body.data) {
      // Store test user data
      bru.setVar("flow_test_email", body.data.user.email);
      bru.setVar("flow_test_user_id", body.data.user.id);
      
      console.log("🚀 Email Verification Flow Demo");
      console.log("📧 Test Email:", body.data.user.email);
      
      if (body.data.requires_email_verification) {
        console.log("✅ Email verification is required - testing verification flow");
        console.log("📝 Registration successful without tokens");
        console.log("💌 Verification email should be sent");
      } else {
        console.log("⚠️  Email verification is disabled - user gets immediate access");
        console.log("🔑 Access token provided immediately");
      }
    }
  }
}

tests {
  test("Registration creates user successfully", function() {
    expect(res.getStatus()).to.equal(201);
    const body = res.getBody();
    expect(body).to.have.property('success', true);
    expect(body.data).to.have.property('user');
  });
  
  test("Demonstrate verification flow requirements", function() {
    const body = res.getBody();
    
    if (body.data.requires_email_verification) {
      // Verification required scenario
      expect(body.data).to.have.property('requires_email_verification', true);
      expect(body.data).to.have.property('message');
      expect(body.data).to.not.have.property('access_token');
      
      console.log("📋 Next steps in verification flow:");
      console.log("1. Check verification status at: GET /auth/verification-status");
      console.log("2. Resend email if needed at: POST /auth/resend-verification");
      console.log("3. Complete verification at: POST /auth/verify-email");
      console.log("4. Then login normally at: POST /auth/login");
    } else {
      // No verification required
      expect(body.data).to.have.property('access_token');
      expect(body.data).to.have.property('refresh_token');
      
      console.log("📋 User can immediately access protected resources");
    }
  });
  
  test("User data is properly structured", function() {
    const body = res.getBody();
    expect(body.data.user).to.have.property('id');
    expect(body.data.user).to.have.property('email');
    expect(body.data.user).to.have.property('email_verified');
    expect(body.data.user).to.have.property('status');
    
    if (body.data.requires_email_verification) {
      expect(body.data.user.email_verified).to.equal(false);
      expect(body.data.user.status).to.equal('pending_verification');
    } else {
      expect(body.data.user.email_verified).to.equal(true);
      expect(body.data.user.status).to.equal('active');
    }
  });
}
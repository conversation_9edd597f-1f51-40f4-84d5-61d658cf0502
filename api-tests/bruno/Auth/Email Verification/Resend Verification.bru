meta {
  name: Resend Verification
  type: http
  seq: 2
}

post {
  url: {{api_url}}/auth/resend-verification
  body: json
  auth: none
}

body:json {
  {
    "email": "{{test_email}}"
  }
}

script:pre-request {
  // Use test_email from registration if available
  const testEmail = bru.getVar("test_email") || "<EMAIL>";
  bru.setVar("test_email", testEmail);
  console.log("Resending verification to:", testEmail);
}

tests {
  test("Resend verification returns expected status", function() {
    const status = res.getStatus();
    // Can be 200 for success, 400 for invalid email, 429 for rate limit
    expect([200, 400, 429]).to.include(status);
  });
  
  test("Response has success structure", function() {
    const body = res.getBody();
    expect(body).to.have.property('success');
    expect(body).to.have.property('data');
  });
  
  test("Response contains resend message", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('message');
  });
  
  test("Handle successful resend", function() {
    const body = res.getBody();
    const status = res.getStatus();
    
    if (status === 200) {
      expect(body.success).to.be.true;
      expect(body.data.message).to.contain('verification');
      
      // Should contain attempt tracking
      if (body.data.attempts_remaining !== undefined) {
        expect(body.data.attempts_remaining).to.be.a('number');
      }
    }
  });
  
  test("Handle rate limiting", function() {
    const body = res.getBody();
    const status = res.getStatus();
    
    if (status === 429) {
      expect(body.success).to.be.false;
      expect(body.error).to.have.property('message');
      expect(body.error.message).to.contain('rate limit');
    }
  });
}
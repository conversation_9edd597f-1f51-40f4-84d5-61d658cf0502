meta {
  name: Get Verification Status
  type: http
  seq: 3
}

get {
  url: {{api_url}}/auth/verification-status?email={{test_email}}
  body: none
  auth: none
}

params:query {
  email: {{test_email}}
}

script:pre-request {
  // Use test_email from registration if available
  const testEmail = bru.getVar("test_email") || "<EMAIL>";
  bru.setVar("test_email", testEmail);
  console.log("Checking verification status for:", testEmail);
}

tests {
  test("Get verification status returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has success status", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
  });
  
  test("Response contains verification status data", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('email');
    expect(body.data).to.have.property('has_token');
    expect(body.data).to.have.property('is_verified');
    expect(body.data).to.have.property('can_resend');
  });
  
  test("Email in response matches requested email", function() {
    const body = res.getBody();
    expect(body.data.email).to.equal(bru.getVar('test_email'));
  });
  
  test("Verification status fields are valid", function() {
    const body = res.getBody();
    expect(body.data.has_token).to.be.a('boolean');
    expect(body.data.is_verified).to.be.a('boolean');
    expect(body.data.can_resend).to.be.a('boolean');
  });
  
  test("Rate limit information is included", function() {
    const body = res.getBody();
    if (body.data.attempts_remaining !== undefined) {
      expect(body.data.attempts_remaining).to.be.a('number');
    }
    
    if (body.data.next_resend_at) {
      expect(new Date(body.data.next_resend_at)).to.be.instanceOf(Date);
    }
  });
}
meta {
  name: Verify Email
  type: http
  seq: 1
}

post {
  url: {{api_url}}/auth/verify-email
  body: json
  auth: none
}

body:json {
  {
    "token": "d3f560c0d2a68c791f8b3a3b05f3f0f20f37c9b4529707bbf7589459106b9fc5"
  }
}

script:post-response {
  if (res.getStatus() === 200) {
    const body = res.getBody();
    if (body.success && body.data) {
      if (body.data.access_token) {
        bru.setEnvVar("access_token", body.data.access_token);
        bru.setEnvVar("bearer_token", body.data.access_token);
        bru.setEnvVar("refresh_token", body.data.refresh_token);
        console.log("Email verified successfully - tokens received");
      }
    }
  }
}

tests {
  test("Email verification returns expected status", function() {
    const status = res.getStatus();
    // Can be 200 for success, 400 for invalid/expired token, 409 for already verified
    expect([200, 400, 409]).to.include(status);
  });
  
  test("Response has success structure", function() {
    const body = res.getBody();
    expect(body).to.have.property('success');
    expect(body).to.have.property('data');
  });
  
  test("Response contains verification message", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('message');
  });
  
  test("Handle successful verification", function() {
    const body = res.getBody();
    const status = res.getStatus();
    
    if (status === 200) {
      expect(body.success).to.be.true;
      expect(body.data).to.have.property('user');
      expect(body.data.user).to.have.property('email_verified', true);
      
      // Should receive tokens after verification
      if (body.data.access_token) {
        expect(body.data).to.have.property('access_token');
        expect(body.data).to.have.property('refresh_token');
        expect(body.data).to.have.property('token_type', 'Bearer');
      }
    }
  });
}

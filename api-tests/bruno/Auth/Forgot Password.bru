meta {
  name: Forgot Password
  type: http
  seq: 6
}

post {
  url: {{api_url}}/auth/forgot-password
  body: json
  auth: none
}

script:pre-request {
  // Use test_email from registration or fallback to a default
  const testEmail = bru.getVar("test_email") || "<EMAIL>";
  bru.setVar("forgot_password_email", testEmail);
  console.log("Sending forgot password request for:", testEmail);
}

body:json {
  {
    "email": "{{forgot_password_email}}"
  }
}

tests {
  test("Forgot password returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has success status", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
  });
}
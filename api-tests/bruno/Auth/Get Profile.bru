meta {
  name: Get Profile
  type: http
  seq: 4
}

get {
  url: {{api_url}}/auth/profile
  body: none
  auth: bearer
}

headers {
}

auth:bearer {
  token: {{bearer_token}}
}

tests {
  test("Profile returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response contains user profile", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
    expect(body).to.have.property('data');
    expect(body.data).to.have.property('user');
    expect(body.data.user).to.have.property('id');
    expect(body.data.user).to.have.property('email');
    expect(body.data.user).to.have.property('first_name');
    expect(body.data.user).to.have.property('last_name');
  });
  
  test("Profile contains user data only (no tenant)", function() {
    const body = res.getBody();
    // Profile endpoint now returns user data only, no tenant info
    expect(body.data).to.not.have.property('tenant');
    expect(body.data).to.not.have.property('current_tenant_id');
    expect(body.data).to.not.have.property('tenant_memberships');
  });
}
meta {
  name: Get Active Sessions
  type: http
  seq: 8
}

get {
  url: {{api_url}}/auth/sessions
  body: none
  auth: bearer
}

headers {
  X-Tenant-ID: {{tenant_id}}
}

auth:bearer {
  token: {{bearer_token}}
}

tests {
  test("Get active sessions returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has success structure", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
    expect(body).to.have.property('data');
  });
  
  test("Response contains sessions array", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('sessions');
    expect(body.data.sessions).to.be.an('array');
  });
  
  test("Sessions have required fields", function() {
    const body = res.getBody();
    if (body.data.sessions.length > 0) {
      const session = body.data.sessions[0];
      expect(session).to.have.property('id');
      expect(session).to.have.property('device_info');
      expect(session).to.have.property('ip_address');
      expect(session).to.have.property('last_activity');
      expect(session).to.have.property('created_at');
    }
  });
  
  test("Save session ID for revocation test", function() {
    const body = res.getBody();
    if (body.data.sessions.length > 0) {
      const session = body.data.sessions[0];
      bru.setVar('session_id', session.id);
    }
  });
}
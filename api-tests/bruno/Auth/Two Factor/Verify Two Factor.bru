meta {
  name: Verify Two Factor
  type: http
  seq: 2
}

post {
  url: {{api_url}}/2fa/verify
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "totp_code": "123456"
  }
}

tests {
  test("Verify 2FA returns 200 or 400", function() {
    const status = res.getStatus();
    expect([200, 400]).to.include(status);
  });
  
  test("Response has success status", function() {
    const body = res.getBody();
    expect(body).to.have.property('success');
  });
  
  test("Response contains verification result", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('verified');
  });
}
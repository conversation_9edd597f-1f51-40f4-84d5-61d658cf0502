meta {
  name: Disable Two Factor
  type: http
  seq: 3
}

post {
  url: {{api_url}}/2fa/disable
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "password": "Password123!",
    "totp_code": "123456"
  }
}

tests {
  test("Disable 2FA returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has success status", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
  });
  
  test("Response contains disable confirmation", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('message');
  });
}
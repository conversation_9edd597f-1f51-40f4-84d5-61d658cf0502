meta {
  name: Complete Login
  type: http
  seq: 4
}

post {
  url: {{api_url}}/2fa/complete-login
  body: json
  auth: none
}

body:json {
  {
    "temp_token": "temp_token_here",
    "totp_code": "123456"
  }
}

script:post-response {
  if (res.getStatus() === 200) {
    const body = res.getBody();
    if (body.success && body.data) {
      bru.setEnvVar("access_token", body.data.access_token);
      bru.setEnvVar("refresh_token", body.data.refresh_token);
      
      if (body.data.user) {
        bru.setEnvVar("user_id", body.data.user.id);
      }
    }
  }
}

tests {
  test("Complete 2FA login returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response contains tokens", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('access_token');
    expect(body.data).to.have.property('refresh_token');
  });
  
  test("Response contains user data", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('user');
    expect(body.data.user).to.have.property('id');
  });
}
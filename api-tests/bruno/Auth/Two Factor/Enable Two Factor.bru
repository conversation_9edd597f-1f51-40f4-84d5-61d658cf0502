meta {
  name: Enable Two Factor
  type: http
  seq: 1
}

post {
  url: {{api_url}}/2fa/enable
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "password": "Password123!"
  }
}

script:post-response {
  if (res.getStatus() === 200) {
    const body = res.getBody();
    if (body.data && body.data.qr_code_url) {
      bru.setEnvVar("totp_secret", body.data.secret);
    }
  }
}

tests {
  test("Enable 2FA returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response contains 2FA setup data", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('secret');
    expect(body.data).to.have.property('qr_code_url');
    expect(body.data).to.have.property('backup_codes');
  });
  
  test("Backup codes are provided", function() {
    const body = res.getBody();
    expect(body.data.backup_codes).to.be.an('array');
    expect(body.data.backup_codes.length).to.be.greaterThan(0);
  });
}
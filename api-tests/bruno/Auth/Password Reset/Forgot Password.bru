meta {
  name: Forgot Password
  type: http
  seq: 1
}

post {
  url: {{api_url}}/auth/forgot-password
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

body:json {
  {
    "email": "<EMAIL>"
  }
}

tests {
  test("Forgot password returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has success status", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
  });
  
  test("Response contains confirmation message", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('message');
    expect(body.data.message).to.include('password reset link');
  });
}

meta {
  name: Reset Password
  type: http
  seq: 2
}

post {
  url: {{api_url}}/auth/reset-password
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "token": "reset_token_here",
    "new_password": "NewPassword123!",
    "confirm_password": "NewPassword123!"
  }
}

tests {
  test("Password reset returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has success status", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
  });
  
  test("Response contains reset confirmation", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('message');
  });
}
---
id: task-171
title: <PERSON><PERSON><PERSON> viết phổ biến theo thời gian
status: Backlog
assignee: []
created_date: '2025-07-25'
labels: []
dependencies: []
---

## Description

Phân tích và hiển thị trending posts theo các khoảng thời gian khác nhau với scoring algorithm

## Acceptance Criteria

- [ ] Tạo migration cho bảng blog_post_trends
- [ ] Tạo models cho trending analysis
- [ ] Tạo repository cho trend calculations
- [ ] Tạo service xử lý trending algorithms
- [ ] Tạo endpoint GET /blog/posts/trending?period=day|week|month
- [ ] Cải tiến endpoint GET /blog/posts/popular
- [ ] Implement trending score calculation
- [ ] Test trending algorithm accuracy

---
id: task-161
title: Media Gallery trong bài viết
status: Backlog
assignee: []
created_date: '2025-07-25'
labels: []
dependencies: []
---

## Description

Tích hợp media gallery để quản lý hình <PERSON>nh, video trong bài viết với lazy loading và optimization

## Acceptance Criteria

- [ ] Tạo migration cho bảng blog_post_media
- [ ] Tạo models và DTOs cho media management
- [ ] Tạo repository cho media operations
- [ ] Tạo service xử lý media upload/optimization
- [ ] Tạo endpoints: POST /blog/posts/:id/media upload
- [ ] Tạo endpoints: GET /blog/posts/:id/media và DELETE /blog/posts/:id/media/:mediaId
- [ ] Tích hợp với media module hiện tại
- [ ] Test media gallery functionality

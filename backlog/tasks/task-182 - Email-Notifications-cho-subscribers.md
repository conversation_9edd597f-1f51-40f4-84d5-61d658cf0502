---
id: task-182
title: Email Notifications cho subscribers
status: Backlog
assignee: []
created_date: '2025-07-25'
labels: []
dependencies: []
---

## Description

Hệ thống thông báo email tự động khi có bài viết mới dựa trên user preferences

## Acceptance Criteria

- [ ] Tạo migration cho bảng blog_email_subscriptions
- [ ] Tạo migration cho notification_preferences
- [ ] Tạo models cho email notifications
- [ ] Tạo repository cho email subscription management
- [ ] Tạo service xử lý email notifications
- [ ] Tạo endpoints: POST /blog/notifications/subscribe
- [ ] Tạo endpoints: PUT /blog/notifications/preferences
- [ ] Tích hợp với notification module
- [ ] Test email delivery và unsubscribe

# Push Notification Configuration
# This file contains environment variables for configuring push notifications

# Enable/disable push notifications
PUSH_ENABLED=true

# Provider type: fcm, apns, or hybrid
PUSH_PROVIDER=hybrid

# Firebase Cloud Messaging (FCM) Configuration
FCM_SERVICE_ACCOUNT_PATH=/path/to/firebase-service-account.json
FCM_PROJECT_ID=your-firebase-project-id
FCM_DEFAULT_ICON=@drawable/ic_notification
FCM_DEFAULT_SOUND=default
FCM_DEFAULT_CLICK_ACTION=FLUTTER_NOTIFICATION_CLICK
FCM_ENABLE_ANALYTICS=true
FCM_DRY_RUN=false

# Apple Push Notification Service (APNS) Configuration
# Token-based authentication (recommended)
APNS_AUTH_KEY_PATH=/path/to/AuthKey_XXXXXXXXXX.p8
APNS_KEY_ID=XXXXXXXXXX
APNS_TEAM_ID=XXXXXXXXXX
APNS_BUNDLE_ID=com.yourcompany.yourapp

# Certificate-based authentication (legacy)
APNS_CERTIFICATE_PATH=/path/to/certificate.p12
APNS_CERTIFICATE_KEY=certificate_password

# APNS Environment
APNS_PRODUCTION=false
APNS_DEFAULT_SOUND=default

# Example configurations for different environments:

## Development Environment
# PUSH_ENABLED=true
# PUSH_PROVIDER=fcm
# FCM_PROJECT_ID=your-dev-project
# FCM_SERVICE_ACCOUNT_PATH=./config/firebase-dev-service-account.json
# APNS_PRODUCTION=false

## Production Environment  
# PUSH_ENABLED=true
# PUSH_PROVIDER=hybrid
# FCM_PROJECT_ID=your-prod-project
# FCM_SERVICE_ACCOUNT_PATH=./config/firebase-prod-service-account.json
# APNS_PRODUCTION=true

## Testing Environment (Mock Provider)
# PUSH_ENABLED=false
# Blog API v3 Project Structure

## Root Directory Structure
```
blog-api-v3/
├── cmd/                    # Application entry points
│   └── server/            # Main server application
├── internal/              # Private application code
│   ├── config/           # Configuration management
│   ├── middleware/       # HTTP middleware
│   ├── database/         # Database migrations and seeders
│   │   ├── migrations/   # Module-based migrations (001-999)
│   │   └── seeders/      # Data seeders
│   └── modules/          # Business modules
├── pkg/                   # Public packages
│   ├── auth/             # Authentication utilities
│   ├── cache/            # Caching utilities
│   ├── database/         # Database connection and utilities
│   ├── http/             # HTTP utilities and responses
│   ├── logging/          # Logging utilities
│   ├── utils/            # Common utilities
│   └── validator/        # Validation utilities
├── api-tests/            # API testing
│   └── bruno/            # Bruno collections for API testing
├── docs/                 # Documentation
│   ├── api/             # API documentation
│   └── modules/         # Module-specific docs
├── scripts/              # Build and utility scripts
├── backlog/              # Task management (CLI tool)
├── bin/                  # Build outputs (gitignored)
└── configs/              # Configuration files
```

## Module Structure
Each module under `internal/modules/` follows this pattern:
```
{module_name}/
├── models/          # Domain models, entities, DTOs
├── repositories/    # Data access layer
├── services/        # Business logic layer
├── handlers/        # HTTP request handlers
├── dto/            # Data transfer objects (optional)
├── middleware/     # Module-specific middleware (optional)
└── routes.go       # Route registration
```

## Available Modules
- **a_tenant** (001-099): Multi-tenancy core
- **b_website** (101-199): Website/blog management
- **c_user** (201-299): User management
- **d_auth** (301-399): Authentication & authorization
- **e_rbac** (401-499): Role-based access control
- **f_onboarding** (501-599): User onboarding
- **g_blog** (601-699): Blog posts and content
- **h_media** (701-799): Media management
- **i_seo** (801-899): SEO features
- **j_ai** (901-999): AI integration
- **k_ads**: Advertisement management
- **l_notification**: Notification system
- **m_settings**: Application settings
- **n_trello**: Trello integration
- **o_apikey**: API key management

## Migration Numbering
- Each module has a reserved range (e.g., auth: 301-399)
- Migrations numbered sequentially within module range
- Format: `{number}_{description}.{up|down}.sql`

## Key Files
- `Makefile`: Build automation and commands
- `.env.example`: Environment configuration template
- `CLAUDE.md`: Project-specific AI instructions
- `go.mod`: Go module definition
- `docker-compose.yml`: Local development services

## Import Path Convention
Always use full module path:
```go
import "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
```
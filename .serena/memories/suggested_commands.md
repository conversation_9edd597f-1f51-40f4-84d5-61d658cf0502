# Suggested Commands for Blog API v3

## Build & Run Commands
```bash
make build          # Build application to ./bin/
make run           # Run application
make dev           # Development mode with hot reload (Air)
make clean         # Clean build artifacts
```

## Testing Commands
```bash
make test                  # Run all unit tests
make test-coverage        # Run tests with coverage report
make test-e2e             # Run end-to-end tests
make test-onboarding      # Test onboarding module
make test-all             # Run all test suites
```

## Code Quality Commands
```bash
make lint          # Run golangci-lint
make fmt           # Format code with go fmt
make vet           # Run go vet
make check         # Run all checks (lint + vet + fmt)
```

## Database Commands
```bash
make migrate-up                    # Run all migrations
make migrate-up MODULE=d_auth     # Run specific module migrations
make migrate-down                 # Rollback last migration
make migrate-status               # Check migration status
make migrate-create NAME=name     # Create new migration
make db-reset                     # Reset database (drop + create + migrate)
make migrate-and-seed             # Migrate and run seeders
```

## API Testing with Bruno
```bash
npm install -g @usebruno/cli      # Install Bruno CLI
bru run api-tests/bruno --env local    # Run all API tests
bru run api-tests/bruno/Auth --env local   # Run specific collection
```

## Port Management
```bash
make kill-all-ports    # Kill processes on app, metrics, and DB ports
make kill-app          # Kill process on app port (9077)
make kill-metrics      # Kill process on metrics port (9090)
make kill-db           # Kill process on DB port (3307)
make ps-ports          # Show processes using configured ports
```

## Dependency Management
```bash
make deps          # Download and tidy Go dependencies
go mod download    # Download dependencies
go mod tidy        # Clean up go.mod and go.sum
```

## Swagger Documentation (if enabled)
```bash
make swagger-gen    # Generate Swagger docs
# Access at: http://localhost:8080/swagger/index.html
```

## Git Workflow
```bash
git status         # Check current status
git add .          # Stage changes
git commit -m "message"    # Commit changes
git push origin branch     # Push to remote
```

## Environment Setup
```bash
cp .env.example .env    # Copy environment template
# Edit .env with your configuration
```
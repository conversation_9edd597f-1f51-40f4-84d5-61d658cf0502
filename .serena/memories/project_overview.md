# Blog API v3 Project Overview

## Purpose
Blog API v3 is a backend API for a blogging system with multi-tenancy support. It provides comprehensive features for managing blogs, users, authentication, and content.

## Tech Stack
- **Language**: Go 1.23.2
- **Web Framework**: Gin (v1.10.1)
- **Database**: MySQL 8.0.16+ (NO PostgreSQL support)
- **Cache**: Redis (optional)
- **Authentication**: JWT tokens (user-only, tenant context via headers)
- **API Testing**: Bruno (NOT Swagger/OpenAPI UI for testing)
- **Validation**: go-playground/validator
- **Monitoring**: OpenTelemetry, Prometheus metrics
- **Build Tool**: Make

## Architecture
- Clean architecture with modular design
- Repository pattern for data access
- Service layer for business logic
- Handler layer for HTTP endpoints
- Middleware for cross-cutting concerns

## Key Features
- Multi-tenancy with tenant isolation
- OAuth2 authentication support
- Email verification system
- Role-based access control (RBAC)
- Blog scheduling and publishing
- Media management
- Notification system
- AI integration capabilities

## Development Environment
- Uses `.env` file for configuration
- Hot reload with Air for development
- Build outputs to `./bin/` directory
- Port configuration: App (9077), <PERSON><PERSON>s (9090), DB (3307)
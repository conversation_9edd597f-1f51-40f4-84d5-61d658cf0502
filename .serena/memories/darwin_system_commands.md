# Darwin (macOS) System Commands

## System Information
- **OS**: Darwin 24.5.0 (macOS on ARM64 architecture)
- **Architecture**: arm64 (Apple Silicon)

## Command Locations
- **git**: `/opt/homebrew/bin/git` (Homebrew installation)
- **ls**: `/bin/ls` (BSD version, not GNU)
- **find**: `/usr/bin/find` (BSD version)
- **grep**: `/usr/bin/grep` (BSD version)
- **cd**: `/usr/bin/cd` (built-in shell command)

## Important Differences from Linux

### ls Command
- No `--version` flag (BSD version)
- Different flags than GNU ls
- Use `-la` for detailed listing with hidden files
- Use `-G` for colorized output

### find Command
- BSD find has different syntax than GNU find
- Example: `find . -name "*.go" -type f`
- No `-printf` option

### grep Command
- BSD grep, different options than GNU grep
- For extended regex use `-E` flag
- Consider using `ripgrep` (rg) if available for better performance

### File System
- Case-insensitive by default (but case-preserving)
- Use `/Users/<USER>/home/<USER>
- Temporary files in `/var/folders/` or `/tmp/`

## Useful macOS-specific Commands
```bash
# Open file/folder in Finder
open .

# Copy to clipboard
echo "text" | pbcopy

# Paste from clipboard
pbpaste

# Show/hide hidden files in Finder
defaults write com.apple.finder AppleShowAllFiles YES

# Check port usage
lsof -i :9077

# Kill process on port
kill -9 $(lsof -t -i:9077)
```

## Package Management
- Homebrew is the primary package manager
- Install paths: `/opt/homebrew/` (Apple Silicon) or `/usr/local/` (Intel)

## Development Tools
- Go installed via Homebrew or official installer
- Use `which` command to find tool locations
- Environment variables in `.zshrc` or `.bash_profile`
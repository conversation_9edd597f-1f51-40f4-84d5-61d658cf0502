package settings

import (
	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/settings/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/settings/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/settings/repositories/mysql"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/settings/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

// SettingsModule represents the settings module with all its components
type SettingsModule struct {
	// Repositories
	SettingsRepository      repositories.SettingsRepository
	SettingSchemaRepository repositories.SettingSchemaRepository

	// Services
	SettingsService      services.SettingsService
	SettingSchemaService services.SettingSchemaService
	EncryptionService    services.EncryptionService
	ValidationService    services.ValidationService
	CacheService         services.CacheService

	// Handlers
	SettingsHandler      *handlers.SettingsHandler
	SettingSchemaHandler *handlers.SettingSchemaHandler
}

// NewSettingsModule creates a new settings module instance
func NewSettingsModule(db *gorm.DB, logger utils.Logger) *SettingsModule {
	// Initialize repositories
	settingsRepo := mysql.NewSettingsRepository(db)
	schemaRepo := mysql.NewSettingSchemaRepository(db)

	// Initialize services
	encryptionService := services.NewEncryptionService()
	validationService := services.NewValidationService()
	cacheService := services.NewCacheService()

	settingsService := services.NewSettingsService(settingsRepo, schemaRepo, encryptionService, validationService, cacheService, logger)
	schemaService := services.NewSettingSchemaService(schemaRepo, logger)

	// Initialize handlers
	settingsHandler := handlers.NewSettingsHandler(settingsService, logger)
	schemaHandler := handlers.NewSettingSchemaHandler(schemaService, logger)

	return &SettingsModule{
		SettingsRepository:      settingsRepo,
		SettingSchemaRepository: schemaRepo,
		SettingsService:         settingsService,
		SettingSchemaService:    schemaService,
		EncryptionService:       encryptionService,
		ValidationService:       validationService,
		CacheService:            cacheService,
		SettingsHandler:         settingsHandler,
		SettingSchemaHandler:    schemaHandler,
	}
}

// RegisterRoutes registers all settings module routes
func (m *SettingsModule) RegisterRoutes(router *gin.RouterGroup) {
	RegisterRoutes(router, m.SettingsHandler, m.SettingSchemaHandler)
}

// GetSettingsService returns the settings service
func (m *SettingsModule) GetSettingsService() services.SettingsService {
	return m.SettingsService
}

// GetSettingSchemaService returns the setting schema service
func (m *SettingsModule) GetSettingSchemaService() services.SettingSchemaService {
	return m.SettingSchemaService
}

// GetEncryptionService returns the encryption service
func (m *SettingsModule) GetEncryptionService() services.EncryptionService {
	return m.EncryptionService
}

// GetValidationService returns the validation service
func (m *SettingsModule) GetValidationService() services.ValidationService {
	return m.ValidationService
}

// GetCacheService returns the cache service
func (m *SettingsModule) GetCacheService() services.CacheService {
	return m.CacheService
}

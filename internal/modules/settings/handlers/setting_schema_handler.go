package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/settings/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/settings/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

type SettingSchemaHandler struct {
	schemaService services.SettingSchemaService
	logger        utils.Logger
}

func NewSettingSchemaHandler(schemaService services.SettingSchemaService, logger utils.Logger) *SettingSchemaHandler {
	return &SettingSchemaHandler{
		schemaService: schemaService,
		logger:        logger,
	}
}

// GetSchemas godoc
// @Summary Get setting schemas
// @Description Get setting schemas with optional filters
// @Tags Setting Schemas
// @Accept json
// @Produce json
// @Param category query string false "Category filter"
// @Param data_type query string false "Data type filter" Enums(string,number,boolean,json,array)
// @Param is_public query bool false "Public schemas only"
// @Param search query string false "Search in name and description"
// @Success 200 {object} response.Response{data=[]models.SettingSchemaResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/cms/v1/setting-schemas [get]
func (h *SettingSchemaHandler) GetSchemas(c *gin.Context) {
	filter := models.SchemaFilter{
		Category: c.Query("category"),
		DataType: models.DataType(c.Query("data_type")),
		Search:   c.Query("search"),
	}

	if isPublicStr := c.Query("is_public"); isPublicStr != "" {
		if isPublic, err := strconv.ParseBool(isPublicStr); err == nil {
			filter.IsPublic = &isPublic
		}
	}

	if isRequiredStr := c.Query("is_required"); isRequiredStr != "" {
		if isRequired, err := strconv.ParseBool(isRequiredStr); err == nil {
			filter.IsRequired = &isRequired
		}
	}

	schemas, err := h.schemaService.GetSchemas(c.Request.Context(), filter)
	if err != nil {
		h.logger.Error("Failed to get schemas", "error", err)
		response.InternalError(c.Writer, "Failed to get schemas", err.Error())
		return
	}

	response.Success(c.Writer, schemas)
}

// GetSchema godoc
// @Summary Get setting schema by ID
// @Description Get a specific setting schema by ID
// @Tags Setting Schemas
// @Accept json
// @Produce json
// @Param id path int true "Schema ID"
// @Success 200 {object} response.Response{data=models.SettingSchemaResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/cms/v1/setting-schemas/{id} [get]
func (h *SettingSchemaHandler) GetSchema(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid schema ID")
		return
	}

	schema, err := h.schemaService.GetSchemaByID(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.Error("Failed to get schema", "error", err, "id", id)
		response.NotFound(c.Writer, "Schema not found")
		return
	}

	response.Success(c.Writer, schema)
}

// CreateSchema godoc
// @Summary Create setting schema
// @Description Create a new setting schema
// @Tags Setting Schemas
// @Accept json
// @Produce json
// @Param request body models.SettingSchemaCreateRequest true "Schema create request"
// @Success 201 {object} response.Response{data=models.SettingSchemaResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/cms/v1/setting-schemas [post]
func (h *SettingSchemaHandler) CreateSchema(c *gin.Context) {
	var req models.SettingSchemaCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body")
		return
	}

	if err := utils.ValidateStruct(&req); err != nil {
		response.BadRequest(c.Writer, "Validation failed")
		return
	}

	schema, err := h.schemaService.CreateSchema(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("Failed to create schema", "error", err)
		response.InternalError(c.Writer, "Failed to create schema", err.Error())
		return
	}

	response.Created(c.Writer, schema)
}

// UpdateSchema godoc
// @Summary Update setting schema
// @Description Update an existing setting schema
// @Tags Setting Schemas
// @Accept json
// @Produce json
// @Param id path int true "Schema ID"
// @Param request body models.SettingSchemaUpdateRequest true "Schema update request"
// @Success 200 {object} response.Response{data=models.SettingSchemaResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/cms/v1/setting-schemas/{id} [put]
func (h *SettingSchemaHandler) UpdateSchema(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid schema ID")
		return
	}

	var req models.SettingSchemaUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body")
		return
	}

	if err := utils.ValidateStruct(&req); err != nil {
		response.BadRequest(c.Writer, "Validation failed")
		return
	}

	schema, err := h.schemaService.UpdateSchema(c.Request.Context(), uint(id), &req)
	if err != nil {
		h.logger.Error("Failed to update schema", "error", err, "id", id)
		response.InternalError(c.Writer, "Failed to update schema", err.Error())
		return
	}

	response.Success(c.Writer, schema)
}

// DeleteSchema godoc
// @Summary Delete setting schema
// @Description Delete a setting schema
// @Tags Setting Schemas
// @Accept json
// @Produce json
// @Param id path int true "Schema ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/cms/v1/setting-schemas/{id} [delete]
func (h *SettingSchemaHandler) DeleteSchema(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid schema ID")
		return
	}

	err = h.schemaService.DeleteSchema(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.Error("Failed to delete schema", "error", err, "id", id)
		response.InternalError(c.Writer, "Failed to delete schema", err.Error())
		return
	}

	response.NoContent(c.Writer)
}

// GetSchemasByCategory godoc
// @Summary Get schemas by category
// @Description Get all schemas in a specific category
// @Tags Setting Schemas
// @Accept json
// @Produce json
// @Param category path string true "Category name"
// @Success 200 {object} response.Response{data=[]models.SettingSchemaResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/cms/v1/setting-schemas/category/{category} [get]
func (h *SettingSchemaHandler) GetSchemasByCategory(c *gin.Context) {
	category := c.Param("category")

	filter := models.SchemaFilter{
		Category: category,
	}

	schemas, err := h.schemaService.GetSchemas(c.Request.Context(), filter)
	if err != nil {
		h.logger.Error("Failed to get schemas by category", "error", err, "category", category)
		response.InternalError(c.Writer, "Failed to get schemas", err.Error())
		return
	}

	response.Success(c.Writer, schemas)
}

// GetSchemaByKey godoc
// @Summary Get schema by category and key
// @Description Get a specific schema by category and key
// @Tags Setting Schemas
// @Accept json
// @Produce json
// @Param category path string true "Category name"
// @Param key path string true "Setting key"
// @Success 200 {object} response.Response{data=models.SettingSchemaResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/cms/v1/setting-schemas/{category}/{key} [get]
func (h *SettingSchemaHandler) GetSchemaByKey(c *gin.Context) {
	category := c.Param("category")
	key := c.Param("key")

	schema, err := h.schemaService.GetSchemaByKey(c.Request.Context(), category, key)
	if err != nil {
		h.logger.Error("Failed to get schema by key", "error", err, "category", category, "key", key)
		response.NotFound(c.Writer, "Schema not found")
		return
	}

	response.Success(c.Writer, schema)
}

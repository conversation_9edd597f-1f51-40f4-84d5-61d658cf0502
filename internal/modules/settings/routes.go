package settings

import (
	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/settings/handlers"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
)

// RegisterRoutes registers all settings module routes
func RegisterRoutes(router *gin.RouterGroup, settingsHandler *handlers.SettingsHandler, schemaHandler *handlers.SettingSchemaHandler) {
	settings := router.Group("/settings")
	{
		// Settings routes
		settings.GET("", middleware.RequireAuthentication(), settingsHandler.GetSettings)
		settings.POST("", middleware.RequireAuthentication(), settingsHandler.CreateSetting)
		settings.GET("/:id", middleware.RequireAuthentication(), settingsHandler.GetSetting)
		settings.PUT("/:id", middleware.RequireAuthentication(), settingsHandler.UpdateSetting)
		settings.DELETE("/:id", middleware.RequireAuthentication(), settingsHandler.DeleteSetting)

		// Category routes
		settings.GET("/categories", settingsHandler.GetCategories)
		settings.GET("/category/:category", middleware.RequireAuthentication(), settingsHandler.GetSettingsByCategory)

		// Value routes
		settings.GET("/:category/:key/value", middleware.RequireAuthentication(), settingsHandler.GetSettingValue)
		settings.PUT("/:category/:key/value", middleware.RequireAuthentication(), settingsHandler.SetSettingValue)
	}

	// Setting schemas routes
	schemas := router.Group("/setting-schemas")
	{
		schemas.GET("", middleware.RequireAuthentication(), schemaHandler.GetSchemas)
		schemas.POST("", middleware.RequireAuthentication(), middleware.RequirePermission("settings.schema.create"), schemaHandler.CreateSchema)
		schemas.GET("/:id", middleware.RequireAuthentication(), schemaHandler.GetSchema)
		schemas.PUT("/:id", middleware.RequireAuthentication(), middleware.RequirePermission("settings.schema.update"), schemaHandler.UpdateSchema)
		schemas.DELETE("/:id", middleware.RequireAuthentication(), middleware.RequirePermission("settings.schema.delete"), schemaHandler.DeleteSchema)

		// Schema category and key routes
		schemas.GET("/category/:category", middleware.RequireAuthentication(), schemaHandler.GetSchemasByCategory)
		schemas.GET("/:category/:key", middleware.RequireAuthentication(), schemaHandler.GetSchemaByKey)
	}
}

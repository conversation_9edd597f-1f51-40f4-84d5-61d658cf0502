package tests

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/providers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// MockPhoneVerificationTokenRepository is a mock implementation of PhoneVerificationTokenRepository
type MockPhoneVerificationTokenRepository struct {
	mock.Mock
}

func (m *MockPhoneVerificationTokenRepository) CreateToken(ctx context.Context, token *models.PhoneVerificationToken) error {
	args := m.Called(ctx, token)
	return args.Error(0)
}

func (m *MockPhoneVerificationTokenRepository) GetTokenByID(ctx context.Context, id uint) (*models.PhoneVerificationToken, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.PhoneVerificationToken), args.Error(1)
}

func (m *MockPhoneVerificationTokenRepository) GetTokenByPhone(ctx context.Context, tenantID uint, phone string) (*models.PhoneVerificationToken, error) {
	args := m.Called(ctx, tenantID, phone)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.PhoneVerificationToken), args.Error(1)
}

func (m *MockPhoneVerificationTokenRepository) GetTokenByUserID(ctx context.Context, tenantID uint, userID uint) (*models.PhoneVerificationToken, error) {
	args := m.Called(ctx, tenantID, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.PhoneVerificationToken), args.Error(1)
}

func (m *MockPhoneVerificationTokenRepository) UpdateToken(ctx context.Context, token *models.PhoneVerificationToken) error {
	args := m.Called(ctx, token)
	return args.Error(0)
}

func (m *MockPhoneVerificationTokenRepository) DeleteToken(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockPhoneVerificationTokenRepository) VerifyToken(ctx context.Context, tenantID uint, phone string, code string) (*models.PhoneVerificationToken, error) {
	args := m.Called(ctx, tenantID, phone, code)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.PhoneVerificationToken), args.Error(1)
}

func (m *MockPhoneVerificationTokenRepository) MarkTokenAsUsed(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockPhoneVerificationTokenRepository) InvalidateTokensForUser(ctx context.Context, tenantID uint, userID uint) error {
	args := m.Called(ctx, tenantID, userID)
	return args.Error(0)
}

func (m *MockPhoneVerificationTokenRepository) InvalidateTokensForPhone(ctx context.Context, tenantID uint, phone string) error {
	args := m.Called(ctx, tenantID, phone)
	return args.Error(0)
}

func (m *MockPhoneVerificationTokenRepository) GetActiveTokensForUser(ctx context.Context, tenantID uint, userID uint) ([]*models.PhoneVerificationToken, error) {
	args := m.Called(ctx, tenantID, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*models.PhoneVerificationToken), args.Error(1)
}

func (m *MockPhoneVerificationTokenRepository) GetRecentTokensForUser(ctx context.Context, tenantID uint, userID uint, limit int) ([]*models.PhoneVerificationToken, error) {
	args := m.Called(ctx, tenantID, userID, limit)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*models.PhoneVerificationToken), args.Error(1)
}

func (m *MockPhoneVerificationTokenRepository) GetRecentTokensForPhone(ctx context.Context, tenantID uint, phone string, hours int) ([]*models.PhoneVerificationToken, error) {
	args := m.Called(ctx, tenantID, phone, hours)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*models.PhoneVerificationToken), args.Error(1)
}

func (m *MockPhoneVerificationTokenRepository) GetTokenStats(ctx context.Context, tenantID uint, userID uint) (*models.PhoneTokenStats, error) {
	args := m.Called(ctx, tenantID, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.PhoneTokenStats), args.Error(1)
}

func (m *MockPhoneVerificationTokenRepository) CleanupExpiredTokens(ctx context.Context) (int64, error) {
	args := m.Called(ctx)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockPhoneVerificationTokenRepository) CleanupExpiredTokensForTenant(ctx context.Context, tenantID uint) (int64, error) {
	args := m.Called(ctx, tenantID)
	return args.Get(0).(int64), args.Error(1)
}

// MockSMSProvider is a mock implementation of SMSProvider
type MockSMSProvider struct {
	mock.Mock
}

func (m *MockSMSProvider) SendSMS(ctx context.Context, message providers.SMSMessage) (*providers.SMSResponse, error) {
	args := m.Called(ctx, message)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*providers.SMSResponse), args.Error(1)
}

func (m *MockSMSProvider) SendBulkSMS(ctx context.Context, messages []providers.SMSMessage) ([]*providers.SMSResponse, error) {
	args := m.Called(ctx, messages)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*providers.SMSResponse), args.Error(1)
}

func (m *MockSMSProvider) GetMessageStatus(ctx context.Context, messageID string) (*providers.SMSResponse, error) {
	args := m.Called(ctx, messageID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*providers.SMSResponse), args.Error(1)
}

func (m *MockSMSProvider) GetProviderName() string {
	args := m.Called()
	return args.String(0)
}

func (m *MockSMSProvider) IsAvailable() bool {
	args := m.Called()
	return args.Bool(0)
}

// MockUserRepository is a mock implementation of UserRepository
type MockUserRepository struct {
	mock.Mock
}

func (m *MockUserRepository) GetByID(ctx context.Context, id uint) (*models.User, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.User), args.Error(1)
}

func (m *MockUserRepository) UpdatePhoneVerification(ctx context.Context, userID uint, verified bool) error {
	args := m.Called(ctx, userID, verified)
	return args.Error(0)
}

// PhoneVerificationTestSuite is the test suite for phone verification
type PhoneVerificationTestSuite struct {
	suite.Suite
	service      services.PhoneVerificationService
	tokenRepo    *MockPhoneVerificationTokenRepository
	userRepo     *MockUserRepository
	smsProvider  *MockSMSProvider
	logger       utils.Logger
}

func (suite *PhoneVerificationTestSuite) SetupTest() {
	suite.tokenRepo = new(MockPhoneVerificationTokenRepository)
	suite.userRepo = new(MockUserRepository)
	suite.smsProvider = new(MockSMSProvider)
	suite.logger = utils.NewLogger()

	suite.service = services.NewPhoneVerificationService(
		suite.tokenRepo,
		suite.userRepo,
		suite.smsProvider,
		suite.logger,
	)
}

func (suite *PhoneVerificationTestSuite) TestCreateVerificationToken_Success() {
	ctx := context.Background()
	tenantID := uint(1)
	userID := uint(123)
	phone := "+**********"

	user := &models.User{
		ID:            userID,
		Phone:         &phone,
		PhoneVerified: false,
	}

	req := &models.CreatePhoneVerificationTokenRequest{
		UserID: userID,
		Phone:  phone,
	}

	// Mock expectations
	suite.userRepo.On("GetByID", ctx, userID).Return(user, nil)
	suite.tokenRepo.On("GetRecentTokensForPhone", ctx, tenantID, phone, 24).Return([]*models.PhoneVerificationToken{}, nil)
	suite.tokenRepo.On("InvalidateTokensForPhone", ctx, tenantID, phone).Return(nil)
	suite.tokenRepo.On("CreateToken", ctx, mock.AnythingOfType("*models.PhoneVerificationToken")).Return(nil)
	
	suite.smsProvider.On("IsAvailable").Return(true)
	suite.smsProvider.On("GetProviderName").Return("twilio")
	suite.smsProvider.On("SendSMS", ctx, mock.AnythingOfType("providers.SMSMessage")).Return(&providers.SMSResponse{
		MessageID: "msg_123",
		Status:    "sent",
		Provider:  "twilio",
	}, nil)
	
	suite.tokenRepo.On("UpdateToken", ctx, mock.AnythingOfType("*models.PhoneVerificationToken")).Return(nil)

	// Execute
	token, err := suite.service.CreateVerificationToken(ctx, tenantID, req)

	// Assert
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), token)
	assert.Equal(suite.T(), userID, token.UserID)
	assert.Equal(suite.T(), phone, token.Phone)
	assert.Equal(suite.T(), tenantID, token.TenantID)
	assert.Empty(suite.T(), token.VerificationCode) // Should not expose the code
}

func (suite *PhoneVerificationTestSuite) TestCreateVerificationToken_UserNotFound() {
	ctx := context.Background()
	tenantID := uint(1)
	userID := uint(123)

	req := &models.CreatePhoneVerificationTokenRequest{
		UserID: userID,
		Phone:  "+**********",
	}

	// Mock expectations
	suite.userRepo.On("GetByID", ctx, userID).Return(nil, repositories.ErrUserNotFound)

	// Execute
	token, err := suite.service.CreateVerificationToken(ctx, tenantID, req)

	// Assert
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), token)
	assert.Contains(suite.T(), err.Error(), "user not found")
}

func (suite *PhoneVerificationTestSuite) TestCreateVerificationToken_PhoneAlreadyVerified() {
	ctx := context.Background()
	tenantID := uint(1)
	userID := uint(123)
	phone := "+**********"

	user := &models.User{
		ID:            userID,
		Phone:         &phone,
		PhoneVerified: true,
	}

	req := &models.CreatePhoneVerificationTokenRequest{
		UserID: userID,
		Phone:  phone,
	}

	// Mock expectations
	suite.userRepo.On("GetByID", ctx, userID).Return(user, nil)

	// Execute
	token, err := suite.service.CreateVerificationToken(ctx, tenantID, req)

	// Assert
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), token)
	assert.Contains(suite.T(), err.Error(), "phone is already verified")
}

func (suite *PhoneVerificationTestSuite) TestVerifyPhone_Success() {
	ctx := context.Background()
	tenantID := uint(1)
	userID := uint(123)
	phone := "+**********"
	code := "123456"

	user := &models.User{
		ID:            userID,
		Phone:         &phone,
		PhoneVerified: false,
	}

	token := &models.PhoneVerificationToken{
		ID:               1,
		TenantID:         tenantID,
		UserID:           userID,
		Phone:            phone,
		VerificationCode: code,
		CodeHash:         "hash",
		IsUsed:           false,
		ExpiresAt:        time.Now().Add(15 * time.Minute),
	}

	req := &models.VerifyPhoneRequest{
		Phone: phone,
		Code:  code,
	}

	// Mock expectations
	suite.tokenRepo.On("VerifyToken", ctx, tenantID, phone, code).Return(token, nil)
	suite.userRepo.On("GetByID", ctx, userID).Return(user, nil)
	suite.tokenRepo.On("MarkTokenAsUsed", ctx, token.ID).Return(nil)
	suite.userRepo.On("UpdatePhoneVerification", ctx, userID, true).Return(nil)
	suite.tokenRepo.On("InvalidateTokensForUser", ctx, tenantID, userID).Return(nil)

	// Execute
	result, err := suite.service.VerifyPhone(ctx, tenantID, req)

	// Assert
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.True(suite.T(), result.Success)
	assert.Equal(suite.T(), "Phone verified successfully", result.Message)
	assert.Equal(suite.T(), userID, result.UserID)
	assert.Equal(suite.T(), phone, result.Phone)
}

func (suite *PhoneVerificationTestSuite) TestVerifyPhone_InvalidCode() {
	ctx := context.Background()
	tenantID := uint(1)
	phone := "+**********"
	code := "999999"

	req := &models.VerifyPhoneRequest{
		Phone: phone,
		Code:  code,
	}

	// Mock expectations
	suite.tokenRepo.On("VerifyToken", ctx, tenantID, phone, code).Return(nil, repositories.ErrInvalidVerificationCode)

	// Execute
	result, err := suite.service.VerifyPhone(ctx, tenantID, req)

	// Assert
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.False(suite.T(), result.Success)
	assert.Equal(suite.T(), "Invalid or expired verification code", result.Message)
}

func TestPhoneVerificationTestSuite(t *testing.T) {
	suite.Run(t, new(PhoneVerificationTestSuite))
}
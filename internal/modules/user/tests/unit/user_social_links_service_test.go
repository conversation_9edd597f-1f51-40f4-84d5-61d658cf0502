package unit

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// MockUserSocialLinksRepository is a mock implementation
type MockUserSocialLinksRepository struct {
	mock.Mock
}

func (m *MockUserSocialLinksRepository) CreateSocialLink(ctx context.Context, tenantID uint, link *models.UserSocialLink) (*models.UserSocialLink, error) {
	args := m.Called(ctx, tenantID, link)
	return args.Get(0).(*models.UserSocialLink), args.Error(1)
}

func (m *MockUserSocialLinksRepository) GetSocialLinkByID(ctx context.Context, tenantID, linkID uint) (*models.UserSocialLink, error) {
	args := m.Called(ctx, tenantID, linkID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.UserSocialLink), args.Error(1)
}

func (m *MockUserSocialLinksRepository) GetSocialLinksByUserID(ctx context.Context, tenantID, userID uint) ([]*models.UserSocialLink, error) {
	args := m.Called(ctx, tenantID, userID)
	return args.Get(0).([]*models.UserSocialLink), args.Error(1)
}

func (m *MockUserSocialLinksRepository) UpdateSocialLink(ctx context.Context, tenantID uint, link *models.UserSocialLink) (*models.UserSocialLink, error) {
	args := m.Called(ctx, tenantID, link)
	return args.Get(0).(*models.UserSocialLink), args.Error(1)
}

func (m *MockUserSocialLinksRepository) DeleteSocialLink(ctx context.Context, tenantID, linkID uint) error {
	args := m.Called(ctx, tenantID, linkID)
	return args.Error(0)
}

func (m *MockUserSocialLinksRepository) GetSocialLinksByPlatform(ctx context.Context, tenantID uint, platform models.SocialPlatform) ([]*models.UserSocialLink, error) {
	args := m.Called(ctx, tenantID, platform)
	return args.Get(0).([]*models.UserSocialLink), args.Error(1)
}

func (m *MockUserSocialLinksRepository) ExistsSocialLinkForUserPlatform(ctx context.Context, tenantID, userID uint, platform models.SocialPlatform, excludeLinkID *uint) (bool, error) {
	args := m.Called(ctx, tenantID, userID, platform, excludeLinkID)
	return args.Bool(0), args.Error(1)
}

func (m *MockUserSocialLinksRepository) ReorderSocialLinks(ctx context.Context, tenantID, userID uint, linkOrders []models.UserSocialLinkOrder) error {
	args := m.Called(ctx, tenantID, userID, linkOrders)
	return args.Error(0)
}

func (m *MockUserSocialLinksRepository) GetPublicSocialLinks(ctx context.Context, tenantID, userID uint) ([]*models.UserSocialLink, error) {
	args := m.Called(ctx, tenantID, userID)
	return args.Get(0).([]*models.UserSocialLink), args.Error(1)
}

func (m *MockUserSocialLinksRepository) VerifySocialLink(ctx context.Context, tenantID, linkID uint) error {
	args := m.Called(ctx, tenantID, linkID)
	return args.Error(0)
}

// UserSocialLinksServiceTestSuite contains tests for social links service
type UserSocialLinksServiceTestSuite struct {
	suite.Suite
	ctx          context.Context
	linksRepo    *MockUserSocialLinksRepository
	logger       *MockLogger
	linksService services.UserSocialLinksService
	tenantID     uint
	userID       uint
}

func (suite *UserSocialLinksServiceTestSuite) SetupTest() {
	suite.ctx = context.Background()
	suite.linksRepo = new(MockUserSocialLinksRepository)
	suite.logger = new(MockLogger)
	suite.linksService = services.NewUserSocialLinksService(suite.linksRepo, suite.logger)
	suite.tenantID = 1
	suite.userID = 1
}

func (suite *UserSocialLinksServiceTestSuite) TearDownTest() {
	suite.linksRepo.AssertExpectations(suite.T())
	suite.logger.AssertExpectations(suite.T())
}

func (suite *UserSocialLinksServiceTestSuite) TestCreateSocialLink_Success() {
	// Arrange
	createReq := &models.UserSocialLinkCreateRequest{
		TenantID:     suite.tenantID,
		UserID:       suite.userID,
		Platform:     models.PlatformGitHub,
		Username:     "johndoe",
		URL:          "https://github.com/johndoe",
		DisplayOrder: 1,
		IsPublic:     true,
	}

	expectedLink := &models.UserSocialLink{
		ID:           1,
		TenantID:     suite.tenantID,
		UserID:       suite.userID,
		Platform:     createReq.Platform,
		Username:     createReq.Username,
		URL:          createReq.URL,
		DisplayOrder: createReq.DisplayOrder,
		IsPublic:     createReq.IsPublic,
		IsVerified:   false,
	}

	suite.linksRepo.On("ExistsSocialLinkForUserPlatform", suite.ctx, suite.tenantID, suite.userID, createReq.Platform, (*uint)(nil)).Return(false, nil)
	suite.linksRepo.On("CreateSocialLink", suite.ctx, suite.tenantID, mock.AnythingOfType("*models.UserSocialLink")).Return(expectedLink, nil)

	// Act
	result, err := suite.linksService.CreateSocialLink(suite.ctx, suite.tenantID, createReq)

	// Assert
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), expectedLink.Platform, result.Platform)
	assert.Equal(suite.T(), expectedLink.Username, result.Username)
	assert.Equal(suite.T(), expectedLink.URL, result.URL)
}

func (suite *UserSocialLinksServiceTestSuite) TestCreateSocialLink_PlatformAlreadyExists() {
	// Arrange
	createReq := &models.UserSocialLinkCreateRequest{
		TenantID: suite.tenantID,
		UserID:   suite.userID,
		Platform: models.PlatformGitHub,
		Username: "johndoe",
		URL:      "https://github.com/johndoe",
	}

	suite.linksRepo.On("ExistsSocialLinkForUserPlatform", suite.ctx, suite.tenantID, suite.userID, createReq.Platform, (*uint)(nil)).Return(true, nil)

	// Act
	result, err := suite.linksService.CreateSocialLink(suite.ctx, suite.tenantID, createReq)

	// Assert
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "already exists")
}

func (suite *UserSocialLinksServiceTestSuite) TestCreateSocialLink_InvalidURL() {
	// Arrange
	createReq := &models.UserSocialLinkCreateRequest{
		TenantID: suite.tenantID,
		UserID:   suite.userID,
		Platform: models.PlatformGitHub,
		Username: "johndoe",
		URL:      "https://twitter.com/johndoe", // Wrong platform URL
	}

	suite.linksRepo.On("ExistsSocialLinkForUserPlatform", suite.ctx, suite.tenantID, suite.userID, createReq.Platform, (*uint)(nil)).Return(false, nil)

	// Act
	result, err := suite.linksService.CreateSocialLink(suite.ctx, suite.tenantID, createReq)

	// Assert
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "invalid URL")
}

func (suite *UserSocialLinksServiceTestSuite) TestGetSocialLinksByUserID_Success() {
	// Arrange
	expectedLinks := []*models.UserSocialLink{
		{
			ID:       1,
			TenantID: suite.tenantID,
			UserID:   suite.userID,
			Platform: models.PlatformGitHub,
			Username: "johndoe",
			URL:      "https://github.com/johndoe",
			IsPublic: true,
		},
		{
			ID:       2,
			TenantID: suite.tenantID,
			UserID:   suite.userID,
			Platform: models.PlatformLinkedIn,
			Username: "johndoe",
			URL:      "https://linkedin.com/in/johndoe",
			IsPublic: true,
		},
	}

	suite.linksRepo.On("GetSocialLinksByUserID", suite.ctx, suite.tenantID, suite.userID).Return(expectedLinks, nil)

	// Act
	result, err := suite.linksService.GetSocialLinksByUserID(suite.ctx, suite.tenantID, suite.userID)

	// Assert
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), len(expectedLinks), len(result))
	assert.Equal(suite.T(), expectedLinks[0].Platform, result[0].Platform)
	assert.Equal(suite.T(), expectedLinks[1].Platform, result[1].Platform)
}

func (suite *UserSocialLinksServiceTestSuite) TestUpdateSocialLink_Success() {
	// Arrange
	linkID := uint(1)
	updateReq := &models.UserSocialLinkUpdateRequest{
		Username:     "john.doe.updated",
		URL:          "https://github.com/john.doe.updated",
		DisplayOrder: 2,
		IsPublic:     false,
	}

	existingLink := &models.UserSocialLink{
		ID:       linkID,
		TenantID: suite.tenantID,
		UserID:   suite.userID,
		Platform: models.PlatformGitHub,
		Username: "johndoe",
		URL:      "https://github.com/johndoe",
	}

	updatedLink := &models.UserSocialLink{
		ID:           linkID,
		TenantID:     suite.tenantID,
		UserID:       suite.userID,
		Platform:     models.PlatformGitHub,
		Username:     updateReq.Username,
		URL:          updateReq.URL,
		DisplayOrder: updateReq.DisplayOrder,
		IsPublic:     updateReq.IsPublic,
	}

	suite.linksRepo.On("GetSocialLinkByID", suite.ctx, suite.tenantID, linkID).Return(existingLink, nil)
	suite.linksRepo.On("UpdateSocialLink", suite.ctx, suite.tenantID, mock.AnythingOfType("*models.UserSocialLink")).Return(updatedLink, nil)

	// Act
	result, err := suite.linksService.UpdateSocialLink(suite.ctx, suite.tenantID, linkID, updateReq)

	// Assert
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), updateReq.Username, result.Username)
	assert.Equal(suite.T(), updateReq.URL, result.URL)
	assert.Equal(suite.T(), updateReq.IsPublic, result.IsPublic)
}

func (suite *UserSocialLinksServiceTestSuite) TestDeleteSocialLink_Success() {
	// Arrange
	linkID := uint(1)
	suite.linksRepo.On("DeleteSocialLink", suite.ctx, suite.tenantID, linkID).Return(nil)

	// Act
	err := suite.linksService.DeleteSocialLink(suite.ctx, suite.tenantID, linkID)

	// Assert
	assert.NoError(suite.T(), err)
}

func (suite *UserSocialLinksServiceTestSuite) TestReorderSocialLinks_Success() {
	// Arrange
	reorderReq := &models.UserSocialLinksReorderRequest{
		LinkOrders: []models.UserSocialLinkOrder{
			{ID: 1, DisplayOrder: 2},
			{ID: 2, DisplayOrder: 1},
		},
	}

	suite.linksRepo.On("ReorderSocialLinks", suite.ctx, suite.tenantID, suite.userID, reorderReq.LinkOrders).Return(nil)

	// Act
	err := suite.linksService.ReorderSocialLinks(suite.ctx, suite.tenantID, suite.userID, reorderReq)

	// Assert
	assert.NoError(suite.T(), err)
}

func (suite *UserSocialLinksServiceTestSuite) TestGetPublicSocialLinks_Success() {
	// Arrange
	expectedLinks := []*models.UserSocialLink{
		{
			ID:       1,
			TenantID: suite.tenantID,
			UserID:   suite.userID,
			Platform: models.PlatformGitHub,
			IsPublic: true,
		},
	}

	suite.linksRepo.On("GetPublicSocialLinks", suite.ctx, suite.tenantID, suite.userID).Return(expectedLinks, nil)

	// Act
	result, err := suite.linksService.GetPublicSocialLinks(suite.ctx, suite.tenantID, suite.userID)

	// Assert
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), len(expectedLinks), len(result))
	assert.True(suite.T(), result[0].IsPublic)
}

func (suite *UserSocialLinksServiceTestSuite) TestVerifySocialLink_Success() {
	// Arrange
	linkID := uint(1)
	verifyReq := &models.UserSocialLinkVerifyRequest{
		VerificationCode: "ABC123",
	}

	suite.linksRepo.On("VerifySocialLink", suite.ctx, suite.tenantID, linkID).Return(nil)

	// Act
	err := suite.linksService.VerifySocialLink(suite.ctx, suite.tenantID, linkID, verifyReq)

	// Assert
	assert.NoError(suite.T(), err)
}

func (suite *UserSocialLinksServiceTestSuite) TestGetSocialLinksByPlatform_Success() {
	// Arrange
	platform := models.PlatformGitHub
	expectedLinks := []*models.UserSocialLink{
		{
			ID:       1,
			TenantID: suite.tenantID,
			Platform: platform,
		},
		{
			ID:       2,
			TenantID: suite.tenantID,
			Platform: platform,
		},
	}

	suite.linksRepo.On("GetSocialLinksByPlatform", suite.ctx, suite.tenantID, platform).Return(expectedLinks, nil)

	// Act
	result, err := suite.linksService.GetSocialLinksByPlatform(suite.ctx, suite.tenantID, platform)

	// Assert
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), len(expectedLinks), len(result))
	assert.Equal(suite.T(), platform, result[0].Platform)
	assert.Equal(suite.T(), platform, result[1].Platform)
}

func (suite *UserSocialLinksServiceTestSuite) TestGetSupportedPlatforms() {
	// Act
	platforms := suite.linksService.GetSupportedPlatforms()

	// Assert
	assert.NotEmpty(suite.T(), platforms)
	assert.Greater(suite.T(), len(platforms), 5) // Should have multiple platforms

	// Check that common platforms are included
	platformNames := make(map[models.SocialPlatform]bool)
	for _, platform := range platforms {
		platformNames[platform.Platform] = true
	}

	assert.True(suite.T(), platformNames[models.PlatformGitHub])
	assert.True(suite.T(), platformNames[models.PlatformLinkedIn])
	assert.True(suite.T(), platformNames[models.PlatformTwitter])
}

// Run the test suite
func TestUserSocialLinksServiceTestSuite(t *testing.T) {
	suite.Run(t, new(UserSocialLinksServiceTestSuite))
}

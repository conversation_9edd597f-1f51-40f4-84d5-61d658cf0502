package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/services"
	httpPkg "github.com/tranthanhloi/wn-api-v3/pkg/http"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// PhoneVerificationHandler handles phone verification operations
type PhoneVerificationHandler struct {
	phoneVerificationService services.PhoneVerificationService
	userService              services.UserService
	logger                   utils.Logger
}

// NewPhoneVerificationHandler creates a new phone verification handler
func NewPhoneVerificationHandler(
	phoneVerificationService services.PhoneVerificationService,
	userService services.UserService,
	logger utils.Logger,
) *PhoneVerificationHandler {
	return &PhoneVerificationHandler{
		phoneVerificationService: phoneVerificationService,
		userService:              userService,
		logger:                   logger,
	}
}

// SendPhoneVerification sends a phone verification code to the user
// @Summary Send phone verification code
// @Description Send a verification code to the user's phone number
// @Tags Phone Verification
// @Accept json
// @Produce json
// @Param request body models.CreatePhoneVerificationTokenRequest true "Send verification request"
// @Success 200 {object} response.Response{data=models.PhoneVerificationTokenResponse}
// @Failure 400 {object} response.Response
// @Failure 429 {object} response.Response
// @Failure 500 {object} response.Response
// @Security Bearer
// @Router /user/phone/send-verification [post]
func (h *PhoneVerificationHandler) SendPhoneVerification(c *gin.Context) {
	var req models.CreatePhoneVerificationTokenRequest

	// Parse request body
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request format", err.Error())
		return
	}

	// Validate request
	if err := validator.ValidateStruct(req); err != nil {
		response.BadRequest(c, "Validation failed", err.Error())
		return
	}

	// Get tenant ID from context
	tenantID := httpPkg.GetTenantID(c)
	if tenantID == 0 {
		response.Unauthorized(c, "Missing tenant context")
		return
	}

	// Get user ID from context if not provided
	if req.UserID == 0 {
		userID := httpPkg.GetUserID(c)
		if userID == 0 {
			response.Unauthorized(c, "Missing user context")
			return
		}
		req.UserID = userID
	}

	// Add request metadata
	req.UserAgent = &c.Request.UserAgent()
	clientIP := c.ClientIP()
	req.IPAddress = &clientIP

	// Send verification code
	token, err := h.phoneVerificationService.CreateVerificationToken(c.Request.Context(), tenantID, &req)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"tenant_id": tenantID,
			"user_id":   req.UserID,
			"phone":     req.Phone,
		}).Error("Failed to send phone verification")

		// Check for specific error types
		switch err.Error() {
		case "user not found":
			response.NotFound(c, "User not found")
		case "phone is already verified":
			response.BadRequest(c, "Phone is already verified", nil)
		case "daily verification limit exceeded":
			response.TooManyRequests(c, "Daily verification limit exceeded")
		default:
			response.InternalServerError(c, "Failed to send verification code", err.Error())
		}
		return
	}

	// Convert to response
	var resp models.PhoneVerificationTokenResponse
	resp.FromPhoneVerificationToken(token)

	response.Success(c, "Verification code sent successfully", resp)
}

// VerifyPhone verifies a phone number with the provided code
// @Summary Verify phone number
// @Description Verify a phone number using the verification code
// @Tags Phone Verification
// @Accept json
// @Produce json
// @Param request body models.VerifyPhoneRequest true "Verification request"
// @Success 200 {object} response.Response{data=models.PhoneVerificationResult}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Security Bearer
// @Router /user/phone/verify [post]
func (h *PhoneVerificationHandler) VerifyPhone(c *gin.Context) {
	var req models.VerifyPhoneRequest

	// Parse request body
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request format", err.Error())
		return
	}

	// Validate request
	if err := validator.ValidateStruct(req); err != nil {
		response.BadRequest(c, "Validation failed", err.Error())
		return
	}

	// Get tenant ID from context
	tenantID := httpPkg.GetTenantID(c)
	if tenantID == 0 {
		response.Unauthorized(c, "Missing tenant context")
		return
	}

	// Add request metadata
	req.UserAgent = &c.Request.UserAgent()
	clientIP := c.ClientIP()
	req.IPAddress = &clientIP

	// Verify phone
	result, err := h.phoneVerificationService.VerifyPhone(c.Request.Context(), tenantID, &req)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"tenant_id": tenantID,
			"phone":     req.Phone,
		}).Error("Failed to verify phone")

		response.InternalServerError(c, "Failed to verify phone", err.Error())
		return
	}

	if !result.Success {
		response.BadRequest(c, result.Message, nil)
		return
	}

	response.Success(c, result.Message, result)
}

// ResendPhoneVerification resends the phone verification code
// @Summary Resend phone verification code
// @Description Resend a verification code to the user's phone number
// @Tags Phone Verification
// @Accept json
// @Produce json
// @Param request body models.ResendPhoneVerificationRequest true "Resend request"
// @Success 200 {object} response.Response{data=models.PhoneVerificationTokenResponse}
// @Failure 400 {object} response.Response
// @Failure 429 {object} response.Response
// @Failure 500 {object} response.Response
// @Security Bearer
// @Router /user/phone/resend-verification [post]
func (h *PhoneVerificationHandler) ResendPhoneVerification(c *gin.Context) {
	var req models.ResendPhoneVerificationRequest

	// Parse request body
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request format", err.Error())
		return
	}

	// Validate request
	if err := validator.ValidateStruct(req); err != nil {
		response.BadRequest(c, "Validation failed", err.Error())
		return
	}

	// Get tenant ID from context
	tenantID := httpPkg.GetTenantID(c)
	if tenantID == 0 {
		response.Unauthorized(c, "Missing tenant context")
		return
	}

	// Add request metadata
	req.UserAgent = &c.Request.UserAgent()
	clientIP := c.ClientIP()
	req.IPAddress = &clientIP

	// Resend verification code
	token, err := h.phoneVerificationService.ResendVerificationCode(c.Request.Context(), tenantID, &req)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"tenant_id": tenantID,
			"phone":     req.Phone,
		}).Error("Failed to resend phone verification")

		// Check for specific error types
		if err.Error() == "maximum resend attempts exceeded" {
			response.TooManyRequests(c, "Maximum resend attempts exceeded")
		} else if err.Error() == "no active verification token found for this phone" {
			response.NotFound(c, "No active verification found")
		} else {
			response.InternalServerError(c, "Failed to resend verification code", err.Error())
		}
		return
	}

	// Convert to response
	var resp models.PhoneVerificationTokenResponse
	resp.FromPhoneVerificationToken(token)

	response.Success(c, "Verification code resent successfully", resp)
}

// GetPhoneVerificationStatus gets the phone verification status for a user
// @Summary Get phone verification status
// @Description Get the current phone verification status for a user
// @Tags Phone Verification
// @Accept json
// @Produce json
// @Param userId path int false "User ID (defaults to current user)"
// @Success 200 {object} response.Response{data=models.PhoneVerificationStatus}
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Security Bearer
// @Router /user/phone/verification-status/{userId} [get]
func (h *PhoneVerificationHandler) GetPhoneVerificationStatus(c *gin.Context) {
	// Get user ID from path or context
	userID := httpPkg.GetUintParam(c, "userId")
	if userID == 0 {
		userID = httpPkg.GetUserID(c)
		if userID == 0 {
			response.Unauthorized(c, "Missing user context")
			return
		}
	}

	// Get tenant ID from context
	tenantID := httpPkg.GetTenantID(c)
	if tenantID == 0 {
		response.Unauthorized(c, "Missing tenant context")
		return
	}

	// Get user
	user, err := h.userService.GetByID(c.Request.Context(), userID)
	if err != nil {
		response.NotFound(c, "User not found")
		return
	}

	// Get active token if exists
	var activeToken *models.PhoneVerificationToken
	activeToken, _ = h.phoneVerificationService.GetActiveTokenForUser(c.Request.Context(), tenantID, userID)

	// Prepare response
	status := models.PhoneVerificationStatus{
		UserID:         userID,
		PhoneVerified:  user.PhoneVerified,
		Phone:          user.Phone,
		VerifiedAt:     user.PhoneVerifiedAt,
		HasActiveToken: activeToken != nil,
	}

	if activeToken != nil {
		status.TokenExpiresAt = &activeToken.ExpiresAt
		status.ResendCount = &activeToken.ResendCount
		status.LastResentAt = activeToken.LastResentAt
	}

	response.Success(c, "Phone verification status retrieved", status)
}

// GetPhoneTokenStats gets phone verification token statistics
// @Summary Get phone token statistics
// @Description Get statistics about phone verification tokens for a user
// @Tags Phone Verification
// @Accept json
// @Produce json
// @Param userId path int false "User ID (defaults to current user)"
// @Success 200 {object} response.Response{data=models.PhoneTokenStats}
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Security Bearer
// @Router /user/phone/token-stats/{userId} [get]
func (h *PhoneVerificationHandler) GetPhoneTokenStats(c *gin.Context) {
	// Get user ID from path or context
	userID := httpPkg.GetUintParam(c, "userId")
	if userID == 0 {
		userID = httpPkg.GetUserID(c)
		if userID == 0 {
			response.Unauthorized(c, "Missing user context")
			return
		}
	}

	// Get tenant ID from context
	tenantID := httpPkg.GetTenantID(c)
	if tenantID == 0 {
		response.Unauthorized(c, "Missing tenant context")
		return
	}

	// Get token statistics
	stats, err := h.phoneVerificationService.GetTokenStats(c.Request.Context(), tenantID, userID)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"tenant_id": tenantID,
			"user_id":   userID,
		}).Error("Failed to get phone token stats")

		response.InternalServerError(c, "Failed to get token statistics", err.Error())
		return
	}

	response.Success(c, "Token statistics retrieved", stats)
}
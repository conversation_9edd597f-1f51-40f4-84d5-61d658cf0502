package services

import (
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"

	authModels "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	authServices "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	notificationServices "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// userVerificationService implements UserVerificationService interface
type userVerificationService struct {
	userRepo                 repositories.UserRepository
	emailVerificationService authServices.EmailVerificationService
	phoneVerificationService PhoneVerificationService
	notificationService      notificationServices.NotificationService
	logger                   utils.Logger
}

// NewUserVerificationService creates a new user verification service
func NewUserVerificationService(
	userRepo repositories.UserRepository,
	emailVerificationService authServices.EmailVerificationService,
	phoneVerificationService PhoneVerificationService,
	notificationService notificationServices.NotificationService,
	logger utils.Logger,
) UserVerificationService {
	return &userVerificationService{
		userRepo:                 userRepo,
		emailVerificationService: emailVerificationService,
		phoneVerificationService: phoneVerificationService,
		notificationService:      notificationService,
		logger:                   logger,
	}
}

// SendEmailVerification sends email verification using the auth service
func (s *userVerificationService) SendEmailVerification(ctx context.Context, userID uint) error {
	// Get user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("user not found")
		}
		return fmt.Errorf("failed to get user: %w", err)
	}

	// Check if email is already verified
	if user.EmailVerified {
		return fmt.Errorf("email already verified")
	}

	// Create verification token request
	req := &authModels.CreateEmailVerificationTokenRequest{
		UserID: userID,
		Email:  user.Email,
	}

	// Get tenant ID from context or use default approach
	// Since users are global, we need to get tenant from context
	tenantID := s.getTenantIDFromContext(ctx, userID)

	// Use the email verification service to create and send token
	token, err := s.emailVerificationService.CreateVerificationToken(ctx, tenantID, req)
	if err != nil {
		s.logger.WithError(err).Error("Failed to create email verification token", "user_id", userID)
		return fmt.Errorf("failed to create email verification token: %w", err)
	}

	s.logger.Info("Email verification sent successfully", "user_id", userID, "email", user.Email, "token_id", token.ID)
	return nil
}

// VerifyEmail verifies email with token using the auth service
func (s *userVerificationService) VerifyEmail(ctx context.Context, userID uint, token string) error {
	// Get user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("user not found")
		}
		return fmt.Errorf("failed to get user: %w", err)
	}

	// Check if email is already verified
	if user.EmailVerified {
		return fmt.Errorf("email already verified")
	}

	// Create verification request
	req := &authModels.VerifyEmailRequest{
		Token: token,
	}

	// Use the email verification service to verify token
	result, err := s.emailVerificationService.VerifyEmail(ctx, req)
	if err != nil {
		s.logger.WithError(err).Error("Failed to verify email token", "user_id", userID)
		return fmt.Errorf("failed to verify email token: %w", err)
	}

	if !result.Success {
		return fmt.Errorf("email verification failed: %s", result.Message)
	}

	// Update user email verification status
	if err := s.userRepo.UpdateEmailVerification(ctx, userID, true); err != nil {
		s.logger.WithError(err).Error("Failed to update email verification status")
		return fmt.Errorf("failed to update email verification status: %w", err)
	}

	s.logger.Info("Email verification completed successfully", "user_id", userID, "email", result.Email)
	return nil
}

// SendPhoneVerification sends phone verification with rate limiting
func (s *userVerificationService) SendPhoneVerification(ctx context.Context, userID uint) error {
	// Get user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("user not found")
		}
		return fmt.Errorf("failed to get user: %w", err)
	}

	// Check if user has phone number
	if user.Phone == nil || *user.Phone == "" {
		return fmt.Errorf("user has no phone number")
	}

	// Check if phone is already verified
	if user.PhoneVerified {
		return fmt.Errorf("phone already verified")
	}

	// Get tenant ID from context
	tenantID := s.getTenantIDFromContext(ctx, userID)

	// Create phone verification token request
	req := &models.CreatePhoneVerificationTokenRequest{
		UserID: userID,
		Phone:  *user.Phone,
	}

	// Use the phone verification service to create and send token
	token, err := s.phoneVerificationService.CreateVerificationToken(ctx, tenantID, req)
	if err != nil {
		s.logger.WithError(err).Error("Failed to create phone verification token", "user_id", userID)
		return fmt.Errorf("failed to create phone verification token: %w", err)
	}

	s.logger.Info("Phone verification sent successfully", "user_id", userID, "phone", *user.Phone, "token_id", token.ID)
	return nil
}

// VerifyPhone verifies phone with code
func (s *userVerificationService) VerifyPhone(ctx context.Context, userID uint, code string) error {
	// Get user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("user not found")
		}
		return fmt.Errorf("failed to get user: %w", err)
	}

	// Check if phone is already verified
	if user.PhoneVerified {
		return fmt.Errorf("phone already verified")
	}

	// Check if user has phone number
	if user.Phone == nil || *user.Phone == "" {
		return fmt.Errorf("user has no phone number")
	}

	// Get tenant ID from context
	tenantID := s.getTenantIDFromContext(ctx, userID)

	// Create verification request
	req := &models.VerifyPhoneRequest{
		Phone: *user.Phone,
		Code:  code,
	}

	// Use the phone verification service to verify code
	result, err := s.phoneVerificationService.VerifyPhone(ctx, tenantID, req)
	if err != nil {
		s.logger.WithError(err).Error("Failed to verify phone code", "user_id", userID)
		return fmt.Errorf("failed to verify phone code: %w", err)
	}

	if !result.Success {
		return fmt.Errorf("phone verification failed: %s", result.Message)
	}

	s.logger.Info("Phone verification completed successfully", "user_id", userID, "phone", result.Phone)
	return nil
}

// ResendVerification resends verification with rate limiting
func (s *userVerificationService) ResendVerification(ctx context.Context, userID uint, verificationType string) error {
	// Get user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("user not found")
		}
		return fmt.Errorf("failed to get user: %w", err)
	}

	switch verificationType {
	case "email":
		if user.EmailVerified {
			return fmt.Errorf("email already verified")
		}

		// Create resend request
		req := &authModels.ResendVerificationEmailRequest{
			Email: user.Email,
		}

		// Get tenant ID from context or use default approach
		tenantID := s.getTenantIDFromContext(ctx, userID)

		// Use the email verification service to resend
		token, err := s.emailVerificationService.ResendVerificationEmail(ctx, tenantID, req)
		if err != nil {
			s.logger.WithError(err).Error("Failed to resend email verification", "user_id", userID)
			return fmt.Errorf("failed to resend email verification: %w", err)
		}

		s.logger.Info("Email verification resent successfully", "user_id", userID, "email", user.Email, "token_id", token.ID)
		return nil

	case "phone":
		if user.PhoneVerified {
			return fmt.Errorf("phone already verified")
		}

		if user.Phone == nil || *user.Phone == "" {
			return fmt.Errorf("user has no phone number")
		}

		// Create resend request
		req := &models.ResendPhoneVerificationRequest{
			Phone: *user.Phone,
		}

		// Get tenant ID from context
		tenantID := s.getTenantIDFromContext(ctx, userID)

		// Use the phone verification service to resend
		token, err := s.phoneVerificationService.ResendVerificationCode(ctx, tenantID, req)
		if err != nil {
			s.logger.WithError(err).Error("Failed to resend phone verification", "user_id", userID)
			return fmt.Errorf("failed to resend phone verification: %w", err)
		}

		s.logger.Info("Phone verification resent successfully", "user_id", userID, "phone", *user.Phone, "token_id", token.ID)
		return nil
	default:
		return fmt.Errorf("invalid verification type: %s", verificationType)
	}
}

// CheckVerificationStatus checks verification status with token information
func (s *userVerificationService) CheckVerificationStatus(ctx context.Context, userID uint) (*VerificationStatus, error) {
	// Get user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	status := &VerificationStatus{
		UserID:        userID,
		EmailVerified: user.EmailVerified,
		PhoneVerified: user.PhoneVerified,
	}

	if user.EmailVerifiedAt != nil {
		status.EmailVerifiedAt = user.EmailVerifiedAt
	}

	if user.PhoneVerifiedAt != nil {
		status.PhoneVerifiedAt = user.PhoneVerifiedAt
	}

	// Get last email verification token sent time
	if emailToken, err := s.emailVerificationService.GetActiveTokenForUser(ctx, userID); err == nil && emailToken != nil {
		status.LastEmailSent = &emailToken.CreatedAt
	}

	// Get last phone verification token sent time
	tenantID := s.getTenantIDFromContext(ctx, userID)
	if phoneToken, err := s.phoneVerificationService.GetActiveTokenForUser(ctx, tenantID, userID); err == nil && phoneToken != nil {
		status.LastPhoneSent = &phoneToken.CreatedAt
	}

	return status, nil
}

// Helper methods

// getTenantIDFromContext gets tenant ID from context or user memberships
func (s *userVerificationService) getTenantIDFromContext(ctx context.Context, userID uint) uint {
	// Try to get tenant ID from context first
	if tenantID, ok := ctx.Value("tenant_id").(uint); ok {
		return tenantID
	}

	// Fallback: Get user's primary tenant membership
	// This would need to be implemented with proper tenant membership lookup
	// For now, we'll use a default tenant ID of 1
	s.logger.Warn("No tenant ID found in context, using default", "user_id", userID)
	return 1
}

package services

import (
	"context"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/providers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

// PhoneVerificationConfig holds configuration for phone verification
type PhoneVerificationConfig struct {
	CodeLength          int           `json:"code_length"`          // Default: 6
	CodeExpiryMinutes   int           `json:"code_expiry_minutes"`  // Default: 15
	MaxResendAttempts   uint          `json:"max_resend_attempts"`  // Default: 3
	ResendIntervalMins  int           `json:"resend_interval_mins"` // Default: 5
	MaxDailyAttempts    int           `json:"max_daily_attempts"`   // Default: 10
	SMSProvider         string        `json:"sms_provider"`         // Default: "twilio"
	SMSTemplate         string        `json:"sms_template"`         // Default template
}

// DefaultPhoneVerificationConfig returns default configuration
func DefaultPhoneVerificationConfig() *PhoneVerificationConfig {
	return &PhoneVerificationConfig{
		CodeLength:         6,
		CodeExpiryMinutes:  15,
		MaxResendAttempts:  3,
		ResendIntervalMins: 5,
		MaxDailyAttempts:   10,
		SMSProvider:        "twilio",
		SMSTemplate:        "Your verification code is: {{code}}. This code will expire in {{expiry}} minutes.",
	}
}

// PhoneVerificationService interface defines methods for phone verification operations
type PhoneVerificationService interface {
	// Token management
	CreateVerificationToken(ctx context.Context, tenantID uint, req *models.CreatePhoneVerificationTokenRequest) (*models.PhoneVerificationToken, error)
	ResendVerificationCode(ctx context.Context, tenantID uint, req *models.ResendPhoneVerificationRequest) (*models.PhoneVerificationToken, error)
	VerifyPhone(ctx context.Context, tenantID uint, req *models.VerifyPhoneRequest) (*models.PhoneVerificationResult, error)

	// Token operations
	GetTokenByID(ctx context.Context, id uint) (*models.PhoneVerificationToken, error)
	GetActiveTokenForUser(ctx context.Context, tenantID uint, userID uint) (*models.PhoneVerificationToken, error)
	GetActiveTokenForPhone(ctx context.Context, tenantID uint, phone string) (*models.PhoneVerificationToken, error)
	InvalidateUserTokens(ctx context.Context, tenantID uint, userID uint) error

	// Analytics and monitoring
	GetTokenStats(ctx context.Context, tenantID uint, userID uint) (*models.PhoneTokenStats, error)
	GetRecentTokensForUser(ctx context.Context, tenantID uint, userID uint, limit int) ([]*models.PhoneVerificationToken, error)
	CleanupExpiredTokens(ctx context.Context) (int64, error)

	// Configuration
	UpdateConfig(config *PhoneVerificationConfig)
	GetConfig() *PhoneVerificationConfig
}

// phoneVerificationService implements PhoneVerificationService
type phoneVerificationService struct {
	tokenRepo    repositories.PhoneVerificationTokenRepository
	userRepo     repositories.UserRepository
	smsProvider  providers.SMSProvider
	config       *PhoneVerificationConfig
	logger       utils.Logger
}

// NewPhoneVerificationService creates a new phone verification service
func NewPhoneVerificationService(
	tokenRepo repositories.PhoneVerificationTokenRepository,
	userRepo repositories.UserRepository,
	smsProvider providers.SMSProvider,
	logger utils.Logger,
) PhoneVerificationService {
	return &phoneVerificationService{
		tokenRepo:   tokenRepo,
		userRepo:    userRepo,
		smsProvider: smsProvider,
		config:      DefaultPhoneVerificationConfig(),
		logger:      logger,
	}
}

// CreateVerificationToken creates a new verification token and sends SMS
func (s *phoneVerificationService) CreateVerificationToken(ctx context.Context, tenantID uint, req *models.CreatePhoneVerificationTokenRequest) (*models.PhoneVerificationToken, error) {
	// Get user to verify they exist
	user, err := s.userRepo.GetByID(ctx, req.UserID)
	if err != nil {
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": req.UserID,
			"phone":   req.Phone,
		}).Error("Failed to get user for phone verification")
		return nil, fmt.Errorf("user not found")
	}

	// Check if user already has verified phone
	if user.PhoneVerified {
		s.logger.WithFields(map[string]interface{}{
			"user_id": req.UserID,
			"phone":   req.Phone,
		}).Info("Attempted to create verification token for already verified phone")
		return nil, fmt.Errorf("phone is already verified")
	}

	// Check daily attempts limit
	if err := s.checkDailyLimit(ctx, tenantID, req.Phone); err != nil {
		s.logger.WithFields(map[string]interface{}{
			"tenant_id": tenantID,
			"phone":     req.Phone,
		}).Warn("Daily limit exceeded for phone verification")
		return nil, err
	}

	// Create new token
	token := &models.PhoneVerificationToken{
		TenantID:  tenantID,
		UserID:    req.UserID,
		Phone:     req.Phone,
		UserAgent: req.UserAgent,
		IPAddress: req.IPAddress,
	}

	// Generate verification code
	if err := token.GenerateCode(s.config.CodeLength); err != nil {
		s.logger.WithError(err).Error("Failed to generate verification code")
		return nil, fmt.Errorf("failed to generate verification code")
	}

	// Set custom expiry if provided
	if req.ExpiresIn != nil {
		token.ExpiresAt = time.Now().Add(time.Duration(*req.ExpiresIn) * time.Second)
	} else {
		token.ExpiresAt = time.Now().Add(time.Duration(s.config.CodeExpiryMinutes) * time.Minute)
	}

	// Create token in database
	if err := s.tokenRepo.CreateToken(ctx, token); err != nil {
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": req.UserID,
			"phone":   req.Phone,
		}).Error("Failed to create verification token")
		return nil, fmt.Errorf("failed to create verification token")
	}

	// Send verification SMS
	if err := s.sendVerificationSMS(ctx, token); err != nil {
		s.logger.WithError(err).WithField("token_id", token.ID).Error("Failed to send verification SMS")
		// Don't return error - token was created successfully
		// Update token with provider error
		token.ProviderResponse["error"] = err.Error()
		s.tokenRepo.UpdateToken(ctx, token)
	}

	s.logger.WithFields(map[string]interface{}{
		"token_id": token.ID,
		"user_id":  req.UserID,
		"phone":    req.Phone,
	}).Info("Created phone verification token")

	// Don't return the actual verification code in response
	tokenCopy := *token
	tokenCopy.VerificationCode = ""
	return &tokenCopy, nil
}

// ResendVerificationCode resends verification code with rate limiting
func (s *phoneVerificationService) ResendVerificationCode(ctx context.Context, tenantID uint, req *models.ResendPhoneVerificationRequest) (*models.PhoneVerificationToken, error) {
	// Get active token for phone
	token, err := s.tokenRepo.GetTokenByPhone(ctx, tenantID, req.Phone)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("no active verification token found for this phone")
		}
		s.logger.WithError(err).WithField("phone", req.Phone).Error("Failed to get token for phone")
		return nil, fmt.Errorf("failed to retrieve verification token")
	}

	// Check rate limiting
	resendInterval := time.Duration(s.config.ResendIntervalMins) * time.Minute
	if !token.CanResend(s.config.MaxResendAttempts, resendInterval) {
		if token.ResendCount >= s.config.MaxResendAttempts {
			s.logger.WithFields(map[string]interface{}{
				"token_id":     token.ID,
				"phone":        req.Phone,
				"resend_count": token.ResendCount,
				"max_resends":  s.config.MaxResendAttempts,
			}).Warn("Maximum resend attempts exceeded")
			return nil, fmt.Errorf("maximum resend attempts exceeded")
		}

		if token.LastResentAt != nil {
			nextAllowedTime := token.LastResentAt.Add(resendInterval)
			waitTime := time.Until(nextAllowedTime)
			if waitTime > 0 {
				s.logger.WithFields(map[string]interface{}{
					"token_id":  token.ID,
					"phone":     req.Phone,
					"wait_time": waitTime.String(),
				}).Warn("Resend rate limit exceeded")
				return nil, fmt.Errorf("please wait %v before requesting another verification code", waitTime.Round(time.Second))
			}
		}
	}

	// Update resend count and timestamp
	token.IncrementResendCount()
	if err := s.tokenRepo.UpdateToken(ctx, token); err != nil {
		s.logger.WithError(err).WithField("token_id", token.ID).Error("Failed to update token resend count")
		return nil, fmt.Errorf("failed to update token")
	}

	// Send verification SMS
	if err := s.sendVerificationSMS(ctx, token); err != nil {
		s.logger.WithError(err).WithField("token_id", token.ID).Error("Failed to resend verification SMS")
		return nil, fmt.Errorf("failed to send verification SMS")
	}

	s.logger.WithFields(map[string]interface{}{
		"token_id":     token.ID,
		"phone":        req.Phone,
		"resend_count": token.ResendCount,
	}).Info("Resent phone verification")

	// Don't return the actual verification code in response
	tokenCopy := *token
	tokenCopy.VerificationCode = ""
	return &tokenCopy, nil
}

// VerifyPhone verifies a phone using the provided code
func (s *phoneVerificationService) VerifyPhone(ctx context.Context, tenantID uint, req *models.VerifyPhoneRequest) (*models.PhoneVerificationResult, error) {
	// Verify and get token
	token, err := s.tokenRepo.VerifyToken(ctx, tenantID, req.Phone, req.Code)
	if err != nil {
		s.logger.WithError(err).WithField("phone", req.Phone).Warn("Invalid verification code")
		return &models.PhoneVerificationResult{
			Success:   false,
			Message:   "Invalid or expired verification code",
			TokenUsed: false,
		}, nil
	}

	// Get user
	user, err := s.userRepo.GetByID(ctx, token.UserID)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", token.UserID).Error("Failed to get user for verification")
		return &models.PhoneVerificationResult{
			Success:   false,
			Message:   "User not found",
			TokenUsed: false,
		}, nil
	}

	// Check if phone is already verified
	if user.PhoneVerified {
		s.logger.WithFields(map[string]interface{}{
			"user_id": token.UserID,
			"phone":   token.Phone,
		}).Info("Attempted to verify already verified phone")

		// Mark token as used anyway
		if err := s.tokenRepo.MarkTokenAsUsed(ctx, token.ID); err != nil {
			s.logger.WithError(err).WithField("token_id", token.ID).Error("Failed to mark token as used")
		}

		return &models.PhoneVerificationResult{
			Success:   true,
			Message:   "Phone is already verified",
			UserID:    user.ID,
			Phone:     token.Phone,
			TokenUsed: true,
		}, nil
	}

	// Mark token as used
	if err := s.tokenRepo.MarkTokenAsUsed(ctx, token.ID); err != nil {
		s.logger.WithError(err).WithField("token_id", token.ID).Error("Failed to mark token as used")
		return &models.PhoneVerificationResult{
			Success:   false,
			Message:   "Failed to process verification",
			TokenUsed: false,
		}, nil
	}

	// Update user's phone verification status
	if err := s.userRepo.UpdatePhoneVerification(ctx, token.UserID, true); err != nil {
		s.logger.WithError(err).WithField("user_id", user.ID).Error("Failed to update phone verification status")
		return &models.PhoneVerificationResult{
			Success:   false,
			Message:   "Failed to update verification status",
			TokenUsed: true,
		}, nil
	}

	// Invalidate all other tokens for this user
	if err := s.tokenRepo.InvalidateTokensForUser(ctx, tenantID, token.UserID); err != nil {
		s.logger.WithError(err).WithField("user_id", token.UserID).Warn("Failed to invalidate other tokens")
	}

	s.logger.WithFields(map[string]interface{}{
		"user_id":  user.ID,
		"phone":    token.Phone,
		"token_id": token.ID,
	}).Info("Successfully verified phone")

	return &models.PhoneVerificationResult{
		Success:   true,
		Message:   "Phone verified successfully",
		UserID:    user.ID,
		Phone:     token.Phone,
		TokenUsed: true,
	}, nil
}

// sendVerificationSMS sends the verification SMS using the configured provider
func (s *phoneVerificationService) sendVerificationSMS(ctx context.Context, token *models.PhoneVerificationToken) error {
	// Format the SMS message
	expiryMinutes := int(time.Until(token.ExpiresAt).Minutes())
	messageBody := fmt.Sprintf(s.config.SMSTemplate, token.VerificationCode, expiryMinutes)

	// Create SMS message
	message := providers.SMSMessage{
		To:   token.Phone,
		Body: messageBody,
		Metadata: map[string]interface{}{
			"type":      "phone_verification",
			"token_id":  token.ID,
			"tenant_id": token.TenantID,
			"user_id":   token.UserID,
		},
	}

	// Send SMS
	response, err := s.smsProvider.SendSMS(ctx, message)
	if err != nil {
		return fmt.Errorf("failed to send SMS: %w", err)
	}

	// Update token with provider information
	providerName := s.smsProvider.GetProviderName()
	token.Provider = &providerName
	if response != nil {
		token.ProviderMessageID = &response.MessageID
		token.ProviderResponse = response.ProviderData
	}

	// Update token in database
	if err := s.tokenRepo.UpdateToken(ctx, token); err != nil {
		s.logger.WithError(err).WithField("token_id", token.ID).Warn("Failed to update token with provider info")
	}

	return nil
}

// checkDailyLimit checks if the daily limit has been exceeded for a phone number
func (s *phoneVerificationService) checkDailyLimit(ctx context.Context, tenantID uint, phone string) error {
	// Get tokens created in the last 24 hours
	tokens, err := s.tokenRepo.GetRecentTokensForPhone(ctx, tenantID, phone, 24)
	if err != nil {
		return fmt.Errorf("failed to check daily limit: %w", err)
	}

	if len(tokens) >= s.config.MaxDailyAttempts {
		return fmt.Errorf("daily verification limit exceeded")
	}

	return nil
}

// GetTokenByID retrieves a token by ID
func (s *phoneVerificationService) GetTokenByID(ctx context.Context, id uint) (*models.PhoneVerificationToken, error) {
	return s.tokenRepo.GetTokenByID(ctx, id)
}

// GetActiveTokenForUser retrieves active token for user
func (s *phoneVerificationService) GetActiveTokenForUser(ctx context.Context, tenantID uint, userID uint) (*models.PhoneVerificationToken, error) {
	return s.tokenRepo.GetTokenByUserID(ctx, tenantID, userID)
}

// GetActiveTokenForPhone retrieves active token for phone
func (s *phoneVerificationService) GetActiveTokenForPhone(ctx context.Context, tenantID uint, phone string) (*models.PhoneVerificationToken, error) {
	return s.tokenRepo.GetTokenByPhone(ctx, tenantID, phone)
}

// InvalidateUserTokens invalidates all tokens for a user
func (s *phoneVerificationService) InvalidateUserTokens(ctx context.Context, tenantID uint, userID uint) error {
	return s.tokenRepo.InvalidateTokensForUser(ctx, tenantID, userID)
}

// GetTokenStats returns token statistics for a user
func (s *phoneVerificationService) GetTokenStats(ctx context.Context, tenantID uint, userID uint) (*models.PhoneTokenStats, error) {
	return s.tokenRepo.GetTokenStats(ctx, tenantID, userID)
}

// GetRecentTokensForUser returns recent tokens for a user
func (s *phoneVerificationService) GetRecentTokensForUser(ctx context.Context, tenantID uint, userID uint, limit int) ([]*models.PhoneVerificationToken, error) {
	return s.tokenRepo.GetRecentTokensForUser(ctx, tenantID, userID, limit)
}

// CleanupExpiredTokens removes expired tokens
func (s *phoneVerificationService) CleanupExpiredTokens(ctx context.Context) (int64, error) {
	return s.tokenRepo.CleanupExpiredTokens(ctx)
}

// UpdateConfig updates the service configuration
func (s *phoneVerificationService) UpdateConfig(config *PhoneVerificationConfig) {
	s.config = config
	s.logger.WithFields(map[string]interface{}{
		"code_length":         config.CodeLength,
		"code_expiry_minutes": config.CodeExpiryMinutes,
		"max_resend_attempts": config.MaxResendAttempts,
		"resend_interval_mins": config.ResendIntervalMins,
		"max_daily_attempts":  config.MaxDailyAttempts,
		"sms_provider":        config.SMSProvider,
	}).Info("Updated phone verification configuration")
}

// GetConfig returns the current configuration
func (s *phoneVerificationService) GetConfig() *PhoneVerificationConfig {
	return s.config
}
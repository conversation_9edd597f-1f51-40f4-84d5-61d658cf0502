package dto

import (
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
)

// UserPreferencesCreateRequest represents input for creating user preferences
type UserPreferencesCreateRequest struct {
	UserID                 uint                     `json:"user_id" validate:"required" example:"1"`
	EmailNotifications     bool                     `json:"email_notifications" example:"true"`
	PushNotifications      bool                     `json:"push_notifications" example:"false"`
	SMSNotifications       bool                     `json:"sms_notifications" example:"false"`
	MarketingEmails        bool                     `json:"marketing_emails" example:"true"`
	NewsletterSubscription bool                     `json:"newsletter_subscription" example:"true"`
	ProductUpdates         bool                     `json:"product_updates" example:"true"`
	SecurityAlerts         bool                     `json:"security_alerts" example:"true"`
	ProfileVisibility      models.ProfileVisibility `json:"profile_visibility" validate:"oneof=public private friends_only" example:"public"`
	ShowOnlineStatus       bool                     `json:"show_online_status" example:"true"`
	AllowSearch            bool                     `json:"allow_search" example:"true"`
	DataProcessingConsent  bool                     `json:"data_processing_consent" example:"true"`
	Theme                  models.Theme             `json:"theme" validate:"oneof=light dark system" example:"light"`
	DashboardLayout        string                   `json:"dashboard_layout" example:"grid"`
	ItemsPerPage           uint                     `json:"items_per_page" validate:"min=10,max=100" example:"20"`
	AutoSave               bool                     `json:"auto_save" example:"true"`
	KeyboardShortcuts      bool                     `json:"keyboard_shortcuts" example:"true"`
	TooltipsEnabled        bool                     `json:"tooltips_enabled" example:"true"`
	NotificationTypes      map[string]bool          `json:"notification_types,omitempty"`
	FeaturePreferences     map[string]interface{}   `json:"feature_preferences,omitempty"`
	CustomPreferences      map[string]interface{}   `json:"custom_preferences,omitempty"`
}

// UserPreferencesUpdateRequest represents input for updating user preferences
type UserPreferencesUpdateRequest struct {
	EmailNotifications     *bool                     `json:"email_notifications,omitempty" example:"false"`
	PushNotifications      *bool                     `json:"push_notifications,omitempty" example:"true"`
	SMSNotifications       *bool                     `json:"sms_notifications,omitempty" example:"true"`
	MarketingEmails        *bool                     `json:"marketing_emails,omitempty" example:"false"`
	NewsletterSubscription *bool                     `json:"newsletter_subscription,omitempty" example:"false"`
	ProductUpdates         *bool                     `json:"product_updates,omitempty" example:"false"`
	SecurityAlerts         *bool                     `json:"security_alerts,omitempty" example:"true"`
	ProfileVisibility      *models.ProfileVisibility `json:"profile_visibility,omitempty" validate:"omitempty,oneof=public private friends_only" example:"private"`
	ShowOnlineStatus       *bool                     `json:"show_online_status,omitempty" example:"false"`
	AllowSearch            *bool                     `json:"allow_search,omitempty" example:"false"`
	DataProcessingConsent  *bool                     `json:"data_processing_consent,omitempty" example:"true"`
	Theme                  *models.Theme             `json:"theme,omitempty" validate:"omitempty,oneof=light dark system" example:"dark"`
	DashboardLayout        *string                   `json:"dashboard_layout,omitempty" example:"list"`
	ItemsPerPage           *uint                     `json:"items_per_page,omitempty" validate:"omitempty,min=10,max=100" example:"50"`
	AutoSave               *bool                     `json:"auto_save,omitempty" example:"false"`
	KeyboardShortcuts      *bool                     `json:"keyboard_shortcuts,omitempty" example:"false"`
	TooltipsEnabled        *bool                     `json:"tooltips_enabled,omitempty" example:"false"`
	NotificationTypes      map[string]bool           `json:"notification_types,omitempty"`
	FeaturePreferences     map[string]interface{}    `json:"feature_preferences,omitempty"`
	CustomPreferences      map[string]interface{}    `json:"custom_preferences,omitempty"`
}

// NotificationPreferencesRequest represents input for notification preferences
type NotificationPreferencesRequest struct {
	EmailNotifications     bool            `json:"email_notifications" example:"true"`
	PushNotifications      bool            `json:"push_notifications" example:"false"`
	SMSNotifications       bool            `json:"sms_notifications" example:"false"`
	MarketingEmails        bool            `json:"marketing_emails" example:"true"`
	NewsletterSubscription bool            `json:"newsletter_subscription" example:"true"`
	ProductUpdates         bool            `json:"product_updates" example:"true"`
	SecurityAlerts         bool            `json:"security_alerts" example:"true"`
	NotificationTypes      map[string]bool `json:"notification_types,omitempty"`
}

// PrivacyPreferencesRequest represents input for privacy preferences
type PrivacyPreferencesRequest struct {
	ProfileVisibility     models.ProfileVisibility `json:"profile_visibility" validate:"oneof=public private friends_only" example:"public"`
	ShowOnlineStatus      bool                     `json:"show_online_status" example:"true"`
	AllowSearch           bool                     `json:"allow_search" example:"true"`
	DataProcessingConsent bool                     `json:"data_processing_consent" example:"true"`
}

// UIPreferencesRequest represents input for UI preferences
type UIPreferencesRequest struct {
	Theme             models.Theme `json:"theme" validate:"oneof=light dark system" example:"light"`
	DashboardLayout   string       `json:"dashboard_layout" example:"grid"`
	ItemsPerPage      uint         `json:"items_per_page" validate:"min=10,max=100" example:"20"`
	AutoSave          bool         `json:"auto_save" example:"true"`
	KeyboardShortcuts bool         `json:"keyboard_shortcuts" example:"true"`
	TooltipsEnabled   bool         `json:"tooltips_enabled" example:"true"`
}

// UserPreferencesResponse represents the response for user preferences operations
// KEEP_OMITEMPTY: Optional custom maps that may be empty
type UserPreferencesResponse struct {
	ID                     uint                     `json:"id" example:"1"`
	UserID                 uint                     `json:"user_id" example:"1"`
	TenantID               uint                     `json:"tenant_id" example:"1"`
	EmailNotifications     bool                     `json:"email_notifications" example:"true"`
	PushNotifications      bool                     `json:"push_notifications" example:"false"`
	SMSNotifications       bool                     `json:"sms_notifications" example:"false"`
	MarketingEmails        bool                     `json:"marketing_emails" example:"true"`
	NewsletterSubscription bool                     `json:"newsletter_subscription" example:"true"`
	ProductUpdates         bool                     `json:"product_updates" example:"true"`
	SecurityAlerts         bool                     `json:"security_alerts" example:"true"`
	ProfileVisibility      models.ProfileVisibility `json:"profile_visibility" example:"public"`
	ShowOnlineStatus       bool                     `json:"show_online_status" example:"true"`
	AllowSearch            bool                     `json:"allow_search" example:"true"`
	DataProcessingConsent  bool                     `json:"data_processing_consent" example:"true"`
	Theme                  models.Theme             `json:"theme" example:"light"`
	DashboardLayout        string                   `json:"dashboard_layout" example:"grid"`
	ItemsPerPage           uint                     `json:"items_per_page" example:"20"`
	AutoSave               bool                     `json:"auto_save" example:"true"`
	KeyboardShortcuts      bool                     `json:"keyboard_shortcuts" example:"true"`
	TooltipsEnabled        bool                     `json:"tooltips_enabled" example:"true"`
	NotificationTypes      map[string]bool          `json:"notification_types,omitempty"`  // KEEP_OMITEMPTY: Optional custom map
	FeaturePreferences     map[string]interface{}   `json:"feature_preferences,omitempty"` // KEEP_OMITEMPTY: Optional custom map
	CustomPreferences      map[string]interface{}   `json:"custom_preferences,omitempty"`  // KEEP_OMITEMPTY: Optional custom map
}

// PreferencesStatsResponse represents preferences statistics
type PreferencesStatsResponse struct {
	TotalPreferences     int64                      `json:"total_preferences" example:"100"`
	ThemeDistribution    map[models.Theme]int64     `json:"theme_distribution"`
	LanguageDistribution map[string]int64           `json:"language_distribution"`
	TimezoneDistribution map[string]int64           `json:"timezone_distribution"`
	NotificationStats    *NotificationStatsResponse `json:"notification_stats"`
	PrivacyStats         *PrivacyStatsResponse      `json:"privacy_stats"`
}

// NotificationStatsResponse represents notification preferences statistics
type NotificationStatsResponse struct {
	EmailEnabled    int64 `json:"email_enabled" example:"75"`
	PushEnabled     int64 `json:"push_enabled" example:"40"`
	SMSEnabled      int64 `json:"sms_enabled" example:"20"`
	MarketingAllow  int64 `json:"marketing_allow" example:"60"`
	NewsletterAllow int64 `json:"newsletter_allow" example:"80"`
	SecurityAllow   int64 `json:"security_allow" example:"95"`
}

// PrivacyStatsResponse represents privacy preferences statistics
type PrivacyStatsResponse struct {
	PublicProfiles   int64 `json:"public_profiles" example:"50"`
	PrivateProfiles  int64 `json:"private_profiles" example:"30"`
	FriendsProfiles  int64 `json:"friends_profiles" example:"20"`
	OnlineStatusShow int64 `json:"online_status_show" example:"70"`
	SearchAllow      int64 `json:"search_allow" example:"85"`
}

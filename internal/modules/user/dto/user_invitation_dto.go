package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// CreateInvitationRequest represents input for creating a user invitation
type CreateInvitationRequest struct {
	TenantID  uint    `json:"tenant_id" validate:"required,min=1" example:"1"`
	WebsiteID *uint   `json:"website_id,omitempty" validate:"omitempty,min=1" example:"1"`
	Email     string  `json:"email" validate:"required,email" example:"<EMAIL>"`
	RoleID    *uint   `json:"role_id,omitempty" validate:"omitempty,min=1" example:"3"`
	Message   *string `json:"message,omitempty" validate:"omitempty,max=1000" example:"Welcome to our team! We're excited to have you join us."`
	ExpiresIn int     `json:"expires_in,omitempty" validate:"omitempty,min=1,max=8760" example:"168"`
}

// UpdateInvitationRequest represents input for updating a user invitation
type UpdateInvitationRequest struct {
	RoleID    *uint      `json:"role_id,omitempty" validate:"omitempty,min=1" example:"2"`
	Message   *string    `json:"message,omitempty" validate:"omitempty,max=1000" example:"Updated welcome message"`
	ExpiresAt *time.Time `json:"expires_at,omitempty"`
}

// InvitationListFilter represents filter for listing invitations
type InvitationListFilter struct {
	TenantID       uint                         `json:"tenant_id,omitempty" example:"1"`
	WebsiteID      *uint                        `json:"website_id,omitempty" example:"1"`
	Email          string                       `json:"email,omitempty" example:"<EMAIL>"`
	Status         models.UserInvitationStatus  `json:"status,omitempty" example:"pending"`
	InvitedBy      uint                         `json:"invited_by,omitempty" example:"2"`
	Search         string                       `json:"search,omitempty" example:"john"`
	SortBy         string                       `json:"sort_by,omitempty" example:"created_at"`
	SortOrder      string                       `json:"sort_order,omitempty" example:"desc"`
	IncludeExpired bool                         `json:"include_expired,omitempty" example:"false"`
	Pagination     *pagination.CursorPagination `json:"pagination,omitempty"`
}

// AcceptInvitationRequest represents input for accepting an invitation
type AcceptInvitationRequest struct {
	Token string `json:"token" validate:"required" example:"abc123def456ghi789"`
}

// RejectInvitationRequest represents input for rejecting an invitation
type RejectInvitationRequest struct {
	Token  string  `json:"token" validate:"required" example:"abc123def456ghi789"`
	Reason *string `json:"reason,omitempty" example:"Not interested at this time"`
}

// InvitationResponse represents the response for user invitation operations
type InvitationResponse struct {
	ID         uint                        `json:"id" example:"1"`
	TenantID   uint                        `json:"tenant_id" example:"1"`
	WebsiteID  *uint                       `json:"website_id,omitempty" example:"1"`
	Email      string                      `json:"email" example:"<EMAIL>"`
	Token      string                      `json:"token" example:"abc123def456ghi789"`
	RoleID     *uint                       `json:"role_id,omitempty" example:"3"`
	Message    *string                     `json:"message,omitempty" example:"Welcome to our team!"`
	Status     models.UserInvitationStatus `json:"status" example:"pending"`
	InvitedBy  uint                        `json:"invited_by" example:"2"`
	ExpiresAt  time.Time                   `json:"expires_at"`
	AcceptedAt *time.Time                  `json:"accepted_at,omitempty"`
	RejectedAt *time.Time                  `json:"rejected_at,omitempty"`
	CreatedAt  time.Time                   `json:"created_at"`
	UpdatedAt  time.Time                   `json:"updated_at"`
}

// InvitationListResponse represents response for listing user invitations
type InvitationListResponse struct {
	Invitations []InvitationResponse       `json:"invitations"`
	Pagination  *pagination.CursorResponse `json:"pagination"`
	Total       int64                      `json:"total" example:"15"`
}

// AcceptInvitationResponse represents response when accepting an invitation
type AcceptInvitationResponse struct {
	Invitation *InvitationResponse       `json:"invitation"`
	Membership *TenantMembershipResponse `json:"membership"`
	Message    string                    `json:"message" example:"Invitation accepted successfully"`
}

// InvitationStatsResponse represents invitation statistics
type InvitationStatsResponse struct {
	TotalInvitations    int64                                 `json:"total_invitations" example:"100"`
	PendingInvitations  int64                                 `json:"pending_invitations" example:"25"`
	AcceptedInvitations int64                                 `json:"accepted_invitations" example:"60"`
	RejectedInvitations int64                                 `json:"rejected_invitations" example:"10"`
	ExpiredInvitations  int64                                 `json:"expired_invitations" example:"5"`
	StatusDistribution  map[models.UserInvitationStatus]int64 `json:"status_distribution"`
	AcceptanceRate      float64                               `json:"acceptance_rate" example:"0.75"`
	AverageResponseTime float64                               `json:"average_response_time" example:"72.5"`
	RecentInvitations   []InvitationResponse                  `json:"recent_invitations"`
}

// BulkInvitationRequest represents input for bulk creating invitations
type BulkInvitationRequest struct {
	TenantID  uint   `json:"tenant_id" validate:"required,min=1" example:"1"`
	WebsiteID *uint  `json:"website_id,omitempty" validate:"omitempty,min=1" example:"1"`
	RoleID    *uint  `json:"role_id,omitempty" validate:"omitempty,min=1" example:"3"`
	Message   string `json:"message,omitempty" validate:"max=1000" example:"Welcome to our team!"`
	ExpiresIn int    `json:"expires_in,omitempty" validate:"omitempty,min=1,max=8760" example:"168"`
	Emails    []struct {
		Email         string  `json:"email" validate:"required,email" example:"<EMAIL>"`
		CustomMessage *string `json:"custom_message,omitempty" example:"Personal welcome message"`
	} `json:"emails" validate:"required,min=1,max=50"`
}

// BulkInvitationResponse represents response for bulk invitation creation
type BulkInvitationResponse struct {
	TotalRequested int                  `json:"total_requested" example:"10"`
	TotalCreated   int                  `json:"total_created" example:"8"`
	TotalFailed    int                  `json:"total_failed" example:"2"`
	Invitations    []InvitationResponse `json:"invitations"`
	Errors         []struct {
		Email string `json:"email" example:"invalid@email"`
		Error string `json:"error" example:"Invalid email format"`
	} `json:"errors,omitempty"`
}

// ResendInvitationRequest represents input for resending an invitation
type ResendInvitationRequest struct {
	InvitationID uint    `json:"invitation_id" validate:"required" example:"1"`
	Message      *string `json:"message,omitempty" validate:"omitempty,max=1000" example:"Reminder: You're invited to join our team"`
}

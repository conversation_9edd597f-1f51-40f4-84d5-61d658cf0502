package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// UserCreateRequest represents input for creating a user
type UserCreateRequest struct {
	TenantID         uint              `json:"tenant_id" validate:"required" example:"1"`
	Email            string            `json:"email" validate:"required,email" example:"<EMAIL>"`
	Username         string            `json:"username" validate:"required,min=3,max=30" example:"john_doe"`
	Password         string            `json:"password" validate:"required,min=8" example:"SecurePass123!"`
	FirstName        string            `json:"first_name" validate:"required,min=1,max=50" example:"John"`
	LastName         string            `json:"last_name" validate:"required,min=1,max=50" example:"Doe"`
	DisplayName      string            `json:"display_name" validate:"max=100" example:"John Doe"`
	Phone            string            `json:"phone,omitempty" validate:"omitempty,phone" example:"+1234567890"`
	AvatarURL        string            `json:"avatar_url,omitempty" validate:"omitempty,url" example:"https://example.com/avatar.jpg"`
	Language         string            `json:"language,omitempty" validate:"omitempty,len=2" example:"en"`
	Timezone         string            `json:"timezone,omitempty" example:"America/New_York"`
	Role             models.UserRole   `json:"role" validate:"required,oneof=user admin moderator" example:"user"`
	Status           models.UserStatus `json:"status,omitempty" validate:"omitempty,oneof=active inactive suspended pending_verification" example:"active"`
	SendWelcomeEmail bool              `json:"send_welcome_email" example:"true"`
}

// UserUpdateRequest represents input for updating a user
type UserUpdateRequest struct {
	Email       *string            `json:"email,omitempty" validate:"omitempty,email" example:"<EMAIL>"`
	Username    *string            `json:"username,omitempty" validate:"omitempty,min=3,max=30" example:"updated_username"`
	FirstName   *string            `json:"first_name,omitempty" validate:"omitempty,min=1,max=50" example:"Jane"`
	LastName    *string            `json:"last_name,omitempty" validate:"omitempty,min=1,max=50" example:"Smith"`
	DisplayName *string            `json:"display_name,omitempty" validate:"omitempty,max=100" example:"Jane Smith"`
	Phone       *string            `json:"phone,omitempty" validate:"omitempty,phone" example:"+9876543210"`
	AvatarURL   *string            `json:"avatar_url,omitempty" validate:"omitempty,url" example:"https://example.com/new-avatar.jpg"`
	Language    *string            `json:"language,omitempty" validate:"omitempty,len=2" example:"fr"`
	Timezone    *string            `json:"timezone,omitempty" example:"Europe/London"`
	Role        *models.UserRole   `json:"role,omitempty" validate:"omitempty,oneof=user admin moderator" example:"admin"`
	Status      *models.UserStatus `json:"status,omitempty" validate:"omitempty,oneof=active inactive suspended pending_verification deleted" example:"inactive"`
}

// UserListFilter represents filter for listing users
type UserListFilter struct {
	TenantID         uint                         `json:"tenant_id,omitempty" example:"1"`
	Status           *models.UserStatus           `json:"status,omitempty" example:"active"`
	Role             *models.UserRole             `json:"role,omitempty" example:"user"`
	EmailVerified    *bool                        `json:"email_verified,omitempty" example:"true"`
	PhoneVerified    *bool                        `json:"phone_verified,omitempty" example:"false"`
	TwoFactorEnabled *bool                        `json:"two_factor_enabled,omitempty" example:"true"`
	CreatedAfter     *time.Time                   `json:"created_after,omitempty"`
	CreatedBefore    *time.Time                   `json:"created_before,omitempty"`
	LastLoginAfter   *time.Time                   `json:"last_login_after,omitempty"`
	LastLoginBefore  *time.Time                   `json:"last_login_before,omitempty"`
	SearchQuery      string                       `json:"search_query,omitempty" example:"john"`
	Pagination       *pagination.CursorPagination `json:"pagination,omitempty"`
}

// UserSearchRequest represents input for searching users
type UserSearchRequest struct {
	TenantID   uint                         `json:"tenant_id,omitempty" example:"1"`
	Query      string                       `json:"query" validate:"required,min=1" example:"developer"`
	Skills     []string                     `json:"skills,omitempty" example:"golang,javascript"`
	Location   string                       `json:"location,omitempty" example:"New York"`
	Company    string                       `json:"company,omitempty" example:"Tech Corp"`
	Role       *models.UserRole             `json:"role,omitempty" example:"user"`
	Status     *models.UserStatus           `json:"status,omitempty" example:"active"`
	Pagination *pagination.CursorPagination `json:"pagination,omitempty"`
}

// UserResponse represents the response for user operations
// KEEP_OMITEMPTY: Optional personal data and nullable verification timestamps
type UserResponse struct {
	ID               uint              `json:"id" example:"1"`
	TenantID         uint              `json:"tenant_id" example:"1"`
	Email            string            `json:"email" example:"<EMAIL>"`
	Username         string            `json:"username" example:"john_doe"`
	FirstName        string            `json:"first_name" example:"John"`
	LastName         string            `json:"last_name" example:"Doe"`
	DisplayName      string            `json:"display_name" example:"John Doe"`
	Phone            string            `json:"phone,omitempty" example:"+1234567890"`                         // KEEP_OMITEMPTY: Optional personal data
	AvatarURL        string            `json:"avatar_url,omitempty" example:"https://example.com/avatar.jpg"` // KEEP_OMITEMPTY: Optional personal data
	Language         string            `json:"language" example:"en"`
	Timezone         string            `json:"timezone" example:"America/New_York"`
	Role             models.UserRole   `json:"role" example:"user"`
	Status           models.UserStatus `json:"status" example:"active"`
	EmailVerified    bool              `json:"email_verified" example:"true"`
	EmailVerifiedAt  *time.Time        `json:"email_verified_at,omitempty"` // KEEP_OMITEMPTY: Nullable timestamp
	PhoneVerified    bool              `json:"phone_verified" example:"false"`
	PhoneVerifiedAt  *time.Time        `json:"phone_verified_at,omitempty"` // KEEP_OMITEMPTY: Nullable timestamp
	TwoFactorEnabled bool              `json:"two_factor_enabled" example:"false"`
	LastLoginAt      *time.Time        `json:"last_login_at,omitempty"` // KEEP_OMITEMPTY: Nullable timestamp
	LoginCount       int64             `json:"login_count" example:"42"`
	CreatedAt        time.Time         `json:"created_at"`
	UpdatedAt        time.Time         `json:"updated_at"`
}

// UserListResponse represents response for listing users
type UserListResponse struct {
	Users      []UserResponse             `json:"users"`
	Pagination *pagination.CursorResponse `json:"pagination"`
	Total      int64                      `json:"total" example:"150"`
}

// UserSearchResponse represents response for searching users
type UserSearchResponse struct {
	Users      []UserResponse             `json:"users"`
	Pagination *pagination.CursorResponse `json:"pagination"`
	Total      int64                      `json:"total" example:"25"`
	Query      string                     `json:"query" example:"developer"`
}

// TwoFactorSetupResponse represents two-factor authentication setup
type TwoFactorSetupResponse struct {
	Secret      string   `json:"secret" example:"JBSWY3DPEHPK3PXP"`
	QRCodeURL   string   `json:"qr_code_url" example:"https://chart.googleapis.com/chart?chs=200x200&chld=M|0&cht=qr&chl=otpauth://totp/..."`
	BackupCodes []string `json:"backup_codes" example:"123456,789012,345678"`
}

// UserStatsResponse represents user statistics
type UserStatsResponse struct {
	TotalUsers          int64                       `json:"total_users" example:"1000"`
	ActiveUsers         int64                       `json:"active_users" example:"850"`
	InactiveUsers       int64                       `json:"inactive_users" example:"100"`
	SuspendedUsers      int64                       `json:"suspended_users" example:"30"`
	DeletedUsers        int64                       `json:"deleted_users" example:"20"`
	VerifiedUsers       int64                       `json:"verified_users" example:"950"`
	UnverifiedUsers     int64                       `json:"unverified_users" example:"50"`
	TwoFactorUsers      int64                       `json:"two_factor_users" example:"300"`
	RoleDistribution    map[models.UserRole]int64   `json:"role_distribution"`
	StatusDistribution  map[models.UserStatus]int64 `json:"status_distribution"`
	RegistrationTrend   []RegistrationPoint         `json:"registration_trend"`
	ActivityTrend       []ActivityPoint             `json:"activity_trend"`
	AverageLoginCount   float64                     `json:"average_login_count" example:"25.5"`
	RecentlyActiveCount int64                       `json:"recently_active_count" example:"450"`
}

// RegistrationPoint represents a point in registration trend
type RegistrationPoint struct {
	Date  time.Time `json:"date"`
	Count int64     `json:"count" example:"15"`
}

// ActivityPoint represents a point in activity trend
type ActivityPoint struct {
	Date   time.Time `json:"date"`
	Logins int64     `json:"logins" example:"250"`
	Active int64     `json:"active" example:"180"`
}

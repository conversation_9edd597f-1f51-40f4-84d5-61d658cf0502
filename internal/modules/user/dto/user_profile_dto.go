package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// UserProfileCreateRequest represents input for creating a user profile
type UserProfileCreateRequest struct {
	UserID         uint                   `json:"user_id" validate:"required" example:"1"`
	Bio            string                 `json:"bio,omitempty" validate:"omitempty,max=500" example:"Software developer passionate about technology"`
	Title          string                 `json:"title,omitempty" validate:"omitempty,max=100" example:"Senior Software Engineer"`
	Company        string                 `json:"company,omitempty" validate:"omitempty,max=100" example:"Tech Corp"`
	Location       string                 `json:"location,omitempty" validate:"omitempty,max=100" example:"New York, NY"`
	Website        string                 `json:"website,omitempty" validate:"omitempty,url" example:"https://johndoe.com"`
	BirthDate      *time.Time             `json:"birth_date,omitempty"`
	Gender         string                 `json:"gender,omitempty" validate:"omitempty,oneof=male female other prefer_not_to_say" example:"male"`
	Address        string                 `json:"address,omitempty" validate:"omitempty,max=200" example:"123 Main St"`
	City           string                 `json:"city,omitempty" validate:"omitempty,max=100" example:"New York"`
	State          string                 `json:"state,omitempty" validate:"omitempty,max=100" example:"NY"`
	Country        string                 `json:"country,omitempty" validate:"omitempty,max=100" example:"USA"`
	PostalCode     string                 `json:"postal_code,omitempty" validate:"omitempty,max=20" example:"10001"`
	JobTitle       string                 `json:"job_title,omitempty" validate:"omitempty,max=100" example:"Lead Developer"`
	Department     string                 `json:"department,omitempty" validate:"omitempty,max=100" example:"Engineering"`
	Skills         []string               `json:"skills,omitempty" example:"golang,javascript,docker"`
	Interests      []string               `json:"interests,omitempty" example:"programming,photography,travel"`
	DisplayProfile bool                   `json:"display_profile" example:"true"`
	AllowContact   bool                   `json:"allow_contact" example:"true"`
	ShowEmail      bool                   `json:"show_email" example:"false"`
	ShowPhone      bool                   `json:"show_phone" example:"false"`
	CustomFields   map[string]interface{} `json:"custom_fields,omitempty"`
}

// UserProfileUpdateRequest represents input for updating a user profile
type UserProfileUpdateRequest struct {
	Bio            *string                `json:"bio,omitempty" validate:"omitempty,max=500" example:"Updated bio"`
	Title          *string                `json:"title,omitempty" validate:"omitempty,max=100" example:"Principal Engineer"`
	Company        *string                `json:"company,omitempty" validate:"omitempty,max=100" example:"New Tech Corp"`
	Location       *string                `json:"location,omitempty" validate:"omitempty,max=100" example:"San Francisco, CA"`
	Website        *string                `json:"website,omitempty" validate:"omitempty,url" example:"https://newsite.com"`
	BirthDate      *time.Time             `json:"birth_date,omitempty"`
	Gender         *string                `json:"gender,omitempty" validate:"omitempty,oneof=male female other prefer_not_to_say" example:"female"`
	Address        *string                `json:"address,omitempty" validate:"omitempty,max=200" example:"456 Oak St"`
	City           *string                `json:"city,omitempty" validate:"omitempty,max=100" example:"San Francisco"`
	State          *string                `json:"state,omitempty" validate:"omitempty,max=100" example:"CA"`
	Country        *string                `json:"country,omitempty" validate:"omitempty,max=100" example:"USA"`
	PostalCode     *string                `json:"postal_code,omitempty" validate:"omitempty,max=20" example:"94105"`
	JobTitle       *string                `json:"job_title,omitempty" validate:"omitempty,max=100" example:"Tech Lead"`
	Department     *string                `json:"department,omitempty" validate:"omitempty,max=100" example:"Platform"`
	Skills         []string               `json:"skills,omitempty" example:"go,react,kubernetes"`
	Interests      []string               `json:"interests,omitempty" example:"ai,music,hiking"`
	DisplayProfile *bool                  `json:"display_profile,omitempty" example:"false"`
	AllowContact   *bool                  `json:"allow_contact,omitempty" example:"false"`
	ShowEmail      *bool                  `json:"show_email,omitempty" example:"true"`
	ShowPhone      *bool                  `json:"show_phone,omitempty" example:"true"`
	CustomFields   map[string]interface{} `json:"custom_fields,omitempty"`
}

// UserProfileResponse represents the response for user profile operations
type UserProfileResponse struct {
	ID             uint                   `json:"id" example:"1"`
	UserID         uint                   `json:"user_id" example:"1"`
	TenantID       uint                   `json:"tenant_id" example:"1"`
	Bio            string                 `json:"bio,omitempty" example:"Software developer passionate about technology"`
	Title          string                 `json:"title,omitempty" example:"Senior Software Engineer"`
	Company        string                 `json:"company,omitempty" example:"Tech Corp"`
	Location       string                 `json:"location,omitempty" example:"New York, NY"`
	Website        string                 `json:"website,omitempty" example:"https://johndoe.com"`
	BirthDate      *time.Time             `json:"birth_date,omitempty"`
	Gender         string                 `json:"gender,omitempty" example:"male"`
	Address        string                 `json:"address,omitempty" example:"123 Main St"`
	City           string                 `json:"city,omitempty" example:"New York"`
	State          string                 `json:"state,omitempty" example:"NY"`
	Country        string                 `json:"country,omitempty" example:"USA"`
	PostalCode     string                 `json:"postal_code,omitempty" example:"10001"`
	JobTitle       string                 `json:"job_title,omitempty" example:"Lead Developer"`
	Department     string                 `json:"department,omitempty" example:"Engineering"`
	Skills         []string               `json:"skills,omitempty" example:"golang,javascript,docker"`
	Interests      []string               `json:"interests,omitempty" example:"programming,photography,travel"`
	DisplayProfile bool                   `json:"display_profile" example:"true"`
	AllowContact   bool                   `json:"allow_contact" example:"true"`
	ShowEmail      bool                   `json:"show_email" example:"false"`
	ShowPhone      bool                   `json:"show_phone" example:"false"`
	CustomFields   map[string]interface{} `json:"custom_fields,omitempty"`
	CreatedAt      time.Time              `json:"created_at"`
	UpdatedAt      time.Time              `json:"updated_at"`
}

// UserProfileListResponse represents response for listing user profiles
type UserProfileListResponse struct {
	Profiles   []UserProfileResponse      `json:"profiles"`
	Pagination *pagination.CursorResponse `json:"pagination"`
	Total      int64                      `json:"total" example:"50"`
}

// ProfileCompletionStatsResponse represents profile completion statistics
type ProfileCompletionStatsResponse struct {
	TotalProfiles        int64   `json:"total_profiles" example:"100"`
	CompletedProfiles    int64   `json:"completed_profiles" example:"75"`
	IncompleteProfiles   int64   `json:"incomplete_profiles" example:"25"`
	AverageCompletion    float64 `json:"average_completion" example:"85.5"`
	CompletionRate       float64 `json:"completion_rate" example:"75.0"`
	ProfilesWithBio      int64   `json:"profiles_with_bio" example:"60"`
	ProfilesWithAvatar   int64   `json:"profiles_with_avatar" example:"80"`
	ProfilesWithLocation int64   `json:"profiles_with_location" example:"70"`
	ProfilesWithCompany  int64   `json:"profiles_with_company" example:"65"`
	ProfilesWithSkills   int64   `json:"profiles_with_skills" example:"55"`
}

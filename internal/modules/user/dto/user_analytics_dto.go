package dto

import (
	"time"
)

// UserActivityResponse represents user activity data
type UserActivityResponse struct {
	UserID                 uint                  `json:"user_id" example:"1"`
	LoginHistory           []LoginActivityItem   `json:"login_history"`
	ActionHistory          []ActionActivityItem  `json:"action_history"`
	SessionHistory         []SessionActivityItem `json:"session_history"`
	TotalLogins            int64                 `json:"total_logins" example:"150"`
	TotalActions           int64                 `json:"total_actions" example:"500"`
	TotalSessions          int64                 `json:"total_sessions" example:"75"`
	AverageSessionDuration float64               `json:"average_session_duration" example:"1800.5"`
	LastActivity           *time.Time            `json:"last_activity,omitempty"`
}

// LoginActivityItem represents login activity
type LoginActivityItem struct {
	Date      time.Time `json:"date"`
	IPAddress string    `json:"ip_address" example:"***********"`
	UserAgent string    `json:"user_agent" example:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"`
	Success   bool      `json:"success" example:"true"`
}

// ActionActivityItem represents action activity
type ActionActivityItem struct {
	Date   time.Time `json:"date"`
	Action string    `json:"action" example:"create_post"`
	Target string    `json:"target" example:"blog_post_123"`
	Result string    `json:"result" example:"success"`
}

// SessionActivityItem represents session activity
type SessionActivityItem struct {
	Date      time.Time `json:"date"`
	Duration  int64     `json:"duration" example:"3600"`
	Actions   int64     `json:"actions" example:"25"`
	IPAddress string    `json:"ip_address" example:"***********"`
}

// RegistrationStatsResponse represents registration statistics
type RegistrationStatsResponse struct {
	Period             string                   `json:"period" example:"2024-01"`
	TotalRegistrations int64                    `json:"total_registrations" example:"500"`
	Trend              []RegistrationTrendPoint `json:"trend"`
	Sources            map[string]int64         `json:"sources"`
	Devices            map[string]int64         `json:"devices"`
	Locations          map[string]int64         `json:"locations"`
}

// RegistrationTrendPoint represents a point in registration trend
type RegistrationTrendPoint struct {
	Date  time.Time `json:"date"`
	Count int64     `json:"count" example:"15"`
}

// EngagementStatsResponse represents engagement statistics
type EngagementStatsResponse struct {
	Period                string  `json:"period" example:"2024-01"`
	TotalActiveUsers      int64   `json:"total_active_users" example:"1000"`
	DailyActiveUsers      int64   `json:"daily_active_users" example:"150"`
	WeeklyActiveUsers     int64   `json:"weekly_active_users" example:"500"`
	MonthlyActiveUsers    int64   `json:"monthly_active_users" example:"800"`
	AverageSessionTime    float64 `json:"average_session_time" example:"1800.5"`
	AverageActionsPerUser float64 `json:"average_actions_per_user" example:"25.3"`
	BounceRate            float64 `json:"bounce_rate" example:"0.15"`
	ReturnRate            float64 `json:"return_rate" example:"0.65"`
}

// RetentionStatsResponse represents retention statistics
type RetentionStatsResponse struct {
	Period         string       `json:"period" example:"2024-01"`
	CohortData     []CohortData `json:"cohort_data"`
	Day1Retention  float64      `json:"day1_retention" example:"0.85"`
	Day7Retention  float64      `json:"day7_retention" example:"0.65"`
	Day30Retention float64      `json:"day30_retention" example:"0.45"`
	ChurnRate      float64      `json:"churn_rate" example:"0.25"`
}

// CohortData represents cohort data
type CohortData struct {
	Date      time.Time `json:"date"`
	Size      int64     `json:"size" example:"100"`
	Retention []float64 `json:"retention" example:"1.0,0.85,0.70,0.60"`
}

// DemographicStatsResponse represents demographic statistics
type DemographicStatsResponse struct {
	AgeDistribution      map[string]int64 `json:"age_distribution"`
	GenderDistribution   map[string]int64 `json:"gender_distribution"`
	LocationDistribution map[string]int64 `json:"location_distribution"`
	LanguageDistribution map[string]int64 `json:"language_distribution"`
	TimezoneDistribution map[string]int64 `json:"timezone_distribution"`
	DeviceDistribution   map[string]int64 `json:"device_distribution"`
}

// VerificationStatusResponse represents verification status
type VerificationStatusResponse struct {
	UserID          uint       `json:"user_id" example:"1"`
	EmailVerified   bool       `json:"email_verified" example:"true"`
	EmailVerifiedAt *time.Time `json:"email_verified_at,omitempty"`
	PhoneVerified   bool       `json:"phone_verified" example:"false"`
	PhoneVerifiedAt *time.Time `json:"phone_verified_at,omitempty"`
	LastEmailSent   *time.Time `json:"last_email_sent,omitempty"`
	LastPhoneSent   *time.Time `json:"last_phone_sent,omitempty"`
}

// UserAnalyticsRequest represents request for user analytics
type UserAnalyticsRequest struct {
	UserID     uint       `json:"user_id,omitempty" example:"1"`
	StartDate  *time.Time `json:"start_date,omitempty"`
	EndDate    *time.Time `json:"end_date,omitempty"`
	Period     string     `json:"period,omitempty" validate:"omitempty,oneof=day week month year" example:"month"`
	MetricType string     `json:"metric_type,omitempty" validate:"omitempty,oneof=activity login session registration engagement retention demographic" example:"activity"`
}

// BulkUserAnalyticsRequest represents request for bulk user analytics
type BulkUserAnalyticsRequest struct {
	UserIDs    []uint     `json:"user_ids" validate:"required,min=1,max=100"`
	StartDate  *time.Time `json:"start_date,omitempty"`
	EndDate    *time.Time `json:"end_date,omitempty"`
	MetricType string     `json:"metric_type" validate:"required,oneof=activity login session" example:"activity"`
}

package models

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strconv"
	"time"

	"gorm.io/gorm"

	tenantModels "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
)

// PhoneVerificationToken represents a phone verification token
type PhoneVerificationToken struct {
	ID       uint `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID uint `gorm:"not null;index" json:"tenant_id"`
	UserID   uint `gorm:"not null;index" json:"user_id"`

	// Token Information
	Phone            string `gorm:"type:varchar(50);not null;index" json:"phone"`
	VerificationCode string `gorm:"type:varchar(10);not null" json:"-"` // Hidden from JSON
	CodeHash         string `gorm:"type:varchar(255);not null" json:"-"` // Hidden from JSON

	// Request Context
	UserAgent *string `gorm:"type:text" json:"user_agent,omitempty"`
	IPAddress *string `gorm:"type:varchar(45)" json:"ip_address,omitempty"`

	// Status and Tracking
	IsUsed bool       `gorm:"default:false;index" json:"is_used"`
	UsedAt *time.Time `json:"used_at,omitempty"`

	// Rate Limiting
	ResendCount  uint       `gorm:"default:0" json:"resend_count"`
	LastResentAt *time.Time `json:"last_resent_at,omitempty"`

	// SMS Provider Info
	Provider          *string                `gorm:"type:varchar(50)" json:"provider,omitempty"`
	ProviderMessageID *string                `gorm:"type:varchar(255)" json:"provider_message_id,omitempty"`
	ProviderResponse  map[string]interface{} `gorm:"type:json;serializer:json" json:"provider_response,omitempty"`

	// Expiration
	ExpiresAt time.Time `gorm:"not null;index" json:"expires_at"`

	// Timestamps
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Relationships
	Tenant *tenantModels.Tenant `gorm:"foreignKey:TenantID" json:"tenant,omitempty"`
	User   *User               `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// TableName returns the table name for the PhoneVerificationToken model
func (PhoneVerificationToken) TableName() string {
	return "phone_verification_tokens"
}

// GenerateCode generates a new verification code (6 digits by default)
func (pvt *PhoneVerificationToken) GenerateCode(length int) error {
	if length == 0 {
		length = 6
	}

	// Generate random digits
	max := 1
	for i := 0; i < length; i++ {
		max *= 10
	}
	min := max / 10

	// Generate random number in range
	b := make([]byte, 8)
	_, err := rand.Read(b)
	if err != nil {
		return fmt.Errorf("failed to generate random bytes: %w", err)
	}

	// Convert to number and get in range
	num := int64(0)
	for i := 0; i < 8; i++ {
		num = (num << 8) | int64(b[i])
	}
	if num < 0 {
		num = -num
	}

	code := (num % int64(max-min)) + int64(min)
	pvt.VerificationCode = fmt.Sprintf("%0*d", length, code)

	// Create hash for storage
	hash := sha256.Sum256([]byte(pvt.VerificationCode))
	pvt.CodeHash = hex.EncodeToString(hash[:])

	return nil
}

// IsExpired checks if the token has expired
func (pvt *PhoneVerificationToken) IsExpired() bool {
	return time.Now().After(pvt.ExpiresAt)
}

// IsValid checks if the token is valid for use
func (pvt *PhoneVerificationToken) IsValid() bool {
	return !pvt.IsUsed && !pvt.IsExpired()
}

// CanResend checks if a new token can be sent (rate limiting)
func (pvt *PhoneVerificationToken) CanResend(maxResends uint, resendInterval time.Duration) bool {
	// Check resend count limit
	if pvt.ResendCount >= maxResends {
		return false
	}

	// Check resend interval if this is not the first send
	if pvt.LastResentAt != nil && time.Since(*pvt.LastResentAt) < resendInterval {
		return false
	}

	return true
}

// MarkAsUsed marks the token as used
func (pvt *PhoneVerificationToken) MarkAsUsed() {
	pvt.IsUsed = true
	now := time.Now()
	pvt.UsedAt = &now
}

// IncrementResendCount increments the resend counter
func (pvt *PhoneVerificationToken) IncrementResendCount() {
	pvt.ResendCount++
	now := time.Now()
	pvt.LastResentAt = &now
}

// VerifyCode verifies if the provided code matches
func (pvt *PhoneVerificationToken) VerifyCode(code string) bool {
	// Basic validation
	if len(code) < 4 || len(code) > 10 {
		return false
	}

	// Ensure code is numeric
	if _, err := strconv.Atoi(code); err != nil {
		return false
	}

	// Hash the provided code
	hash := sha256.Sum256([]byte(code))
	providedHash := hex.EncodeToString(hash[:])

	// Compare hashes
	return providedHash == pvt.CodeHash
}

// BeforeCreate hook to generate code and set defaults
func (pvt *PhoneVerificationToken) BeforeCreate(tx *gorm.DB) error {
	// Generate code if not already set
	if pvt.VerificationCode == "" {
		if err := pvt.GenerateCode(6); err != nil {
			return err
		}
	}

	// Set default expiration if not set (15 minutes)
	if pvt.ExpiresAt.IsZero() {
		pvt.ExpiresAt = time.Now().Add(15 * time.Minute)
	}

	// Initialize provider response if nil
	if pvt.ProviderResponse == nil {
		pvt.ProviderResponse = make(map[string]interface{})
	}

	return nil
}

// CreatePhoneVerificationTokenRequest represents the request to create a new phone verification token
type CreatePhoneVerificationTokenRequest struct {
	UserID    uint    `json:"user_id" validate:"required"`
	Phone     string  `json:"phone" validate:"required,e164"`
	UserAgent *string `json:"user_agent,omitempty"`
	IPAddress *string `json:"ip_address,omitempty"`
	ExpiresIn *int    `json:"expires_in,omitempty" validate:"omitempty,min=60,max=3600"` // 1 minute to 1 hour
}

// VerifyPhoneRequest represents the request to verify a phone
type VerifyPhoneRequest struct {
	Phone     string  `json:"phone" validate:"required,e164"`
	Code      string  `json:"code" validate:"required,min=4,max=10,numeric"`
	UserAgent *string `json:"user_agent,omitempty"`
	IPAddress *string `json:"ip_address,omitempty"`
}

// ResendPhoneVerificationRequest represents the request to resend phone verification
type ResendPhoneVerificationRequest struct {
	Phone     string  `json:"phone" validate:"required,e164"`
	UserAgent *string `json:"user_agent,omitempty"`
	IPAddress *string `json:"ip_address,omitempty"`
}

// PhoneVerificationTokenResponse represents the response when returning token data
type PhoneVerificationTokenResponse struct {
	ID               uint                   `json:"id"`
	TenantID         uint                   `json:"tenant_id"`
	UserID           uint                   `json:"user_id"`
	Phone            string                 `json:"phone"`
	IsUsed           bool                   `json:"is_used"`
	UsedAt           *time.Time             `json:"used_at,omitempty"`
	ResendCount      uint                   `json:"resend_count"`
	LastResentAt     *time.Time             `json:"last_resent_at,omitempty"`
	Provider         *string                `json:"provider,omitempty"`
	ProviderResponse map[string]interface{} `json:"provider_response,omitempty"`
	ExpiresAt        time.Time              `json:"expires_at"`
	CreatedAt        time.Time              `json:"created_at"`
	UpdatedAt        time.Time              `json:"updated_at"`
	IsValid          bool                   `json:"is_valid"`
	IsExpired        bool                   `json:"is_expired"`
	TimeRemaining    string                 `json:"time_remaining"`
}

// FromPhoneVerificationToken converts a PhoneVerificationToken model to response
func (pvtr *PhoneVerificationTokenResponse) FromPhoneVerificationToken(token *PhoneVerificationToken) {
	pvtr.ID = token.ID
	pvtr.TenantID = token.TenantID
	pvtr.UserID = token.UserID
	pvtr.Phone = token.Phone
	pvtr.IsUsed = token.IsUsed
	pvtr.UsedAt = token.UsedAt
	pvtr.ResendCount = token.ResendCount
	pvtr.LastResentAt = token.LastResentAt
	pvtr.Provider = token.Provider
	pvtr.ProviderResponse = token.ProviderResponse
	pvtr.ExpiresAt = token.ExpiresAt
	pvtr.CreatedAt = token.CreatedAt
	pvtr.UpdatedAt = token.UpdatedAt
	pvtr.IsValid = token.IsValid()
	pvtr.IsExpired = token.IsExpired()

	// Calculate time remaining
	if !token.IsExpired() {
		remaining := time.Until(token.ExpiresAt)
		if remaining > time.Hour {
			pvtr.TimeRemaining = fmt.Sprintf("%.1f hours", remaining.Hours())
		} else if remaining > time.Minute {
			pvtr.TimeRemaining = fmt.Sprintf("%.0f minutes", remaining.Minutes())
		} else {
			pvtr.TimeRemaining = fmt.Sprintf("%.0f seconds", remaining.Seconds())
		}
	} else {
		pvtr.TimeRemaining = "expired"
	}
}

// PhoneVerificationResult represents the result of phone verification
type PhoneVerificationResult struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	UserID    uint   `json:"user_id,omitempty"`
	Phone     string `json:"phone,omitempty"`
	TokenUsed bool   `json:"token_used"`
}

// PhoneTokenStats represents statistics about phone verification tokens
type PhoneTokenStats struct {
	TotalCreated int64 `json:"total_created"`
	TotalUsed    int64 `json:"total_used"`
	TotalExpired int64 `json:"total_expired"`
	TotalActive  int64 `json:"total_active"`
	TotalResends int64 `json:"total_resends"`
}

// PhoneVerificationStatus represents the phone verification status
type PhoneVerificationStatus struct {
	UserID         uint       `json:"user_id"`
	PhoneVerified  bool       `json:"phone_verified"`
	Phone          *string    `json:"phone,omitempty"`
	VerifiedAt     *time.Time `json:"verified_at,omitempty"`
	HasActiveToken bool       `json:"has_active_token"`
	TokenExpiresAt *time.Time `json:"token_expires_at,omitempty"`
	ResendCount    *uint      `json:"resend_count,omitempty"`
	LastResentAt   *time.Time `json:"last_resent_at,omitempty"`
}
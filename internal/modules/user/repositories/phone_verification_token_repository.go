package repositories

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
)

// PhoneVerificationTokenRepository defines the interface for phone verification token operations
type PhoneVerificationTokenRepository interface {
	// Token CRUD operations
	CreateToken(ctx context.Context, token *models.PhoneVerificationToken) error
	GetTokenByID(ctx context.Context, id uint) (*models.PhoneVerificationToken, error)
	GetTokenByPhone(ctx context.Context, tenantID uint, phone string) (*models.PhoneVerificationToken, error)
	GetTokenByUserID(ctx context.Context, tenantID uint, userID uint) (*models.PhoneVerificationToken, error)
	UpdateToken(ctx context.Context, token *models.PhoneVerificationToken) error
	DeleteToken(ctx context.Context, id uint) error

	// Token verification
	VerifyToken(ctx context.Context, tenantID uint, phone string, code string) (*models.PhoneVerificationToken, error)
	MarkTokenAsUsed(ctx context.Context, id uint) error

	// Token management
	InvalidateTokensForUser(ctx context.Context, tenantID uint, userID uint) error
	InvalidateTokensForPhone(ctx context.Context, tenantID uint, phone string) error
	GetActiveTokensForUser(ctx context.Context, tenantID uint, userID uint) ([]*models.PhoneVerificationToken, error)

	// Rate limiting and analytics
	GetRecentTokensForUser(ctx context.Context, tenantID uint, userID uint, limit int) ([]*models.PhoneVerificationToken, error)
	GetRecentTokensForPhone(ctx context.Context, tenantID uint, phone string, hours int) ([]*models.PhoneVerificationToken, error)
	GetTokenStats(ctx context.Context, tenantID uint, userID uint) (*models.PhoneTokenStats, error)

	// Cleanup
	CleanupExpiredTokens(ctx context.Context) (int64, error)
	CleanupExpiredTokensForTenant(ctx context.Context, tenantID uint) (int64, error)
}
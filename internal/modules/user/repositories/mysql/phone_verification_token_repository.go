package mysql

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
)

// phoneVerificationTokenRepository implements PhoneVerificationTokenRepository
type phoneVerificationTokenRepository struct {
	db *gorm.DB
}

// NewPhoneVerificationTokenRepository creates a new phone verification token repository
func NewPhoneVerificationTokenRepository(db *gorm.DB) repositories.PhoneVerificationTokenRepository {
	return &phoneVerificationTokenRepository{
		db: db,
	}
}

// CreateToken creates a new phone verification token
func (r *phoneVerificationTokenRepository) CreateToken(ctx context.Context, token *models.PhoneVerificationToken) error {
	// First, invalidate any existing active tokens for this phone
	if err := r.InvalidateTokensForPhone(ctx, token.TenantID, token.Phone); err != nil {
		return fmt.Errorf("failed to invalidate existing tokens: %w", err)
	}

	// Create the new token
	if err := r.db.WithContext(ctx).Create(token).Error; err != nil {
		return fmt.Errorf("failed to create phone verification token: %w", err)
	}

	return nil
}

// GetTokenByID retrieves a token by its ID
func (r *phoneVerificationTokenRepository) GetTokenByID(ctx context.Context, id uint) (*models.PhoneVerificationToken, error) {
	var token models.PhoneVerificationToken
	err := r.db.WithContext(ctx).
		Where("id = ?", id).
		First(&token).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("phone verification token not found")
		}
		return nil, fmt.Errorf("failed to get phone verification token: %w", err)
	}

	return &token, nil
}

// GetTokenByPhone retrieves the active token for a phone number
func (r *phoneVerificationTokenRepository) GetTokenByPhone(ctx context.Context, tenantID uint, phone string) (*models.PhoneVerificationToken, error) {
	var token models.PhoneVerificationToken
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND phone = ? AND is_used = ? AND expires_at > ?", 
			tenantID, phone, false, time.Now()).
		Order("created_at DESC").
		First(&token).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("no active phone verification token found")
		}
		return nil, fmt.Errorf("failed to get phone verification token: %w", err)
	}

	return &token, nil
}

// GetTokenByUserID retrieves the active token for a user
func (r *phoneVerificationTokenRepository) GetTokenByUserID(ctx context.Context, tenantID uint, userID uint) (*models.PhoneVerificationToken, error) {
	var token models.PhoneVerificationToken
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND user_id = ? AND is_used = ? AND expires_at > ?", 
			tenantID, userID, false, time.Now()).
		Order("created_at DESC").
		First(&token).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("no active phone verification token found")
		}
		return nil, fmt.Errorf("failed to get phone verification token: %w", err)
	}

	return &token, nil
}

// UpdateToken updates an existing token
func (r *phoneVerificationTokenRepository) UpdateToken(ctx context.Context, token *models.PhoneVerificationToken) error {
	if err := r.db.WithContext(ctx).Save(token).Error; err != nil {
		return fmt.Errorf("failed to update phone verification token: %w", err)
	}
	return nil
}

// DeleteToken deletes a token by ID
func (r *phoneVerificationTokenRepository) DeleteToken(ctx context.Context, id uint) error {
	result := r.db.WithContext(ctx).Delete(&models.PhoneVerificationToken{}, id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete phone verification token: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("phone verification token not found")
	}
	return nil
}

// VerifyToken verifies a phone verification code
func (r *phoneVerificationTokenRepository) VerifyToken(ctx context.Context, tenantID uint, phone string, code string) (*models.PhoneVerificationToken, error) {
	// Get active token for phone
	token, err := r.GetTokenByPhone(ctx, tenantID, phone)
	if err != nil {
		return nil, err
	}

	// Check if token is valid
	if !token.IsValid() {
		if token.IsExpired() {
			return nil, fmt.Errorf("verification code has expired")
		}
		if token.IsUsed {
			return nil, fmt.Errorf("verification code has already been used")
		}
		return nil, fmt.Errorf("invalid verification token")
	}

	// Verify the code
	if !token.VerifyCode(code) {
		return nil, fmt.Errorf("invalid verification code")
	}

	return token, nil
}

// MarkTokenAsUsed marks a token as used
func (r *phoneVerificationTokenRepository) MarkTokenAsUsed(ctx context.Context, id uint) error {
	now := time.Now()
	result := r.db.WithContext(ctx).
		Model(&models.PhoneVerificationToken{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"is_used": true,
			"used_at": now,
		})

	if result.Error != nil {
		return fmt.Errorf("failed to mark token as used: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("token not found")
	}

	return nil
}

// InvalidateTokensForUser invalidates all active tokens for a user
func (r *phoneVerificationTokenRepository) InvalidateTokensForUser(ctx context.Context, tenantID uint, userID uint) error {
	result := r.db.WithContext(ctx).
		Model(&models.PhoneVerificationToken{}).
		Where("tenant_id = ? AND user_id = ? AND is_used = ?", tenantID, userID, false).
		Update("is_used", true)

	if result.Error != nil {
		return fmt.Errorf("failed to invalidate tokens: %w", result.Error)
	}

	return nil
}

// InvalidateTokensForPhone invalidates all active tokens for a phone number
func (r *phoneVerificationTokenRepository) InvalidateTokensForPhone(ctx context.Context, tenantID uint, phone string) error {
	result := r.db.WithContext(ctx).
		Model(&models.PhoneVerificationToken{}).
		Where("tenant_id = ? AND phone = ? AND is_used = ?", tenantID, phone, false).
		Update("is_used", true)

	if result.Error != nil {
		return fmt.Errorf("failed to invalidate tokens: %w", result.Error)
	}

	return nil
}

// GetActiveTokensForUser retrieves all active tokens for a user
func (r *phoneVerificationTokenRepository) GetActiveTokensForUser(ctx context.Context, tenantID uint, userID uint) ([]*models.PhoneVerificationToken, error) {
	var tokens []*models.PhoneVerificationToken
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND user_id = ? AND is_used = ? AND expires_at > ?", 
			tenantID, userID, false, time.Now()).
		Order("created_at DESC").
		Find(&tokens).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get active tokens: %w", err)
	}

	return tokens, nil
}

// GetRecentTokensForUser retrieves recent tokens for a user
func (r *phoneVerificationTokenRepository) GetRecentTokensForUser(ctx context.Context, tenantID uint, userID uint, limit int) ([]*models.PhoneVerificationToken, error) {
	var tokens []*models.PhoneVerificationToken
	
	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND user_id = ?", tenantID, userID).
		Order("created_at DESC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	err := query.Find(&tokens).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get recent tokens: %w", err)
	}

	return tokens, nil
}

// GetRecentTokensForPhone retrieves recent tokens for a phone number within specified hours
func (r *phoneVerificationTokenRepository) GetRecentTokensForPhone(ctx context.Context, tenantID uint, phone string, hours int) ([]*models.PhoneVerificationToken, error) {
	var tokens []*models.PhoneVerificationToken
	
	since := time.Now().Add(-time.Duration(hours) * time.Hour)
	
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND phone = ? AND created_at > ?", tenantID, phone, since).
		Order("created_at DESC").
		Find(&tokens).Error
		
	if err != nil {
		return nil, fmt.Errorf("failed to get recent tokens: %w", err)
	}

	return tokens, nil
}

// GetTokenStats returns statistics about phone verification tokens
func (r *phoneVerificationTokenRepository) GetTokenStats(ctx context.Context, tenantID uint, userID uint) (*models.PhoneTokenStats, error) {
	stats := &models.PhoneTokenStats{}

	// Total created
	if err := r.db.WithContext(ctx).
		Model(&models.PhoneVerificationToken{}).
		Where("tenant_id = ? AND user_id = ?", tenantID, userID).
		Count(&stats.TotalCreated).Error; err != nil {
		return nil, fmt.Errorf("failed to count total tokens: %w", err)
	}

	// Total used
	if err := r.db.WithContext(ctx).
		Model(&models.PhoneVerificationToken{}).
		Where("tenant_id = ? AND user_id = ? AND is_used = ?", tenantID, userID, true).
		Count(&stats.TotalUsed).Error; err != nil {
		return nil, fmt.Errorf("failed to count used tokens: %w", err)
	}

	// Total expired
	if err := r.db.WithContext(ctx).
		Model(&models.PhoneVerificationToken{}).
		Where("tenant_id = ? AND user_id = ? AND is_used = ? AND expires_at < ?", 
			tenantID, userID, false, time.Now()).
		Count(&stats.TotalExpired).Error; err != nil {
		return nil, fmt.Errorf("failed to count expired tokens: %w", err)
	}

	// Total active
	if err := r.db.WithContext(ctx).
		Model(&models.PhoneVerificationToken{}).
		Where("tenant_id = ? AND user_id = ? AND is_used = ? AND expires_at > ?", 
			tenantID, userID, false, time.Now()).
		Count(&stats.TotalActive).Error; err != nil {
		return nil, fmt.Errorf("failed to count active tokens: %w", err)
	}

	// Total resends
	var resendSum int64
	if err := r.db.WithContext(ctx).
		Model(&models.PhoneVerificationToken{}).
		Where("tenant_id = ? AND user_id = ?", tenantID, userID).
		Select("COALESCE(SUM(resend_count), 0)").
		Scan(&resendSum).Error; err != nil {
		return nil, fmt.Errorf("failed to sum resend counts: %w", err)
	}
	stats.TotalResends = resendSum

	return stats, nil
}

// CleanupExpiredTokens removes expired tokens from all tenants
func (r *phoneVerificationTokenRepository) CleanupExpiredTokens(ctx context.Context) (int64, error) {
	// Delete tokens that are either:
	// 1. Used and older than 7 days
	// 2. Expired and older than 1 day
	weekAgo := time.Now().AddDate(0, 0, -7)
	dayAgo := time.Now().AddDate(0, 0, -1)

	result := r.db.WithContext(ctx).
		Where("(is_used = ? AND updated_at < ?) OR (expires_at < ? AND created_at < ?)", 
			true, weekAgo, time.Now(), dayAgo).
		Delete(&models.PhoneVerificationToken{})

	if result.Error != nil {
		return 0, fmt.Errorf("failed to cleanup expired tokens: %w", result.Error)
	}

	return result.RowsAffected, nil
}

// CleanupExpiredTokensForTenant removes expired tokens for a specific tenant
func (r *phoneVerificationTokenRepository) CleanupExpiredTokensForTenant(ctx context.Context, tenantID uint) (int64, error) {
	// Delete tokens that are either:
	// 1. Used and older than 7 days
	// 2. Expired and older than 1 day
	weekAgo := time.Now().AddDate(0, 0, -7)
	dayAgo := time.Now().AddDate(0, 0, -1)

	result := r.db.WithContext(ctx).
		Where("tenant_id = ? AND ((is_used = ? AND updated_at < ?) OR (expires_at < ? AND created_at < ?))", 
			tenantID, true, weekAgo, time.Now(), dayAgo).
		Delete(&models.PhoneVerificationToken{})

	if result.Error != nil {
		return 0, fmt.Errorf("failed to cleanup expired tokens: %w", result.Error)
	}

	return result.RowsAffected, nil
}
package repositories

import "errors"

var (
	// ErrUserNotFound is returned when a user is not found
	ErrUserNotFound = errors.New("user not found")

	// ErrInvalidVerificationCode is returned when verification code is invalid
	ErrInvalidVerificationCode = errors.New("invalid verification code")

	// ErrTokenExpired is returned when verification token has expired
	ErrTokenExpired = errors.New("verification token has expired")

	// ErrTokenAlreadyUsed is returned when verification token has already been used
	ErrTokenAlreadyUsed = errors.New("verification token has already been used")

	// ErrMaxResendAttemptsExceeded is returned when maximum resend attempts exceeded
	ErrMaxResendAttemptsExceeded = errors.New("maximum resend attempts exceeded")

	// ErrResendTooSoon is returned when resend is attempted too soon
	ErrResendTooSoon = errors.New("please wait before requesting another verification code")
)
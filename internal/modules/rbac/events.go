package rbac

import (
	"context"
	"fmt"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/repositories/mysql"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/events"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

// RegisterEventHandlers registers all RBAC event handlers
func RegisterEventHandlers(db *gorm.DB, logger utils.Logger) {
	eventBus := events.GetEventBus()

	// Create repositories
	roleRepo := mysql.NewRoleRepository(db)
	permissionRepo := mysql.NewPermissionRepository(db)
	userRoleRepo := mysql.NewUserRoleRepository(db)
	rolePermissionRepo := mysql.NewRolePermissionRepository(db)

	// Create RBAC services
	roleService := services.NewRoleService(roleRepo, rolePermissionRepo, userRoleRepo, permissionRepo)
	permissionService := services.NewPermissionService(permissionRepo, rolePermissionRepo, userRoleRepo)
	rbacEngine := services.NewRBACEngine(roleService, permissionService, userRoleRepo, rolePermissionRepo)
	userRoleService := services.NewUserRoleService(userRoleRepo, roleService, rbacEngine)

	// Register handler for tenant created events
	eventBus.SubscribeTenantCreated("tenant_created", func(ctx context.Context, event events.TenantCreatedEvent) error {
		logger.WithFields(map[string]interface{}{
			"tenant_id": event.TenantID,
			"owner_id":  event.OwnerID,
			"domain":    event.Domain,
		}).Info("Handling tenant created event - initializing RBAC")

		// Initialize RBAC for the new tenant
		if err := initializeRBACForTenant(ctx, event.TenantID, event.OwnerID, roleService, permissionService, userRoleService, rolePermissionRepo, permissionRepo, logger); err != nil {
			logger.WithError(err).WithFields(map[string]interface{}{
				"tenant_id": event.TenantID,
				"owner_id":  event.OwnerID,
			}).Error("Failed to initialize RBAC for tenant")
			return err
		}

		logger.WithFields(map[string]interface{}{
			"tenant_id": event.TenantID,
			"owner_id":  event.OwnerID,
		}).Info("Successfully initialized RBAC for tenant")

		return nil
	})
}

// initializeRBACForTenant creates default roles and assigns admin role to owner
func initializeRBACForTenant(
	ctx context.Context,
	tenantID, ownerID uint,
	roleService services.RoleService,
	permissionService services.PermissionService,
	userRoleService services.UserRoleService,
	rolePermissionRepo repositories.RolePermissionRepository,
	permissionRepo repositories.PermissionRepository,
	logger utils.Logger,
) error {
	// Define default roles
	defaultRoles := []struct {
		Name        string
		DisplayName string
		Description string
		Level       int
		IsDefault   bool
	}{
		{
			Name:        "admin",
			DisplayName: "Administrator",
			Description: "Full access to all tenant resources",
			Level:       100,
			IsDefault:   false,
		},
		{
			Name:        "editor",
			DisplayName: "Editor",
			Description: "Can create and edit content",
			Level:       75,
			IsDefault:   false,
		},
		{
			Name:        "moderator",
			DisplayName: "Moderator",
			Description: "Can moderate content and users",
			Level:       50,
			IsDefault:   false,
		},
		{
			Name:        "viewer",
			DisplayName: "Viewer",
			Description: "Read-only access to content",
			Level:       25,
			IsDefault:   true, // Default role for new users
		},
	}

	// Create roles
	roleMap := make(map[string]*models.Role)
	for _, roleData := range defaultRoles {
		description := roleData.Description
		roleReq := &models.RoleCreateRequest{
			TenantID:      tenantID,
			Name:          roleData.Name,
			DisplayName:   roleData.DisplayName,
			Description:   &description,
			Level:         uint(roleData.Level),
			Scope:         "tenant",
			IsSystemRole:  true,
			IsDefaultRole: roleData.IsDefault,
		}

		createdRole, err := roleService.CreateRole(ctx, roleReq)
		if err != nil {
			return fmt.Errorf("failed to create role %s: %w", roleData.Name, err)
		}
		roleMap[roleData.Name] = createdRole
	}

	// Get all permissions using a large limit (we don't have FindAll)
	var permissions []*models.Permission
	// Get permissions from common modules
	modules := []string{"user", "website", "auth", "rbac", "tenant", "content"}
	for _, module := range modules {
		modulePerms, err := permissionRepo.GetByModule(ctx, module, 100, 0)
		if err != nil {
			logger.WithError(err).WithField("module", module).Warn("Failed to get permissions for module")
			continue
		}
		permissions = append(permissions, modulePerms...)
	}

	// Create permission map for easy lookup
	permissionMap := make(map[string]*models.Permission)
	for _, perm := range permissions {
		permissionMap[perm.Name] = perm
	}

	// Define role-permission mappings
	rolePermissions := map[string][]string{
		"admin": {
			// User management
			"users.create", "users.read", "users.update", "users.delete", "users.list",
			"user-profiles.create", "user-profiles.read", "user-profiles.update", "user-profiles.delete",
			// Website management
			"websites.create", "websites.read", "websites.update", "websites.delete", "websites.list", "websites.manage-settings",
			// Content management
			"content.create", "content.read", "content.update", "content.delete", "content.publish",
			// Auth management
			"auth.manage-sessions", "auth.manage-tokens", "auth.manage-providers", "auth.view-logs",
			// RBAC management
			"rbac.roles.create", "rbac.roles.read", "rbac.roles.update", "rbac.roles.delete",
			"rbac.permissions.read", "rbac.user-roles.assign", "rbac.user-roles.revoke",
			// Tenant management
			"tenants.manage-settings",
		},
		"editor": {
			// User management - read only
			"users.read", "users.list",
			"user-profiles.read",
			// Website management - read only
			"websites.read", "websites.list",
			// Content management - full access
			"content.create", "content.read", "content.update", "content.delete", "content.publish",
			// RBAC - read only
			"rbac.roles.read", "rbac.permissions.read",
		},
		"moderator": {
			// User management - limited
			"users.read", "users.list", "users.update",
			"user-profiles.read", "user-profiles.update",
			// Content management - moderate
			"content.read", "content.update", "content.delete",
			// Website management - read only
			"websites.read", "websites.list",
		},
		"viewer": {
			// Read-only access
			"users.read", "users.list",
			"user-profiles.read",
			"websites.read", "websites.list",
			"content.read",
			"rbac.roles.read", "rbac.permissions.read",
		},
	}

	// Assign permissions to roles
	for roleName, permNames := range rolePermissions {
		role, exists := roleMap[roleName]
		if !exists {
			continue
		}

		for _, permName := range permNames {
			perm, exists := permissionMap[permName]
			if !exists {
				logger.WithFields(map[string]interface{}{
					"permission": permName,
					"role":       roleName,
				}).Warn("Permission not found, skipping")
				continue
			}

			rolePermission := &models.RolePermission{
				RoleID:       role.ID,
				PermissionID: perm.ID,
				IsInherited:  false,
			}

			if err := rolePermissionRepo.Create(ctx, rolePermission); err != nil {
				logger.WithError(err).WithFields(map[string]interface{}{
					"role":       roleName,
					"permission": permName,
				}).Warn("Failed to assign permission to role")
			}
		}
	}

	// Assign admin role to the owner
	adminRole, exists := roleMap["admin"]
	if !exists {
		return fmt.Errorf("admin role not found")
	}

	contextID := tenantID
	userRoleReq := &models.UserRoleCreateRequest{
		TenantID:    tenantID,
		UserID:      ownerID,
		RoleID:      adminRole.ID,
		ContextType: "tenant",
		ContextID:   &contextID,
		IsPrimary:   true,
	}

	if _, err := userRoleService.AssignRole(ctx, userRoleReq); err != nil {
		return fmt.Errorf("failed to assign admin role to owner: %w", err)
	}

	logger.WithFields(map[string]interface{}{
		"tenant_id": tenantID,
		"owner_id":  ownerID,
		"roles":     len(roleMap),
	}).Info("Successfully initialized RBAC for tenant")

	return nil
}


package rbac

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/events"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// Mock repositories
type mockRoleRepository struct {
	mock.Mock
}

func (m *mockRoleRepository) Create(ctx context.Context, role *models.Role) error {
	args := m.Called(ctx, role)
	return args.Error(0)
}

func (m *mockRoleRepository) GetByID(ctx context.Context, tenantID, id uint) (*models.Role, error) {
	args := m.Called(ctx, tenantID, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Role), args.Error(1)
}

func (m *mockRoleRepository) GetByName(ctx context.Context, tenantID uint, name string) (*models.Role, error) {
	args := m.Called(ctx, tenantID, name)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Role), args.Error(1)
}

func (m *mockRoleRepository) Update(ctx context.Context, role *models.Role) error {
	args := m.Called(ctx, role)
	return args.Error(0)
}

func (m *mockRoleRepository) Delete(ctx context.Context, tenantID, id uint) error {
	args := m.Called(ctx, tenantID, id)
	return args.Error(0)
}

func (m *mockRoleRepository) GetByTenant(ctx context.Context, tenantID uint, limit, offset int) ([]*models.Role, error) {
	args := m.Called(ctx, tenantID, limit, offset)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*models.Role), args.Error(1)
}

func (m *mockRoleRepository) CountByTenant(ctx context.Context, tenantID uint) (int64, error) {
	args := m.Called(ctx, tenantID)
	return args.Get(0).(int64), args.Error(1)
}

// Mock permission repository
type mockPermissionRepository struct {
	mock.Mock
}

func (m *mockPermissionRepository) Create(ctx context.Context, permission *models.Permission) error {
	args := m.Called(ctx, permission)
	return args.Error(0)
}

func (m *mockPermissionRepository) GetByID(ctx context.Context, id uint) (*models.Permission, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Permission), args.Error(1)
}

func (m *mockPermissionRepository) GetByName(ctx context.Context, name string) (*models.Permission, error) {
	args := m.Called(ctx, name)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Permission), args.Error(1)
}

func (m *mockPermissionRepository) Update(ctx context.Context, permission *models.Permission) error {
	args := m.Called(ctx, permission)
	return args.Error(0)
}

func (m *mockPermissionRepository) Delete(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *mockPermissionRepository) GetByModule(ctx context.Context, module string, limit, offset int) ([]*models.Permission, error) {
	args := m.Called(ctx, module, limit, offset)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*models.Permission), args.Error(1)
}

func (m *mockPermissionRepository) GetByResource(ctx context.Context, resource string, limit, offset int) ([]*models.Permission, error) {
	args := m.Called(ctx, resource, limit, offset)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*models.Permission), args.Error(1)
}

func (m *mockPermissionRepository) Count(ctx context.Context) (int64, error) {
	args := m.Called(ctx)
	return args.Get(0).(int64), args.Error(1)
}

// Mock role permission repository
type mockRolePermissionRepository struct {
	mock.Mock
}

func (m *mockRolePermissionRepository) Create(ctx context.Context, rolePermission *models.RolePermission) error {
	args := m.Called(ctx, rolePermission)
	return args.Error(0)
}

func (m *mockRolePermissionRepository) GetByID(ctx context.Context, id uint) (*models.RolePermission, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.RolePermission), args.Error(1)
}

func (m *mockRolePermissionRepository) Delete(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *mockRolePermissionRepository) GetByRole(ctx context.Context, roleID uint) ([]*models.RolePermission, error) {
	args := m.Called(ctx, roleID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*models.RolePermission), args.Error(1)
}

func (m *mockRolePermissionRepository) GetByPermission(ctx context.Context, permissionID uint) ([]*models.RolePermission, error) {
	args := m.Called(ctx, permissionID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*models.RolePermission), args.Error(1)
}

func (m *mockRolePermissionRepository) DeleteByRole(ctx context.Context, roleID uint) error {
	args := m.Called(ctx, roleID)
	return args.Error(0)
}

func (m *mockRolePermissionRepository) DeleteByPermission(ctx context.Context, permissionID uint) error {
	args := m.Called(ctx, permissionID)
	return args.Error(0)
}

// Mock services
type mockRoleService struct {
	mock.Mock
}

func (m *mockRoleService) CreateRole(ctx context.Context, req *models.RoleCreateRequest) (*models.Role, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Role), args.Error(1)
}

func (m *mockRoleService) GetRole(ctx context.Context, tenantID, id uint) (*models.Role, error) {
	args := m.Called(ctx, tenantID, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Role), args.Error(1)
}

func (m *mockRoleService) GetRoleByName(ctx context.Context, tenantID uint, name string) (*models.Role, error) {
	args := m.Called(ctx, tenantID, name)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Role), args.Error(1)
}

func (m *mockRoleService) UpdateRole(ctx context.Context, tenantID, id uint, req *models.RoleUpdateRequest) (*models.Role, error) {
	args := m.Called(ctx, tenantID, id, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Role), args.Error(1)
}

func (m *mockRoleService) DeleteRole(ctx context.Context, tenantID, id uint) error {
	args := m.Called(ctx, tenantID, id)
	return args.Error(0)
}

func (m *mockRoleService) ListRoles(ctx context.Context, tenantID uint, page, pageSize int) ([]*models.Role, int64, error) {
	args := m.Called(ctx, tenantID, page, pageSize)
	if args.Get(0) == nil {
		return nil, args.Get(1).(int64), args.Error(2)
	}
	return args.Get(0).([]*models.Role), args.Get(1).(int64), args.Error(2)
}

func (m *mockRoleService) AssignPermissionsToRole(ctx context.Context, tenantID, roleID uint, req *models.RolePermissionAssignRequest) error {
	args := m.Called(ctx, tenantID, roleID, req)
	return args.Error(0)
}

func (m *mockRoleService) RemovePermissionsFromRole(ctx context.Context, tenantID, roleID uint, req *models.RolePermissionRemoveRequest) error {
	args := m.Called(ctx, tenantID, roleID, req)
	return args.Error(0)
}

func (m *mockRoleService) GetRolePermissions(ctx context.Context, tenantID, roleID uint, page, pageSize int) ([]*models.Permission, int64, error) {
	args := m.Called(ctx, tenantID, roleID, page, pageSize)
	if args.Get(0) == nil {
		return nil, args.Get(1).(int64), args.Error(2)
	}
	return args.Get(0).([]*models.Permission), args.Get(1).(int64), args.Error(2)
}

// Mock user role service
type mockUserRoleService struct {
	mock.Mock
}

func (m *mockUserRoleService) AssignRole(ctx context.Context, req *models.UserRoleCreateRequest) (*models.UserRole, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.UserRole), args.Error(1)
}

func (m *mockUserRoleService) RevokeRole(ctx context.Context, tenantID, userRoleID uint, req *models.UserRoleRevokeRequest) error {
	args := m.Called(ctx, tenantID, userRoleID, req)
	return args.Error(0)
}

func (m *mockUserRoleService) GetUserRole(ctx context.Context, tenantID, userRoleID uint) (*models.UserRole, error) {
	args := m.Called(ctx, tenantID, userRoleID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.UserRole), args.Error(1)
}

func (m *mockUserRoleService) GetUserRoles(ctx context.Context, tenantID, userID uint, page, pageSize int) ([]*models.UserRole, int64, error) {
	args := m.Called(ctx, tenantID, userID, page, pageSize)
	if args.Get(0) == nil {
		return nil, args.Get(1).(int64), args.Error(2)
	}
	return args.Get(0).([]*models.UserRole), args.Get(1).(int64), args.Error(2)
}

func (m *mockUserRoleService) UpdateUserRole(ctx context.Context, tenantID, userRoleID uint, req *models.UserRoleUpdateRequest) (*models.UserRole, error) {
	args := m.Called(ctx, tenantID, userRoleID, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.UserRole), args.Error(1)
}

func (m *mockUserRoleService) GetEffectivePermissions(ctx context.Context, tenantID, userID uint) (map[string]bool, error) {
	args := m.Called(ctx, tenantID, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(map[string]bool), args.Error(1)
}

func TestInitializeRBACForTenant(t *testing.T) {
	ctx := context.Background()
	logger := utils.NewTestLogger()

	t.Run("Successfully Initialize RBAC", func(t *testing.T) {
		// Create mocks
		roleService := new(mockRoleService)
		userRoleService := new(mockUserRoleService)
		rolePermissionRepo := new(mockRolePermissionRepository)
		permissionRepo := new(mockPermissionRepository)

		tenantID := uint(1)
		ownerID := uint(100)

		// Mock role creation
		adminRole := &models.Role{ID: 1, Name: "admin", TenantID: tenantID}
		editorRole := &models.Role{ID: 2, Name: "editor", TenantID: tenantID}
		moderatorRole := &models.Role{ID: 3, Name: "moderator", TenantID: tenantID}
		viewerRole := &models.Role{ID: 4, Name: "viewer", TenantID: tenantID}

		roleService.On("CreateRole", ctx, mock.MatchedBy(func(req *models.RoleCreateRequest) bool {
			return req.Name == "admin" && req.TenantID == tenantID
		})).Return(adminRole, nil)

		roleService.On("CreateRole", ctx, mock.MatchedBy(func(req *models.RoleCreateRequest) bool {
			return req.Name == "editor" && req.TenantID == tenantID
		})).Return(editorRole, nil)

		roleService.On("CreateRole", ctx, mock.MatchedBy(func(req *models.RoleCreateRequest) bool {
			return req.Name == "moderator" && req.TenantID == tenantID
		})).Return(moderatorRole, nil)

		roleService.On("CreateRole", ctx, mock.MatchedBy(func(req *models.RoleCreateRequest) bool {
			return req.Name == "viewer" && req.TenantID == tenantID
		})).Return(viewerRole, nil)

		// Mock permissions
		permissions := []*models.Permission{
			{ID: 1, Name: "users.create"},
			{ID: 2, Name: "users.read"},
			{ID: 3, Name: "users.update"},
			{ID: 4, Name: "content.create"},
			{ID: 5, Name: "content.read"},
		}

		// Mock permission retrieval for each module
		permissionRepo.On("GetByModule", ctx, "user", 100, 0).Return(permissions[:3], nil)
		permissionRepo.On("GetByModule", ctx, "website", 100, 0).Return([]*models.Permission{}, nil)
		permissionRepo.On("GetByModule", ctx, "auth", 100, 0).Return([]*models.Permission{}, nil)
		permissionRepo.On("GetByModule", ctx, "rbac", 100, 0).Return([]*models.Permission{}, nil)
		permissionRepo.On("GetByModule", ctx, "tenant", 100, 0).Return([]*models.Permission{}, nil)
		permissionRepo.On("GetByModule", ctx, "content", 100, 0).Return(permissions[3:], nil)

		// Mock role permission creation
		rolePermissionRepo.On("Create", ctx, mock.Anything).Return(nil)

		// Mock user role assignment
		userRoleService.On("AssignRole", ctx, mock.MatchedBy(func(req *models.UserRoleCreateRequest) bool {
			return req.UserID == ownerID && req.RoleID == adminRole.ID && req.TenantID == tenantID
		})).Return(&models.UserRole{ID: 1}, nil)

		// Call the function
		err := initializeRBACForTenant(
			ctx,
			tenantID,
			ownerID,
			roleService,
			nil, // permission service not used in current implementation
			userRoleService,
			rolePermissionRepo,
			permissionRepo,
			logger,
		)

		// Assert
		assert.NoError(t, err)
		roleService.AssertExpectations(t)
		userRoleService.AssertExpectations(t)
		rolePermissionRepo.AssertExpectations(t)
		permissionRepo.AssertExpectations(t)
	})

	t.Run("Role Creation Failure", func(t *testing.T) {
		// Create mocks
		roleService := new(mockRoleService)
		userRoleService := new(mockUserRoleService)
		rolePermissionRepo := new(mockRolePermissionRepository)
		permissionRepo := new(mockPermissionRepository)

		tenantID := uint(1)
		ownerID := uint(100)

		// Mock role creation failure
		roleService.On("CreateRole", ctx, mock.MatchedBy(func(req *models.RoleCreateRequest) bool {
			return req.Name == "admin"
		})).Return(nil, assert.AnError)

		// Call the function
		err := initializeRBACForTenant(
			ctx,
			tenantID,
			ownerID,
			roleService,
			nil,
			userRoleService,
			rolePermissionRepo,
			permissionRepo,
			logger,
		)

		// Assert
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to create role admin")
		roleService.AssertExpectations(t)
	})

	t.Run("User Role Assignment Failure", func(t *testing.T) {
		// Create mocks
		roleService := new(mockRoleService)
		userRoleService := new(mockUserRoleService)
		rolePermissionRepo := new(mockRolePermissionRepository)
		permissionRepo := new(mockPermissionRepository)

		tenantID := uint(1)
		ownerID := uint(100)

		// Mock successful role creation
		adminRole := &models.Role{ID: 1, Name: "admin", TenantID: tenantID}
		roleService.On("CreateRole", ctx, mock.Anything).Return(adminRole, nil)

		// Mock permissions
		permissionRepo.On("GetByModule", ctx, mock.Anything, 100, 0).Return([]*models.Permission{}, nil)

		// Mock user role assignment failure
		userRoleService.On("AssignRole", ctx, mock.Anything).Return(nil, assert.AnError)

		// Call the function
		err := initializeRBACForTenant(
			ctx,
			tenantID,
			ownerID,
			roleService,
			nil,
			userRoleService,
			rolePermissionRepo,
			permissionRepo,
			logger,
		)

		// Assert
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to assign admin role to owner")
		userRoleService.AssertExpectations(t)
	})
}
package dto

import (
	"encoding/json"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// NotificationCreateRequest represents request to create a notification
type NotificationCreateRequest struct {
	Type         string                         `json:"type" validate:"required,max=50" example:"welcome_email"`
	Channel      models.NotificationChannel     `json:"channel" validate:"required,oneof=email socket push sms" example:"email"`
	Priority     models.NotificationPriority    `json:"priority" validate:"omitempty,oneof=low normal high urgent" example:"normal"`
	Subject      string                         `json:"subject" validate:"required,max=255" example:"Welcome to our platform!"`
	TemplateID   *uint                          `json:"template_id" example:"1"`
	TemplateData map[string]interface{}         `json:"template_data" example:"{\"name\":\"John <PERSON>e\",\"company\":\"ACME Corp\"}"`
	Recipients   []NotificationRecipientRequest `json:"recipients" validate:"required,min=1"`
	ScheduledAt  *time.Time                     `json:"scheduled_at" example:"2024-01-15T10:00:00Z"`
	Metadata     map[string]interface{}         `json:"metadata" example:"{\"campaign_id\":\"welcome-2024\"}"`
}

// NotificationUpdateRequest represents request to update a notification
type NotificationUpdateRequest struct {
	Status      *models.NotificationStatus `json:"status" validate:"omitempty,oneof=pending queued sent delivered failed cancelled" example:"cancelled"`
	ScheduledAt *time.Time                 `json:"scheduled_at" example:"2024-01-15T14:00:00Z"`
	Metadata    map[string]interface{}     `json:"metadata" example:"{\"updated_reason\":\"reschedule\"}"`
}

// NotificationRecipientRequest represents recipient information in create request
type NotificationRecipientRequest struct {
	UserID           *uint                  `json:"user_id" example:"123"`
	RecipientType    models.RecipientType   `json:"recipient_type" validate:"required,oneof=user email phone device" example:"email"`
	RecipientAddress string                 `json:"recipient_address" validate:"required,max=255" example:"<EMAIL>"`
	DeviceToken      *string                `json:"device_token" example:"fcm_token_123456"`
	Metadata         map[string]interface{} `json:"metadata" example:"{\"preferences\":{\"language\":\"en\"}}"`
}

// NotificationResponse represents notification information in responses
type NotificationResponse struct {
	ID           uint                        `json:"id" example:"1"`
	TenantID     uint                        `json:"tenant_id" example:"1"`
	Type         string                      `json:"type" example:"welcome_email"`
	Channel      models.NotificationChannel  `json:"channel" example:"email"`
	Priority     models.NotificationPriority `json:"priority" example:"normal"`
	Subject      string                      `json:"subject" example:"Welcome to our platform!"`
	TemplateID   *uint                       `json:"template_id" example:"1"` // KEEP_OMITEMPTY: Optional template reference
	TemplateData json.RawMessage             `json:"template_data,omitempty"` // KEEP_OMITEMPTY: Optional data payload
	Status       models.NotificationStatus   `json:"status" example:"sent"`
	ScheduledAt  *time.Time                  `json:"scheduled_at,omitempty"`  // KEEP_OMITEMPTY: Optional scheduling
	SentAt       *time.Time                  `json:"sent_at,omitempty"`       // KEEP_OMITEMPTY: Optional delivery timestamp
	DeliveredAt  *time.Time                  `json:"delivered_at,omitempty"`  // KEEP_OMITEMPTY: Optional delivery timestamp
	FailedAt     *time.Time                  `json:"failed_at,omitempty"`     // KEEP_OMITEMPTY: Optional failure timestamp
	ErrorMessage *string                     `json:"error_message,omitempty"` // KEEP_OMITEMPTY: Optional error details
	RetryCount   uint                        `json:"retry_count" example:"0"`
	MaxRetries   uint                        `json:"max_retries" example:"3"`
	Metadata     json.RawMessage             `json:"metadata,omitempty"` // KEEP_OMITEMPTY: Optional metadata
	CreatedAt    time.Time                   `json:"created_at"`
	UpdatedAt    time.Time                   `json:"updated_at"`

	// Optional relationships
	Template   *NotificationTemplateResponse   `json:"template,omitempty"`   // KEEP_OMITEMPTY: Optional nested object
	Recipients []NotificationRecipientResponse `json:"recipients,omitempty"` // KEEP_OMITEMPTY: Optional nested array
}

// NotificationRecipientResponse represents recipient information in responses
type NotificationRecipientResponse struct {
	ID               uint                   `json:"id" example:"1"`
	TenantID         uint                   `json:"tenant_id" example:"1"`
	NotificationID   uint                   `json:"notification_id" example:"1"`
	UserID           *uint                  `json:"user_id" example:"123"` // KEEP_OMITEMPTY: Optional user reference
	RecipientType    models.RecipientType   `json:"recipient_type" example:"email"`
	RecipientAddress string                 `json:"recipient_address" example:"<EMAIL>"`
	DeviceToken      *string                `json:"device_token,omitempty"` // KEEP_OMITEMPTY: Optional device token
	Status           models.RecipientStatus `json:"status" example:"delivered"`
	DeliveredAt      *time.Time             `json:"delivered_at,omitempty"`    // KEEP_OMITEMPTY: Optional delivery timestamp
	ReadAt           *time.Time             `json:"read_at,omitempty"`         // KEEP_OMITEMPTY: Optional engagement timestamp
	FailedAt         *time.Time             `json:"failed_at,omitempty"`       // KEEP_OMITEMPTY: Optional failure timestamp
	BouncedAt        *time.Time             `json:"bounced_at,omitempty"`      // KEEP_OMITEMPTY: Optional bounce timestamp
	ErrorMessage     *string                `json:"error_message,omitempty"`   // KEEP_OMITEMPTY: Optional error details
	DeliveryInfo     json.RawMessage        `json:"delivery_info,omitempty"`   // KEEP_OMITEMPTY: Optional delivery metadata
	EngagementData   json.RawMessage        `json:"engagement_data,omitempty"` // KEEP_OMITEMPTY: Optional engagement data
	CreatedAt        time.Time              `json:"created_at"`
	UpdatedAt        time.Time              `json:"updated_at"`
}

// NotificationListResponse represents response for listing notifications
type NotificationListResponse struct {
	Notifications []NotificationResponse     `json:"notifications"`
	Pagination    *pagination.CursorResponse `json:"pagination"`
}

// NotificationFilter represents filter parameters for listing notifications
type NotificationFilter struct {
	pagination.CursorRequest
	Type      string                      `json:"type,omitempty" example:"welcome_email"`
	Channel   models.NotificationChannel  `json:"channel,omitempty" validate:"omitempty,oneof=email socket push sms" example:"email"`
	Status    models.NotificationStatus   `json:"status,omitempty" validate:"omitempty,oneof=pending queued sent delivered failed cancelled" example:"sent"`
	Priority  models.NotificationPriority `json:"priority,omitempty" validate:"omitempty,oneof=low normal high urgent" example:"normal"`
	DateFrom  *time.Time                  `json:"date_from,omitempty" example:"2024-01-01T00:00:00Z"`
	DateTo    *time.Time                  `json:"date_to,omitempty" example:"2024-01-31T23:59:59Z"`
	SortBy    string                      `json:"sort_by,omitempty" validate:"omitempty,oneof=id created_at updated_at sent_at priority" example:"created_at"`
	SortOrder string                      `json:"sort_order,omitempty" validate:"omitempty,oneof=asc desc" example:"desc"`
}

// NotificationBulkActionRequest represents request for bulk operations on notifications
type NotificationBulkActionRequest struct {
	NotificationIDs []uint `json:"notification_ids" validate:"required,min=1" example:"1,2,3"`
	Action          string `json:"action" validate:"required,oneof=cancel retry delete" example:"cancel"`
	Reason          string `json:"reason,omitempty" example:"Bulk cancellation for outdated campaign"`
}

// NotificationStatsRequest represents request for notification statistics
type NotificationStatsRequest struct {
	DateFrom *time.Time `json:"date_from,omitempty" example:"2024-01-01T00:00:00Z"`
	DateTo   *time.Time `json:"date_to,omitempty" example:"2024-01-31T23:59:59Z"`
	GroupBy  string     `json:"group_by,omitempty" validate:"omitempty,oneof=day week month channel type status" example:"day"`
}

// NotificationStatsResponse represents notification statistics
type NotificationStatsResponse struct {
	TotalNotifications     int64                               `json:"total_notifications" example:"1000"`
	SentNotifications      int64                               `json:"sent_notifications" example:"950"`
	DeliveredNotifications int64                               `json:"delivered_notifications" example:"920"`
	FailedNotifications    int64                               `json:"failed_notifications" example:"30"`
	ClickRate              float64                             `json:"click_rate" example:"15.5"`
	OpenRate               float64                             `json:"open_rate" example:"25.8"`
	BounceRate             float64                             `json:"bounce_rate" example:"2.1"`
	ByChannel              map[string]NotificationChannelStats `json:"by_channel"`
	ByType                 map[string]NotificationTypeStats    `json:"by_type"`
	ByStatus               map[string]int64                    `json:"by_status"`
	Timeline               []NotificationTimelineStats         `json:"timeline,omitempty"` // KEEP_OMITEMPTY: Optional timeline data
}

// NotificationChannelStats represents statistics by channel
type NotificationChannelStats struct {
	Channel   models.NotificationChannel `json:"channel" example:"email"`
	Total     int64                      `json:"total" example:"800"`
	Sent      int64                      `json:"sent" example:"780"`
	Delivered int64                      `json:"delivered" example:"760"`
	Failed    int64                      `json:"failed" example:"20"`
	OpenRate  float64                    `json:"open_rate" example:"28.5"`
	ClickRate float64                    `json:"click_rate" example:"12.3"`
}

// NotificationTypeStats represents statistics by type
type NotificationTypeStats struct {
	Type      string  `json:"type" example:"welcome_email"`
	Total     int64   `json:"total" example:"200"`
	Sent      int64   `json:"sent" example:"195"`
	Delivered int64   `json:"delivered" example:"190"`
	Failed    int64   `json:"failed" example:"5"`
	OpenRate  float64 `json:"open_rate" example:"35.2"`
	ClickRate float64 `json:"click_rate" example:"18.7"`
}

// NotificationTimelineStats represents statistics over time
type NotificationTimelineStats struct {
	Date      time.Time `json:"date" example:"2024-01-15T00:00:00Z"`
	Total     int64     `json:"total" example:"50"`
	Sent      int64     `json:"sent" example:"48"`
	Delivered int64     `json:"delivered" example:"46"`
	Failed    int64     `json:"failed" example:"2"`
}

// NotificationPreviewRequest represents request to preview notification content
type NotificationPreviewRequest struct {
	TemplateID       uint                   `json:"template_id" validate:"required" example:"1"`
	TemplateData     map[string]interface{} `json:"template_data" example:"{\"name\":\"John Doe\",\"company\":\"ACME Corp\"}"`
	RecipientAddress string                 `json:"recipient_address" validate:"required,email" example:"<EMAIL>"`
}

// NotificationPreviewResponse represents response for notification preview
type NotificationPreviewResponse struct {
	Subject    string `json:"subject" example:"Welcome to our platform, John Doe!"`
	BodyHTML   string `json:"body_html" example:"<h1>Welcome John!</h1><p>Thanks for joining ACME Corp...</p>"`
	BodyText   string `json:"body_text" example:"Welcome John! Thanks for joining ACME Corp..."`
	PreviewURL string `json:"preview_url" example:"https://api.example.com/notifications/preview/abc123"`
}

// NotificationTestRequest represents request to send test notification
type NotificationTestRequest struct {
	Type         string                         `json:"type" validate:"required" example:"test_email"`
	Channel      models.NotificationChannel     `json:"channel" validate:"required,oneof=email socket push sms" example:"email"`
	Subject      string                         `json:"subject" validate:"required" example:"Test notification"`
	TemplateID   *uint                          `json:"template_id" example:"1"`
	TemplateData map[string]interface{}         `json:"template_data" example:"{\"name\":\"Test User\"}"`
	Recipients   []NotificationRecipientRequest `json:"recipients" validate:"required,min=1"`
}

// NotificationTestResponse represents response for test notification
type NotificationTestResponse struct {
	Success        bool   `json:"success" example:"true"`
	Message        string `json:"message" example:"Test notification sent successfully"`
	NotificationID uint   `json:"notification_id" example:"123"`
	TestID         string `json:"test_id" example:"test_abc123"`
}

// ToServiceModel converts NotificationCreateRequest to models.CreateNotificationRequest
func (r *NotificationCreateRequest) ToServiceModel() models.CreateNotificationRequest {
	recipients := make([]models.CreateRecipientRequest, len(r.Recipients))
	for i, recipient := range r.Recipients {
		recipients[i] = models.CreateRecipientRequest{
			UserID:           recipient.UserID,
			RecipientType:    recipient.RecipientType,
			RecipientAddress: recipient.RecipientAddress,
			DeviceToken:      recipient.DeviceToken,
			DeliveryInfo:     recipient.Metadata,
		}
	}

	return models.CreateNotificationRequest{
		Type:         r.Type,
		Channel:      r.Channel,
		Priority:     r.Priority,
		Subject:      r.Subject,
		TemplateID:   r.TemplateID,
		TemplateData: r.TemplateData,
		Recipients:   recipients,
		ScheduledAt:  r.ScheduledAt,
		Metadata:     r.Metadata,
	}
}

// ToServiceModel converts NotificationUpdateRequest to models.UpdateNotificationRequest
func (r *NotificationUpdateRequest) ToServiceModel() models.UpdateNotificationRequest {
	return models.UpdateNotificationRequest{
		Status:      r.Status,
		ScheduledAt: r.ScheduledAt,
		Metadata:    r.Metadata,
	}
}

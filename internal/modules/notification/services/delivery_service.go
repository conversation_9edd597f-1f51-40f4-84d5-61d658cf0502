package services

import (
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

type DeliveryService interface {
	ProcessNotification(notification *models.Notification) error
	DeliverEmail(notification *models.Notification, recipients []models.NotificationRecipient) error
	DeliverSocket(notification *models.Notification, recipients []models.NotificationRecipient) error
	DeliverPush(notification *models.Notification, recipients []models.NotificationRecipient) error
	DeliverSMS(notification *models.Notification, recipients []models.NotificationRecipient) error
}

type deliveryService struct {
	notificationRepo repositories.NotificationRepository
	recipientRepo    repositories.RecipientRepository
	templateRepo     repositories.TemplateRepository
	logRepo          repositories.LogRepository
	templateService  TemplateService
	emailProvider    EmailProvider
	socketProvider   SocketProvider
	smsProvider      SMSProvider
	pushProvider     PushProvider
	logger           utils.Logger
}

func NewDeliveryService(
	notificationRepo repositories.NotificationRepository,
	recipientRepo repositories.RecipientRepository,
	templateRepo repositories.TemplateRepository,
	logRepo repositories.LogRepository,
	templateService TemplateService,
	emailProvider EmailProvider,
	socketProvider SocketProvider,
	smsProvider SMSProvider,
	pushProvider PushProvider,
	logger utils.Logger,
) DeliveryService {
	return &deliveryService{
		notificationRepo: notificationRepo,
		recipientRepo:    recipientRepo,
		templateRepo:     templateRepo,
		logRepo:          logRepo,
		templateService:  templateService,
		emailProvider:    emailProvider,
		socketProvider:   socketProvider,
		smsProvider:      smsProvider,
		pushProvider:     pushProvider,
		logger:           logger,
	}
}

func (s *deliveryService) ProcessNotification(notification *models.Notification) error {
	// Get recipients
	recipients, err := s.recipientRepo.GetPendingByNotification(notification.TenantID, notification.ID)
	if err != nil {
		return err
	}

	if len(recipients) == 0 {
		// No recipients, mark as sent
		notification.MarkAsSent()
		return s.notificationRepo.Update(notification)
	}

	// Deliver based on channel
	switch notification.Channel {
	case models.ChannelEmail:
		return s.DeliverEmail(notification, recipients)
	case models.ChannelSocket:
		return s.DeliverSocket(notification, recipients)
	case models.ChannelPush:
		return s.DeliverPush(notification, recipients)
	case models.ChannelSMS:
		return s.DeliverSMS(notification, recipients)
	default:
		return fmt.Errorf("unsupported channel: %s", notification.Channel)
	}
}

func (s *deliveryService) DeliverEmail(notification *models.Notification, recipients []models.NotificationRecipient) error {
	// Render content if using template
	var content string

	if notification.TemplateID != nil {
		templateData, err := notification.GetTemplateDataMap()
		if err != nil {
			return err
		}

		// Get active template version
		version, err := s.templateRepo.GetActiveVersion(notification.TenantID, *notification.TemplateID)
		if err != nil {
			return err
		}

		content, err = s.templateService.RenderTemplate(version, templateData)
		if err != nil {
			return err
		}
	} else {
		// Use subject as basic content
		content = notification.Subject
	}

	// Process each recipient
	successCount := 0
	for _, recipient := range recipients {
		if err := s.deliverEmailToRecipient(notification, &recipient, content); err != nil {
			// Log failure
			log := models.CreateFailedLog(notification.TenantID, notification.ID, &recipient.ID, "DELIVERY_FAILED", err.Error())
			s.logRepo.Create(log)

			// Mark recipient as failed
			recipient.MarkAsFailed(err.Error())
			s.recipientRepo.Update(&recipient)
			continue
		}

		successCount++
	}

	// Update notification status based on results
	if successCount == len(recipients) {
		notification.MarkAsSent()
	} else if successCount > 0 {
		// Partial success - keep as sent but log the issues
		notification.MarkAsSent()
	} else {
		// Complete failure
		notification.MarkAsFailed("All recipients failed")
	}

	return s.notificationRepo.Update(notification)
}

func (s *deliveryService) deliverEmailToRecipient(notification *models.Notification, recipient *models.NotificationRecipient, content string) error {
	// Prepare metadata for the email provider
	metadata := map[string]interface{}{
		"notification_id": notification.ID,
		"recipient_id":    recipient.ID,
		"tenant_id":       notification.TenantID,
		"type":            notification.Type,
	}

	// Send email through the configured provider
	if err := s.emailProvider.SendEmail(recipient, notification.Subject, content, metadata); err != nil {
		return fmt.Errorf("email delivery failed: %w", err)
	}

	// Mark recipient as sent
	recipient.MarkAsSent()
	if err := s.recipientRepo.Update(recipient); err != nil {
		return err
	}

	// Create sent log
	providerResponse := map[string]interface{}{
		"message_id": fmt.Sprintf("email_%d_%d", notification.ID, recipient.ID),
		"provider":   s.emailProvider.GetProviderName(),
		"status":     "sent",
		"sent_at":    time.Now(),
		"recipient":  recipient.RecipientAddress,
		"subject":    notification.Subject,
	}

	log := models.CreateSentLog(notification.TenantID, notification.ID, &recipient.ID, providerResponse)
	return s.logRepo.Create(log)
}

func (s *deliveryService) DeliverSocket(notification *models.Notification, recipients []models.NotificationRecipient) error {
	// Render content if using template
	var content string

	if notification.TemplateID != nil {
		templateData, err := notification.GetTemplateDataMap()
		if err != nil {
			return err
		}

		// Get active template version
		version, err := s.templateRepo.GetActiveVersion(notification.TenantID, *notification.TemplateID)
		if err != nil {
			return err
		}

		content, err = s.templateService.RenderTemplate(version, templateData)
		if err != nil {
			return err
		}
	} else {
		// Use subject as basic content
		content = notification.Subject
	}

	// Process each recipient
	successCount := 0
	for _, recipient := range recipients {
		if err := s.deliverSocketToRecipient(notification, &recipient, content); err != nil {
			// Log failure
			log := models.CreateFailedLog(notification.TenantID, notification.ID, &recipient.ID, "SOCKET_FAILED", err.Error())
			s.logRepo.Create(log)

			recipient.MarkAsFailed(err.Error())
			s.recipientRepo.Update(&recipient)
			continue
		}

		successCount++
	}

	// Update notification status
	if successCount == len(recipients) {
		notification.MarkAsSent()
	} else if successCount > 0 {
		notification.MarkAsSent()
	} else {
		notification.MarkAsFailed("All socket deliveries failed")
	}

	return s.notificationRepo.Update(notification)
}

func (s *deliveryService) deliverSocketToRecipient(notification *models.Notification, recipient *models.NotificationRecipient, content string) error {
	// Prepare metadata for the socket provider
	metadata := map[string]interface{}{
		"notification_id": notification.ID,
		"recipient_id":    recipient.ID,
		"tenant_id":       notification.TenantID,
		"type":            notification.Type,
		"room":            fmt.Sprintf("tenant_%d", notification.TenantID),
	}

	// Send socket notification through the configured provider
	if err := s.socketProvider.SendNotification(recipient, notification, content, metadata); err != nil {
		return fmt.Errorf("socket delivery failed: %w", err)
	}

	// Mark as delivered immediately for socket (real-time)
	recipient.MarkAsDelivered()
	if err := s.recipientRepo.Update(recipient); err != nil {
		return err
	}

	// Create delivered log
	deliveryInfo := map[string]interface{}{
		"socket_id":    fmt.Sprintf("socket_%d_%d", notification.ID, recipient.ID),
		"provider":     s.socketProvider.GetProviderName(),
		"room":         fmt.Sprintf("tenant_%d", notification.TenantID),
		"delivered_at": time.Now(),
		"recipient":    recipient.RecipientAddress,
	}

	log := models.CreateDeliveredLog(notification.TenantID, notification.ID, &recipient.ID, deliveryInfo)
	return s.logRepo.Create(log)
}

func (s *deliveryService) DeliverPush(notification *models.Notification, recipients []models.NotificationRecipient) error {
	if s.pushProvider == nil {
		return fmt.Errorf("push provider not configured")
	}

	// Render content if using template
	var content string

	if notification.TemplateID != nil {
		templateData, err := notification.GetTemplateDataMap()
		if err != nil {
			return err
		}

		// Get active template version
		version, err := s.templateRepo.GetActiveVersion(notification.TenantID, *notification.TemplateID)
		if err != nil {
			return err
		}

		content, err = s.templateService.RenderTemplate(version, templateData)
		if err != nil {
			return err
		}
	} else {
		// Use subject as basic content
		content = notification.Subject
	}

	// Prepare metadata for push provider
	metadata := map[string]interface{}{
		"notification_id": notification.ID,
		"tenant_id":       notification.TenantID,
		"type":            notification.Type,
		"priority":        string(notification.Priority),
		"channel":         string(notification.Channel),
	}

	// Add template data to metadata if available
	if notification.TemplateID != nil {
		templateData, _ := notification.GetTemplateDataMap()
		if templateData != nil {
			metadata["template_data"] = templateData
		}
	}

	// Use multicast for efficiency when possible
	if len(recipients) > 1 {
		if err := s.pushProvider.SendMulticast(recipients, notification, content, metadata); err != nil {
			s.logger.WithError(err).Error("Push multicast delivery failed")
			// Fall back to individual delivery
			return s.deliverPushIndividually(notification, recipients, content, metadata)
		}
	} else if len(recipients) == 1 {
		// Single recipient delivery
		return s.deliverPushIndividually(notification, recipients, content, metadata)
	}

	// Process each recipient for status tracking
	successCount := 0
	for _, recipient := range recipients {
		if err := s.updateRecipientAfterPush(notification, &recipient, nil); err != nil {
			s.logger.WithError(err).WithField("recipient_id", recipient.ID).Error("Failed to update recipient after push")
			continue
		}
		successCount++
	}

	// Update notification status
	if successCount == len(recipients) {
		notification.MarkAsSent()
	} else if successCount > 0 {
		notification.MarkAsSent()
	} else {
		notification.MarkAsFailed("All push deliveries failed")
	}

	return s.notificationRepo.Update(notification)
}

// deliverPushIndividually handles individual push delivery for each recipient
func (s *deliveryService) deliverPushIndividually(notification *models.Notification, recipients []models.NotificationRecipient, content string, metadata map[string]interface{}) error {
	successCount := 0
	
	for _, recipient := range recipients {
		if err := s.deliverPushToRecipient(notification, &recipient, content, metadata); err != nil {
			log := models.CreateFailedLog(notification.TenantID, notification.ID, &recipient.ID, "PUSH_FAILED", err.Error())
			s.logRepo.Create(log)

			recipient.MarkAsFailed(err.Error())
			s.recipientRepo.Update(&recipient)
			continue
		}
		successCount++
	}

	// Update notification status
	if successCount == len(recipients) {
		notification.MarkAsSent()
	} else if successCount > 0 {
		notification.MarkAsSent()
	} else {
		notification.MarkAsFailed("All push deliveries failed")
	}

	return s.notificationRepo.Update(notification)
}

// deliverPushToRecipient handles push delivery to a single recipient
func (s *deliveryService) deliverPushToRecipient(notification *models.Notification, recipient *models.NotificationRecipient, content string, metadata map[string]interface{}) error {
	// Validate device token
	if recipient.DeviceToken == nil || *recipient.DeviceToken == "" {
		return fmt.Errorf("device token is required for push notification")
	}

	// Send push notification
	if err := s.pushProvider.SendPush(recipient, notification, content, metadata); err != nil {
		return fmt.Errorf("push delivery failed: %w", err)
	}

	return s.updateRecipientAfterPush(notification, recipient, nil)
}

// updateRecipientAfterPush updates recipient status and creates delivery log after successful push
func (s *deliveryService) updateRecipientAfterPush(notification *models.Notification, recipient *models.NotificationRecipient, pushResponse map[string]interface{}) error {
	// Mark recipient as sent
	recipient.MarkAsSent()
	if err := s.recipientRepo.Update(recipient); err != nil {
		return err
	}

	// Create sent log
	providerResponse := map[string]interface{}{
		"message_id":   fmt.Sprintf("push_%d_%d", notification.ID, recipient.ID),
		"provider":     s.pushProvider.GetProviderName(),
		"status":       "sent",
		"sent_at":      time.Now(),
		"device_token": recipient.DeviceToken,
		"title":        notification.Subject,
	}

	// Merge push provider response if available
	if pushResponse != nil {
		for key, value := range pushResponse {
			providerResponse[key] = value
		}
	}

	log := models.CreateSentLog(notification.TenantID, notification.ID, &recipient.ID, providerResponse)
	return s.logRepo.Create(log)
}

func (s *deliveryService) DeliverSMS(notification *models.Notification, recipients []models.NotificationRecipient) error {
	successCount := 0
	for _, recipient := range recipients {
		if err := s.deliverSMSToRecipient(notification, &recipient); err != nil {
			log := models.CreateFailedLog(notification.TenantID, notification.ID, &recipient.ID, "SMS_FAILED", err.Error())
			s.logRepo.Create(log)

			recipient.MarkAsFailed(err.Error())
			s.recipientRepo.Update(&recipient)
			continue
		}

		successCount++
	}

	// Update notification status
	if successCount == len(recipients) {
		notification.MarkAsSent()
	} else if successCount > 0 {
		notification.MarkAsSent()
	} else {
		notification.MarkAsFailed("All SMS deliveries failed")
	}

	return s.notificationRepo.Update(notification)
}

func (s *deliveryService) deliverSMSToRecipient(notification *models.Notification, recipient *models.NotificationRecipient) error {
	// Render content if using template
	var smsMessage string
	var metadata map[string]interface{}

	if notification.TemplateID != nil {
		templateData, err := notification.GetTemplateDataMap()
		if err != nil {
			return fmt.Errorf("failed to get template data: %w", err)
		}

		// Get active template version
		version, err := s.templateRepo.GetActiveVersion(notification.TenantID, *notification.TemplateID)
		if err != nil {
			return fmt.Errorf("failed to get active template version: %w", err)
		}

		content, err := s.templateService.RenderTemplate(version, templateData)
		if err != nil {
			return fmt.Errorf("failed to render template: %w", err)
		}

		smsMessage = content
		metadata = templateData
	} else {
		// Use subject as SMS content
		smsMessage = notification.Subject
		metadata = make(map[string]interface{})
	}

	// Ensure SMS message is within character limit
	if len(smsMessage) > 160 {
		// Truncate to 157 chars and add "..."
		smsMessage = smsMessage[:157] + "..."
	}

	// Send SMS through the configured provider
	if err := s.smsProvider.SendSMS(recipient, notification, smsMessage); err != nil {
		return fmt.Errorf("SMS delivery failed: %w", err)
	}

	// Update recipient status
	recipient.MarkAsSent()
	if err := s.recipientRepo.Update(recipient); err != nil {
		s.logger.WithError(err).Error("Failed to update recipient status after SMS delivery")
		return err
	}

	// Create delivery log
	deliveryInfo := map[string]interface{}{
		"message_id":    fmt.Sprintf("sms_%d_%d", notification.ID, recipient.ID),
		"provider":      s.smsProvider.GetProviderName(),
		"status":        "sent",
		"sent_at":       time.Now(),
		"phone":         recipient.RecipientAddress,
		"message":       smsMessage,
		"message_chars": len(smsMessage),
		"metadata":      metadata,
	}

	log := models.CreateSentLog(notification.TenantID, notification.ID, &recipient.ID, deliveryInfo)
	if err := s.logRepo.Create(log); err != nil {
		s.logger.WithError(err).Error("Failed to create SMS delivery log")
	}

	return nil
}

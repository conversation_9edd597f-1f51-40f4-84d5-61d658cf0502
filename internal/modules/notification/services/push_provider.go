package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"firebase.google.com/go/v4"
	"firebase.google.com/go/v4/messaging"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"google.golang.org/api/option"
)

// PushProvider defines the interface for push notification providers
type PushProvider interface {
	// SendPush sends a push notification to a recipient
	SendPush(recipient *models.NotificationRecipient, notification *models.Notification, message string, metadata map[string]interface{}) error
	
	// SendMulticast sends push notifications to multiple recipients
	SendMulticast(recipients []models.NotificationRecipient, notification *models.Notification, message string, metadata map[string]interface{}) error
	
	// ValidateToken validates a device token
	ValidateToken(token string) error
	
	// GetProviderName returns the name of the provider
	GetProviderName() string
	
	// SubscribeToTopic subscribes tokens to a topic
	SubscribeToTopic(tokens []string, topic string) error
	
	// UnsubscribeFromTopic unsubscribes tokens from a topic
	UnsubscribeFromTopic(tokens []string, topic string) error
	
	// SendToTopic sends notification to all devices subscribed to a topic
	SendToTopic(topic string, notification *models.Notification, message string, metadata map[string]interface{}) error
}

// FCMConfig represents FCM configuration
type FCMConfig struct {
	ServiceAccountPath string `json:"service_account_path" yaml:"service_account_path"`
	ProjectID         string `json:"project_id" yaml:"project_id"`
	DefaultIcon       string `json:"default_icon" yaml:"default_icon"`
	DefaultSound      string `json:"default_sound" yaml:"default_sound"`
	DefaultClickAction string `json:"default_click_action" yaml:"default_click_action"`
	EnableAnalytics   bool   `json:"enable_analytics" yaml:"enable_analytics"`
	DryRun           bool   `json:"dry_run" yaml:"dry_run"`
}

// FCMProvider implements push notifications using Firebase Cloud Messaging
type FCMProvider struct {
	client *messaging.Client
	config *FCMConfig
	logger utils.Logger
}

// NewFCMProvider creates a new FCM provider instance
func NewFCMProvider(config *FCMConfig, logger utils.Logger) (PushProvider, error) {
	if config == nil {
		return nil, fmt.Errorf("FCM config is required")
	}

	if config.ServiceAccountPath == "" {
		return nil, fmt.Errorf("service account path is required")
	}

	if config.ProjectID == "" {
		return nil, fmt.Errorf("project ID is required")
	}

	// Initialize Firebase app with service account
	opt := option.WithCredentialsFile(config.ServiceAccountPath)
	app, err := firebase.NewApp(context.Background(), &firebase.Config{
		ProjectID: config.ProjectID,
	}, opt)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize Firebase app: %w", err)
	}

	// Get messaging client
	client, err := app.Messaging(context.Background())
	if err != nil {
		return nil, fmt.Errorf("failed to get messaging client: %w", err)
	}

	return &FCMProvider{
		client: client,
		config: config,
		logger: logger,
	}, nil
}

// SendPush sends a push notification to a single recipient
func (f *FCMProvider) SendPush(recipient *models.NotificationRecipient, notification *models.Notification, message string, metadata map[string]interface{}) error {
	if recipient.DeviceToken == nil || *recipient.DeviceToken == "" {
		return fmt.Errorf("device token is required for push notification")
	}

	// Build FCM message
	fcmMessage, err := f.buildFCMMessage(*recipient.DeviceToken, notification, message, metadata)
	if err != nil {
		return fmt.Errorf("failed to build FCM message: %w", err)
	}

	// Send message
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	response, err := f.client.Send(ctx, fcmMessage)
	if err != nil {
		f.logger.WithError(err).WithField("device_token", *recipient.DeviceToken).Error("Failed to send FCM push notification")
		return fmt.Errorf("failed to send FCM message: %w", err)
	}

	f.logger.WithFields(map[string]interface{}{
		"message_id":   response,
		"device_token": *recipient.DeviceToken,
		"notification_id": notification.ID,
		"recipient_id": recipient.ID,
	}).Info("FCM push notification sent successfully")

	// Store the FCM message ID in metadata for tracking
	if metadata == nil {
		metadata = make(map[string]interface{})
	}
	metadata["fcm_message_id"] = response
	metadata["provider"] = f.GetProviderName()
	metadata["sent_at"] = time.Now()

	return nil
}

// SendMulticast sends push notifications to multiple recipients
func (f *FCMProvider) SendMulticast(recipients []models.NotificationRecipient, notification *models.Notification, message string, metadata map[string]interface{}) error {
	if len(recipients) == 0 {
		return fmt.Errorf("no recipients provided")
	}

	// Extract device tokens
	var tokens []string
	tokenToRecipient := make(map[string]*models.NotificationRecipient)
	
	for i := range recipients {
		recipient := &recipients[i]
		if recipient.DeviceToken != nil && *recipient.DeviceToken != "" {
			tokens = append(tokens, *recipient.DeviceToken)
			tokenToRecipient[*recipient.DeviceToken] = recipient
		}
	}

	if len(tokens) == 0 {
		return fmt.Errorf("no valid device tokens found")
	}

	// Build multicast message
	multicastMessage, err := f.buildMulticastMessage(tokens, notification, message, metadata)
	if err != nil {
		return fmt.Errorf("failed to build multicast message: %w", err)
	}

	// Send multicast message
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	response, err := f.client.SendMulticast(ctx, multicastMessage)
	if err != nil {
		f.logger.WithError(err).WithField("token_count", len(tokens)).Error("Failed to send FCM multicast")
		return fmt.Errorf("failed to send multicast message: %w", err)
	}

	// Log results
	f.logger.WithFields(map[string]interface{}{
		"success_count": response.SuccessCount,
		"failure_count": response.FailureCount,
		"total_count":   len(tokens),
		"notification_id": notification.ID,
	}).Info("FCM multicast completed")

	// Handle individual failures
	if response.FailureCount > 0 {
		for i, result := range response.Responses {
			if !result.Success {
				token := tokens[i]
				f.logger.WithFields(map[string]interface{}{
					"token":   token,
					"error":   result.Error,
					"message_id": result.MessageID,
				}).Warn("FCM message failed for token")
			}
		}
	}

	return nil
}

// ValidateToken validates a device token by sending a dry-run message
func (f *FCMProvider) ValidateToken(token string) error {
	if token == "" {
		return fmt.Errorf("token cannot be empty")
	}

	// Create a simple validation message
	message := &messaging.Message{
		Token: token,
		Notification: &messaging.Notification{
			Title: "Token Validation",
			Body:  "This is a token validation message",
		},
	}

	// Send as dry run
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	_, err := f.client.SendDryRun(ctx, message)
	if err != nil {
		return fmt.Errorf("token validation failed: %w", err)
	}

	return nil
}

// GetProviderName returns the provider name
func (f *FCMProvider) GetProviderName() string {
	return "fcm"
}

// SubscribeToTopic subscribes device tokens to a topic
func (f *FCMProvider) SubscribeToTopic(tokens []string, topic string) error {
	if len(tokens) == 0 {
		return fmt.Errorf("no tokens provided")
	}

	if topic == "" {
		return fmt.Errorf("topic cannot be empty")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	response, err := f.client.SubscribeToTopic(ctx, tokens, topic)
	if err != nil {
		return fmt.Errorf("failed to subscribe to topic: %w", err)
	}

	f.logger.WithFields(map[string]interface{}{
		"topic":         topic,
		"success_count": response.SuccessCount,
		"failure_count": response.FailureCount,
		"total_count":   len(tokens),
	}).Info("Topic subscription completed")

	return nil
}

// UnsubscribeFromTopic unsubscribes device tokens from a topic
func (f *FCMProvider) UnsubscribeFromTopic(tokens []string, topic string) error {
	if len(tokens) == 0 {
		return fmt.Errorf("no tokens provided")
	}

	if topic == "" {
		return fmt.Errorf("topic cannot be empty")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	response, err := f.client.UnsubscribeFromTopic(ctx, tokens, topic)
	if err != nil {
		return fmt.Errorf("failed to unsubscribe from topic: %w", err)
	}

	f.logger.WithFields(map[string]interface{}{
		"topic":         topic,
		"success_count": response.SuccessCount,
		"failure_count": response.FailureCount,
		"total_count":   len(tokens),
	}).Info("Topic unsubscription completed")

	return nil
}

// SendToTopic sends a notification to all devices subscribed to a topic
func (f *FCMProvider) SendToTopic(topic string, notification *models.Notification, message string, metadata map[string]interface{}) error {
	if topic == "" {
		return fmt.Errorf("topic cannot be empty")
	}

	// Build topic message
	topicMessage, err := f.buildTopicMessage(topic, notification, message, metadata)
	if err != nil {
		return fmt.Errorf("failed to build topic message: %w", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	response, err := f.client.Send(ctx, topicMessage)
	if err != nil {
		f.logger.WithError(err).WithField("topic", topic).Error("Failed to send FCM topic message")
		return fmt.Errorf("failed to send topic message: %w", err)
	}

	f.logger.WithFields(map[string]interface{}{
		"message_id":      response,
		"topic":           topic,
		"notification_id": notification.ID,
	}).Info("FCM topic message sent successfully")

	return nil
}

// buildFCMMessage constructs an FCM message for a single device
func (f *FCMProvider) buildFCMMessage(token string, notification *models.Notification, message string, metadata map[string]interface{}) (*messaging.Message, error) {
	fcmMessage := &messaging.Message{
		Token: token,
		Notification: &messaging.Notification{
			Title: notification.Subject,
			Body:  message,
		},
		Android: &messaging.AndroidConfig{
			Priority: f.getAndroidPriority(notification.Priority),
			Notification: &messaging.AndroidNotification{
				Icon:        f.config.DefaultIcon,
				Sound:       f.config.DefaultSound,
				ClickAction: f.config.DefaultClickAction,
			},
		},
		APNS: &messaging.APNSConfig{
			Payload: &messaging.APNSPayload{
				Aps: &messaging.Aps{
					Alert: &messaging.ApsAlert{
						Title: notification.Subject,
						Body:  message,
					},
					Sound: f.config.DefaultSound,
				},
			},
		},
	}

	// Add custom data
	if metadata != nil {
		data := make(map[string]string)
		for key, value := range metadata {
			// Convert all values to strings for FCM data payload
			if str, ok := value.(string); ok {
				data[key] = str
			} else {
				jsonBytes, err := json.Marshal(value)
				if err != nil {
					f.logger.WithError(err).WithField("key", key).Warn("Failed to marshal metadata value")
					continue
				}
				data[key] = string(jsonBytes)
			}
		}
		fcmMessage.Data = data
	}

	return fcmMessage, nil
}

// buildMulticastMessage constructs an FCM multicast message
func (f *FCMProvider) buildMulticastMessage(tokens []string, notification *models.Notification, message string, metadata map[string]interface{}) (*messaging.MulticastMessage, error) {
	multicastMessage := &messaging.MulticastMessage{
		Tokens: tokens,
		Notification: &messaging.Notification{
			Title: notification.Subject,
			Body:  message,
		},
		Android: &messaging.AndroidConfig{
			Priority: f.getAndroidPriority(notification.Priority),
			Notification: &messaging.AndroidNotification{
				Icon:        f.config.DefaultIcon,
				Sound:       f.config.DefaultSound,
				ClickAction: f.config.DefaultClickAction,
			},
		},
		APNS: &messaging.APNSConfig{
			Payload: &messaging.APNSPayload{
				Aps: &messaging.Aps{
					Alert: &messaging.ApsAlert{
						Title: notification.Subject,
						Body:  message,
					},
					Sound: f.config.DefaultSound,
				},
			},
		},
	}

	// Add custom data
	if metadata != nil {
		data := make(map[string]string)
		for key, value := range metadata {
			if str, ok := value.(string); ok {
				data[key] = str
			} else {
				jsonBytes, err := json.Marshal(value)
				if err != nil {
					f.logger.WithError(err).WithField("key", key).Warn("Failed to marshal metadata value")
					continue
				}
				data[key] = string(jsonBytes)
			}
		}
		multicastMessage.Data = data
	}

	return multicastMessage, nil
}

// buildTopicMessage constructs an FCM topic message
func (f *FCMProvider) buildTopicMessage(topic string, notification *models.Notification, message string, metadata map[string]interface{}) (*messaging.Message, error) {
	topicMessage := &messaging.Message{
		Topic: topic,
		Notification: &messaging.Notification{
			Title: notification.Subject,
			Body:  message,
		},
		Android: &messaging.AndroidConfig{
			Priority: f.getAndroidPriority(notification.Priority),
			Notification: &messaging.AndroidNotification{
				Icon:        f.config.DefaultIcon,
				Sound:       f.config.DefaultSound,
				ClickAction: f.config.DefaultClickAction,
			},
		},
		APNS: &messaging.APNSConfig{
			Payload: &messaging.APNSPayload{
				Aps: &messaging.Aps{
					Alert: &messaging.ApsAlert{
						Title: notification.Subject,
						Body:  message,
					},
					Sound: f.config.DefaultSound,
				},
			},
		},
	}

	// Add custom data
	if metadata != nil {
		data := make(map[string]string)
		for key, value := range metadata {
			if str, ok := value.(string); ok {
				data[key] = str
			} else {
				jsonBytes, err := json.Marshal(value)
				if err != nil {
					f.logger.WithError(err).WithField("key", key).Warn("Failed to marshal metadata value")
					continue
				}
				data[key] = string(jsonBytes)
			}
		}
		topicMessage.Data = data
	}

	return topicMessage, nil
}

// getAndroidPriority converts notification priority to Android priority
func (f *FCMProvider) getAndroidPriority(priority models.NotificationPriority) string {
	switch priority {
	case models.PriorityUrgent, models.PriorityHigh:
		return "high"
	default:
		return "normal"
	}
}
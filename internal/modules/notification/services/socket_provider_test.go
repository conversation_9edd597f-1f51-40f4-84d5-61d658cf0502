package services

import (
	"testing"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

func TestSocketIOProvider_Creation(t *testing.T) {
	logger := utils.NewLogger()
	
	// Test Socket.IO provider creation
	provider := NewSocketIOProvider("http://localhost:3000", "/notifications", logger)
	
	if provider == nil {
		t.Fatal("Expected Socket.IO provider to be created")
	}
	
	if provider.GetProviderName() != "socket.io" {
		t.Errorf("Expected provider name 'socket.io', got %s", provider.GetProviderName())
	}
}

func TestSocketIOProvider_SendNotification(t *testing.T) {
	logger := utils.NewLogger()
	
	// Create provider (will fail to connect but that's ok for testing)
	provider := NewSocketIOProvider("http://localhost:3000", "/notifications", logger)
	
	// Create test notification
	notification := &models.Notification{
		ID:       1,
		TenantID: 1,
		Type:     "test",
		Subject:  "Test Notification",
		Priority: models.PriorityNormal,
	}
	
	// Create test recipient
	userID := uint(123)
	recipient := &models.NotificationRecipient{
		ID:               1,
		NotificationID:   1,
		RecipientType:    "user",
		RecipientAddress: "<EMAIL>",
		UserID:           &userID,
		Status:           models.RecipientStatusPending,
	}
	
	// Test sending notification (should handle connection failure gracefully)
	err := provider.SendNotification(recipient, notification, "Test content", map[string]interface{}{
		"test": true,
	})
	
	// We expect an error since we can't connect to localhost:3000
	if err == nil {
		t.Error("Expected error when connection fails, but got nil")
	}
	
	// Close provider
	err = provider.Close()
	if err != nil {
		t.Errorf("Expected Close() to succeed, got error: %v", err)
	}
}

func TestMockSocketProvider(t *testing.T) {
	logger := utils.NewLogger()
	
	// Test mock provider creation
	provider := NewMockSocketProvider(logger)
	
	if provider == nil {
		t.Fatal("Expected mock provider to be created")
	}
	
	if provider.GetProviderName() != "mock_socket" {
		t.Errorf("Expected provider name 'mock_socket', got %s", provider.GetProviderName())
	}
	
	// Create test notification
	notification := &models.Notification{
		ID:       1,
		TenantID: 1,
		Type:     "test",
		Subject:  "Test Notification",
		Priority: models.PriorityNormal,
	}
	
	// Create test recipient
	userID := uint(123)
	recipient := &models.NotificationRecipient{
		ID:               1,
		NotificationID:   1,
		RecipientType:    "user",
		RecipientAddress: "<EMAIL>",
		UserID:           &userID,
		Status:           models.RecipientStatusPending,
	}
	
	// Test sending notification (mock should always succeed)
	err := provider.SendNotification(recipient, notification, "Test content", map[string]interface{}{
		"test": true,
	})
	
	if err != nil {
		t.Errorf("Expected mock provider to succeed, got error: %v", err)
	}
	
	// Close provider
	err = provider.Close()
	if err != nil {
		t.Errorf("Expected Close() to succeed, got error: %v", err)
	}
}

func TestWebSocketProvider_Creation(t *testing.T) {
	logger := utils.NewLogger()
	
	// Test WebSocket provider creation
	provider := NewWebSocketProvider("ws://localhost:3000", logger)
	
	if provider == nil {
		t.Fatal("Expected WebSocket provider to be created")
	}
	
	if provider.GetProviderName() != "websocket" {
		t.Errorf("Expected provider name 'websocket', got %s", provider.GetProviderName())
	}
}

func TestCreateSocketProvider(t *testing.T) {
	// Test that the factory function exists and can be called
	// This test is mainly to ensure compilation works
	t.Log("Socket provider factory function exists")
}
package services

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

// WebhookService interface defines webhook operations
type WebhookService interface {
	// StoreWebhookEvent stores a raw webhook event
	StoreWebhookEvent(tenantID uint, provider models.WebhookProvider, eventType string, payload json.RawMessage) (*models.WebhookEvent, error)
	
	// ProcessWebhookEvent processes a stored webhook event
	ProcessWebhookEvent(eventID uint) error
	
	// GetWebhookEvent retrieves a webhook event by ID
	GetWebhookEvent(eventID uint) (*models.WebhookEvent, error)
	
	// GetWebhookEvents retrieves webhook events with filters
	GetWebhookEvents(tenantID uint, filters WebhookEventFilters) ([]*models.WebhookEvent, int64, error)
	
	// ReprocessFailedEvents reprocesses failed webhook events
	ReprocessFailedEvents(tenantID uint, since time.Time) (int, error)
}

// WebhookEventFilters defines filters for webhook events
type WebhookEventFilters struct {
	Provider         *models.WebhookProvider
	EventType        *string
	MessageID        *string
	RecipientAddress *string
	ProcessedStatus  *bool // true for processed, false for unprocessed, nil for all
	StartDate        *time.Time
	EndDate          *time.Time
	Page             int
	Limit            int
}

// webhookService implements WebhookService
type webhookService struct {
	db     *gorm.DB
	logger utils.Logger
}

// NewWebhookService creates a new webhook service
func NewWebhookService(db *gorm.DB, logger utils.Logger) WebhookService {
	return &webhookService{
		db:     db,
		logger: logger,
	}
}

// StoreWebhookEvent stores a raw webhook event
func (s *webhookService) StoreWebhookEvent(tenantID uint, provider models.WebhookProvider, eventType string, payload json.RawMessage) (*models.WebhookEvent, error) {
	event := &models.WebhookEvent{
		TenantID:   tenantID,
		Provider:   provider,
		EventType:  eventType,
		RawPayload: payload,
	}

	// Extract message ID and recipient based on provider
	s.extractEventMetadata(provider, payload, event)

	if err := s.db.Create(event).Error; err != nil {
		s.logger.WithError(err).Error("Failed to store webhook event")
		return nil, err
	}

	s.logger.WithFields(map[string]interface{}{
		"event_id":  event.ID,
		"tenant_id": tenantID,
		"provider":  provider,
		"event_type": eventType,
	}).Info("Stored webhook event")

	return event, nil
}

// ProcessWebhookEvent processes a stored webhook event
func (s *webhookService) ProcessWebhookEvent(eventID uint) error {
	// Get the event
	var event models.WebhookEvent
	if err := s.db.First(&event, eventID).Error; err != nil {
		return fmt.Errorf("webhook event not found: %w", err)
	}

	// Check if already processed
	if event.ProcessedAt != nil {
		return fmt.Errorf("webhook event already processed")
	}

	// Process based on provider
	// This would typically involve:
	// 1. Parsing the raw payload
	// 2. Finding the related notification/recipient
	// 3. Creating appropriate logs
	// 4. Updating recipient status
	
	// For now, just mark as processed
	now := time.Now()
	event.ProcessedAt = &now
	
	if err := s.db.Save(&event).Error; err != nil {
		errorMsg := fmt.Sprintf("Failed to update webhook event: %v", err)
		event.ProcessingError = &errorMsg
		s.db.Save(&event)
		return err
	}

	return nil
}

// GetWebhookEvent retrieves a webhook event by ID
func (s *webhookService) GetWebhookEvent(eventID uint) (*models.WebhookEvent, error) {
	var event models.WebhookEvent
	if err := s.db.First(&event, eventID).Error; err != nil {
		return nil, err
	}
	return &event, nil
}

// GetWebhookEvents retrieves webhook events with filters
func (s *webhookService) GetWebhookEvents(tenantID uint, filters WebhookEventFilters) ([]*models.WebhookEvent, int64, error) {
	query := s.db.Model(&models.WebhookEvent{}).Where("tenant_id = ?", tenantID)

	// Apply filters
	if filters.Provider != nil {
		query = query.Where("provider = ?", *filters.Provider)
	}
	if filters.EventType != nil {
		query = query.Where("event_type = ?", *filters.EventType)
	}
	if filters.MessageID != nil {
		query = query.Where("message_id = ?", *filters.MessageID)
	}
	if filters.RecipientAddress != nil {
		query = query.Where("recipient_address = ?", *filters.RecipientAddress)
	}
	if filters.ProcessedStatus != nil {
		if *filters.ProcessedStatus {
			query = query.Where("processed_at IS NOT NULL")
		} else {
			query = query.Where("processed_at IS NULL")
		}
	}
	if filters.StartDate != nil {
		query = query.Where("created_at >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("created_at <= ?", *filters.EndDate)
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	if filters.Page <= 0 {
		filters.Page = 1
	}
	if filters.Limit <= 0 {
		filters.Limit = 20
	}
	offset := (filters.Page - 1) * filters.Limit
	query = query.Offset(offset).Limit(filters.Limit)

	// Get events
	var events []*models.WebhookEvent
	if err := query.Order("created_at DESC").Find(&events).Error; err != nil {
		return nil, 0, err
	}

	return events, total, nil
}

// ReprocessFailedEvents reprocesses failed webhook events
func (s *webhookService) ReprocessFailedEvents(tenantID uint, since time.Time) (int, error) {
	var events []*models.WebhookEvent
	
	// Find unprocessed events or events with processing errors
	if err := s.db.Where("tenant_id = ? AND created_at >= ? AND (processed_at IS NULL OR processing_error IS NOT NULL)", 
		tenantID, since).Find(&events).Error; err != nil {
		return 0, err
	}

	processedCount := 0
	for _, event := range events {
		// Clear previous error
		event.ProcessingError = nil
		
		// Try to process again
		if err := s.ProcessWebhookEvent(event.ID); err != nil {
			s.logger.WithError(err).WithField("event_id", event.ID).Error("Failed to reprocess webhook event")
		} else {
			processedCount++
		}
	}

	return processedCount, nil
}

// extractEventMetadata extracts message ID and recipient from webhook payload
func (s *webhookService) extractEventMetadata(provider models.WebhookProvider, payload json.RawMessage, event *models.WebhookEvent) {
	switch provider {
	case models.WebhookProviderSendGrid:
		var sgEvent models.SendGridWebhookEvent
		if err := json.Unmarshal(payload, &sgEvent); err == nil {
			event.MessageID = &sgEvent.SGMessageID
			event.RecipientAddress = &sgEvent.Email
		}
	
	case models.WebhookProviderMailgun:
		var mgEvent models.MailgunWebhookEvent
		if err := json.Unmarshal(payload, &mgEvent); err == nil {
			messageID := mgEvent.EventData.Message.Headers.MessageID
			event.MessageID = &messageID
			event.RecipientAddress = &mgEvent.EventData.Recipient
		}
	
	case models.WebhookProviderSES:
		var sesEvent models.SESWebhookEvent
		if err := json.Unmarshal(payload, &sesEvent); err == nil {
			// Parse the nested message
			var sesMessage models.SESMessage
			if err := json.Unmarshal(sesEvent.Message, &sesMessage); err == nil {
				event.MessageID = &sesMessage.Mail.MessageId
				if len(sesMessage.Mail.Destination) > 0 {
					event.RecipientAddress = &sesMessage.Mail.Destination[0]
				}
			}
		}
	
	case models.WebhookProviderTwilio:
		var twilioEvent models.TwilioWebhookEvent
		if err := json.Unmarshal(payload, &twilioEvent); err == nil {
			event.MessageID = &twilioEvent.MessageSid
			event.RecipientAddress = &twilioEvent.To
		}
	}
}
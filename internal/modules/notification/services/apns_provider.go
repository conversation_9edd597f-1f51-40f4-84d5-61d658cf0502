package services

import (
	"fmt"
	"time"

	"github.com/sideshow/apns2"
	"github.com/sideshow/apns2/certificate"
	"github.com/sideshow/apns2/payload"
	"github.com/sideshow/apns2/token"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// APNSConfig represents APNS configuration
type APNSConfig struct {
	// Certificate-based authentication
	CertificatePath string `json:"certificate_path" yaml:"certificate_path"`
	CertificateKey  string `json:"certificate_key" yaml:"certificate_key"`
	
	// Token-based authentication (recommended)
	AuthKeyPath string `json:"auth_key_path" yaml:"auth_key_path"`
	KeyID       string `json:"key_id" yaml:"key_id"`
	TeamID      string `json:"team_id" yaml:"team_id"`
	
	// Environment settings
	Production    bool   `json:"production" yaml:"production"`
	BundleID      string `json:"bundle_id" yaml:"bundle_id"`
	DefaultSound  string `json:"default_sound" yaml:"default_sound"`
	
	// Connection settings
	MaxConnections int           `json:"max_connections" yaml:"max_connections"`
	Timeout        time.Duration `json:"timeout" yaml:"timeout"`
}

// APNSProvider implements push notifications using Apple Push Notification Service
type APNSProvider struct {
	client *apns2.Client
	config *APNSConfig
	logger utils.Logger
}

// NewAPNSProvider creates a new APNS provider instance
func NewAPNSProvider(config *APNSConfig, logger utils.Logger) (PushProvider, error) {
	if config == nil {
		return nil, fmt.Errorf("APNS config is required")
	}

	if config.BundleID == "" {
		return nil, fmt.Errorf("bundle ID is required")
	}

	// Set defaults
	if config.DefaultSound == "" {
		config.DefaultSound = "default"
	}
	if config.MaxConnections <= 0 {
		config.MaxConnections = 10
	}
	if config.Timeout <= 0 {
		config.Timeout = 30 * time.Second
	}

	var client *apns2.Client

	// Token-based authentication (preferred)
	if config.AuthKeyPath != "" && config.KeyID != "" && config.TeamID != "" {
		authKey, err := token.AuthKeyFromFile(config.AuthKeyPath)
		if err != nil {
			return nil, fmt.Errorf("failed to load auth key: %w", err)
		}

		token := &token.Token{
			AuthKey: authKey,
			KeyID:   config.KeyID,
			TeamID:  config.TeamID,
		}

		client = apns2.NewTokenClient(token)
	} else if config.CertificatePath != "" {
		// Certificate-based authentication
		cert, err := certificate.FromP12File(config.CertificatePath, "")
		if err != nil {
			return nil, fmt.Errorf("failed to load certificate: %w", err)
		}

		client = apns2.NewClient(cert)
	} else {
		return nil, fmt.Errorf("either token-based or certificate-based authentication must be configured")
	}

	// Set environment
	if config.Production {
		client.Production()
	} else {
		client.Development()
	}

	return &APNSProvider{
		client: client,
		config: config,
		logger: logger,
	}, nil
}

// SendPush sends a push notification to a single recipient
func (a *APNSProvider) SendPush(recipient *models.NotificationRecipient, notification *models.Notification, message string, metadata map[string]interface{}) error {
	if recipient.DeviceToken == nil || *recipient.DeviceToken == "" {
		return fmt.Errorf("device token is required for APNS notification")
	}

	// Build APNS notification
	apnsNotification, err := a.buildAPNSNotification(*recipient.DeviceToken, notification, message, metadata)
	if err != nil {
		return fmt.Errorf("failed to build APNS notification: %w", err)
	}

	// Send notification
	response, err := a.client.Push(apnsNotification)
	if err != nil {
		a.logger.WithError(err).WithField("device_token", *recipient.DeviceToken).Error("Failed to send APNS push notification")
		return fmt.Errorf("failed to send APNS notification: %w", err)
	}

	// Check response status
	if !response.Sent() {
		errorMsg := fmt.Sprintf("APNS push failed: %s (reason: %s)", response.StatusCode, response.Reason)
		a.logger.WithFields(map[string]interface{}{
			"status_code":  response.StatusCode,
			"reason":       response.Reason,
			"device_token": *recipient.DeviceToken,
			"apns_id":      response.ApnsID,
		}).Error("APNS push notification failed")
		return fmt.Errorf(errorMsg)
	}

	a.logger.WithFields(map[string]interface{}{
		"apns_id":         response.ApnsID,
		"device_token":    *recipient.DeviceToken,
		"notification_id": notification.ID,
		"recipient_id":    recipient.ID,
		"status_code":     response.StatusCode,
	}).Info("APNS push notification sent successfully")

	// Store the APNS ID in metadata for tracking
	if metadata == nil {
		metadata = make(map[string]interface{})
	}
	metadata["apns_id"] = response.ApnsID
	metadata["provider"] = a.GetProviderName()
	metadata["sent_at"] = time.Now()
	metadata["status_code"] = response.StatusCode

	return nil
}

// SendMulticast sends push notifications to multiple recipients
func (a *APNSProvider) SendMulticast(recipients []models.NotificationRecipient, notification *models.Notification, message string, metadata map[string]interface{}) error {
	if len(recipients) == 0 {
		return fmt.Errorf("no recipients provided")
	}

	successCount := 0
	failureCount := 0

	// Send to each recipient individually (APNS doesn't have native multicast)
	for i := range recipients {
		recipient := &recipients[i]
		if recipient.DeviceToken == nil || *recipient.DeviceToken == "" {
			failureCount++
			a.logger.WithField("recipient_id", recipient.ID).Warn("Skipping recipient with empty device token")
			continue
		}

		err := a.SendPush(recipient, notification, message, metadata)
		if err != nil {
			failureCount++
			a.logger.WithError(err).WithField("recipient_id", recipient.ID).Error("Failed to send APNS push to recipient")
		} else {
			successCount++
		}
	}

	a.logger.WithFields(map[string]interface{}{
		"success_count":   successCount,
		"failure_count":   failureCount,
		"total_count":     len(recipients),
		"notification_id": notification.ID,
	}).Info("APNS multicast completed")

	if successCount == 0 {
		return fmt.Errorf("all APNS push notifications failed")
	}

	return nil
}

// ValidateToken validates a device token by sending a dry-run notification
func (a *APNSProvider) ValidateToken(token string) error {
	if token == "" {
		return fmt.Errorf("token cannot be empty")
	}

	// Create a simple validation notification with no alert
	notification := &apns2.Notification{
		DeviceToken: token,
		Topic:       a.config.BundleID,
		Payload: payload.NewPayload().ContentAvailable().Custom("validation", true),
	}

	// Send validation notification
	response, err := a.client.Push(notification)
	if err != nil {
		return fmt.Errorf("token validation failed: %w", err)
	}

	if !response.Sent() {
		return fmt.Errorf("token validation failed: %s (reason: %s)", response.StatusCode, response.Reason)
	}

	return nil
}

// GetProviderName returns the provider name
func (a *APNSProvider) GetProviderName() string {
	return "apns"
}

// SubscribeToTopic subscribes device tokens to a topic (not natively supported by APNS)
func (a *APNSProvider) SubscribeToTopic(tokens []string, topic string) error {
	// APNS doesn't have native topic subscription like FCM
	// This would need to be implemented at the application level
	return fmt.Errorf("topic subscription not supported by APNS provider")
}

// UnsubscribeFromTopic unsubscribes device tokens from a topic (not natively supported by APNS)
func (a *APNSProvider) UnsubscribeFromTopic(tokens []string, topic string) error {
	// APNS doesn't have native topic unsubscription like FCM
	// This would need to be implemented at the application level
	return fmt.Errorf("topic unsubscription not supported by APNS provider")
}

// SendToTopic sends a notification to all devices subscribed to a topic (not natively supported by APNS)
func (a *APNSProvider) SendToTopic(topic string, notification *models.Notification, message string, metadata map[string]interface{}) error {
	// APNS doesn't have native topic messaging like FCM
	// This would need to be implemented at the application level with device token management
	return fmt.Errorf("topic messaging not supported by APNS provider")
}

// buildAPNSNotification constructs an APNS notification
func (a *APNSProvider) buildAPNSNotification(deviceToken string, notification *models.Notification, message string, metadata map[string]interface{}) (*apns2.Notification, error) {
	// Build payload
	p := payload.NewPayload()
	p.AlertTitle(notification.Subject)
	p.AlertBody(message)
	p.Sound(a.config.DefaultSound)

	// Set badge if provided in metadata
	if metadata != nil {
		if badge, ok := metadata["badge"].(int); ok {
			p.Badge(badge)
		}
		
		// Add custom data
		for key, value := range metadata {
			// Skip system fields
			if key == "badge" {
				continue
			}
			p.Custom(key, value)
		}
	}

	// Set priority based on notification priority
	priority := apns2.PriorityLow
	switch notification.Priority {
	case models.PriorityHigh, models.PriorityUrgent:
		priority = apns2.PriorityHigh
	}

	apnsNotification := &apns2.Notification{
		DeviceToken: deviceToken,
		Topic:       a.config.BundleID,
		Payload:     p,
		Priority:    priority,
	}

	// Set collapse ID if provided
	if metadata != nil {
		if collapseID, ok := metadata["collapse_id"].(string); ok && collapseID != "" {
			apnsNotification.CollapseID = collapseID
		}
	}

	return apnsNotification, nil
}

// HybridPushProvider combines FCM and APNS providers for comprehensive push support
type HybridPushProvider struct {
	fcmProvider  PushProvider
	apnsProvider PushProvider
	logger       utils.Logger
}

// NewHybridPushProvider creates a provider that supports both FCM and APNS
func NewHybridPushProvider(fcmProvider, apnsProvider PushProvider, logger utils.Logger) PushProvider {
	return &HybridPushProvider{
		fcmProvider:  fcmProvider,
		apnsProvider: apnsProvider,
		logger:       logger,
	}
}

// SendPush sends push notification using the appropriate provider based on platform
func (h *HybridPushProvider) SendPush(recipient *models.NotificationRecipient, notification *models.Notification, message string, metadata map[string]interface{}) error {
	// Determine platform from metadata or device token characteristics
	platform := h.determinePlatform(recipient, metadata)
	
	switch platform {
	case "ios":
		if h.apnsProvider != nil {
			return h.apnsProvider.SendPush(recipient, notification, message, metadata)
		}
		return fmt.Errorf("APNS provider not configured")
	case "android":
		if h.fcmProvider != nil {
			return h.fcmProvider.SendPush(recipient, notification, message, metadata)
		}
		return fmt.Errorf("FCM provider not configured")
	default:
		// Try FCM first (it supports both Android and iOS)
		if h.fcmProvider != nil {
			return h.fcmProvider.SendPush(recipient, notification, message, metadata)
		}
		if h.apnsProvider != nil {
			return h.apnsProvider.SendPush(recipient, notification, message, metadata)
		}
		return fmt.Errorf("no push providers configured")
	}
}

// SendMulticast sends push notifications to multiple recipients
func (h *HybridPushProvider) SendMulticast(recipients []models.NotificationRecipient, notification *models.Notification, message string, metadata map[string]interface{}) error {
	// Group recipients by platform
	var iosRecipients, androidRecipients []models.NotificationRecipient
	
	for _, recipient := range recipients {
		platform := h.determinePlatform(&recipient, metadata)
		switch platform {
		case "ios":
			iosRecipients = append(iosRecipients, recipient)
		case "android":
			androidRecipients = append(androidRecipients, recipient)
		default:
			// Default to FCM (supports both platforms)
			androidRecipients = append(androidRecipients, recipient)
		}
	}

	var errors []error

	// Send to iOS devices via APNS
	if len(iosRecipients) > 0 && h.apnsProvider != nil {
		if err := h.apnsProvider.SendMulticast(iosRecipients, notification, message, metadata); err != nil {
			errors = append(errors, fmt.Errorf("APNS multicast failed: %w", err))
		}
	}

	// Send to Android devices via FCM
	if len(androidRecipients) > 0 && h.fcmProvider != nil {
		if err := h.fcmProvider.SendMulticast(androidRecipients, notification, message, metadata); err != nil {
			errors = append(errors, fmt.Errorf("FCM multicast failed: %w", err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("multicast errors: %v", errors)
	}

	return nil
}

// ValidateToken validates a device token using the appropriate provider
func (h *HybridPushProvider) ValidateToken(token string) error {
	// Try FCM first (more permissive)
	if h.fcmProvider != nil {
		if err := h.fcmProvider.ValidateToken(token); err == nil {
			return nil
		}
	}

	// Try APNS
	if h.apnsProvider != nil {
		if err := h.apnsProvider.ValidateToken(token); err == nil {
			return nil
		}
	}

	return fmt.Errorf("token validation failed on all providers")
}

// GetProviderName returns the hybrid provider name
func (h *HybridPushProvider) GetProviderName() string {
	return "hybrid_push"
}

// SubscribeToTopic subscribes tokens to a topic (FCM only)
func (h *HybridPushProvider) SubscribeToTopic(tokens []string, topic string) error {
	if h.fcmProvider != nil {
		return h.fcmProvider.SubscribeToTopic(tokens, topic)
	}
	return fmt.Errorf("topic subscription requires FCM provider")
}

// UnsubscribeFromTopic unsubscribes tokens from a topic (FCM only)
func (h *HybridPushProvider) UnsubscribeFromTopic(tokens []string, topic string) error {
	if h.fcmProvider != nil {
		return h.fcmProvider.UnsubscribeFromTopic(tokens, topic)
	}
	return fmt.Errorf("topic unsubscription requires FCM provider")
}

// SendToTopic sends notification to topic (FCM only)
func (h *HybridPushProvider) SendToTopic(topic string, notification *models.Notification, message string, metadata map[string]interface{}) error {
	if h.fcmProvider != nil {
		return h.fcmProvider.SendToTopic(topic, notification, message, metadata)
	}
	return fmt.Errorf("topic messaging requires FCM provider")
}

// determinePlatform determines the platform based on recipient and metadata
func (h *HybridPushProvider) determinePlatform(recipient *models.NotificationRecipient, metadata map[string]interface{}) string {
	// Check metadata for explicit platform
	if metadata != nil {
		if platform, ok := metadata["platform"].(string); ok {
			return platform
		}
	}

	// Check recipient device info if available
	if recipient.DeviceToken != nil {
		token := *recipient.DeviceToken
		// iOS tokens are typically 64 characters hex
		if len(token) == 64 {
			return "ios"
		}
		// FCM tokens are longer and contain various characters
		if len(token) > 100 {
			return "android"
		}
	}

	// Default to android (FCM supports both platforms)
	return "android"
}
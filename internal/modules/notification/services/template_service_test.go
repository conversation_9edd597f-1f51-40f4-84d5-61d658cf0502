package services

import (
	"strings"
	"testing"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
)

func TestRenderTemplate_HTMLTemplate(t *testing.T) {
	// Create template service instance for testing
	service := &templateService{}

	// Mock HTML template version
	version := &models.NotificationTemplateVersion{
		BodyHTML: `
<h2>Hello {{.user.name}}! 📧</h2>
<p>Thank you for signing up with {{.brand.name}}.</p>
<p>Your email: {{.user.email}}</p>
<p>Contact: {{.brand.support_email}}</p>
<p>Year: {{.current_year}}</p>
<p>Uppercase: {{.user.name | upper}}</p>
<p>Default value: {{.missing_value | default "Default Text"}}</p>
`,
	}

	// Mock template data with nested structure
	templateData := map[string]interface{}{
		"user": map[string]interface{}{
			"name":  "<PERSON>",
			"email": "<EMAIL>",
		},
		"brand": map[string]interface{}{
			"name":          "BlogAPI",
			"support_email": "<EMAIL>",
		},
	}

	// Test rendering
	result, err := service.RenderTemplate(version, templateData)
	if err != nil {
		t.Fatalf("RenderTemplate failed: %v", err)
	}

	// Test cases for variable replacement
	tests := []struct {
		name     string
		expected string
	}{
		{"User name", "John Doe"},
		{"User email", "<EMAIL>"},
		{"Brand name", "BlogAPI"},
		{"Support email", "<EMAIL>"},
		{"Uppercase function", "JOHN DOE"},
		{"Default function", "Default Text"},
	}

	for _, test := range tests {
		if !strings.Contains(result, test.expected) {
			t.Errorf("Expected value %s not found in result for test %s", test.expected, test.name)
		}
	}

	// Test current_year is added automatically
	currentYear := time.Now().Year()
	if !strings.Contains(result, string(rune(currentYear/1000+'0'))+string(rune((currentYear/100)%10+'0'))+string(rune((currentYear/10)%10+'0'))+string(rune(currentYear%10+'0'))) {
		t.Errorf("Current year not found in result")
	}
}

func TestRenderTemplate_TextTemplate(t *testing.T) {
	// Create template service instance for testing
	service := &templateService{}

	// Mock text template version (no HTML tags)
	version := &models.NotificationTemplateVersion{
		BodyHTML: `
Hello {{.user.name}}!

Thank you for signing up with {{.brand.name}}.
Your email: {{.user.email}}
Contact: {{.brand.support_email}}
Year: {{.current_year}}

Best regards,
{{.brand.name}} Team
`,
	}

	// Mock template data
	templateData := map[string]interface{}{
		"user": map[string]interface{}{
			"name":  "Jane Smith",
			"email": "<EMAIL>",
		},
		"brand": map[string]interface{}{
			"name":          "BlogAPI",
			"support_email": "<EMAIL>",
		},
	}

	// Test rendering
	result, err := service.RenderTemplate(version, templateData)
	if err != nil {
		t.Fatalf("RenderTemplate failed: %v", err)
	}

	// Test cases for variable replacement
	tests := []string{
		"Jane Smith",
		"<EMAIL>",
		"BlogAPI",
		"<EMAIL>",
	}

	for _, expected := range tests {
		if !strings.Contains(result, expected) {
			t.Errorf("Expected value %s not found in result", expected)
		}
	}
}

func TestIsHTMLTemplate(t *testing.T) {
	service := &templateService{}

	tests := []struct {
		content  string
		expected bool
	}{
		{"<html><body>Test</body></html>", true},
		{"<div>Test</div>", true},
		{"<p>Test paragraph</p>", true},
		{"<h1>Heading</h1>", true},
		{"<a href='test'>Link</a>", true},
		{"Plain text content", false},
		{"Hello {{.name}}, this is plain text", false},
		{"No HTML tags here", false},
	}

	for _, test := range tests {
		result := service.isHTMLTemplate(test.content)
		if result != test.expected {
			t.Errorf("isHTMLTemplate(%s) = %v, expected %v", test.content, result, test.expected)
		}
	}
}

func TestTemplateFunctions(t *testing.T) {
	service := &templateService{}

	// Test HTML template with functions
	version := &models.NotificationTemplateVersion{
		BodyHTML: `
<p>Name: {{.name | upper}}</p>
<p>Email: {{.email | lower}}</p>
<p>Missing: {{.missing | default "Not provided"}}</p>
<p>Current Year: {{currentYear}}</p>
<p>Formatted Date: {{formatDate (now) "2006-01-02"}}</p>
`,
	}

	templateData := map[string]interface{}{
		"name":  "John DOE",
		"email": "<EMAIL>",
	}

	result, err := service.RenderTemplate(version, templateData)
	if err != nil {
		t.Fatalf("RenderTemplate failed: %v", err)
	}

	// Test function results
	if !strings.Contains(result, "JOHN DOE") {
		t.Error("upper function not working")
	}
	if !strings.Contains(result, "<EMAIL>") {
		t.Error("lower function not working")
	}
	if !strings.Contains(result, "Not provided") {
		t.Error("default function not working")
	}
}

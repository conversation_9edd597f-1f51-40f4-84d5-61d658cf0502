package services

import (
	"context"
	"fmt"
	"strings"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/sns"
	"github.com/aws/aws-sdk-go-v2/service/sns/types"
	notificationConfig "github.com/tranthanhloi/wn-api-v3/internal/config"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/twilio/twilio-go"
	twilioApi "github.com/twilio/twilio-go/rest/api/v2010"
)

// SMSProvider interface for different SMS delivery providers
type SMSProvider interface {
	SendSMS(recipient *models.NotificationRecipient, notification *models.Notification, message string) error
	GetProviderName() string
	ValidatePhoneNumber(phoneNumber string) error
}

// TwilioSMSProvider implements SMSProvider using Twilio
type TwilioSMSProvider struct {
	client     *twilio.RestClient
	fromNumber string
	logger     utils.Logger
}

// NewTwilioSMSProvider creates a new Twilio SMS provider
func NewTwilioSMSProvider(accountSID, authToken, fromNumber string, logger utils.Logger) SMSProvider {
	client := twilio.NewRestClientWithParams(twilio.ClientParams{
		Username: accountSID,
		Password: authToken,
	})

	return &TwilioSMSProvider{
		client:     client,
		fromNumber: fromNumber,
		logger:     logger,
	}
}

func (p *TwilioSMSProvider) SendSMS(recipient *models.NotificationRecipient, notification *models.Notification, message string) error {
	// Validate phone number
	if err := p.ValidatePhoneNumber(recipient.RecipientAddress); err != nil {
		return fmt.Errorf("invalid phone number: %w", err)
	}

	// Create message parameters
	params := &twilioApi.CreateMessageParams{}
	params.SetTo(recipient.RecipientAddress)
	params.SetFrom(p.fromNumber)
	params.SetBody(message)

	// Send SMS
	resp, err := p.client.Api.CreateMessage(params)
	if err != nil {
		p.logger.WithFields(map[string]interface{}{
			"error":        err.Error(),
			"recipient":    recipient.RecipientAddress,
			"notification": notification.ID,
		}).Error("Failed to send SMS via Twilio")
		return fmt.Errorf("twilio send failed: %w", err)
	}

	p.logger.WithFields(map[string]interface{}{
		"message_sid":  resp.Sid,
		"status":       resp.Status,
		"recipient":    recipient.RecipientAddress,
		"notification": notification.ID,
		"price":        resp.Price,
		"price_unit":   resp.PriceUnit,
	}).Info("SMS sent successfully via Twilio")

	return nil
}

func (p *TwilioSMSProvider) GetProviderName() string {
	return "twilio"
}

func (p *TwilioSMSProvider) ValidatePhoneNumber(phoneNumber string) error {
	// Basic E.164 format validation
	if !strings.HasPrefix(phoneNumber, "+") {
		return fmt.Errorf("phone number must be in E.164 format (starting with +)")
	}

	// Remove + and check if all remaining characters are digits
	digits := phoneNumber[1:]
	if len(digits) < 4 || len(digits) > 15 {
		return fmt.Errorf("phone number must be between 4 and 15 digits")
	}

	for _, char := range digits {
		if char < '0' || char > '9' {
			return fmt.Errorf("phone number must contain only digits")
		}
	}

	return nil
}

// AWSSNSSMSProvider implements SMSProvider using AWS SNS
type AWSSNSSMSProvider struct {
	client   *sns.Client
	senderID string
	logger   utils.Logger
}

// NewAWSSNSSMSProvider creates a new AWS SNS SMS provider
func NewAWSSNSSMSProvider(region, senderID string, logger utils.Logger) (SMSProvider, error) {
	// Load AWS configuration
	cfg, err := config.LoadDefaultConfig(context.TODO(), config.WithRegion(region))
	if err != nil {
		return nil, fmt.Errorf("unable to load AWS config: %w", err)
	}

	// Create SNS client
	client := sns.NewFromConfig(cfg)

	return &AWSSNSSMSProvider{
		client:   client,
		senderID: senderID,
		logger:   logger,
	}, nil
}

func (p *AWSSNSSMSProvider) SendSMS(recipient *models.NotificationRecipient, notification *models.Notification, message string) error {
	// Validate phone number
	if err := p.ValidatePhoneNumber(recipient.RecipientAddress); err != nil {
		return fmt.Errorf("invalid phone number: %w", err)
	}

	// Set SMS attributes
	attributes := map[string]string{
		"AWS.SNS.SMS.SenderID": p.senderID,
		"AWS.SNS.SMS.SMSType":  "Transactional", // or "Promotional"
		"AWS.SNS.SMS.MaxPrice": "0.50",          // Maximum price in USD
	}

	// Publish SMS
	input := &sns.PublishInput{
		Message:           &message,
		PhoneNumber:       &recipient.RecipientAddress,
		MessageAttributes: make(map[string]types.MessageAttributeValue),
	}

	// Add attributes to message
	for key, value := range attributes {
		val := value
		input.MessageAttributes[key] = types.MessageAttributeValue{
			DataType:    strPtr("String"),
			StringValue: &val,
		}
	}

	// Send SMS
	result, err := p.client.Publish(context.TODO(), input)
	if err != nil {
		p.logger.WithFields(map[string]interface{}{
			"error":        err.Error(),
			"recipient":    recipient.RecipientAddress,
			"notification": notification.ID,
		}).Error("Failed to send SMS via AWS SNS")
		return fmt.Errorf("aws sns send failed: %w", err)
	}

	p.logger.WithFields(map[string]interface{}{
		"message_id":   *result.MessageId,
		"recipient":    recipient.RecipientAddress,
		"notification": notification.ID,
	}).Info("SMS sent successfully via AWS SNS")

	return nil
}

func (p *AWSSNSSMSProvider) GetProviderName() string {
	return "aws_sns"
}

func (p *AWSSNSSMSProvider) ValidatePhoneNumber(phoneNumber string) error {
	// Basic E.164 format validation
	if !strings.HasPrefix(phoneNumber, "+") {
		return fmt.Errorf("phone number must be in E.164 format (starting with +)")
	}

	// Remove + and check if all remaining characters are digits
	digits := phoneNumber[1:]
	if len(digits) < 4 || len(digits) > 15 {
		return fmt.Errorf("phone number must be between 4 and 15 digits")
	}

	for _, char := range digits {
		if char < '0' || char > '9' {
			return fmt.Errorf("phone number must contain only digits")
		}
	}

	return nil
}

// MockSMSProvider implements SMSProvider for testing
type MockSMSProvider struct {
	logger utils.Logger
}

// NewMockSMSProvider creates a new mock SMS provider
func NewMockSMSProvider(logger utils.Logger) SMSProvider {
	return &MockSMSProvider{
		logger: logger,
	}
}

func (p *MockSMSProvider) SendSMS(recipient *models.NotificationRecipient, notification *models.Notification, message string) error {
	// Validate phone number
	if err := p.ValidatePhoneNumber(recipient.RecipientAddress); err != nil {
		return fmt.Errorf("invalid phone number: %w", err)
	}

	// Log SMS details for debugging
	p.logger.WithFields(map[string]interface{}{
		"provider":       "mock_sms",
		"recipient":      recipient.RecipientAddress,
		"notification":   notification.ID,
		"message":        message,
		"message_length": len(message),
	}).Info("SMS sent successfully via Mock provider")

	return nil
}

func (p *MockSMSProvider) GetProviderName() string {
	return "mock_sms"
}

func (p *MockSMSProvider) ValidatePhoneNumber(phoneNumber string) error {
	// Basic E.164 format validation
	if !strings.HasPrefix(phoneNumber, "+") {
		return fmt.Errorf("phone number must be in E.164 format (starting with +)")
	}

	// Remove + and check if all remaining characters are digits
	digits := phoneNumber[1:]
	if len(digits) < 4 || len(digits) > 15 {
		return fmt.Errorf("phone number must be between 4 and 15 digits")
	}

	for _, char := range digits {
		if char < '0' || char > '9' {
			return fmt.Errorf("phone number must contain only digits")
		}
	}

	return nil
}

// CreateSMSProvider creates an SMS provider based on configuration
func CreateSMSProvider(cfg *notificationConfig.NotificationConfig, logger utils.Logger) SMSProvider {
	logger.WithFields(map[string]interface{}{
		"requested_provider": cfg.SMSProvider,
	}).Info("Creating SMS provider")

	switch cfg.SMSProvider {
	case "twilio":
		if cfg.TwilioConfig.AccountSID == "" || cfg.TwilioConfig.AuthToken == "" {
			logger.Warn("Twilio credentials not configured, falling back to mock")
			return NewMockSMSProvider(logger)
		}
		logger.Info("Creating Twilio SMS provider")
		return NewTwilioSMSProvider(
			cfg.TwilioConfig.AccountSID,
			cfg.TwilioConfig.AuthToken,
			cfg.TwilioConfig.FromNumber,
			logger,
		)
	case "aws_sns":
		if cfg.AWSSNSConfig.Region == "" {
			logger.Warn("AWS SNS region not configured, falling back to mock")
			return NewMockSMSProvider(logger)
		}
		logger.Info("Creating AWS SNS SMS provider")
		provider, err := NewAWSSNSSMSProvider(
			cfg.AWSSNSConfig.Region,
			cfg.AWSSNSConfig.SenderID,
			logger,
		)
		if err != nil {
			logger.WithError(err).Error("Failed to create AWS SNS provider, falling back to mock")
			return NewMockSMSProvider(logger)
		}
		return provider
	case "mock":
		logger.Info("Creating mock SMS provider for development/testing")
		return NewMockSMSProvider(logger)
	default:
		logger.WithFields(map[string]interface{}{
			"provider": cfg.SMSProvider,
		}).Warn("Unknown SMS provider, falling back to mock")
		return NewMockSMSProvider(logger)
	}
}

// Helper function to get string pointer
func strPtr(s string) *string {
	return &s
}


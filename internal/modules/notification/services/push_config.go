package services

import (
	"fmt"
	"os"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// PushProviderType represents the type of push provider
type PushProviderType string

const (
	PushProviderFCM    PushProviderType = "fcm"
	PushProviderAPNS   PushProviderType = "apns"
	PushProviderHybrid PushProviderType = "hybrid"
)

// PushConfig represents the configuration for push notifications
type PushConfig struct {
	Provider PushProviderType `json:"provider" yaml:"provider"`
	FCM      *FCMConfig       `json:"fcm,omitempty" yaml:"fcm,omitempty"`
	APNS     *APNSConfig      `json:"apns,omitempty" yaml:"apns,omitempty"`
	Enabled  bool             `json:"enabled" yaml:"enabled"`
}

// PushProviderFactory creates push providers based on configuration
type PushProviderFactory struct {
	logger utils.Logger
}

// NewPushProviderFactory creates a new push provider factory
func NewPushProviderFactory(logger utils.Logger) *PushProviderFactory {
	return &PushProviderFactory{
		logger: logger,
	}
}

// CreateProvider creates a push provider based on configuration
func (f *PushProviderFactory) CreateProvider(config *PushConfig) (PushProvider, error) {
	if config == nil || !config.Enabled {
		f.logger.Info("Push notifications disabled")
		return NewMockPushProvider(f.logger), nil
	}

	switch config.Provider {
	case PushProviderFCM:
		return f.createFCMProvider(config.FCM)
	case PushProviderAPNS:
		return f.createAPNSProvider(config.APNS)
	case PushProviderHybrid:
		return f.createHybridProvider(config.FCM, config.APNS)
	default:
		return nil, fmt.Errorf("unsupported push provider: %s", config.Provider)
	}
}

// createFCMProvider creates an FCM provider
func (f *PushProviderFactory) createFCMProvider(config *FCMConfig) (PushProvider, error) {
	if config == nil {
		return nil, fmt.Errorf("FCM config is required")
	}

	// Set defaults from environment if not specified
	if config.ServiceAccountPath == "" {
		config.ServiceAccountPath = os.Getenv("FCM_SERVICE_ACCOUNT_PATH")
	}
	if config.ProjectID == "" {
		config.ProjectID = os.Getenv("FCM_PROJECT_ID")
	}
	if config.DefaultIcon == "" {
		config.DefaultIcon = os.Getenv("FCM_DEFAULT_ICON")
	}
	if config.DefaultSound == "" {
		config.DefaultSound = "default"
	}

	return NewFCMProvider(config, f.logger)
}

// createAPNSProvider creates an APNS provider
func (f *PushProviderFactory) createAPNSProvider(config *APNSConfig) (PushProvider, error) {
	if config == nil {
		return nil, fmt.Errorf("APNS config is required")
	}

	// Set defaults from environment if not specified
	if config.AuthKeyPath == "" {
		config.AuthKeyPath = os.Getenv("APNS_AUTH_KEY_PATH")
	}
	if config.KeyID == "" {
		config.KeyID = os.Getenv("APNS_KEY_ID")
	}
	if config.TeamID == "" {
		config.TeamID = os.Getenv("APNS_TEAM_ID")
	}
	if config.BundleID == "" {
		config.BundleID = os.Getenv("APNS_BUNDLE_ID")
	}
	if config.CertificatePath == "" {
		config.CertificatePath = os.Getenv("APNS_CERTIFICATE_PATH")
	}

	// Check production environment
	if os.Getenv("APNS_PRODUCTION") == "true" {
		config.Production = true
	}

	return NewAPNSProvider(config, f.logger)
}

// createHybridProvider creates a hybrid provider with both FCM and APNS
func (f *PushProviderFactory) createHybridProvider(fcmConfig *FCMConfig, apnsConfig *APNSConfig) (PushProvider, error) {
	var fcmProvider, apnsProvider PushProvider
	var err error

	// Create FCM provider if configured
	if fcmConfig != nil {
		fcmProvider, err = f.createFCMProvider(fcmConfig)
		if err != nil {
			f.logger.WithError(err).Warn("Failed to create FCM provider for hybrid setup")
		}
	}

	// Create APNS provider if configured
	if apnsConfig != nil {
		apnsProvider, err = f.createAPNSProvider(apnsConfig)
		if err != nil {
			f.logger.WithError(err).Warn("Failed to create APNS provider for hybrid setup")
		}
	}

	// Need at least one provider
	if fcmProvider == nil && apnsProvider == nil {
		return nil, fmt.Errorf("at least one push provider (FCM or APNS) must be configured for hybrid setup")
	}

	return NewHybridPushProvider(fcmProvider, apnsProvider, f.logger), nil
}

// LoadConfigFromEnv loads push configuration from environment variables
func LoadPushConfigFromEnv() *PushConfig {
	config := &PushConfig{
		Enabled: os.Getenv("PUSH_ENABLED") == "true",
	}

	// Determine provider type
	providerType := os.Getenv("PUSH_PROVIDER")
	switch providerType {
	case "fcm":
		config.Provider = PushProviderFCM
	case "apns":
		config.Provider = PushProviderAPNS
	case "hybrid":
		config.Provider = PushProviderHybrid
	default:
		config.Provider = PushProviderFCM // Default to FCM
	}

	// Load FCM config if needed
	if config.Provider == PushProviderFCM || config.Provider == PushProviderHybrid {
		config.FCM = &FCMConfig{
			ServiceAccountPath: os.Getenv("FCM_SERVICE_ACCOUNT_PATH"),
			ProjectID:         os.Getenv("FCM_PROJECT_ID"),
			DefaultIcon:       os.Getenv("FCM_DEFAULT_ICON"),
			DefaultSound:      getEnvWithDefault("FCM_DEFAULT_SOUND", "default"),
			DefaultClickAction: os.Getenv("FCM_DEFAULT_CLICK_ACTION"),
			EnableAnalytics:   os.Getenv("FCM_ENABLE_ANALYTICS") == "true",
			DryRun:           os.Getenv("FCM_DRY_RUN") == "true",
		}
	}

	// Load APNS config if needed
	if config.Provider == PushProviderAPNS || config.Provider == PushProviderHybrid {
		config.APNS = &APNSConfig{
			AuthKeyPath:     os.Getenv("APNS_AUTH_KEY_PATH"),
			KeyID:          os.Getenv("APNS_KEY_ID"),
			TeamID:         os.Getenv("APNS_TEAM_ID"),
			CertificatePath: os.Getenv("APNS_CERTIFICATE_PATH"),
			CertificateKey:  os.Getenv("APNS_CERTIFICATE_KEY"),
			Production:     os.Getenv("APNS_PRODUCTION") == "true",
			BundleID:       os.Getenv("APNS_BUNDLE_ID"),
			DefaultSound:   getEnvWithDefault("APNS_DEFAULT_SOUND", "default"),
		}
	}

	return config
}

// getEnvWithDefault returns environment variable value or default if empty
func getEnvWithDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// MockPushProvider provides a mock implementation for testing
type MockPushProvider struct {
	logger utils.Logger
}

// NewMockPushProvider creates a new mock push provider
func NewMockPushProvider(logger utils.Logger) PushProvider {
	return &MockPushProvider{
		logger: logger,
	}
}

// SendPush simulates sending a push notification
func (m *MockPushProvider) SendPush(recipient *models.NotificationRecipient, notification *models.Notification, message string, metadata map[string]interface{}) error {
	m.logger.WithFields(map[string]interface{}{
		"recipient_id":    recipient.ID,
		"notification_id": notification.ID,
		"message":         message,
		"device_token":    recipient.DeviceToken,
	}).Info("Mock push notification sent")
	return nil
}

// SendMulticast simulates sending multicast push notifications
func (m *MockPushProvider) SendMulticast(recipients []models.NotificationRecipient, notification *models.Notification, message string, metadata map[string]interface{}) error {
	m.logger.WithFields(map[string]interface{}{
		"recipient_count": len(recipients),
		"notification_id": notification.ID,
		"message":         message,
	}).Info("Mock multicast push notifications sent")
	return nil
}

// ValidateToken simulates token validation
func (m *MockPushProvider) ValidateToken(token string) error {
	m.logger.WithField("token", token).Info("Mock token validation successful")
	return nil
}

// GetProviderName returns the mock provider name
func (m *MockPushProvider) GetProviderName() string {
	return "mock"
}

// SubscribeToTopic simulates topic subscription
func (m *MockPushProvider) SubscribeToTopic(tokens []string, topic string) error {
	m.logger.WithFields(map[string]interface{}{
		"token_count": len(tokens),
		"topic":       topic,
	}).Info("Mock topic subscription successful")
	return nil
}

// UnsubscribeFromTopic simulates topic unsubscription
func (m *MockPushProvider) UnsubscribeFromTopic(tokens []string, topic string) error {
	m.logger.WithFields(map[string]interface{}{
		"token_count": len(tokens),
		"topic":       topic,
	}).Info("Mock topic unsubscription successful")
	return nil
}

// SendToTopic simulates sending to topic
func (m *MockPushProvider) SendToTopic(topic string, notification *models.Notification, message string, metadata map[string]interface{}) error {
	m.logger.WithFields(map[string]interface{}{
		"topic":           topic,
		"notification_id": notification.ID,
		"message":         message,
	}).Info("Mock topic message sent")
	return nil
}
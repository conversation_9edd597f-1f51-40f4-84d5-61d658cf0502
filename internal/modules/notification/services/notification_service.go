package services

import (
	"context"
	"errors"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

type NotificationService interface {
	CreateNotification(tenantID uint, req models.CreateNotificationRequest) (*models.Notification, error)
	GetNotification(tenantID, id uint) (*models.Notification, error)
	GetNotificationWithRelations(tenantID, id uint) (*models.Notification, error)
	ListNotifications(tenantID uint, filters models.NotificationFilters) ([]models.Notification, int64, error)
	ListNotificationsWithCursor(ctx context.Context, tenantID uint, req *pagination.CursorRequest, filters map[string]interface{}) (*dto.NotificationListResponse, error)
	ListUserNotifications(tenantID, userID uint, filters models.NotificationFilters) ([]models.Notification, int64, error)
	UpdateNotification(tenantID, id uint, req models.UpdateNotificationRequest) (*models.Notification, error)
	DeleteNotification(tenantID, id uint) error
	SendNotification(tenantID, id uint) error
	CancelNotification(tenantID, id uint) error
	RetryNotification(tenantID, id uint) error
	GetStatistics(tenantID uint, dateFrom, dateTo *time.Time) (*repositories.NotificationStatistics, error)
	ProcessPendingNotifications(tenantID uint, limit int) error
	ProcessScheduledNotifications(limit int) error
	GetUserUnreadCount(tenantID, userID uint) (int64, error)
}

type notificationService struct {
	notificationRepo repositories.NotificationRepository
	recipientRepo    repositories.RecipientRepository
	templateRepo     repositories.TemplateRepository
	logRepo          repositories.LogRepository
	deliveryService  DeliveryService
}

func NewNotificationService(
	notificationRepo repositories.NotificationRepository,
	recipientRepo repositories.RecipientRepository,
	templateRepo repositories.TemplateRepository,
	logRepo repositories.LogRepository,
	deliveryService DeliveryService,
) NotificationService {
	return &notificationService{
		notificationRepo: notificationRepo,
		recipientRepo:    recipientRepo,
		templateRepo:     templateRepo,
		logRepo:          logRepo,
		deliveryService:  deliveryService,
	}
}

func (s *notificationService) CreateNotification(tenantID uint, req models.CreateNotificationRequest) (*models.Notification, error) {
	// Create notification
	notification := &models.Notification{
		TenantID:    tenantID,
		Type:        req.Type,
		Channel:     req.Channel,
		Priority:    req.Priority,
		Subject:     req.Subject,
		TemplateID:  req.TemplateID,
		Status:      models.StatusPending,
		ScheduledAt: req.ScheduledAt,
		MaxRetries:  3, // Default
	}

	// Set default priority if not provided
	if notification.Priority == "" {
		notification.Priority = models.PriorityNormal
	}

	// Set template data
	if req.TemplateData != nil {
		if err := notification.SetTemplateData(req.TemplateData); err != nil {
			return nil, err
		}
	}

	// Set metadata
	if req.Metadata != nil {
		if err := notification.SetMetadata(req.Metadata); err != nil {
			return nil, err
		}
	}

	// Create notification
	if err := s.notificationRepo.Create(notification); err != nil {
		return nil, err
	}

	// Create recipients
	var recipients []models.NotificationRecipient
	for _, recipientReq := range req.Recipients {
		recipient := models.NotificationRecipient{
			TenantID:         tenantID,
			NotificationID:   notification.ID,
			UserID:           recipientReq.UserID,
			RecipientType:    recipientReq.RecipientType,
			RecipientAddress: recipientReq.RecipientAddress,
			DeviceToken:      recipientReq.DeviceToken,
			Status:           models.RecipientStatusPending,
		}

		if recipientReq.DeliveryInfo != nil {
			if err := recipient.SetDeliveryInfo(recipientReq.DeliveryInfo); err != nil {
				return nil, err
			}
		}

		recipients = append(recipients, recipient)
	}

	if err := s.recipientRepo.CreateBatch(recipients); err != nil {
		return nil, err
	}

	// Create log entry
	log := models.CreateCreatedLog(tenantID, notification.ID)
	if err := s.logRepo.Create(log); err != nil {
		// Log creation failure shouldn't stop the process
		// TODO: Add proper logging
	}

	// Load recipients for response
	notification.Recipients = recipients

	return notification, nil
}

func (s *notificationService) GetNotification(tenantID, id uint) (*models.Notification, error) {
	return s.notificationRepo.GetByID(tenantID, id)
}

func (s *notificationService) GetNotificationWithRelations(tenantID, id uint) (*models.Notification, error) {
	return s.notificationRepo.GetWithRelations(tenantID, id)
}

func (s *notificationService) ListNotifications(tenantID uint, filters models.NotificationFilters) ([]models.Notification, int64, error) {
	return s.notificationRepo.List(tenantID, filters)
}

func (s *notificationService) ListNotificationsWithCursor(ctx context.Context, tenantID uint, req *pagination.CursorRequest, filters map[string]interface{}) (*dto.NotificationListResponse, error) {
	notifications, paginationResp, err := s.notificationRepo.ListWithCursor(tenantID, req, filters)
	if err != nil {
		return nil, err
	}

	// Convert notifications to response DTOs
	notificationResponses := make([]dto.NotificationResponse, len(notifications))
	for i, notification := range notifications {
		notificationResponses[i] = s.convertToNotificationResponse(&notification)
	}

	return &dto.NotificationListResponse{
		Notifications: notificationResponses,
		Pagination:    paginationResp,
	}, nil
}

func (s *notificationService) ListUserNotifications(tenantID, userID uint, filters models.NotificationFilters) ([]models.Notification, int64, error) {
	return s.notificationRepo.ListByUserID(tenantID, userID, filters)
}

func (s *notificationService) UpdateNotification(tenantID, id uint, req models.UpdateNotificationRequest) (*models.Notification, error) {
	notification, err := s.notificationRepo.GetByID(tenantID, id)
	if err != nil {
		return nil, err
	}

	// Update fields
	if req.Status != nil {
		notification.Status = *req.Status
	}
	if req.ScheduledAt != nil {
		notification.ScheduledAt = req.ScheduledAt
	}
	if req.Metadata != nil {
		if err := notification.SetMetadata(req.Metadata); err != nil {
			return nil, err
		}
	}

	if err := s.notificationRepo.Update(notification); err != nil {
		return nil, err
	}

	return notification, nil
}

func (s *notificationService) DeleteNotification(tenantID, id uint) error {
	return s.notificationRepo.Delete(tenantID, id)
}

func (s *notificationService) SendNotification(tenantID, id uint) error {
	notification, err := s.notificationRepo.GetWithRelations(tenantID, id)
	if err != nil {
		return err
	}

	if notification.Status != models.StatusPending {
		return errors.New("notification is not in pending status")
	}

	// Process the notification
	return s.processNotification(notification)
}

func (s *notificationService) CancelNotification(tenantID, id uint) error {
	notification, err := s.notificationRepo.GetByID(tenantID, id)
	if err != nil {
		return err
	}

	if notification.Status != models.StatusPending && notification.Status != models.StatusQueued {
		return errors.New("notification cannot be cancelled")
	}

	notification.Status = models.StatusCancelled
	return s.notificationRepo.Update(notification)
}

func (s *notificationService) RetryNotification(tenantID, id uint) error {
	notification, err := s.notificationRepo.GetWithRelations(tenantID, id)
	if err != nil {
		return err
	}

	if !notification.CanRetry() {
		return errors.New("notification cannot be retried")
	}

	// Reset status and process again
	notification.Status = models.StatusPending
	if err := s.notificationRepo.Update(notification); err != nil {
		return err
	}

	return s.processNotification(notification)
}

func (s *notificationService) GetStatistics(tenantID uint, dateFrom, dateTo *time.Time) (*repositories.NotificationStatistics, error) {
	return s.notificationRepo.GetStatistics(tenantID, dateFrom, dateTo)
}

func (s *notificationService) ProcessPendingNotifications(tenantID uint, limit int) error {
	notifications, err := s.notificationRepo.GetPending(tenantID, limit)
	if err != nil {
		return err
	}

	for _, notification := range notifications {
		if err := s.processNotification(&notification); err != nil {
			// Log error but continue processing others
			// TODO: Add proper logging
		}
	}

	return nil
}

func (s *notificationService) ProcessScheduledNotifications(limit int) error {
	notifications, err := s.notificationRepo.GetScheduledForProcessing(time.Now(), limit)
	if err != nil {
		return err
	}

	for _, notification := range notifications {
		if err := s.processNotification(&notification); err != nil {
			// Log error but continue processing others
			// TODO: Add proper logging
		}
	}

	return nil
}

func (s *notificationService) GetUserUnreadCount(tenantID, userID uint) (int64, error) {
	return s.recipientRepo.GetUserUnreadCount(tenantID, userID)
}

func (s *notificationService) processNotification(notification *models.Notification) error {
	// Mark as queued
	log := models.CreateQueuedLog(notification.TenantID, notification.ID, string(notification.Channel))
	if err := s.logRepo.Create(log); err != nil {
		// TODO: Add proper logging
	}

	notification.Status = models.StatusQueued
	if err := s.notificationRepo.Update(notification); err != nil {
		return err
	}

	// Process through delivery service
	return s.deliveryService.ProcessNotification(notification)
}

// convertToNotificationResponse converts a notification model to response DTO
func (s *notificationService) convertToNotificationResponse(notification *models.Notification) dto.NotificationResponse {
	response := dto.NotificationResponse{
		ID:           notification.ID,
		TenantID:     notification.TenantID,
		Type:         notification.Type,
		Channel:      notification.Channel,
		Priority:     notification.Priority,
		Subject:      notification.Subject,
		TemplateID:   notification.TemplateID,
		TemplateData: notification.TemplateData,
		Status:       notification.Status,
		ScheduledAt:  notification.ScheduledAt,
		SentAt:       notification.SentAt,
		DeliveredAt:  notification.DeliveredAt,
		FailedAt:     notification.FailedAt,
		ErrorMessage: notification.ErrorMessage,
		RetryCount:   notification.RetryCount,
		MaxRetries:   notification.MaxRetries,
		Metadata:     notification.Metadata,
		CreatedAt:    notification.CreatedAt,
		UpdatedAt:    notification.UpdatedAt,
	}

	// Convert template if loaded
	if notification.Template != nil {
		response.Template = &dto.NotificationTemplateResponse{
			ID:           notification.Template.ID,
			TenantID:     notification.Template.TenantID,
			Code:         notification.Template.Code,
			Name:         notification.Template.Name,
			Type:         notification.Template.Type,
			Channel:      notification.Template.Channel,
			Description:  notification.Template.Description,
			Variables:    notification.Template.Variables,
			IsActive:     notification.Template.IsActive,
			VersionCount: notification.Template.VersionCount,
		}
	}

	// Convert recipients if loaded
	if len(notification.Recipients) > 0 {
		response.Recipients = make([]dto.NotificationRecipientResponse, len(notification.Recipients))
		for i, recipient := range notification.Recipients {
			response.Recipients[i] = dto.NotificationRecipientResponse{
				ID:               recipient.ID,
				TenantID:         recipient.TenantID,
				NotificationID:   recipient.NotificationID,
				UserID:           recipient.UserID,
				RecipientType:    recipient.RecipientType,
				RecipientAddress: recipient.RecipientAddress,
				DeviceToken:      recipient.DeviceToken,
				Status:           recipient.Status,
				DeliveredAt:      recipient.DeliveredAt,
				ReadAt:           recipient.ReadAt,
				FailedAt:         recipient.FailedAt,
				BouncedAt:        recipient.BouncedAt,
				ErrorMessage:     recipient.ErrorMessage,
				DeliveryInfo:     recipient.DeliveryInfo,
				EngagementData:   recipient.EngagementData,
				CreatedAt:        recipient.CreatedAt,
				UpdatedAt:        recipient.UpdatedAt,
			}
		}
	}

	return response
}

package handlers

import (
	"encoding/json"
	"net/http"
	"os"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// SendGridWebhookHandler handles SendGrid webhook events
type SendGridWebhookHandler struct {
	*BaseWebhookHandler
	verificationKey string
}

// NewSendGridWebhookHandler creates a new SendGrid webhook handler
func NewSendGridWebhookHandler(
	notificationRepo repositories.NotificationRepository,
	recipientRepo repositories.RecipientRepository,
	logRepo repositories.LogRepository,
	webhookService services.WebhookService,
	logger utils.Logger,
) *SendGridWebhookHandler {
	return &SendGridWebhookHandler{
		BaseWebhookHandler: NewBaseWebhookHandler(notificationRepo, recipientRepo, logRepo, webhookService, logger),
		verificationKey:    os.Getenv("SENDGRID_WEBHOOK_VERIFICATION_KEY"),
	}
}

// HandleWebhook handles SendGrid webhook events
func (h *SendGridWebhookHandler) HandleWebhook(c *gin.Context) {
	// Verify signature if verification key is set
	if h.verificationKey != "" && !h.VerifySignature(c) {
		h.logger.Warn("Invalid SendGrid webhook signature")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid signature"})
		return
	}

	// Get tenant ID from header
	tenantID, err := GetTenantIDFromHeader(c)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get tenant ID from header")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing or invalid tenant ID"})
		return
	}

	// Parse webhook events (SendGrid sends an array)
	var events []models.SendGridWebhookEvent
	if err := c.ShouldBindJSON(&events); err != nil {
		h.logger.WithError(err).Error("Failed to parse SendGrid webhook payload")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid payload"})
		return
	}

	// Process each event
	successCount := 0
	failureCount := 0

	for _, event := range events {
		// Store raw event
		rawPayload, _ := json.Marshal(event)
		webhookEvent, err := h.webhookService.StoreWebhookEvent(
			tenantID,
			models.WebhookProviderSendGrid,
			event.Event,
			rawPayload,
		)
		if err != nil {
			h.logger.WithError(err).Error("Failed to store SendGrid webhook event")
			failureCount++
			continue
		}

		// Process event
		if err := h.ProcessEvent(tenantID, &event); err != nil {
			h.logger.WithError(err).WithField("event_id", webhookEvent.ID).Error("Failed to process SendGrid event")
			failureCount++
		} else {
			successCount++
			// Mark webhook event as processed
			h.webhookService.ProcessWebhookEvent(webhookEvent.ID)
		}
	}

	h.logger.WithFields(map[string]interface{}{
		"tenant_id":     tenantID,
		"total_events":  len(events),
		"success_count": successCount,
		"failure_count": failureCount,
	}).Info("Processed SendGrid webhook batch")

	c.JSON(http.StatusOK, gin.H{
		"status":   "ok",
		"received": len(events),
		"success":  successCount,
		"failed":   failureCount,
	})
}

// VerifySignature verifies SendGrid webhook signature
func (h *SendGridWebhookHandler) VerifySignature(c *gin.Context) bool {
	if h.verificationKey == "" {
		return true // Skip verification if key not configured
	}

	signature := c.GetHeader("X-Twilio-Email-Event-Webhook-Signature")
	timestamp := c.GetHeader("X-Twilio-Email-Event-Webhook-Timestamp")
	
	if signature == "" || timestamp == "" {
		return false
	}

	// Read body
	body, err := ReadBody(c)
	if err != nil {
		h.logger.WithError(err).Error("Failed to read request body")
		return false
	}

	// Construct the payload for verification
	payload := timestamp + string(body)
	
	// Verify HMAC signature
	return VerifyHMACSignature([]byte(payload), signature, h.verificationKey)
}

// ProcessEvent processes a single SendGrid event
func (h *SendGridWebhookHandler) ProcessEvent(tenantID uint, event *models.SendGridWebhookEvent) error {
	// Build event data
	eventData := map[string]interface{}{
		"email":         event.Email,
		"timestamp":     event.Timestamp,
		"smtp_id":       event.SMTPId,
		"sg_event_id":   event.SGEventID,
		"sg_message_id": event.SGMessageID,
		"category":      event.Category,
	}

	// Add event-specific data
	switch event.Event {
	case models.SendGridEventBounce:
		eventData["reason"] = event.Reason
		eventData["type"] = event.Type
		eventData["status"] = event.Status
		
	case models.SendGridEventDropped:
		eventData["reason"] = event.Reason
		
	case models.SendGridEventDeferred:
		eventData["response"] = event.Response
		eventData["attempt"] = event.Attempt
		
	case models.SendGridEventOpen:
		eventData["user_agent"] = event.UserAgent
		eventData["ip"] = event.IP
		
	case models.SendGridEventClick:
		eventData["user_agent"] = event.UserAgent
		eventData["ip"] = event.IP
		eventData["url"] = event.URL
		
	case models.SendGridEventSpamReport, models.SendGridEventUnsubscribe:
		// No additional data needed
	}

	// Add custom args if present
	if event.CustomArgs != nil {
		eventData["custom_args"] = event.CustomArgs
	}

	// Process webhook event
	messageID := event.SGMessageID
	recipientAddress := event.Email
	
	return h.ProcessWebhookEvent(
		tenantID,
		models.WebhookProviderSendGrid,
		event.Event,
		&messageID,
		&recipientAddress,
		eventData,
	)
}

// parseSendGridBounceType parses SendGrid bounce type to determine if it's a hard or soft bounce
func parseSendGridBounceType(bounceType string) string {
	bounceType = strings.ToLower(bounceType)
	
	// Hard bounces
	hardBounces := []string{"bounce", "blocked", "invalid", "expired"}
	for _, hard := range hardBounces {
		if strings.Contains(bounceType, hard) {
			return "hard"
		}
	}
	
	// Default to soft bounce
	return "soft"
}
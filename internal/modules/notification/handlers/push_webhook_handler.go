package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// PushWebhookHandler handles webhook callbacks from push notification providers
type PushWebhookHandler struct {
	notificationRepo repositories.NotificationRepository
	recipientRepo    repositories.RecipientRepository
	logRepo          repositories.LogRepository
	pushProvider     services.PushProvider
	logger           utils.Logger
}

// NewPushWebhookHandler creates a new push webhook handler
func NewPushWebhookHandler(
	notificationRepo repositories.NotificationRepository,
	recipientRepo repositories.RecipientRepository,
	logRepo repositories.LogRepository,
	pushProvider services.PushProvider,
	logger utils.Logger,
) *PushWebhookHandler {
	return &PushWebhookHandler{
		notificationRepo: notificationRepo,
		recipientRepo:    recipientRepo,
		logRepo:          logRepo,
		pushProvider:     pushProvider,
		logger:           logger,
	}
}

// FCMWebhook handles FCM delivery receipts and registration token updates
// @Summary FCM Webhook Handler
// @Description Handle FCM webhooks for delivery receipts and token updates
// @Tags push-webhooks
// @Accept json
// @Produce json
// @Param webhook_data body FCMWebhookPayload true "FCM webhook payload"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /webhooks/push/fcm [post]
func (h *PushWebhookHandler) FCMWebhook(c *gin.Context) {
	var payload FCMWebhookPayload
	if err := c.ShouldBindJSON(&payload); err != nil {
		h.logger.WithError(err).Error("Failed to parse FCM webhook payload")
		response.BadRequest(c.Writer, "Invalid webhook payload")
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"message_type": payload.MessageType,
		"message_id":   payload.MessageID,
		"from":         payload.From,
	}).Info("Received FCM webhook")

	// Process different types of FCM messages
	switch payload.MessageType {
	case "send_event":
		if err := h.handleFCMSendEvent(&payload); err != nil {
			h.logger.WithError(err).Error("Failed to handle FCM send event")
			response.InternalServerError(c.Writer, "Failed to process webhook")
			return
		}
	case "registration_token_update":
		if err := h.handleFCMTokenUpdate(&payload); err != nil {
			h.logger.WithError(err).Error("Failed to handle FCM token update")
			response.InternalServerError(c.Writer, "Failed to process webhook")
			return
		}
	default:
		h.logger.WithField("message_type", payload.MessageType).Warn("Unknown FCM webhook message type")
	}

	response.Success(c.Writer, gin.H{"status": "processed"})
}

// APNSWebhook handles APNS delivery receipts and feedback
// @Summary APNS Webhook Handler
// @Description Handle APNS webhooks for delivery receipts
// @Tags push-webhooks
// @Accept json
// @Produce json
// @Param webhook_data body APNSWebhookPayload true "APNS webhook payload"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /webhooks/push/apns [post]
func (h *PushWebhookHandler) APNSWebhook(c *gin.Context) {
	var payload APNSWebhookPayload
	if err := c.ShouldBindJSON(&payload); err != nil {
		h.logger.WithError(err).Error("Failed to parse APNS webhook payload")
		response.BadRequest(c.Writer, "Invalid webhook payload")
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"event_type":   payload.EventType,
		"apns_id":      payload.ApnsID,
		"device_token": payload.DeviceToken,
	}).Info("Received APNS webhook")

	// Process APNS events
	switch payload.EventType {
	case "delivered":
		if err := h.handleAPNSDelivered(&payload); err != nil {
			h.logger.WithError(err).Error("Failed to handle APNS delivered event")
			response.InternalServerError(c.Writer, "Failed to process webhook")
			return
		}
	case "failed":
		if err := h.handleAPNSFailed(&payload); err != nil {
			h.logger.WithError(err).Error("Failed to handle APNS failed event")
			response.InternalServerError(c.Writer, "Failed to process webhook")
			return
		}
	default:
		h.logger.WithField("event_type", payload.EventType).Warn("Unknown APNS webhook event type")
	}

	response.Success(c.Writer, gin.H{"status": "processed"})
}

// DeviceTokenUpdate handles device token updates from mobile apps
// @Summary Update Device Token
// @Description Update device token for push notifications
// @Tags push-webhooks
// @Accept json
// @Produce json
// @Param token_data body DeviceTokenUpdateRequest true "Device token update request"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /webhooks/push/token-update [post]
func (h *PushWebhookHandler) DeviceTokenUpdate(c *gin.Context) {
	var request DeviceTokenUpdateRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.WithError(err).Error("Failed to parse device token update request")
		response.BadRequest(c.Writer, "Invalid request payload")
		return
	}

	// Validate the new device token
	if request.NewToken != "" {
		if err := h.pushProvider.ValidateToken(request.NewToken); err != nil {
			h.logger.WithError(err).WithField("token", request.NewToken).Warn("Invalid device token provided")
			response.BadRequest(c.Writer, "Invalid device token")
			return
		}
	}

	// Update device tokens for all recipients with the old token
	if err := h.updateDeviceToken(request.OldToken, request.NewToken, request.Platform); err != nil {
		h.logger.WithError(err).Error("Failed to update device token")
		response.InternalServerError(c.Writer, "Failed to update device token")
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"old_token": request.OldToken,
		"new_token": request.NewToken,
		"platform":  request.Platform,
	}).Info("Device token updated successfully")

	response.Success(c.Writer, gin.H{"updated": true})
}

// handleFCMSendEvent processes FCM send events (delivery confirmations)
func (h *PushWebhookHandler) handleFCMSendEvent(payload *FCMWebhookPayload) error {
	// Extract notification details from FCM data
	notificationID, err := h.extractNotificationIDFromFCMData(payload.Data)
	if err != nil {
		return fmt.Errorf("failed to extract notification ID: %w", err)
	}

	recipientID, err := h.extractRecipientIDFromFCMData(payload.Data)
	if err != nil {
		return fmt.Errorf("failed to extract recipient ID: %w", err)
	}

	// Update recipient status based on FCM event
	recipient, err := h.recipientRepo.GetByID(0, recipientID) // TODO: Get actual tenant ID from payload
	if err != nil {
		return fmt.Errorf("failed to get recipient: %w", err)
	}

	switch payload.EventType {
	case "delivered":
		recipient.MarkAsDelivered()
		if err := h.recipientRepo.Update(recipient); err != nil {
			return fmt.Errorf("failed to update recipient status: %w", err)
		}

		// Log delivery
		logData := map[string]interface{}{
			"provider":     "fcm",
			"message_id":   payload.MessageID,
			"event_type":   payload.EventType,
			"timestamp":    payload.Timestamp,
			"device_token": payload.To,
		}
		log := models.CreateDeliveredLog(recipient.TenantID, notificationID, &recipientID, logData)
		return h.logRepo.Create(log)

	case "failed":
		errorMsg := fmt.Sprintf("FCM delivery failed: %s", payload.ErrorCode)
		recipient.MarkAsFailed(errorMsg)
		if err := h.recipientRepo.Update(recipient); err != nil {
			return fmt.Errorf("failed to update recipient status: %w", err)
		}

		// Log failure
		log := models.CreateFailedLog(recipient.TenantID, notificationID, &recipientID, "FCM_DELIVERY_FAILED", errorMsg)
		return h.logRepo.Create(log)
	}

	return nil
}

// handleFCMTokenUpdate processes FCM token updates
func (h *PushWebhookHandler) handleFCMTokenUpdate(payload *FCMWebhookPayload) error {
	oldToken := payload.From
	newToken := payload.To

	return h.updateDeviceToken(oldToken, newToken, "android")
}

// handleAPNSDelivered processes APNS delivery confirmations
func (h *PushWebhookHandler) handleAPNSDelivered(payload *APNSWebhookPayload) error {
	// Extract notification details from APNS payload
	notificationID, recipientID, err := h.extractNotificationDetailsFromAPNSPayload(payload)
	if err != nil {
		return fmt.Errorf("failed to extract notification details: %w", err)
	}

	// Update recipient status
	recipient, err := h.recipientRepo.GetByID(0, recipientID) // TODO: Get actual tenant ID from payload
	if err != nil {
		return fmt.Errorf("failed to get recipient: %w", err)
	}

	recipient.MarkAsDelivered()
	if err := h.recipientRepo.Update(recipient); err != nil {
		return fmt.Errorf("failed to update recipient status: %w", err)
	}

	// Log delivery
	logData := map[string]interface{}{
		"provider":     "apns",
		"apns_id":      payload.ApnsID,
		"device_token": payload.DeviceToken,
		"timestamp":    payload.Timestamp,
	}
	log := models.CreateDeliveredLog(recipient.TenantID, notificationID, &recipientID, logData)
	return h.logRepo.Create(log)
}

// handleAPNSFailed processes APNS delivery failures
func (h *PushWebhookHandler) handleAPNSFailed(payload *APNSWebhookPayload) error {
	// Extract notification details from APNS payload
	notificationID, recipientID, err := h.extractNotificationDetailsFromAPNSPayload(payload)
	if err != nil {
		return fmt.Errorf("failed to extract notification details: %w", err)
	}

	// Update recipient status
	recipient, err := h.recipientRepo.GetByID(0, recipientID) // TODO: Get actual tenant ID from payload
	if err != nil {
		return fmt.Errorf("failed to get recipient: %w", err)
	}

	errorMsg := fmt.Sprintf("APNS delivery failed: %s - %s", payload.StatusCode, payload.Reason)
	recipient.MarkAsFailed(errorMsg)
	if err := h.recipientRepo.Update(recipient); err != nil {
		return fmt.Errorf("failed to update recipient status: %w", err)
	}

	// Log failure
	log := models.CreateFailedLog(recipient.TenantID, notificationID, &recipientID, "APNS_DELIVERY_FAILED", errorMsg)
	return h.logRepo.Create(log)
}

// updateDeviceToken updates device tokens across all recipients
func (h *PushWebhookHandler) updateDeviceToken(oldToken, newToken, platform string) error {
	// Find all recipients with the old token
	recipients, err := h.recipientRepo.GetByDeviceToken(oldToken)
	if err != nil {
		return fmt.Errorf("failed to find recipients with token: %w", err)
	}

	// Update each recipient
	for _, recipient := range recipients {
		if newToken == "" {
			// Token invalidated - mark as null
			recipient.DeviceToken = nil
		} else {
			// Update to new token
			recipient.DeviceToken = &newToken
		}

		if err := h.recipientRepo.Update(&recipient); err != nil {
			h.logger.WithError(err).WithField("recipient_id", recipient.ID).Error("Failed to update recipient token")
			continue
		}
	}

	return nil
}

// Helper functions to extract data from webhook payloads
func (h *PushWebhookHandler) extractNotificationIDFromFCMData(data map[string]string) (uint, error) {
	if idStr, ok := data["notification_id"]; ok {
		if id, err := strconv.ParseUint(idStr, 10, 32); err == nil {
			return uint(id), nil
		}
	}
	return 0, fmt.Errorf("notification_id not found in FCM data")
}

func (h *PushWebhookHandler) extractRecipientIDFromFCMData(data map[string]string) (uint, error) {
	if idStr, ok := data["recipient_id"]; ok {
		if id, err := strconv.ParseUint(idStr, 10, 32); err == nil {
			return uint(id), nil
		}
	}
	return 0, fmt.Errorf("recipient_id not found in FCM data")
}

func (h *PushWebhookHandler) extractNotificationDetailsFromAPNSPayload(payload *APNSWebhookPayload) (notificationID, recipientID uint, err error) {
	// This would need to be implemented based on how you structure APNS custom data
	// For now, return error indicating it needs implementation
	return 0, 0, fmt.Errorf("APNS notification detail extraction not implemented")
}

// Webhook payload structures

// FCMWebhookPayload represents FCM webhook payload
type FCMWebhookPayload struct {
	MessageType string            `json:"message_type"`
	MessageID   string            `json:"message_id"`
	From        string            `json:"from"`
	To          string            `json:"to"`
	EventType   string            `json:"event_type"`
	ErrorCode   string            `json:"error_code,omitempty"`
	Data        map[string]string `json:"data,omitempty"`
	Timestamp   time.Time         `json:"timestamp"`
}

// APNSWebhookPayload represents APNS webhook payload
type APNSWebhookPayload struct {
	EventType   string                 `json:"event_type"`
	ApnsID      string                 `json:"apns_id"`
	DeviceToken string                 `json:"device_token"`
	StatusCode  string                 `json:"status_code,omitempty"`
	Reason      string                 `json:"reason,omitempty"`
	Timestamp   time.Time              `json:"timestamp"`
	Payload     map[string]interface{} `json:"payload,omitempty"`
}

// DeviceTokenUpdateRequest represents device token update request
type DeviceTokenUpdateRequest struct {
	OldToken string `json:"old_token" binding:"required"`
	NewToken string `json:"new_token"` // Empty string means token is invalidated
	Platform string `json:"platform" binding:"required,oneof=ios android"`
	UserID   *uint  `json:"user_id,omitempty"`
}

// PushTestRequest represents a request to test push notifications
type PushTestRequest struct {
	DeviceToken string                 `json:"device_token" binding:"required"`
	Platform    string                 `json:"platform" binding:"required,oneof=ios android"`
	Title       string                 `json:"title" binding:"required"`
	Body        string                 `json:"body" binding:"required"`
	Data        map[string]interface{} `json:"data,omitempty"`
	Sound       string                 `json:"sound,omitempty"`
	Badge       *int                   `json:"badge,omitempty"`
	ClickAction string                 `json:"click_action,omitempty"`
	CollapseID  string                 `json:"collapse_id,omitempty"`
}

// TestPushNotification sends a test push notification
// @Summary Test Push Notification
// @Description Send a test push notification to verify configuration
// @Tags push-webhooks
// @Accept json
// @Produce json
// @Param test_data body PushTestRequest true "Test push notification request"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /webhooks/push/test [post]
func (h *PushWebhookHandler) TestPushNotification(c *gin.Context) {
	var request PushTestRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		response.BadRequest(c.Writer, "Invalid request payload")
		return
	}

	// Create a mock notification for testing
	notification := &models.Notification{
		Subject:  request.Title,
		Channel:  models.ChannelPush,
		Priority: models.PriorityNormal,
	}

	// Create a mock recipient
	recipient := &models.NotificationRecipient{
		DeviceToken:      &request.DeviceToken,
		RecipientType:    models.RecipientTypeDevice,
		RecipientAddress: request.DeviceToken,
	}

	// Prepare metadata
	metadata := map[string]interface{}{
		"test":     true,
		"platform": request.Platform,
	}

	if request.Data != nil {
		for key, value := range request.Data {
			metadata[key] = value
		}
	}

	if request.Sound != "" {
		metadata["sound"] = request.Sound
	}

	if request.Badge != nil {
		metadata["badge"] = *request.Badge
	}

	if request.ClickAction != "" {
		metadata["click_action"] = request.ClickAction
	}

	if request.CollapseID != "" {
		metadata["collapse_id"] = request.CollapseID
	}

	// Send test push notification
	if err := h.pushProvider.SendPush(recipient, notification, request.Body, metadata); err != nil {
		h.logger.WithError(err).Error("Failed to send test push notification")
		response.InternalServerError(c.Writer, "Failed to send test notification")
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"device_token": request.DeviceToken,
		"platform":     request.Platform,
		"title":        request.Title,
	}).Info("Test push notification sent successfully")

	response.Success(c.Writer, gin.H{
		"sent":         true,
		"device_token": request.DeviceToken,
		"platform":     request.Platform,
	})
}

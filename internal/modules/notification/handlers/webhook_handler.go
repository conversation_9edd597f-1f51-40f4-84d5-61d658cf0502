package handlers

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// WebhookHandler interface defines methods for handling webhook events
type WebhookHandler interface {
	HandleWebhook(c *gin.Context)
	VerifySignature(c *gin.Context) bool
	ProcessEvent(event interface{}) error
}

// BaseWebhookHandler provides common functionality for all webhook handlers
type BaseWebhookHandler struct {
	notificationRepo repositories.NotificationRepository
	recipientRepo    repositories.RecipientRepository
	logRepo          repositories.LogRepository
	webhookService   services.WebhookService
	logger           utils.Logger
}

// NewBaseWebhookHandler creates a new base webhook handler
func NewBaseWebhookHandler(
	notificationRepo repositories.NotificationRepository,
	recipientRepo repositories.RecipientRepository,
	logRepo repositories.LogRepository,
	webhookService services.WebhookService,
	logger utils.Logger,
) *BaseWebhookHandler {
	return &BaseWebhookHandler{
		notificationRepo: notificationRepo,
		recipientRepo:    recipientRepo,
		logRepo:          logRepo,
		webhookService:   webhookService,
		logger:           logger,
	}
}

// ProcessWebhookEvent processes a webhook event and creates appropriate logs
func (h *BaseWebhookHandler) ProcessWebhookEvent(tenantID uint, provider models.WebhookProvider, eventType string, messageID *string, recipientAddress *string, eventData map[string]interface{}) error {
	// Find notification and recipient if messageID is provided
	var notificationID *uint
	var recipientID *uint

	if messageID != nil && *messageID != "" {
		// Try to find notification by external message ID
		notification, err := h.notificationRepo.GetByExternalID(*messageID)
		if err == nil && notification != nil {
			notificationID = &notification.ID
			
			// Try to find recipient by address
			if recipientAddress != nil && *recipientAddress != "" {
				recipients, err := h.recipientRepo.GetByNotificationID(notification.ID)
				if err == nil {
					for _, recipient := range recipients {
						if recipient.RecipientAddress == *recipientAddress {
							recipientID = &recipient.ID
							break
						}
					}
				}
			}
		}
	}

	// Map provider event types to our internal event types
	logEventType := h.mapEventTypeToLogEvent(provider, eventType)
	if logEventType == "" {
		h.logger.WithFields(map[string]interface{}{
			"provider":   provider,
			"event_type": eventType,
		}).Warn("Unknown webhook event type")
		return nil
	}

	// Create notification log
	now := time.Now()
	eventDataJSON, _ := json.Marshal(eventData)
	
	log := &models.NotificationLog{
		TenantID:    tenantID,
		EventType:   logEventType,
		EventData:   json.RawMessage(eventDataJSON),
		ExternalID:  messageID,
		OccurredAt:  now,
	}

	if notificationID != nil {
		log.NotificationID = *notificationID
	}
	if recipientID != nil {
		log.RecipientID = recipientID
	}

	// Set error information for failed events
	if logEventType == models.EventTypeFailed || logEventType == models.EventTypeBounced {
		if errorCode, ok := eventData["error_code"].(string); ok {
			log.ErrorCode = &errorCode
		}
		if errorMessage, ok := eventData["error_message"].(string); ok {
			log.ErrorMessage = &errorMessage
		}
	}

	// Create log entry
	if err := h.logRepo.Create(tenantID, log); err != nil {
		h.logger.WithError(err).Error("Failed to create notification log for webhook event")
		return err
	}

	// Update recipient status if applicable
	if recipientID != nil {
		status := h.mapEventTypeToRecipientStatus(logEventType)
		if status != "" {
			if err := h.recipientRepo.UpdateStatus(*recipientID, status); err != nil {
				h.logger.WithError(err).Error("Failed to update recipient status")
			}
		}
	}

	return nil
}

// mapEventTypeToLogEvent maps provider-specific event types to our internal log event types
func (h *BaseWebhookHandler) mapEventTypeToLogEvent(provider models.WebhookProvider, eventType string) models.LogEventType {
	switch provider {
	case models.WebhookProviderSendGrid:
		return h.mapSendGridEventType(eventType)
	case models.WebhookProviderMailgun:
		return h.mapMailgunEventType(eventType)
	case models.WebhookProviderSES:
		return h.mapSESEventType(eventType)
	case models.WebhookProviderTwilio:
		return h.mapTwilioEventType(eventType)
	default:
		return ""
	}
}

// mapSendGridEventType maps SendGrid event types to internal event types
func (h *BaseWebhookHandler) mapSendGridEventType(eventType string) models.LogEventType {
	switch eventType {
	case models.SendGridEventDelivered:
		return models.EventTypeDelivered
	case models.SendGridEventOpen:
		return models.EventTypeOpened
	case models.SendGridEventClick:
		return models.EventTypeClicked
	case models.SendGridEventBounce:
		return models.EventTypeBounced
	case models.SendGridEventDropped, models.SendGridEventDeferred:
		return models.EventTypeFailed
	case models.SendGridEventSpamReport:
		return models.EventTypeComplaint
	case models.SendGridEventUnsubscribe, models.SendGridEventGroup:
		return models.EventTypeUnsubscribed
	default:
		return ""
	}
}

// mapMailgunEventType maps Mailgun event types to internal event types
func (h *BaseWebhookHandler) mapMailgunEventType(eventType string) models.LogEventType {
	switch eventType {
	case models.MailgunEventDelivered:
		return models.EventTypeDelivered
	case models.MailgunEventOpened:
		return models.EventTypeOpened
	case models.MailgunEventClicked:
		return models.EventTypeClicked
	case models.MailgunEventFailed:
		return models.EventTypeFailed
	case models.MailgunEventUnsubscribed:
		return models.EventTypeUnsubscribed
	case models.MailgunEventComplained:
		return models.EventTypeComplaint
	default:
		return ""
	}
}

// mapSESEventType maps SES event types to internal event types
func (h *BaseWebhookHandler) mapSESEventType(eventType string) models.LogEventType {
	switch eventType {
	case models.SESEventDelivery:
		return models.EventTypeDelivered
	case models.SESEventBounce:
		return models.EventTypeBounced
	case models.SESEventComplaint:
		return models.EventTypeComplaint
	case models.SESEventReject:
		return models.EventTypeFailed
	case models.SESEventOpen:
		return models.EventTypeOpened
	case models.SESEventClick:
		return models.EventTypeClicked
	default:
		return ""
	}
}

// mapTwilioEventType maps Twilio event types to internal event types
func (h *BaseWebhookHandler) mapTwilioEventType(status string) models.LogEventType {
	switch status {
	case models.TwilioStatusDelivered:
		return models.EventTypeDelivered
	case models.TwilioStatusFailed, models.TwilioStatusUndelivered:
		return models.EventTypeFailed
	case models.TwilioStatusSent:
		return models.EventTypeSent
	default:
		return ""
	}
}

// mapEventTypeToRecipientStatus maps log event types to recipient status
func (h *BaseWebhookHandler) mapEventTypeToRecipientStatus(eventType models.LogEventType) models.RecipientStatus {
	switch eventType {
	case models.EventTypeDelivered:
		return models.RecipientStatusDelivered
	case models.EventTypeFailed, models.EventTypeBounced:
		return models.RecipientStatusFailed
	case models.EventTypeOpened:
		return models.RecipientStatusOpened
	case models.EventTypeClicked:
		return models.RecipientStatusClicked
	case models.EventTypeUnsubscribed:
		return models.RecipientStatusUnsubscribed
	default:
		return ""
	}
}

// VerifyHMACSignature verifies HMAC signature
func VerifyHMACSignature(payload []byte, signature, secret string) bool {
	mac := hmac.New(sha256.New, []byte(secret))
	mac.Write(payload)
	expectedSignature := hex.EncodeToString(mac.Sum(nil))
	return hmac.Equal([]byte(signature), []byte(expectedSignature))
}

// ReadBody reads the request body and returns it as bytes
func ReadBody(c *gin.Context) ([]byte, error) {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return nil, err
	}
	// Restore the body for other handlers
	c.Request.Body = io.NopCloser(strings.NewReader(string(body)))
	return body, nil
}

// GetTenantIDFromHeader gets tenant ID from request header
func GetTenantIDFromHeader(c *gin.Context) (uint, error) {
	tenantIDStr := c.GetHeader("X-Tenant-ID")
	if tenantIDStr == "" {
		// Try to get from custom webhook headers
		tenantIDStr = c.GetHeader("X-Custom-Tenant-ID")
	}
	if tenantIDStr == "" {
		return 0, fmt.Errorf("tenant ID not found in headers")
	}
	
	var tenantID uint
	if _, err := fmt.Sscanf(tenantIDStr, "%d", &tenantID); err != nil {
		return 0, fmt.Errorf("invalid tenant ID format")
	}
	
	return tenantID, nil
}
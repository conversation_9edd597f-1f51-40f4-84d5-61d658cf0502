package handlers

import (
	"encoding/json"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// FCMWebhookHandler handles FCM webhook events
// Note: FCM doesn't provide traditional webhooks, but this handler can be used
// for custom FCM status tracking implementations
type FCMWebhookHandler struct {
	*BaseWebhookHandler
}

// FCMWebhookEvent represents a custom FCM webhook event
// This would typically be sent by your own FCM integration service
type FCMWebhookEvent struct {
	MessageID    string    `json:"message_id"`
	DeviceToken  string    `json:"device_token"`
	Status       string    `json:"status"` // success, failed, invalid_token
	Timestamp    time.Time `json:"timestamp"`
	ErrorCode    string    `json:"error_code,omitempty"`
	ErrorMessage string    `json:"error_message,omitempty"`
	Platform     string    `json:"platform,omitempty"` // android, ios
	AppID        string    `json:"app_id,omitempty"`
}

// NewFCMWebhookHandler creates a new FCM webhook handler
func NewFCMWebhookHandler(
	notificationRepo repositories.NotificationRepository,
	recipientRepo repositories.RecipientRepository,
	logRepo repositories.LogRepository,
	webhookService services.WebhookService,
	logger utils.Logger,
) *FCMWebhookHandler {
	return &FCMWebhookHandler{
		BaseWebhookHandler: NewBaseWebhookHandler(notificationRepo, recipientRepo, logRepo, webhookService, logger),
	}
}

// HandleWebhook handles FCM webhook events
func (h *FCMWebhookHandler) HandleWebhook(c *gin.Context) {
	// Get tenant ID from header
	tenantID, err := GetTenantIDFromHeader(c)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get tenant ID from header")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing or invalid tenant ID"})
		return
	}

	// Parse webhook event
	var event FCMWebhookEvent
	if err := c.ShouldBindJSON(&event); err != nil {
		h.logger.WithError(err).Error("Failed to parse FCM webhook payload")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid payload"})
		return
	}

	// Map status to event type
	eventType := h.mapFCMStatusToEventType(event.Status)
	if eventType == "" {
		h.logger.WithField("status", event.Status).Warn("Unknown FCM status")
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
		return
	}

	// Store raw event
	rawPayload, _ := json.Marshal(event)
	webhookEvent, err := h.webhookService.StoreWebhookEvent(
		tenantID,
		models.WebhookProviderFCM,
		eventType,
		rawPayload,
	)
	if err != nil {
		h.logger.WithError(err).Error("Failed to store FCM webhook event")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to store event"})
		return
	}

	// Process event
	if err := h.ProcessEvent(tenantID, &event); err != nil {
		h.logger.WithError(err).WithField("event_id", webhookEvent.ID).Error("Failed to process FCM event")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process event"})
		return
	}

	// Mark webhook event as processed
	h.webhookService.ProcessWebhookEvent(webhookEvent.ID)

	h.logger.WithFields(map[string]interface{}{
		"tenant_id":    tenantID,
		"event_type":   eventType,
		"message_id":   event.MessageID,
		"device_token": event.DeviceToken,
	}).Info("Processed FCM webhook event")

	c.JSON(http.StatusOK, gin.H{"status": "ok"})
}

// VerifySignature verifies FCM webhook signature
// Note: This would depend on your custom FCM integration implementation
func (h *FCMWebhookHandler) VerifySignature(c *gin.Context) bool {
	// Implement based on your FCM integration security requirements
	// For example, you might use an API key or HMAC signature
	return true
}

// ProcessEvent processes a single FCM event
func (h *FCMWebhookHandler) ProcessEvent(tenantID uint, event *FCMWebhookEvent) error {
	// Build event data
	eventData := map[string]interface{}{
		"message_id":   event.MessageID,
		"device_token": event.DeviceToken,
		"timestamp":    event.Timestamp,
		"status":       event.Status,
	}

	// Add optional fields
	if event.Platform != "" {
		eventData["platform"] = event.Platform
	}
	if event.AppID != "" {
		eventData["app_id"] = event.AppID
	}

	// Add error information for failed events
	if event.Status == "failed" || event.Status == "invalid_token" {
		if event.ErrorCode != "" {
			eventData["error_code"] = event.ErrorCode
		}
		if event.ErrorMessage != "" {
			eventData["error_message"] = event.ErrorMessage
		}
	}

	// Map status to event type
	eventType := h.mapFCMStatusToEventType(event.Status)

	// Process webhook event
	messageID := event.MessageID
	recipientAddress := event.DeviceToken

	return h.ProcessWebhookEvent(
		tenantID,
		models.WebhookProviderFCM,
		eventType,
		&messageID,
		&recipientAddress,
		eventData,
	)
}

// mapFCMStatusToEventType maps FCM status to internal event type
func (h *FCMWebhookHandler) mapFCMStatusToEventType(status string) string {
	switch status {
	case "success":
		return "delivered"
	case "failed":
		return "failed"
	case "invalid_token":
		return "invalid_recipient"
	default:
		return ""
	}
}
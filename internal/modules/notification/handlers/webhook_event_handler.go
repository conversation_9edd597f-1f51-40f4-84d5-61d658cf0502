package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// WebhookEventHandler handles webhook event management endpoints
type WebhookEventHandler struct {
	webhookService services.WebhookService
	logger         utils.Logger
}

// NewWebhookEventHandler creates a new webhook event handler
func NewWebhookEventHandler(webhookService services.WebhookService, logger utils.Logger) *WebhookEventHandler {
	return &WebhookEventHandler{
		webhookService: webhookService,
		logger:         logger,
	}
}

// ListWebhookEvents lists webhook events for a tenant
func (h *WebhookEventHandler) ListWebhookEvents(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.BadRequest(c.Writer, "Tenant ID not found")
		return
	}

	// Parse query parameters
	filters := services.WebhookEventFilters{
		Page:  1,
		Limit: 20,
	}

	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			filters.Page = p
		}
	}

	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil && l > 0 && l <= 100 {
			filters.Limit = l
		}
	}

	if provider := c.Query("provider"); provider != "" {
		p := models.WebhookProvider(provider)
		filters.Provider = &p
	}

	if eventType := c.Query("event_type"); eventType != "" {
		filters.EventType = &eventType
	}

	if messageID := c.Query("message_id"); messageID != "" {
		filters.MessageID = &messageID
	}

	if recipientAddress := c.Query("recipient_address"); recipientAddress != "" {
		filters.RecipientAddress = &recipientAddress
	}

	if processedParam := c.Query("processed"); processedParam != "" {
		processed := processedParam == "true"
		filters.ProcessedStatus = &processed
	}

	if startDate := c.Query("start_date"); startDate != "" {
		if t, err := time.Parse(time.RFC3339, startDate); err == nil {
			filters.StartDate = &t
		}
	}

	if endDate := c.Query("end_date"); endDate != "" {
		if t, err := time.Parse(time.RFC3339, endDate); err == nil {
			filters.EndDate = &t
		}
	}

	// Get webhook events
	events, total, err := h.webhookService.GetWebhookEvents(tenantID.(uint), filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get webhook events")
		response.InternalServerError(c.Writer, "Failed to get webhook events")
		return
	}

	// Calculate pagination meta
	totalPages := (int(total) + filters.Limit - 1) / filters.Limit
	hasNextPage := filters.Page < totalPages
	hasPrevPage := filters.Page > 1

	// Convert to response format
	responseEvents := make([]models.WebhookEventResponse, len(events))
	for i, event := range events {
		responseEvents[i] = models.WebhookEventResponse{
			ID:              event.ID,
			Provider:        event.Provider,
			EventType:       event.EventType,
			MessageID:       event.MessageID,
			ProcessedAt:     event.ProcessedAt,
			ProcessingError: event.ProcessingError,
			CreatedAt:       event.CreatedAt,
		}
	}

	response.SuccessWithMeta(c, responseEvents, map[string]interface{}{
		"total":         total,
		"page":          filters.Page,
		"limit":         filters.Limit,
		"total_pages":   totalPages,
		"has_next_page": hasNextPage,
		"has_prev_page": hasPrevPage,
	})
}

// GetWebhookEvent gets a specific webhook event
func (h *WebhookEventHandler) GetWebhookEvent(c *gin.Context) {
	// Get event ID
	eventIDStr := c.Param("id")
	eventID, err := strconv.ParseUint(eventIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid event ID")
		return
	}

	// Get webhook event
	event, err := h.webhookService.GetWebhookEvent(uint(eventID))
	if err != nil {
		response.NotFound(c.Writer, "Webhook event not found")
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists || event.TenantID != tenantID.(uint) {
		response.NotFound(c.Writer, "Webhook event not found")
		return
	}

	response.Success(c, event)
}

// ReprocessWebhookEvent reprocesses a webhook event
func (h *WebhookEventHandler) ReprocessWebhookEvent(c *gin.Context) {
	// Get event ID
	eventIDStr := c.Param("id")
	eventID, err := strconv.ParseUint(eventIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid event ID")
		return
	}

	// Get webhook event
	event, err := h.webhookService.GetWebhookEvent(uint(eventID))
	if err != nil {
		response.NotFound(c.Writer, "Webhook event not found")
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists || event.TenantID != tenantID.(uint) {
		response.NotFound(c.Writer, "Webhook event not found")
		return
	}

	// Reprocess event
	if err := h.webhookService.ProcessWebhookEvent(uint(eventID)); err != nil {
		h.logger.WithError(err).WithField("event_id", eventID).Error("Failed to reprocess webhook event")
		response.InternalServerError(c.Writer, "Failed to reprocess webhook event")
		return
	}

	response.Success(c, gin.H{"message": "Webhook event reprocessed successfully"})
}

// ReprocessFailedEvents reprocesses all failed webhook events
func (h *WebhookEventHandler) ReprocessFailedEvents(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.BadRequest(c.Writer, "Tenant ID not found")
		return
	}

	// Parse since parameter (default to last 24 hours)
	since := time.Now().Add(-24 * time.Hour)
	if sinceParam := c.Query("since"); sinceParam != "" {
		if t, err := time.Parse(time.RFC3339, sinceParam); err == nil {
			since = t
		}
	}

	// Reprocess failed events
	count, err := h.webhookService.ReprocessFailedEvents(tenantID.(uint), since)
	if err != nil {
		h.logger.WithError(err).Error("Failed to reprocess failed webhook events")
		response.InternalServerError(c.Writer, "Failed to reprocess failed webhook events")
		return
	}

	response.Success(c, gin.H{
		"message":         "Failed webhook events reprocessed",
		"processed_count": count,
	})
}
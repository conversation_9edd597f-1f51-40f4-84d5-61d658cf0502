package handlers

import (
	"crypto"
	"crypto/rsa"
	"crypto/sha1"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// SESWebhookHandler handles AWS SES webhook events
type SESWebhookHandler struct {
	*BaseWebhookHandler
	certificateCache map[string]*x509.Certificate
}

// NewSESWebhookHandler creates a new SES webhook handler
func NewSESWebhookHandler(
	notificationRepo repositories.NotificationRepository,
	recipientRepo repositories.RecipientRepository,
	logRepo repositories.LogRepository,
	webhookService services.WebhookService,
	logger utils.Logger,
) *SESWebhookHandler {
	return &SESWebhookHandler{
		BaseWebhookHandler: NewBaseWebhookHandler(notificationRepo, recipientRepo, logRepo, webhookService, logger),
		certificateCache:   make(map[string]*x509.Certificate),
	}
}

// HandleWebhook handles SES webhook events
func (h *SESWebhookHandler) HandleWebhook(c *gin.Context) {
	// AWS SES sends events through SNS, so we need to handle SNS messages
	var snsMessage models.SNSWebhookEvent
	if err := c.ShouldBindJSON(&snsMessage); err != nil {
		h.logger.WithError(err).Error("Failed to parse SNS message")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid payload"})
		return
	}

	// Verify SNS signature
	if !h.VerifySignature(&snsMessage) {
		h.logger.Warn("Invalid SNS signature")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid signature"})
		return
	}

	// Handle subscription confirmation
	if snsMessage.Type == models.SNSNotificationTypeSubscriptionConfirmation {
		h.handleSubscriptionConfirmation(&snsMessage)
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
		return
	}

	// Handle notification
	if snsMessage.Type != models.SNSNotificationTypeNotification {
		h.logger.WithField("type", snsMessage.Type).Warn("Unexpected SNS message type")
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
		return
	}

	// Get tenant ID from header
	tenantID, err := GetTenantIDFromHeader(c)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get tenant ID from header")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing or invalid tenant ID"})
		return
	}

	// Parse SES message
	var sesMessage models.SESMessage
	if err := json.Unmarshal(snsMessage.Message, &sesMessage); err != nil {
		h.logger.WithError(err).Error("Failed to parse SES message")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid SES message"})
		return
	}

	// Store raw event
	rawPayload, _ := json.Marshal(sesMessage)
	webhookEvent, err := h.webhookService.StoreWebhookEvent(
		tenantID,
		models.WebhookProviderSES,
		sesMessage.NotificationType,
		rawPayload,
	)
	if err != nil {
		h.logger.WithError(err).Error("Failed to store SES webhook event")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to store event"})
		return
	}

	// Process event
	if err := h.ProcessEvent(tenantID, &sesMessage); err != nil {
		h.logger.WithError(err).WithField("event_id", webhookEvent.ID).Error("Failed to process SES event")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process event"})
		return
	}

	// Mark webhook event as processed
	h.webhookService.ProcessWebhookEvent(webhookEvent.ID)

	h.logger.WithFields(map[string]interface{}{
		"tenant_id":  tenantID,
		"event_type": sesMessage.NotificationType,
		"message_id": sesMessage.Mail.MessageId,
	}).Info("Processed SES webhook event")

	c.JSON(http.StatusOK, gin.H{"status": "ok"})
}

// VerifySignature verifies SNS message signature
func (h *SESWebhookHandler) VerifySignature(message *models.SNSWebhookEvent) bool {
	// Get certificate
	cert, err := h.getCertificate(message.SigningCertURL)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get signing certificate")
		return false
	}

	// Build string to sign
	var stringToSign string
	if message.Type == models.SNSNotificationTypeSubscriptionConfirmation {
		stringToSign = h.buildSubscriptionStringToSign(message)
	} else {
		stringToSign = h.buildNotificationStringToSign(message)
	}

	// Decode signature
	signature, err := base64.StdEncoding.DecodeString(message.Signature)
	if err != nil {
		h.logger.WithError(err).Error("Failed to decode signature")
		return false
	}

	// Verify signature
	hashed := sha1.Sum([]byte(stringToSign))
	err = rsa.VerifyPKCS1v15(cert.PublicKey.(*rsa.PublicKey), crypto.SHA1, hashed[:], signature)
	return err == nil
}

// getCertificate retrieves and caches the signing certificate
func (h *SESWebhookHandler) getCertificate(url string) (*x509.Certificate, error) {
	// Check cache
	if cert, ok := h.certificateCache[url]; ok {
		return cert, nil
	}

	// Validate URL
	if !strings.HasPrefix(url, "https://sns.") || !strings.HasSuffix(url, ".amazonaws.com/") {
		return nil, fmt.Errorf("invalid certificate URL")
	}

	// Fetch certificate
	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// Parse certificate
	block, _ := pem.Decode(body)
	if block == nil {
		return nil, fmt.Errorf("failed to parse certificate PEM")
	}

	cert, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		return nil, err
	}

	// Cache certificate
	h.certificateCache[url] = cert
	return cert, nil
}

// buildSubscriptionStringToSign builds the string to sign for subscription confirmations
func (h *SESWebhookHandler) buildSubscriptionStringToSign(message *models.SNSWebhookEvent) string {
	var parts []string
	parts = append(parts, "Message", string(message.Message))
	parts = append(parts, "MessageId", message.MessageId)
	if message.Subject != "" {
		parts = append(parts, "Subject", message.Subject)
	}
	parts = append(parts, "SubscribeURL", message.SubscribeURL)
	parts = append(parts, "Timestamp", message.Timestamp)
	parts = append(parts, "Token", message.Token)
	parts = append(parts, "TopicArn", message.TopicArn)
	parts = append(parts, "Type", message.Type)
	return strings.Join(parts, "\n") + "\n"
}

// buildNotificationStringToSign builds the string to sign for notifications
func (h *SESWebhookHandler) buildNotificationStringToSign(message *models.SNSWebhookEvent) string {
	var parts []string
	parts = append(parts, "Message", string(message.Message))
	parts = append(parts, "MessageId", message.MessageId)
	if message.Subject != "" {
		parts = append(parts, "Subject", message.Subject)
	}
	parts = append(parts, "Timestamp", message.Timestamp)
	parts = append(parts, "TopicArn", message.TopicArn)
	parts = append(parts, "Type", message.Type)
	return strings.Join(parts, "\n") + "\n"
}

// handleSubscriptionConfirmation handles SNS subscription confirmation
func (h *SESWebhookHandler) handleSubscriptionConfirmation(message *models.SNSWebhookEvent) {
	// Auto-confirm subscription
	resp, err := http.Get(message.SubscribeURL)
	if err != nil {
		h.logger.WithError(err).Error("Failed to confirm SNS subscription")
		return
	}
	defer resp.Body.Close()

	h.logger.WithFields(map[string]interface{}{
		"topic_arn": message.TopicArn,
		"message_id": message.MessageId,
	}).Info("Confirmed SNS subscription")
}

// ProcessEvent processes a single SES event
func (h *SESWebhookHandler) ProcessEvent(tenantID uint, sesMessage *models.SESMessage) error {
	// Build event data
	eventData := map[string]interface{}{
		"message_id": sesMessage.Mail.MessageId,
		"timestamp":  sesMessage.Mail.Timestamp,
		"source":     sesMessage.Mail.Source,
	}

	// Add event-specific data
	switch sesMessage.NotificationType {
	case models.SESEventBounce:
		if sesMessage.Bounce != nil {
			eventData["bounce_type"] = sesMessage.Bounce.BounceType
			eventData["bounce_subtype"] = sesMessage.Bounce.BounceSubType
			eventData["feedback_id"] = sesMessage.Bounce.FeedbackId
			if sesMessage.Bounce.RemoteMtaIp != "" {
				eventData["remote_mta_ip"] = sesMessage.Bounce.RemoteMtaIp
			}
			
			// Process each bounced recipient
			for _, recipient := range sesMessage.Bounce.BouncedRecipients {
				recipientData := make(map[string]interface{})
				for k, v := range eventData {
					recipientData[k] = v
				}
				recipientData["recipient"] = recipient.EmailAddress
				recipientData["status"] = recipient.Status
				recipientData["action"] = recipient.Action
				if recipient.DiagnosticCode != "" {
					recipientData["diagnostic_code"] = recipient.DiagnosticCode
				}
				
				err := h.ProcessWebhookEvent(
					tenantID,
					models.WebhookProviderSES,
					models.SESEventBounce,
					&sesMessage.Mail.MessageId,
					&recipient.EmailAddress,
					recipientData,
				)
				if err != nil {
					h.logger.WithError(err).WithField("recipient", recipient.EmailAddress).Error("Failed to process bounce recipient")
				}
			}
			return nil
		}
		
	case models.SESEventComplaint:
		if sesMessage.Complaint != nil {
			eventData["feedback_id"] = sesMessage.Complaint.FeedbackId
			if sesMessage.Complaint.ComplaintFeedbackType != "" {
				eventData["complaint_type"] = sesMessage.Complaint.ComplaintFeedbackType
			}
			if sesMessage.Complaint.UserAgent != "" {
				eventData["user_agent"] = sesMessage.Complaint.UserAgent
			}
			
			// Process each complained recipient
			for _, recipient := range sesMessage.Complaint.ComplainedRecipients {
				recipientData := make(map[string]interface{})
				for k, v := range eventData {
					recipientData[k] = v
				}
				recipientData["recipient"] = recipient.EmailAddress
				
				err := h.ProcessWebhookEvent(
					tenantID,
					models.WebhookProviderSES,
					models.SESEventComplaint,
					&sesMessage.Mail.MessageId,
					&recipient.EmailAddress,
					recipientData,
				)
				if err != nil {
					h.logger.WithError(err).WithField("recipient", recipient.EmailAddress).Error("Failed to process complaint recipient")
				}
			}
			return nil
		}
		
	case models.SESEventDelivery:
		if sesMessage.Delivery != nil {
			eventData["processing_time_millis"] = sesMessage.Delivery.ProcessingTimeMillis
			eventData["smtp_response"] = sesMessage.Delivery.SmtpResponse
			if sesMessage.Delivery.RemoteMtaIp != "" {
				eventData["remote_mta_ip"] = sesMessage.Delivery.RemoteMtaIp
			}
			
			// Process each delivered recipient
			for _, recipient := range sesMessage.Delivery.Recipients {
				recipientData := make(map[string]interface{})
				for k, v := range eventData {
					recipientData[k] = v
				}
				recipientData["recipient"] = recipient
				
				err := h.ProcessWebhookEvent(
					tenantID,
					models.WebhookProviderSES,
					models.SESEventDelivery,
					&sesMessage.Mail.MessageId,
					&recipient,
					recipientData,
				)
				if err != nil {
					h.logger.WithError(err).WithField("recipient", recipient).Error("Failed to process delivery recipient")
				}
			}
			return nil
		}
		
	case models.SESEventReject:
		if sesMessage.Reject != nil {
			eventData["reason"] = sesMessage.Reject.Reason
		}
		
	case models.SESEventOpen:
		if sesMessage.Open != nil {
			eventData["ip_address"] = sesMessage.Open.IpAddress
			eventData["user_agent"] = sesMessage.Open.UserAgent
			eventData["timestamp"] = sesMessage.Open.Timestamp
		}
		
	case models.SESEventClick:
		if sesMessage.Click != nil {
			eventData["ip_address"] = sesMessage.Click.IpAddress
			eventData["user_agent"] = sesMessage.Click.UserAgent
			eventData["timestamp"] = sesMessage.Click.Timestamp
			eventData["link"] = sesMessage.Click.Link
			if sesMessage.Click.LinkTags != nil {
				eventData["link_tags"] = sesMessage.Click.LinkTags
			}
		}
		
	case models.SESEventRenderingFailure:
		if sesMessage.Failure != nil {
			eventData["error_message"] = sesMessage.Failure.ErrorMessage
			eventData["template_name"] = sesMessage.Failure.TemplateName
		}
	}

	// If event type doesn't require multiple recipients processing
	if sesMessage.NotificationType != models.SESEventBounce && 
	   sesMessage.NotificationType != models.SESEventComplaint && 
	   sesMessage.NotificationType != models.SESEventDelivery {
		// Use first destination recipient
		var recipientAddress string
		if len(sesMessage.Mail.Destination) > 0 {
			recipientAddress = sesMessage.Mail.Destination[0]
		}
		
		return h.ProcessWebhookEvent(
			tenantID,
			models.WebhookProviderSES,
			sesMessage.NotificationType,
			&sesMessage.Mail.MessageId,
			&recipientAddress,
			eventData,
		)
	}

	return nil
}
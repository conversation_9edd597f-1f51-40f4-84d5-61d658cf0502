package handlers

import (
	"crypto"
	"crypto/rsa"
	"crypto/sha1"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// SNSWebhookHandler handles AWS SNS webhook events
type SNSWebhookHandler struct {
	*BaseWebhookHandler
	certificateCache map[string]*x509.Certificate
}

// SNSNotificationMessage represents a custom notification message sent through SNS
type SNSNotificationMessage struct {
	Type             string    `json:"type"` // sms, push
	MessageID        string    `json:"message_id"`
	RecipientAddress string    `json:"recipient_address"` // phone number or device token
	Status           string    `json:"status"`            // success, failed
	Timestamp        time.Time `json:"timestamp"`
	Provider         string    `json:"provider,omitempty"` // twilio, fcm, apns
	ErrorCode        string    `json:"error_code,omitempty"`
	ErrorMessage     string    `json:"error_message,omitempty"`
	Metadata         map[string]interface{} `json:"metadata,omitempty"`
}

// NewSNSWebhookHandler creates a new SNS webhook handler
func NewSNSWebhookHandler(
	notificationRepo repositories.NotificationRepository,
	recipientRepo repositories.RecipientRepository,
	logRepo repositories.LogRepository,
	webhookService services.WebhookService,
	logger utils.Logger,
) *SNSWebhookHandler {
	return &SNSWebhookHandler{
		BaseWebhookHandler: NewBaseWebhookHandler(notificationRepo, recipientRepo, logRepo, webhookService, logger),
		certificateCache:   make(map[string]*x509.Certificate),
	}
}

// HandleWebhook handles SNS webhook events
func (h *SNSWebhookHandler) HandleWebhook(c *gin.Context) {
	// Parse SNS message
	var snsMessage models.SNSWebhookEvent
	if err := c.ShouldBindJSON(&snsMessage); err != nil {
		h.logger.WithError(err).Error("Failed to parse SNS message")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid payload"})
		return
	}

	// Verify SNS signature
	if !h.VerifySignature(&snsMessage) {
		h.logger.Warn("Invalid SNS signature")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid signature"})
		return
	}

	// Handle subscription confirmation
	if snsMessage.Type == models.SNSNotificationTypeSubscriptionConfirmation {
		h.handleSubscriptionConfirmation(&snsMessage)
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
		return
	}

	// Handle unsubscribe confirmation
	if snsMessage.Type == models.SNSNotificationTypeUnsubscribeConfirmation {
		h.logger.WithField("topic_arn", snsMessage.TopicArn).Info("Received unsubscribe confirmation")
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
		return
	}

	// Handle notification
	if snsMessage.Type != models.SNSNotificationTypeNotification {
		h.logger.WithField("type", snsMessage.Type).Warn("Unexpected SNS message type")
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
		return
	}

	// Get tenant ID from header
	tenantID, err := GetTenantIDFromHeader(c)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get tenant ID from header")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing or invalid tenant ID"})
		return
	}

	// Parse custom notification message
	var notificationMessage SNSNotificationMessage
	if err := json.Unmarshal(snsMessage.Message, &notificationMessage); err != nil {
		h.logger.WithError(err).Error("Failed to parse SNS notification message")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid notification message"})
		return
	}

	// Store raw event
	rawPayload, _ := json.Marshal(notificationMessage)
	webhookEvent, err := h.webhookService.StoreWebhookEvent(
		tenantID,
		models.WebhookProviderSNS,
		notificationMessage.Status,
		rawPayload,
	)
	if err != nil {
		h.logger.WithError(err).Error("Failed to store SNS webhook event")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to store event"})
		return
	}

	// Process event
	if err := h.ProcessEvent(tenantID, &notificationMessage); err != nil {
		h.logger.WithError(err).WithField("event_id", webhookEvent.ID).Error("Failed to process SNS event")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process event"})
		return
	}

	// Mark webhook event as processed
	h.webhookService.ProcessWebhookEvent(webhookEvent.ID)

	h.logger.WithFields(map[string]interface{}{
		"tenant_id":  tenantID,
		"event_type": notificationMessage.Status,
		"message_id": notificationMessage.MessageID,
		"type":       notificationMessage.Type,
		"provider":   notificationMessage.Provider,
	}).Info("Processed SNS webhook event")

	c.JSON(http.StatusOK, gin.H{"status": "ok"})
}

// VerifySignature verifies SNS message signature
func (h *SNSWebhookHandler) VerifySignature(message *models.SNSWebhookEvent) bool {
	// Get certificate
	cert, err := h.getCertificate(message.SigningCertURL)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get signing certificate")
		return false
	}

	// Build string to sign
	var stringToSign string
	switch message.Type {
	case models.SNSNotificationTypeSubscriptionConfirmation:
		stringToSign = h.buildSubscriptionStringToSign(message)
	case models.SNSNotificationTypeUnsubscribeConfirmation:
		stringToSign = h.buildUnsubscribeStringToSign(message)
	case models.SNSNotificationTypeNotification:
		stringToSign = h.buildNotificationStringToSign(message)
	default:
		h.logger.WithField("type", message.Type).Error("Unknown SNS message type")
		return false
	}

	// Decode signature
	signature, err := base64.StdEncoding.DecodeString(message.Signature)
	if err != nil {
		h.logger.WithError(err).Error("Failed to decode signature")
		return false
	}

	// Verify signature
	hashed := sha1.Sum([]byte(stringToSign))
	err = rsa.VerifyPKCS1v15(cert.PublicKey.(*rsa.PublicKey), crypto.SHA1, hashed[:], signature)
	return err == nil
}

// getCertificate retrieves and caches the signing certificate
func (h *SNSWebhookHandler) getCertificate(url string) (*x509.Certificate, error) {
	// Check cache
	if cert, ok := h.certificateCache[url]; ok {
		return cert, nil
	}

	// Validate URL
	if !strings.HasPrefix(url, "https://sns.") || !strings.HasSuffix(url, ".amazonaws.com/") {
		return nil, fmt.Errorf("invalid certificate URL")
	}

	// Fetch certificate
	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// Parse certificate
	block, _ := pem.Decode(body)
	if block == nil {
		return nil, fmt.Errorf("failed to parse certificate PEM")
	}

	cert, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		return nil, err
	}

	// Cache certificate
	h.certificateCache[url] = cert
	return cert, nil
}

// buildSubscriptionStringToSign builds the string to sign for subscription confirmations
func (h *SNSWebhookHandler) buildSubscriptionStringToSign(message *models.SNSWebhookEvent) string {
	var parts []string
	parts = append(parts, "Message", string(message.Message))
	parts = append(parts, "MessageId", message.MessageId)
	if message.Subject != "" {
		parts = append(parts, "Subject", message.Subject)
	}
	parts = append(parts, "SubscribeURL", message.SubscribeURL)
	parts = append(parts, "Timestamp", message.Timestamp)
	parts = append(parts, "Token", message.Token)
	parts = append(parts, "TopicArn", message.TopicArn)
	parts = append(parts, "Type", message.Type)
	return strings.Join(parts, "\n") + "\n"
}

// buildUnsubscribeStringToSign builds the string to sign for unsubscribe confirmations
func (h *SNSWebhookHandler) buildUnsubscribeStringToSign(message *models.SNSWebhookEvent) string {
	var parts []string
	parts = append(parts, "Message", string(message.Message))
	parts = append(parts, "MessageId", message.MessageId)
	if message.Subject != "" {
		parts = append(parts, "Subject", message.Subject)
	}
	parts = append(parts, "SubscribeURL", message.SubscribeURL)
	parts = append(parts, "Timestamp", message.Timestamp)
	parts = append(parts, "Token", message.Token)
	parts = append(parts, "TopicArn", message.TopicArn)
	parts = append(parts, "Type", message.Type)
	return strings.Join(parts, "\n") + "\n"
}

// buildNotificationStringToSign builds the string to sign for notifications
func (h *SNSWebhookHandler) buildNotificationStringToSign(message *models.SNSWebhookEvent) string {
	var parts []string
	parts = append(parts, "Message", string(message.Message))
	parts = append(parts, "MessageId", message.MessageId)
	if message.Subject != "" {
		parts = append(parts, "Subject", message.Subject)
	}
	parts = append(parts, "Timestamp", message.Timestamp)
	parts = append(parts, "TopicArn", message.TopicArn)
	parts = append(parts, "Type", message.Type)
	return strings.Join(parts, "\n") + "\n"
}

// handleSubscriptionConfirmation handles SNS subscription confirmation
func (h *SNSWebhookHandler) handleSubscriptionConfirmation(message *models.SNSWebhookEvent) {
	// Auto-confirm subscription
	resp, err := http.Get(message.SubscribeURL)
	if err != nil {
		h.logger.WithError(err).Error("Failed to confirm SNS subscription")
		return
	}
	defer resp.Body.Close()

	h.logger.WithFields(map[string]interface{}{
		"topic_arn": message.TopicArn,
		"message_id": message.MessageId,
	}).Info("Confirmed SNS subscription")
}

// ProcessEvent processes a single SNS notification event
func (h *SNSWebhookHandler) ProcessEvent(tenantID uint, notification *SNSNotificationMessage) error {
	// Build event data
	eventData := map[string]interface{}{
		"message_id":        notification.MessageID,
		"type":              notification.Type,
		"recipient_address": notification.RecipientAddress,
		"timestamp":         notification.Timestamp,
		"status":            notification.Status,
	}

	// Add optional fields
	if notification.Provider != "" {
		eventData["provider"] = notification.Provider
	}

	// Add metadata if present
	if notification.Metadata != nil {
		eventData["metadata"] = notification.Metadata
	}

	// Add error information for failed events
	if notification.Status == "failed" {
		if notification.ErrorCode != "" {
			eventData["error_code"] = notification.ErrorCode
		}
		if notification.ErrorMessage != "" {
			eventData["error_message"] = notification.ErrorMessage
		}
	}

	// Map status to event type
	eventType := h.mapSNSStatusToEventType(notification.Status)

	// Process webhook event
	messageID := notification.MessageID
	recipientAddress := notification.RecipientAddress

	return h.ProcessWebhookEvent(
		tenantID,
		models.WebhookProviderSNS,
		eventType,
		&messageID,
		&recipientAddress,
		eventData,
	)
}

// mapSNSStatusToEventType maps SNS notification status to internal event type
func (h *SNSWebhookHandler) mapSNSStatusToEventType(status string) string {
	switch status {
	case "success":
		return "delivered"
	case "failed":
		return "failed"
	default:
		return status
	}
}
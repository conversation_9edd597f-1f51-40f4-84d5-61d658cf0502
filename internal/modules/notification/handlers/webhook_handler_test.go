package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// Mock repositories and services
type MockNotificationRepository struct {
	mock.Mock
}

func (m *MockNotificationRepository) GetByExternalID(externalID string) (*models.Notification, error) {
	args := m.Called(externalID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Notification), args.Error(1)
}

type MockRecipientRepository struct {
	mock.Mock
}

func (m *MockRecipientRepository) GetByNotificationID(notificationID uint) ([]*models.Recipient, error) {
	args := m.Called(notificationID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*models.Recipient), args.Error(1)
}

func (m *MockRecipientRepository) UpdateStatus(recipientID uint, status models.RecipientStatus) error {
	args := m.Called(recipientID, status)
	return args.Error(0)
}

type MockLogRepository struct {
	mock.Mock
}

func (m *MockLogRepository) Create(tenantID uint, log *models.NotificationLog) error {
	args := m.Called(tenantID, log)
	return args.Error(0)
}

type MockWebhookService struct {
	mock.Mock
}

func (m *MockWebhookService) StoreWebhookEvent(tenantID uint, provider models.WebhookProvider, eventType string, payload json.RawMessage) (*models.WebhookEvent, error) {
	args := m.Called(tenantID, provider, eventType, payload)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.WebhookEvent), args.Error(1)
}

func (m *MockWebhookService) ProcessWebhookEvent(eventID uint) error {
	args := m.Called(eventID)
	return args.Error(0)
}

func (m *MockWebhookService) GetWebhookEvent(eventID uint) (*models.WebhookEvent, error) {
	args := m.Called(eventID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.WebhookEvent), args.Error(1)
}

func (m *MockWebhookService) GetWebhookEvents(tenantID uint, filters services.WebhookEventFilters) ([]*models.WebhookEvent, int64, error) {
	args := m.Called(tenantID, filters)
	if args.Get(0) == nil {
		return nil, 0, args.Error(2)
	}
	return args.Get(0).([]*models.WebhookEvent), args.Get(1).(int64), args.Error(2)
}

func (m *MockWebhookService) ReprocessFailedEvents(tenantID uint, since time.Time) (int, error) {
	args := m.Called(tenantID, since)
	return args.Get(0).(int), args.Error(1)
}

// Test SendGrid webhook handler
func TestSendGridWebhookHandler(t *testing.T) {
	// Setup
	gin.SetMode(gin.TestMode)
	
	mockNotificationRepo := new(MockNotificationRepository)
	mockRecipientRepo := new(MockRecipientRepository)
	mockLogRepo := new(MockLogRepository)
	mockWebhookService := new(MockWebhookService)
	logger := utils.NewLogger()
	
	handler := NewSendGridWebhookHandler(
		mockNotificationRepo,
		mockRecipientRepo,
		mockLogRepo,
		mockWebhookService,
		logger,
	)
	
	// Test data
	webhookPayload := []models.SendGridWebhookEvent{
		{
			Email:       "<EMAIL>",
			Timestamp:   time.Now().Unix(),
			Event:       models.SendGridEventDelivered,
			SGEventID:   "test-event-id",
			SGMessageID: "test-message-id",
		},
	}
	
	// Mock expectations
	webhookEvent := &models.WebhookEvent{
		ID:       1,
		TenantID: 1,
		Provider: models.WebhookProviderSendGrid,
	}
	
	mockWebhookService.On("StoreWebhookEvent", uint(1), models.WebhookProviderSendGrid, models.SendGridEventDelivered, mock.Anything).Return(webhookEvent, nil)
	mockWebhookService.On("ProcessWebhookEvent", uint(1)).Return(nil)
	
	notification := &models.Notification{
		ID: 100,
	}
	mockNotificationRepo.On("GetByExternalID", "test-message-id").Return(notification, nil)
	
	recipients := []*models.Recipient{
		{
			ID:               200,
			RecipientAddress: "<EMAIL>",
		},
	}
	mockRecipientRepo.On("GetByNotificationID", uint(100)).Return(recipients, nil)
	mockRecipientRepo.On("UpdateStatus", uint(200), models.RecipientStatusDelivered).Return(nil)
	mockLogRepo.On("Create", uint(1), mock.Anything).Return(nil)
	
	// Create request
	body, _ := json.Marshal(webhookPayload)
	req := httptest.NewRequest("POST", "/webhooks/notifications/sendgrid", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Tenant-ID", "1")
	
	// Create response recorder
	w := httptest.NewRecorder()
	
	// Create Gin context
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	
	// Call handler
	handler.HandleWebhook(c)
	
	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "ok", response["status"])
	
	// Verify mocks
	mockWebhookService.AssertExpectations(t)
	mockNotificationRepo.AssertExpectations(t)
	mockRecipientRepo.AssertExpectations(t)
	mockLogRepo.AssertExpectations(t)
}

// Test Twilio webhook handler
func TestTwilioWebhookHandler(t *testing.T) {
	// Setup
	gin.SetMode(gin.TestMode)
	
	mockNotificationRepo := new(MockNotificationRepository)
	mockRecipientRepo := new(MockRecipientRepository)
	mockLogRepo := new(MockLogRepository)
	mockWebhookService := new(MockWebhookService)
	logger := utils.NewLogger()
	
	handler := NewTwilioWebhookHandler(
		mockNotificationRepo,
		mockRecipientRepo,
		mockLogRepo,
		mockWebhookService,
		logger,
	)
	
	// Test data
	formData := "MessageSid=test-message-sid&MessageStatus=delivered&To=%2B**********&From=%2B0987654321&AccountSid=test-account-sid"
	
	// Mock expectations
	webhookEvent := &models.WebhookEvent{
		ID:       1,
		TenantID: 1,
		Provider: models.WebhookProviderTwilio,
	}
	
	mockWebhookService.On("StoreWebhookEvent", uint(1), models.WebhookProviderTwilio, "delivered", mock.Anything).Return(webhookEvent, nil)
	mockWebhookService.On("ProcessWebhookEvent", uint(1)).Return(nil)
	
	notification := &models.Notification{
		ID: 100,
	}
	mockNotificationRepo.On("GetByExternalID", "test-message-sid").Return(notification, nil)
	
	recipients := []*models.Recipient{
		{
			ID:               200,
			RecipientAddress: "+**********",
		},
	}
	mockRecipientRepo.On("GetByNotificationID", uint(100)).Return(recipients, nil)
	mockRecipientRepo.On("UpdateStatus", uint(200), models.RecipientStatusDelivered).Return(nil)
	mockLogRepo.On("Create", uint(1), mock.Anything).Return(nil)
	
	// Create request
	req := httptest.NewRequest("POST", "/webhooks/notifications/twilio", bytes.NewBufferString(formData))
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("X-Tenant-ID", "1")
	
	// Create response recorder
	w := httptest.NewRecorder()
	
	// Create Gin context
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	
	// Call handler
	handler.HandleWebhook(c)
	
	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)
	
	// Verify mocks
	mockWebhookService.AssertExpectations(t)
	mockNotificationRepo.AssertExpectations(t)
	mockRecipientRepo.AssertExpectations(t)
	mockLogRepo.AssertExpectations(t)
}

// Test webhook event handler
func TestWebhookEventHandler_ListWebhookEvents(t *testing.T) {
	// Setup
	gin.SetMode(gin.TestMode)
	
	mockWebhookService := new(MockWebhookService)
	logger := utils.NewLogger()
	
	handler := NewWebhookEventHandler(mockWebhookService, logger)
	
	// Test data
	events := []*models.WebhookEvent{
		{
			ID:        1,
			TenantID:  1,
			Provider:  models.WebhookProviderSendGrid,
			EventType: "delivered",
			CreatedAt: time.Now(),
		},
	}
	
	// Mock expectations
	mockWebhookService.On("GetWebhookEvents", uint(1), mock.Anything).Return(events, int64(1), nil)
	
	// Create request
	req := httptest.NewRequest("GET", "/notification-webhooks/events", nil)
	
	// Create response recorder
	w := httptest.NewRecorder()
	
	// Create Gin context
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Set("tenant_id", uint(1))
	
	// Call handler
	handler.ListWebhookEvents(c)
	
	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)
	
	// Verify mocks
	mockWebhookService.AssertExpectations(t)
}
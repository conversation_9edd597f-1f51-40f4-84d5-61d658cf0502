package handlers

import (
	"encoding/json"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// APNSWebhookHandler handles APNS webhook events
// Note: APNS doesn't provide traditional webhooks, but this handler can be used
// for custom APNS status tracking implementations
type APNSWebhookHandler struct {
	*BaseWebhookHandler
}

// APNSWebhookEvent represents a custom APNS webhook event
// This would typically be sent by your own APNS integration service
type APNSWebhookEvent struct {
	MessageID     string    `json:"message_id"`
	DeviceToken   string    `json:"device_token"`
	Status        string    `json:"status"` // success, failed, invalid_token, expired_token
	Timestamp     time.Time `json:"timestamp"`
	Reason        string    `json:"reason,omitempty"`
	StatusCode    int       `json:"status_code,omitempty"`
	ApnsID        string    `json:"apns_id,omitempty"`
	Priority      int       `json:"priority,omitempty"`
	Topic         string    `json:"topic,omitempty"`
	Expiration    *int64    `json:"expiration,omitempty"`
	CollapseID    string    `json:"collapse_id,omitempty"`
}

// NewAPNSWebhookHandler creates a new APNS webhook handler
func NewAPNSWebhookHandler(
	notificationRepo repositories.NotificationRepository,
	recipientRepo repositories.RecipientRepository,
	logRepo repositories.LogRepository,
	webhookService services.WebhookService,
	logger utils.Logger,
) *APNSWebhookHandler {
	return &APNSWebhookHandler{
		BaseWebhookHandler: NewBaseWebhookHandler(notificationRepo, recipientRepo, logRepo, webhookService, logger),
	}
}

// HandleWebhook handles APNS webhook events
func (h *APNSWebhookHandler) HandleWebhook(c *gin.Context) {
	// Get tenant ID from header
	tenantID, err := GetTenantIDFromHeader(c)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get tenant ID from header")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing or invalid tenant ID"})
		return
	}

	// Parse webhook event
	var event APNSWebhookEvent
	if err := c.ShouldBindJSON(&event); err != nil {
		h.logger.WithError(err).Error("Failed to parse APNS webhook payload")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid payload"})
		return
	}

	// Map status to event type
	eventType := h.mapAPNSStatusToEventType(event.Status)
	if eventType == "" {
		h.logger.WithField("status", event.Status).Warn("Unknown APNS status")
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
		return
	}

	// Store raw event
	rawPayload, _ := json.Marshal(event)
	webhookEvent, err := h.webhookService.StoreWebhookEvent(
		tenantID,
		models.WebhookProviderAPNS,
		eventType,
		rawPayload,
	)
	if err != nil {
		h.logger.WithError(err).Error("Failed to store APNS webhook event")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to store event"})
		return
	}

	// Process event
	if err := h.ProcessEvent(tenantID, &event); err != nil {
		h.logger.WithError(err).WithField("event_id", webhookEvent.ID).Error("Failed to process APNS event")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process event"})
		return
	}

	// Mark webhook event as processed
	h.webhookService.ProcessWebhookEvent(webhookEvent.ID)

	h.logger.WithFields(map[string]interface{}{
		"tenant_id":    tenantID,
		"event_type":   eventType,
		"message_id":   event.MessageID,
		"device_token": event.DeviceToken,
		"apns_id":      event.ApnsID,
	}).Info("Processed APNS webhook event")

	c.JSON(http.StatusOK, gin.H{"status": "ok"})
}

// VerifySignature verifies APNS webhook signature
// Note: This would depend on your custom APNS integration implementation
func (h *APNSWebhookHandler) VerifySignature(c *gin.Context) bool {
	// Implement based on your APNS integration security requirements
	// For example, you might use an API key or HMAC signature
	return true
}

// ProcessEvent processes a single APNS event
func (h *APNSWebhookHandler) ProcessEvent(tenantID uint, event *APNSWebhookEvent) error {
	// Build event data
	eventData := map[string]interface{}{
		"message_id":   event.MessageID,
		"device_token": event.DeviceToken,
		"timestamp":    event.Timestamp,
		"status":       event.Status,
	}

	// Add optional fields
	if event.ApnsID != "" {
		eventData["apns_id"] = event.ApnsID
	}
	if event.Topic != "" {
		eventData["topic"] = event.Topic
	}
	if event.Priority != 0 {
		eventData["priority"] = event.Priority
	}
	if event.CollapseID != "" {
		eventData["collapse_id"] = event.CollapseID
	}
	if event.Expiration != nil {
		eventData["expiration"] = *event.Expiration
	}

	// Add error information for failed events
	if event.Status == "failed" || event.Status == "invalid_token" || event.Status == "expired_token" {
		if event.Reason != "" {
			eventData["reason"] = event.Reason
			eventData["error_message"] = h.getAPNSErrorMessage(event.Reason)
		}
		if event.StatusCode != 0 {
			eventData["status_code"] = event.StatusCode
			eventData["error_code"] = event.Reason
		}
	}

	// Map status to event type
	eventType := h.mapAPNSStatusToEventType(event.Status)

	// Process webhook event
	messageID := event.MessageID
	recipientAddress := event.DeviceToken

	return h.ProcessWebhookEvent(
		tenantID,
		models.WebhookProviderAPNS,
		eventType,
		&messageID,
		&recipientAddress,
		eventData,
	)
}

// mapAPNSStatusToEventType maps APNS status to internal event type
func (h *APNSWebhookHandler) mapAPNSStatusToEventType(status string) string {
	switch status {
	case "success":
		return "delivered"
	case "failed":
		return "failed"
	case "invalid_token", "expired_token":
		return "invalid_recipient"
	default:
		return ""
	}
}

// getAPNSErrorMessage returns human-readable error message for APNS error reasons
func (h *APNSWebhookHandler) getAPNSErrorMessage(reason string) string {
	errorMessages := map[string]string{
		"BadDeviceToken":          "The specified device token was bad",
		"BadExpirationDate":       "The apns-expiration value is bad",
		"BadMessageId":            "The apns-id value is bad",
		"BadPriority":             "The apns-priority value is bad",
		"BadTopic":                "The apns-topic was invalid",
		"DeviceTokenNotForTopic":  "The device token does not match the specified topic",
		"DuplicateHeaders":        "One or more headers were repeated",
		"IdleTimeout":             "Idle time out",
		"InvalidPushType":         "The apns-push-type value is invalid",
		"MissingDeviceToken":      "The device token is not specified in the request",
		"MissingTopic":            "The apns-topic header of the request was not specified",
		"PayloadEmpty":            "The message payload was empty",
		"TopicDisallowed":         "Pushing to this topic is not allowed",
		"BadCertificate":          "The certificate was bad",
		"BadCertificateEnvironment": "The client certificate was for the wrong environment",
		"ExpiredProviderToken":     "The provider token is stale and a new token should be generated",
		"Forbidden":                "The specified action is not allowed",
		"InvalidProviderToken":     "The provider token is not valid",
		"MissingProviderToken":     "No provider certificate was used to connect to APNs",
		"BadPath":                  "The request contained a bad :path value",
		"MethodNotAllowed":         "The specified :method was not POST",
		"Unregistered":             "The device token is inactive for the specified topic",
		"PayloadTooLarge":          "The message payload was too large",
		"TooManyProviderTokenUpdates": "The provider token is being updated too often",
		"TooManyRequests":          "Too many requests were made consecutively to the same device token",
		"InternalServerError":      "An internal server error occurred",
		"ServiceUnavailable":       "The service is unavailable",
		"Shutdown":                 "The server is shutting down",
	}

	if msg, ok := errorMessages[reason]; ok {
		return msg
	}
	return "Unknown error"
}
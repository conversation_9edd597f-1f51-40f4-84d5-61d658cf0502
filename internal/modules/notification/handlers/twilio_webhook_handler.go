package handlers

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"sort"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// TwilioWebhookHandler handles Twilio webhook events
type TwilioWebhookHandler struct {
	*BaseWebhookHandler
	authToken string
}

// NewTwilioWebhookHandler creates a new Twilio webhook handler
func NewTwilioWebhookHandler(
	notificationRepo repositories.NotificationRepository,
	recipientRepo repositories.RecipientRepository,
	logRepo repositories.LogRepository,
	webhookService services.WebhookService,
	logger utils.Logger,
) *TwilioWebhookHandler {
	return &TwilioWebhookHandler{
		BaseWebhookHandler: NewBaseWebhookHandler(notificationRepo, recipientRepo, logRepo, webhookService, logger),
		authToken:          os.Getenv("TWILIO_AUTH_TOKEN"),
	}
}

// HandleWebhook handles Twilio webhook events
func (h *TwilioWebhookHandler) HandleWebhook(c *gin.Context) {
	// Verify signature if auth token is set
	if h.authToken != "" && !h.VerifySignature(c) {
		h.logger.Warn("Invalid Twilio webhook signature")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid signature"})
		return
	}

	// Get tenant ID from header
	tenantID, err := GetTenantIDFromHeader(c)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get tenant ID from header")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing or invalid tenant ID"})
		return
	}

	// Parse webhook event (Twilio sends form data)
	var event models.TwilioWebhookEvent
	if err := c.ShouldBind(&event); err != nil {
		h.logger.WithError(err).Error("Failed to parse Twilio webhook payload")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid payload"})
		return
	}

	// Store raw event
	rawPayload, _ := json.Marshal(event)
	webhookEvent, err := h.webhookService.StoreWebhookEvent(
		tenantID,
		models.WebhookProviderTwilio,
		event.MessageStatus,
		rawPayload,
	)
	if err != nil {
		h.logger.WithError(err).Error("Failed to store Twilio webhook event")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to store event"})
		return
	}

	// Process event
	if err := h.ProcessEvent(tenantID, &event); err != nil {
		h.logger.WithError(err).WithField("event_id", webhookEvent.ID).Error("Failed to process Twilio event")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process event"})
		return
	}

	// Mark webhook event as processed
	h.webhookService.ProcessWebhookEvent(webhookEvent.ID)

	h.logger.WithFields(map[string]interface{}{
		"tenant_id":   tenantID,
		"event_type":  event.MessageStatus,
		"message_sid": event.MessageSid,
		"to":          event.To,
		"from":        event.From,
	}).Info("Processed Twilio webhook event")

	// Twilio expects 200 OK with empty body
	c.Status(http.StatusOK)
}

// VerifySignature verifies Twilio webhook signature
func (h *TwilioWebhookHandler) VerifySignature(c *gin.Context) bool {
	if h.authToken == "" {
		return true // Skip verification if token not configured
	}

	// Get the signature from header
	signature := c.GetHeader("X-Twilio-Signature")
	if signature == "" {
		return false
	}

	// Get the webhook URL
	webhookURL := c.Request.Header.Get("X-Original-URL")
	if webhookURL == "" {
		// Construct URL from request
		scheme := "https"
		if c.Request.TLS == nil {
			scheme = "http"
		}
		webhookURL = fmt.Sprintf("%s://%s%s", scheme, c.Request.Host, c.Request.URL.Path)
	}

	// Get form values
	c.Request.ParseForm()
	params := make(map[string]string)
	for key, values := range c.Request.Form {
		if len(values) > 0 {
			params[key] = values[0]
		}
	}

	// Build the string to sign
	stringToSign := h.buildTwilioStringToSign(webhookURL, params)

	// Calculate expected signature
	mac := hmac.New(sha256.New, []byte(h.authToken))
	mac.Write([]byte(stringToSign))
	expectedSignature := base64.StdEncoding.EncodeToString(mac.Sum(nil))

	// Compare signatures
	return hmac.Equal([]byte(signature), []byte(expectedSignature))
}

// buildTwilioStringToSign builds the string to sign for Twilio webhook verification
func (h *TwilioWebhookHandler) buildTwilioStringToSign(webhookURL string, params map[string]string) string {
	// Start with the URL
	stringToSign := webhookURL

	// Sort the parameter names
	keys := make([]string, 0, len(params))
	for key := range params {
		keys = append(keys, key)
	}
	sort.Strings(keys)

	// Append each parameter
	for _, key := range keys {
		stringToSign += key + params[key]
	}

	return stringToSign
}

// ProcessEvent processes a single Twilio event
func (h *TwilioWebhookHandler) ProcessEvent(tenantID uint, event *models.TwilioWebhookEvent) error {
	// Build event data
	eventData := map[string]interface{}{
		"message_sid":    event.MessageSid,
		"message_status": event.MessageStatus,
		"to":             event.To,
		"from":           event.From,
		"account_sid":    event.AccountSid,
	}

	// Add optional fields
	if event.Body != "" {
		eventData["body"] = event.Body
	}
	if event.NumSegments != "" {
		eventData["num_segments"] = event.NumSegments
	}
	if event.Price != "" {
		eventData["price"] = event.Price
		eventData["price_unit"] = event.PriceUnit
	}
	if event.Direction != "" {
		eventData["direction"] = event.Direction
	}
	if event.DateCreated != "" {
		eventData["date_created"] = event.DateCreated
	}
	if event.DateSent != "" {
		eventData["date_sent"] = event.DateSent
	}
	if event.DateUpdated != "" {
		eventData["date_updated"] = event.DateUpdated
	}

	// Add error information for failed events
	if event.MessageStatus == models.TwilioStatusFailed || event.MessageStatus == models.TwilioStatusUndelivered {
		if event.ErrorCode != "" {
			eventData["error_code"] = event.ErrorCode
			eventData["error_message"] = h.getTwilioErrorMessage(event.ErrorCode)
		}
		if event.ErrorMessage != "" {
			eventData["error_details"] = event.ErrorMessage
		}
	}

	// Process webhook event
	messageID := event.MessageSid
	recipientAddress := event.To

	return h.ProcessWebhookEvent(
		tenantID,
		models.WebhookProviderTwilio,
		event.MessageStatus,
		&messageID,
		&recipientAddress,
		eventData,
	)
}

// getTwilioErrorMessage returns human-readable error message for Twilio error codes
func (h *TwilioWebhookHandler) getTwilioErrorMessage(errorCode string) string {
	errorMessages := map[string]string{
		// Messaging errors
		"30001": "Queue overflow",
				// Authentication errors
		"20003": "Authentication error",
		"20008": "Invalid permission",
		
		// Invalid parameter errors
		"21201": "No 'To' number is specified",
		"21202": "Invalid 'To' phone number",
		"21211": "'To' phone number not verified",
		"21212": "Invalid 'From' phone number",
		"21213": "'From' phone number not verified",
		"21214": "'From' phone number is required",
		"21215": "Invalid 'Body' text",
		"21216": "Account doesn't have international permissions",
		"21217": "Phone number does not appear to be valid",
		
		// Rate limit errors
		"21608": "The 'To' phone number is not currently reachable via SMS",
		"21610": "Message cannot be sent to the 'To' number because the customer has replied with STOP",
		"21611": "This 'From' number has exceeded the maximum allowed SMS message rate",
		"21612": "The 'To' phone number is not currently reachable via SMS",
		"21614": "'To' number is not a valid mobile number",
		"21617": "The concatenated message body exceeds the 1600 character limit",
		
		// Carrier errors
		"30002": "Account suspended",
		"30003": "Unreachable destination handset",
		"30004": "Message blocked by intended recipient",
		"30005": "Unknown destination handset",
		"30006": "Landline or unreachable carrier",
		"30007": "Message filtered by carrier",
		"30008": "Unknown error from carrier",
		"30009": "Missing segment",
		"30010": "Message price exceeds max price",
		"30022": "US A2P 10DLC - Rate Limits Exceeded",
		"30023": "US A2P 10DLC - Daily Message Cap Reached",
		"30034": "US A2P 10DLC - T-Mobile Daily Message Limit Reached",
	}

	if msg, ok := errorMessages[errorCode]; ok {
		return msg
	}
	return fmt.Sprintf("Unknown error code: %s", errorCode)
}
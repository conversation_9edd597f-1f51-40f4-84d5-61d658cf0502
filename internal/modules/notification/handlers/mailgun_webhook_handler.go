package handlers

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// MailgunWebhookHandler handles Mailgun webhook events
type MailgunWebhookHandler struct {
	*BaseWebhookHandler
	signingKey string
}

// NewMailgunWebhookHandler creates a new Mailgun webhook handler
func NewMailgunWebhookHandler(
	notificationRepo repositories.NotificationRepository,
	recipientRepo repositories.RecipientRepository,
	logRepo repositories.LogRepository,
	webhookService services.WebhookService,
	logger utils.Logger,
) *MailgunWebhookHandler {
	return &MailgunWebhookHandler{
		BaseWebhookHandler: NewBaseWebhookHandler(notificationRepo, recipientRepo, logRepo, webhookService, logger),
		signingKey:         os.Getenv("MAILGUN_WEBHOOK_SIGNING_KEY"),
	}
}

// HandleWebhook handles Mailgun webhook events
func (h *MailgunWebhookHandler) HandleWebhook(c *gin.Context) {
	// Verify signature if signing key is set
	if h.signingKey != "" && !h.VerifySignature(c) {
		h.logger.Warn("Invalid Mailgun webhook signature")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid signature"})
		return
	}

	// Get tenant ID from header
	tenantID, err := GetTenantIDFromHeader(c)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get tenant ID from header")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing or invalid tenant ID"})
		return
	}

	// Parse webhook event
	var event models.MailgunWebhookEvent
	if err := c.ShouldBindJSON(&event); err != nil {
		h.logger.WithError(err).Error("Failed to parse Mailgun webhook payload")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid payload"})
		return
	}

	// Store raw event
	rawPayload, _ := json.Marshal(event)
	webhookEvent, err := h.webhookService.StoreWebhookEvent(
		tenantID,
		models.WebhookProviderMailgun,
		event.EventData.Event,
		rawPayload,
	)
	if err != nil {
		h.logger.WithError(err).Error("Failed to store Mailgun webhook event")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to store event"})
		return
	}

	// Process event
	if err := h.ProcessEvent(tenantID, &event); err != nil {
		h.logger.WithError(err).WithField("event_id", webhookEvent.ID).Error("Failed to process Mailgun event")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process event"})
		return
	}

	// Mark webhook event as processed
	h.webhookService.ProcessWebhookEvent(webhookEvent.ID)

	h.logger.WithFields(map[string]interface{}{
		"tenant_id":  tenantID,
		"event_type": event.EventData.Event,
		"message_id": event.EventData.Message.Headers.MessageID,
		"recipient":  event.EventData.Recipient,
	}).Info("Processed Mailgun webhook event")

	c.JSON(http.StatusOK, gin.H{"status": "ok"})
}

// VerifySignature verifies Mailgun webhook signature
func (h *MailgunWebhookHandler) VerifySignature(c *gin.Context) bool {
	if h.signingKey == "" {
		return true // Skip verification if key not configured
	}

	// Parse the event to get signature data
	var event models.MailgunWebhookEvent
	if err := c.ShouldBindJSON(&event); err != nil {
		return false
	}

	// Construct the string to sign
	signatureData := fmt.Sprintf("%s%s", event.Signature.Timestamp, event.Signature.Token)
	
	// Calculate HMAC
	mac := hmac.New(sha256.New, []byte(h.signingKey))
	mac.Write([]byte(signatureData))
	expectedSignature := hex.EncodeToString(mac.Sum(nil))
	
	// Compare signatures
	return hmac.Equal([]byte(event.Signature.Signature), []byte(expectedSignature))
}

// ProcessEvent processes a single Mailgun event
func (h *MailgunWebhookHandler) ProcessEvent(tenantID uint, event *models.MailgunWebhookEvent) error {
	// Build event data
	eventData := map[string]interface{}{
		"event_id":   event.EventData.ID,
		"timestamp":  event.EventData.Timestamp,
		"recipient":  event.EventData.Recipient,
		"message_id": event.EventData.Message.Headers.MessageID,
		"tags":       event.EventData.Tags,
	}

	// Add event-specific data
	switch event.EventData.Event {
	case models.MailgunEventDelivered:
		if event.EventData.DeliveryStatus.Code != "" {
			eventData["smtp_code"] = event.EventData.DeliveryStatus.Code
			eventData["smtp_description"] = event.EventData.DeliveryStatus.Description
			eventData["smtp_message"] = event.EventData.DeliveryStatus.Message
			eventData["mx_host"] = event.EventData.DeliveryStatus.MxHost
		}
		
	case models.MailgunEventFailed:
		eventData["severity"] = event.EventData.Severity
		eventData["reason"] = event.EventData.Reason
		if event.EventData.DeliveryStatus.Code != "" {
			eventData["error_code"] = event.EventData.DeliveryStatus.Code
			eventData["error_message"] = event.EventData.DeliveryStatus.Message
		}
		
	case models.MailgunEventOpened:
		eventData["ip"] = event.EventData.IP
		if event.EventData.Geolocation.Country != "" {
			eventData["country"] = event.EventData.Geolocation.Country
			eventData["region"] = event.EventData.Geolocation.Region
			eventData["city"] = event.EventData.Geolocation.City
		}
		if event.EventData.ClientInfo.ClientName != "" {
			eventData["client_name"] = event.EventData.ClientInfo.ClientName
			eventData["client_type"] = event.EventData.ClientInfo.ClientType
			eventData["client_os"] = event.EventData.ClientInfo.ClientOS
			eventData["device_type"] = event.EventData.ClientInfo.DeviceType
			eventData["user_agent"] = event.EventData.ClientInfo.UserAgent
		}
		
	case models.MailgunEventClicked:
		eventData["url"] = event.EventData.URL
		eventData["ip"] = event.EventData.IP
		if event.EventData.Geolocation.Country != "" {
			eventData["country"] = event.EventData.Geolocation.Country
			eventData["region"] = event.EventData.Geolocation.Region
			eventData["city"] = event.EventData.Geolocation.City
		}
		if event.EventData.ClientInfo.ClientName != "" {
			eventData["client_name"] = event.EventData.ClientInfo.ClientName
			eventData["client_type"] = event.EventData.ClientInfo.ClientType
			eventData["client_os"] = event.EventData.ClientInfo.ClientOS
			eventData["device_type"] = event.EventData.ClientInfo.DeviceType
			eventData["user_agent"] = event.EventData.ClientInfo.UserAgent
		}
		
	case models.MailgunEventComplained:
		// No additional data needed
		
	case models.MailgunEventUnsubscribed:
		if event.EventData.IP != "" {
			eventData["ip"] = event.EventData.IP
		}
		if event.EventData.Campaigns != nil {
			eventData["campaigns"] = event.EventData.Campaigns
		}
	}

	// Add user variables if present
	if event.EventData.UserVariables != nil {
		eventData["user_variables"] = event.EventData.UserVariables
	}

	// Process webhook event
	messageID := event.EventData.Message.Headers.MessageID
	recipientAddress := event.EventData.Recipient
	
	return h.ProcessWebhookEvent(
		tenantID,
		models.WebhookProviderMailgun,
		event.EventData.Event,
		&messageID,
		&recipientAddress,
		eventData,
	)
}
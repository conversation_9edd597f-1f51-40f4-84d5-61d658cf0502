package providers

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"sync"

	"github.com/twilio/twilio-go"
	twilioApi "github.com/twilio/twilio-go/rest/api/v2010"
)

// TwilioSMSProvider implements SMSProvider using Twilio
type TwilioSMSProvider struct {
	client      *twilio.RestClient
	accountSid  string
	authToken   string
	fromNumber  string
	configured  bool
	mu          sync.RWMutex
}

// NewTwilioSMSProvider creates a new Twilio SMS provider
func NewTwilioSMSProvider() *TwilioSMSProvider {
	provider := &TwilioSMSProvider{}
	provider.configure()
	return provider
}

// configure initializes the Twilio client with credentials
func (p *TwilioSMSProvider) configure() {
	p.mu.Lock()
	defer p.mu.Unlock()

	// Get credentials from environment variables
	p.accountSid = os.Getenv("TWILIO_ACCOUNT_SID")
	p.authToken = os.Getenv("TWILIO_AUTH_TOKEN")
	p.fromNumber = os.Getenv("TWILIO_FROM_NUMBER")

	// Check if all required credentials are present
	if p.accountSid == "" || p.authToken == "" || p.fromNumber == "" {
		p.configured = false
		return
	}

	// Create Twilio client
	p.client = twilio.NewRestClientWithParams(twilio.ClientParams{
		Username: p.accountSid,
		Password: p.authToken,
	})

	p.configured = true
}

// SendSMS sends a single SMS message using Twilio
func (p *TwilioSMSProvider) SendSMS(ctx context.Context, message SMSMessage) (*SMSResponse, error) {
	if !p.IsAvailable() {
		return nil, fmt.Errorf("Twilio SMS provider is not configured")
	}

	p.mu.RLock()
	defer p.mu.RUnlock()

	// Create message params
	params := &twilioApi.CreateMessageParams{}
	params.SetTo(message.To)
	params.SetFrom(p.fromNumber)
	params.SetBody(message.Body)

	// Send message
	resp, err := p.client.Api.CreateMessage(params)
	if err != nil {
		errorMsg := err.Error()
		return &SMSResponse{
			Status:   "failed",
			Provider: p.GetProviderName(),
			Error:    &errorMsg,
		}, fmt.Errorf("failed to send SMS via Twilio: %w", err)
	}

	// Parse response
	providerData := make(map[string]interface{})
	if resp.Sid != nil {
		providerData["sid"] = *resp.Sid
	}
	if resp.Status != nil {
		providerData["status"] = *resp.Status
	}
	if resp.DateCreated != nil {
		providerData["date_created"] = *resp.DateCreated
	}
	if resp.DateSent != nil {
		providerData["date_sent"] = *resp.DateSent
	}
	if resp.Price != nil {
		providerData["price"] = *resp.Price
	}
	if resp.PriceUnit != nil {
		providerData["price_unit"] = *resp.PriceUnit
	}
	if resp.ErrorCode != nil {
		providerData["error_code"] = *resp.ErrorCode
	}
	if resp.ErrorMessage != nil {
		providerData["error_message"] = *resp.ErrorMessage
	}

	// Convert full response to JSON for debugging
	if respJSON, err := json.Marshal(resp); err == nil {
		providerData["full_response"] = string(respJSON)
	}

	messageID := ""
	if resp.Sid != nil {
		messageID = *resp.Sid
	}

	status := "sent"
	if resp.Status != nil {
		status = *resp.Status
	}

	return &SMSResponse{
		MessageID:    messageID,
		Status:       status,
		Provider:     p.GetProviderName(),
		ProviderData: providerData,
	}, nil
}

// SendBulkSMS sends multiple SMS messages
func (p *TwilioSMSProvider) SendBulkSMS(ctx context.Context, messages []SMSMessage) ([]*SMSResponse, error) {
	if !p.IsAvailable() {
		return nil, fmt.Errorf("Twilio SMS provider is not configured")
	}

	responses := make([]*SMSResponse, len(messages))
	
	// Send messages concurrently with a limit
	const maxConcurrent = 10
	semaphore := make(chan struct{}, maxConcurrent)
	var wg sync.WaitGroup
	var mu sync.Mutex

	for i, message := range messages {
		wg.Add(1)
		go func(idx int, msg SMSMessage) {
			defer wg.Done()
			
			semaphore <- struct{}{} // Acquire
			defer func() { <-semaphore }() // Release

			resp, err := p.SendSMS(ctx, msg)
			
			mu.Lock()
			if err != nil {
				errorMsg := err.Error()
				responses[idx] = &SMSResponse{
					Status:   "failed",
					Provider: p.GetProviderName(),
					Error:    &errorMsg,
				}
			} else {
				responses[idx] = resp
			}
			mu.Unlock()
		}(i, message)
	}

	wg.Wait()
	return responses, nil
}

// GetMessageStatus retrieves the status of a sent message
func (p *TwilioSMSProvider) GetMessageStatus(ctx context.Context, messageID string) (*SMSResponse, error) {
	if !p.IsAvailable() {
		return nil, fmt.Errorf("Twilio SMS provider is not configured")
	}

	p.mu.RLock()
	defer p.mu.RUnlock()

	// Fetch message details from Twilio
	params := &twilioApi.FetchMessageParams{}
	resp, err := p.client.Api.FetchMessage(messageID, params)
	if err != nil {
		errorMsg := err.Error()
		return &SMSResponse{
			MessageID: messageID,
			Status:    "unknown",
			Provider:  p.GetProviderName(),
			Error:     &errorMsg,
		}, fmt.Errorf("failed to fetch message status from Twilio: %w", err)
	}

	// Parse response
	providerData := make(map[string]interface{})
	if resp.Status != nil {
		providerData["status"] = *resp.Status
	}
	if resp.DateSent != nil {
		providerData["date_sent"] = *resp.DateSent
	}
	if resp.DateUpdated != nil {
		providerData["date_updated"] = *resp.DateUpdated
	}
	if resp.ErrorCode != nil {
		providerData["error_code"] = *resp.ErrorCode
	}
	if resp.ErrorMessage != nil {
		providerData["error_message"] = *resp.ErrorMessage
	}

	status := "unknown"
	if resp.Status != nil {
		status = *resp.Status
	}

	return &SMSResponse{
		MessageID:    messageID,
		Status:       status,
		Provider:     p.GetProviderName(),
		ProviderData: providerData,
	}, nil
}

// GetProviderName returns the name of the SMS provider
func (p *TwilioSMSProvider) GetProviderName() string {
	return "twilio"
}

// IsAvailable checks if the provider is available and configured
func (p *TwilioSMSProvider) IsAvailable() bool {
	p.mu.RLock()
	defer p.mu.RUnlock()
	return p.configured
}
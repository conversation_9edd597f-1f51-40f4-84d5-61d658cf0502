package providers

import (
	"context"
)

// SMSMessage represents an SMS message to be sent
type SMSMessage struct {
	To      string                 `json:"to"`      // Phone number in E.164 format
	From    string                 `json:"from"`    // Sender ID or phone number
	Body    string                 `json:"body"`    // Message content
	Metadata map[string]interface{} `json:"metadata,omitempty"` // Additional metadata
}

// SMSResponse represents the response from sending an SMS
type SMSResponse struct {
	MessageID    string                 `json:"message_id"`
	Status       string                 `json:"status"`
	Provider     string                 `json:"provider"`
	ProviderData map[string]interface{} `json:"provider_data,omitempty"`
	Error        *string                `json:"error,omitempty"`
}

// SMSProvider defines the interface for SMS providers
type SMSProvider interface {
	// SendSMS sends a single SMS message
	SendSMS(ctx context.Context, message SMSMessage) (*SMSResponse, error)

	// SendBulkSMS sends multiple SMS messages
	SendBulkSMS(ctx context.Context, messages []SMSMessage) ([]*SMSResponse, error)

	// GetMessageStatus retrieves the status of a sent message
	GetMessageStatus(ctx context.Context, messageID string) (*SMSResponse, error)

	// GetProviderName returns the name of the SMS provider
	GetProviderName() string

	// IsAvailable checks if the provider is available and configured
	IsAvailable() bool
}
package models

import (
	"encoding/json"
	"time"
)

// WebhookProvider represents different webhook providers
type WebhookProvider string

const (
	WebhookProviderSendGrid WebhookProvider = "sendgrid"
	WebhookProviderMailgun  WebhookProvider = "mailgun"
	WebhookProviderSES      WebhookProvider = "ses"
	WebhookProviderFCM      WebhookProvider = "fcm"
	WebhookProviderAPNS     WebhookProvider = "apns"
	WebhookProviderTwilio   WebhookProvider = "twilio"
	WebhookProviderSNS      WebhookProvider = "sns"
)

// WebhookEvent represents a webhook event from any provider
type WebhookEvent struct {
	ID               uint            `gorm:"primaryKey" json:"id"`
	TenantID         uint            `gorm:"not null;index" json:"tenant_id"`
	Provider         WebhookProvider `gorm:"type:varchar(50);not null;index" json:"provider"`
	EventType        string          `gorm:"type:varchar(100);not null;index" json:"event_type"`
	MessageID        *string         `gorm:"type:varchar(255);index" json:"message_id,omitempty"`
	RecipientAddress *string         `gorm:"type:varchar(255);index" json:"recipient_address,omitempty"`
	RawPayload       json.RawMessage `gorm:"type:json;not null" json:"raw_payload"`
	ProcessedAt      *time.Time      `json:"processed_at,omitempty"`
	ProcessingError  *string         `gorm:"type:text" json:"processing_error,omitempty"`
	CreatedAt        time.Time       `json:"created_at"`
	UpdatedAt        time.Time       `json:"updated_at"`
}

func (WebhookEvent) TableName() string {
	return "notification_webhook_events"
}

// SendGrid webhook event types
const (
	SendGridEventProcessed   = "processed"
	SendGridEventDropped     = "dropped"
	SendGridEventDelivered   = "delivered"
	SendGridEventDeferred    = "deferred"
	SendGridEventBounce      = "bounce"
	SendGridEventOpen        = "open"
	SendGridEventClick       = "click"
	SendGridEventSpamReport  = "spamreport"
	SendGridEventUnsubscribe = "unsubscribe"
	SendGridEventGroup       = "group_unsubscribe"
	SendGridEventGroupResubscribe = "group_resubscribe"
)

// SendGridWebhookEvent represents a SendGrid webhook event
type SendGridWebhookEvent struct {
	Email       string                 `json:"email"`
	Timestamp   int64                  `json:"timestamp"`
	SMTPId      string                 `json:"smtp-id"`
	Event       string                 `json:"event"`
	Category    []string               `json:"category,omitempty"`
	SGEventID   string                 `json:"sg_event_id"`
	SGMessageID string                 `json:"sg_message_id"`
	Response    string                 `json:"response,omitempty"`
	Attempt     string                 `json:"attempt,omitempty"`
	UserAgent   string                 `json:"useragent,omitempty"`
	IP          string                 `json:"ip,omitempty"`
	URL         string                 `json:"url,omitempty"`
	Reason      string                 `json:"reason,omitempty"`
	Status      string                 `json:"status,omitempty"`
	Type        string                 `json:"type,omitempty"`
	TLS         int                    `json:"tls,omitempty"`
	CustomArgs  map[string]interface{} `json:"unique_args,omitempty"`
}

// Mailgun webhook event types
const (
	MailgunEventAccepted     = "accepted"
	MailgunEventDelivered    = "delivered"
	MailgunEventFailed       = "failed"
	MailgunEventOpened       = "opened"
	MailgunEventClicked      = "clicked"
	MailgunEventUnsubscribed = "unsubscribed"
	MailgunEventComplained   = "complained"
	MailgunEventStored       = "stored"
)

// MailgunWebhookEvent represents a Mailgun webhook event
type MailgunWebhookEvent struct {
	Signature struct {
		Timestamp string `json:"timestamp"`
		Token     string `json:"token"`
		Signature string `json:"signature"`
	} `json:"signature"`
	EventData struct {
		Event          string                 `json:"event"`
		Timestamp      float64                `json:"timestamp"`
		ID             string                 `json:"id"`
		Log            map[string]interface{} `json:"log-level,omitempty"`
		Message        MailgunMessage         `json:"message"`
		Recipient      string                 `json:"recipient"`
		RecipientDomain string                `json:"recipient-domain,omitempty"`
		Method         string                 `json:"method,omitempty"`
		Campaigns      []interface{}          `json:"campaigns,omitempty"`
		Tags           []string               `json:"tags,omitempty"`
		UserVariables  map[string]interface{} `json:"user-variables,omitempty"`
		Flags          map[string]bool        `json:"flags,omitempty"`
		DeliveryStatus MailgunDeliveryStatus  `json:"delivery-status,omitempty"`
		Severity       string                 `json:"severity,omitempty"`
		Reason         string                 `json:"reason,omitempty"`
		Geolocation    MailgunGeolocation     `json:"geolocation,omitempty"`
		IP             string                 `json:"ip,omitempty"`
		ClientInfo     MailgunClientInfo      `json:"client-info,omitempty"`
		URL            string                 `json:"url,omitempty"`
	} `json:"event-data"`
}

type MailgunMessage struct {
	Headers struct {
		To        string `json:"to"`
		MessageID string `json:"message-id"`
		From      string `json:"from"`
		Subject   string `json:"subject"`
	} `json:"headers"`
	Attachments []interface{} `json:"attachments,omitempty"`
	Size        int           `json:"size,omitempty"`
}

type MailgunDeliveryStatus struct {
	AttemptNo   int     `json:"attempt-no,omitempty"`
	Code        string  `json:"code,omitempty"`
	Description string  `json:"description,omitempty"`
	Message     string  `json:"message,omitempty"`
	MxHost      string  `json:"mx-host,omitempty"`
	SessionTime float64 `json:"session-seconds,omitempty"`
	Utf8        bool    `json:"utf8,omitempty"`
	TLS         bool    `json:"tls,omitempty"`
}

type MailgunGeolocation struct {
	Country string `json:"country"`
	Region  string `json:"region"`
	City    string `json:"city"`
}

type MailgunClientInfo struct {
	ClientOS   string `json:"client-os,omitempty"`
	DeviceType string `json:"device-type,omitempty"`
	ClientName string `json:"client-name,omitempty"`
	ClientType string `json:"client-type,omitempty"`
	UserAgent  string `json:"user-agent,omitempty"`
}

// AWS SES webhook event types
const (
	SESEventSend       = "send"
	SESEventBounce     = "Bounce"
	SESEventComplaint  = "Complaint"
	SESEventDelivery   = "Delivery"
	SESEventReject     = "Reject"
	SESEventOpen       = "Open"
	SESEventClick      = "Click"
	SESEventRenderingFailure = "Rendering Failure"
	SESEventDeliveryDelay    = "DeliveryDelay"
	SESEventSubscription     = "Subscription"
)

// SESWebhookEvent represents an AWS SES webhook event
type SESWebhookEvent struct {
	Type      string          `json:"Type"`
	MessageId string          `json:"MessageId"`
	TopicArn  string          `json:"TopicArn,omitempty"`
	Subject   string          `json:"Subject,omitempty"`
	Message   json.RawMessage `json:"Message"`
	Timestamp string          `json:"Timestamp"`
	UnsubscribeURL string     `json:"UnsubscribeURL,omitempty"`
}

// SESMessage represents the message content in SES webhook
type SESMessage struct {
	NotificationType string               `json:"notificationType"`
	Mail             SESMail              `json:"mail"`
	Bounce           *SESBounce           `json:"bounce,omitempty"`
	Complaint        *SESComplaint        `json:"complaint,omitempty"`
	Delivery         *SESDelivery         `json:"delivery,omitempty"`
	Send             *interface{}         `json:"send,omitempty"`
	Reject           *SESReject           `json:"reject,omitempty"`
	Open             *SESOpen             `json:"open,omitempty"`
	Click            *SESClick            `json:"click,omitempty"`
	Failure          *SESRenderingFailure `json:"failure,omitempty"`
}

type SESMail struct {
	Timestamp        string            `json:"timestamp"`
	MessageId        string            `json:"messageId"`
	Source           string            `json:"source"`
	SourceArn        string            `json:"sourceArn,omitempty"`
	SourceIp         string            `json:"sourceIp,omitempty"`
	SendingAccountId string            `json:"sendingAccountId,omitempty"`
	Destination      []string          `json:"destination"`
	HeadersTruncated bool              `json:"headersTruncated,omitempty"`
	Headers          []SESHeader       `json:"headers,omitempty"`
	CommonHeaders    SESCommonHeaders  `json:"commonHeaders,omitempty"`
	Tags             map[string]string `json:"tags,omitempty"`
}

type SESHeader struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

type SESCommonHeaders struct {
	From      []string `json:"from,omitempty"`
	To        []string `json:"to,omitempty"`
	MessageId string   `json:"messageId,omitempty"`
	Subject   string   `json:"subject,omitempty"`
}

type SESBounce struct {
	BounceType        string              `json:"bounceType"`
	BounceSubType     string              `json:"bounceSubType,omitempty"`
	BouncedRecipients []SESRecipientInfo  `json:"bouncedRecipients"`
	Timestamp         string              `json:"timestamp"`
	FeedbackId        string              `json:"feedbackId"`
	RemoteMtaIp       string              `json:"remoteMtaIp,omitempty"`
	ReportingMTA      string              `json:"reportingMTA,omitempty"`
}

type SESComplaint struct {
	ComplainedRecipients []SESRecipientInfo `json:"complainedRecipients"`
	Timestamp            string             `json:"timestamp"`
	FeedbackId           string             `json:"feedbackId"`
	UserAgent            string             `json:"userAgent,omitempty"`
	ComplaintFeedbackType string            `json:"complaintFeedbackType,omitempty"`
	ArrivalDate          string             `json:"arrivalDate,omitempty"`
}

type SESDelivery struct {
	Timestamp            string   `json:"timestamp"`
	ProcessingTimeMillis int      `json:"processingTimeMillis"`
	Recipients           []string `json:"recipients"`
	SmtpResponse         string   `json:"smtpResponse"`
	RemoteMtaIp          string   `json:"remoteMtaIp,omitempty"`
	ReportingMTA         string   `json:"reportingMTA,omitempty"`
}

type SESReject struct {
	Reason string `json:"reason"`
}

type SESOpen struct {
	IpAddress string `json:"ipAddress"`
	Timestamp string `json:"timestamp"`
	UserAgent string `json:"userAgent"`
}

type SESClick struct {
	IpAddress string `json:"ipAddress"`
	Timestamp string `json:"timestamp"`
	UserAgent string `json:"userAgent"`
	Link      string `json:"link"`
	LinkTags  map[string]string `json:"linkTags,omitempty"`
}

type SESRenderingFailure struct {
	ErrorMessage string `json:"errorMessage"`
	TemplateName string `json:"templateName"`
}

type SESRecipientInfo struct {
	EmailAddress   string `json:"emailAddress"`
	Action         string `json:"action,omitempty"`
	Status         string `json:"status,omitempty"`
	Error          string `json:"error,omitempty"`
	DiagnosticCode string `json:"diagnosticCode,omitempty"`
}

// Twilio webhook event types
const (
	TwilioStatusQueued      = "queued"
	TwilioStatusFailed      = "failed"
	TwilioStatusSent        = "sent"
	TwilioStatusDelivered   = "delivered"
	TwilioStatusUndelivered = "undelivered"
)

// TwilioWebhookEvent represents a Twilio webhook event
type TwilioWebhookEvent struct {
	MessageSid    string `form:"MessageSid" json:"MessageSid"`
	MessageStatus string `form:"MessageStatus" json:"MessageStatus"`
	To            string `form:"To" json:"To"`
	From          string `form:"From" json:"From"`
	ApiVersion    string `form:"ApiVersion" json:"ApiVersion"`
	AccountSid    string `form:"AccountSid" json:"AccountSid"`
	ErrorCode     string `form:"ErrorCode" json:"ErrorCode,omitempty"`
	ErrorMessage  string `form:"ErrorMessage" json:"ErrorMessage,omitempty"`
	Body          string `form:"Body" json:"Body,omitempty"`
	NumMedia      string `form:"NumMedia" json:"NumMedia,omitempty"`
	NumSegments   string `form:"NumSegments" json:"NumSegments,omitempty"`
	Price         string `form:"Price" json:"Price,omitempty"`
	PriceUnit     string `form:"PriceUnit" json:"PriceUnit,omitempty"`
	Direction     string `form:"Direction" json:"Direction,omitempty"`
	DateCreated   string `form:"DateCreated" json:"DateCreated,omitempty"`
	DateSent      string `form:"DateSent" json:"DateSent,omitempty"`
	DateUpdated   string `form:"DateUpdated" json:"DateUpdated,omitempty"`
}

// AWS SNS webhook event types
const (
	SNSNotificationTypeSubscriptionConfirmation = "SubscriptionConfirmation"
	SNSNotificationTypeNotification             = "Notification"
	SNSNotificationTypeUnsubscribeConfirmation  = "UnsubscribeConfirmation"
)

// SNSWebhookEvent represents an AWS SNS webhook event
type SNSWebhookEvent struct {
	Type             string          `json:"Type"`
	MessageId        string          `json:"MessageId"`
	Token            string          `json:"Token,omitempty"`
	TopicArn         string          `json:"TopicArn"`
	Subject          string          `json:"Subject,omitempty"`
	Message          json.RawMessage `json:"Message"`
	SubscribeURL     string          `json:"SubscribeURL,omitempty"`
	Timestamp        string          `json:"Timestamp"`
	SignatureVersion string          `json:"SignatureVersion"`
	Signature        string          `json:"Signature"`
	SigningCertURL   string          `json:"SigningCertURL"`
	UnsubscribeURL   string          `json:"UnsubscribeURL,omitempty"`
}

// WebhookEventRequest represents a generic webhook event request
type WebhookEventRequest struct {
	Provider WebhookProvider        `json:"provider" binding:"required"`
	Payload  map[string]interface{} `json:"payload" binding:"required"`
}

// WebhookEventResponse represents a webhook event response
type WebhookEventResponse struct {
	ID        uint            `json:"id"`
	Provider  WebhookProvider `json:"provider"`
	EventType string          `json:"event_type"`
	MessageID *string         `json:"message_id,omitempty"`
	ProcessedAt *time.Time    `json:"processed_at,omitempty"`
	ProcessingError *string   `json:"processing_error,omitempty"`
	CreatedAt time.Time       `json:"created_at"`
}
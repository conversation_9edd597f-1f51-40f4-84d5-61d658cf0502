package notification

import (
	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/config"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	tenantServices "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/services"
	tenantRepositories "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

// RegisterRoutes registers notification module routes
func RegisterRoutes(router *gin.RouterGroup, db *gorm.DB, cfg *config.Config, logger utils.Logger) {
	// Initialize repositories
	notificationRepo := repositories.NewNotificationRepository(db)
	recipientRepo := repositories.NewRecipientRepository(db)
	templateRepo := repositories.NewTemplateRepository(db)
	logRepo := repositories.NewLogRepository(db)
	
	// Initialize tenant service for middleware
	tenantRepo := tenantRepositories.NewMySQLTenantRepository(db)
	planRepo := tenantRepositories.NewMySQLTenantPlanRepository(db)
	tenantService := tenantServices.NewTenantService(tenantRepo, planRepo)

	// Initialize services
	templateService := services.NewTemplateService(templateRepo)

	// Initialize providers based on configuration
	emailProvider := services.CreateEmailProvider(&cfg.Notification, logger)
	socketProvider := services.CreateSocketProvider(&cfg.Notification, logger)
	smsProvider := services.CreateSMSProvider(&cfg.Notification, logger)
	
	// Initialize push provider
	pushProviderFactory := services.NewPushProviderFactory(logger)
	pushConfig := services.LoadPushConfigFromEnv()
	pushProvider, err := pushProviderFactory.CreateProvider(pushConfig)
	if err != nil {
		logger.WithError(err).Warn("Failed to create push provider, using mock provider")
		pushProvider = services.NewMockPushProvider(logger)
	}
	
	deliveryService := services.NewDeliveryService(notificationRepo, recipientRepo, templateRepo, logRepo, templateService, emailProvider, socketProvider, smsProvider, pushProvider, logger)
	notificationService := services.NewNotificationService(notificationRepo, recipientRepo, templateRepo, logRepo, deliveryService)

	// Initialize handlers
	notificationHandler := handlers.NewSimpleNotificationHandler(notificationService)
	templateHandler := handlers.NewTemplateHandler(templateService)
	trackingHandler := handlers.NewTrackingHandler(recipientRepo, logRepo)
	pushWebhookHandler := handlers.NewPushWebhookHandler(notificationRepo, recipientRepo, logRepo, pushProvider, logger)

	// Notification routes
	notifications := router.Group("/notifications")
	{
		notifications.POST("", notificationHandler.CreateNotification)
		notifications.GET("", notificationHandler.ListNotifications)
		notifications.GET("/me", notificationHandler.GetUserNotifications)
		notifications.GET("/me/unread-count", notificationHandler.GetUserUnreadCount)
		notifications.GET("/:id", notificationHandler.GetNotification)
		notifications.POST("/:id/send", notificationHandler.SendNotification)
	}

	// Template routes
	templates := router.Group("/templates")
	templates.Use(middleware.TenantIsolationMiddleware(tenantService))
	{
		templates.POST("", templateHandler.CreateTemplate)
		templates.GET("", templateHandler.ListTemplates)
		templates.GET("/:id", templateHandler.GetTemplate)
		templates.PUT("/:id", templateHandler.UpdateTemplate)
		templates.DELETE("/:id", templateHandler.DeleteTemplate)
		templates.POST("/:id/activate", templateHandler.ActivateTemplate)
		templates.POST("/:id/deactivate", templateHandler.DeactivateTemplate)
		templates.POST("/:id/versions", templateHandler.CreateTemplateVersion)
		templates.GET("/:id/versions", templateHandler.ListTemplateVersions)
		templates.PUT("/:id/versions/:version_id", templateHandler.UpdateTemplateVersion)
		templates.POST("/:id/versions/:version_id/activate", templateHandler.ActivateTemplateVersion)
		templates.POST("/:id/versions/:version_id/preview", templateHandler.PreviewTemplate)
	}

	// Tracking routes
	tracking := router.Group("/tracking")
	{
		tracking.POST("/:tenant_id/recipients/:recipient_id/open", trackingHandler.TrackOpen)
		tracking.POST("/:tenant_id/recipients/:recipient_id/click", trackingHandler.TrackClick)
		tracking.GET("/:tenant_id/recipients/:recipient_id/pixel", trackingHandler.GetPixel)
		tracking.GET("/:tenant_id/recipients/:recipient_id/redirect", trackingHandler.RedirectAndTrack)
	}

	// Push notification webhooks and management
	pushRoutes := router.Group("/push")
	{
		pushRoutes.POST("/webhooks/fcm", pushWebhookHandler.FCMWebhook)
		pushRoutes.POST("/webhooks/apns", pushWebhookHandler.APNSWebhook)
		pushRoutes.POST("/webhooks/token-update", pushWebhookHandler.DeviceTokenUpdate)
		pushRoutes.POST("/test", pushWebhookHandler.TestPushNotification)
	}
}

// RegisterWebhookRoutes registers webhook routes for notification providers
func RegisterWebhookRoutes(router *gin.RouterGroup, db *gorm.DB, tenantService tenantServices.TenantService, logger utils.Logger) {
	// Initialize repositories
	notificationRepo := repositories.NewNotificationRepository(db)
	recipientRepo := repositories.NewRecipientRepository(db)
	logRepo := repositories.NewLogRepository(db)
	
	// Initialize webhook service
	webhookService := services.NewWebhookService(db, logger)

	// Initialize webhook handlers
	sendGridHandler := handlers.NewSendGridWebhookHandler(notificationRepo, recipientRepo, logRepo, webhookService, logger)
	mailgunHandler := handlers.NewMailgunWebhookHandler(notificationRepo, recipientRepo, logRepo, webhookService, logger)
	sesHandler := handlers.NewSESWebhookHandler(notificationRepo, recipientRepo, logRepo, webhookService, logger)
	fcmHandler := handlers.NewFCMWebhookHandler(notificationRepo, recipientRepo, logRepo, webhookService, logger)
	apnsHandler := handlers.NewAPNSWebhookHandler(notificationRepo, recipientRepo, logRepo, webhookService, logger)
	twilioHandler := handlers.NewTwilioWebhookHandler(notificationRepo, recipientRepo, logRepo, webhookService, logger)
	snsHandler := handlers.NewSNSWebhookHandler(notificationRepo, recipientRepo, logRepo, webhookService, logger)

	// Webhook routes (these are typically public endpoints)
	webhooks := router.Group("/webhooks/notifications")
	{
		// Email provider webhooks
		webhooks.POST("/sendgrid", sendGridHandler.HandleWebhook)
		webhooks.POST("/mailgun", mailgunHandler.HandleWebhook)
		webhooks.POST("/ses", sesHandler.HandleWebhook)

		// Push notification webhooks
		webhooks.POST("/fcm", fcmHandler.HandleWebhook)
		webhooks.POST("/apns", apnsHandler.HandleWebhook)

		// SMS provider webhooks
		webhooks.POST("/twilio", twilioHandler.HandleWebhook)
		webhooks.POST("/sns", snsHandler.HandleWebhook)
	}
	
	// Webhook management routes (authenticated)
	webhookEventHandler := handlers.NewWebhookEventHandler(webhookService, logger)
	management := router.Group("/notification-webhooks")
	management.Use(middleware.TenantIsolationMiddleware(tenantService))
	{
		management.GET("/events", webhookEventHandler.ListWebhookEvents)
		management.GET("/events/:id", webhookEventHandler.GetWebhookEvent)
		management.POST("/events/:id/reprocess", webhookEventHandler.ReprocessWebhookEvent)
		management.POST("/events/reprocess-failed", webhookEventHandler.ReprocessFailedEvents)
	}
}

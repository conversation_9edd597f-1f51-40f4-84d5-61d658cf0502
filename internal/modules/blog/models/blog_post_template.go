package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

// BlogPostTemplateType represents the type of template
type BlogPostTemplateType string

const (
	// Standard post types
	BlogPostTemplateTypeStandard      BlogPostTemplateType = "standard"
	BlogPostTemplateTypeTutorial      BlogPostTemplateType = "tutorial"
	BlogPostTemplateTypeReview        BlogPostTemplateType = "review"
	BlogPostTemplateTypeInterview     BlogPostTemplateType = "interview"
	BlogPostTemplateTypeNewsArticle   BlogPostTemplateType = "news_article"
	BlogPostTemplateTypeOpinion       BlogPostTemplateType = "opinion"
	BlogPostTemplateTypeCaseStudy     BlogPostTemplateType = "case_study"
	BlogPostTemplateTypeProductLaunch BlogPostTemplateType = "product_launch"
	BlogPostTemplateTypeEvent         BlogPostTemplateType = "event"
	BlogPostTemplateTypeAnnouncement  BlogPostTemplateType = "announcement"
	BlogPostTemplateTypeCustom        BlogPostTemplateType = "custom"
)

// BlogPostTemplateScope represents who can use the template
type BlogPostTemplateScope string

const (
	BlogPostTemplateScopeSystem  BlogPostTemplateScope = "system"  // Available to all tenants
	BlogPostTemplateScopeTenant  BlogPostTemplateScope = "tenant"  // Available to specific tenant
	BlogPostTemplateScopeWebsite BlogPostTemplateScope = "website" // Available to specific website
	BlogPostTemplateScopeUser    BlogPostTemplateScope = "user"    // Private user template
)

// TemplateField represents a dynamic field in the template
type TemplateField struct {
	Name        string      `json:"name"`
	Label       string      `json:"label"`
	Type        string      `json:"type"` // text, textarea, number, date, select, image
	Required    bool        `json:"required"`
	Placeholder string      `json:"placeholder,omitempty"`
	Options     []string    `json:"options,omitempty"` // For select type
	Default     interface{} `json:"default,omitempty"`
	Validation  string      `json:"validation,omitempty"` // Regex pattern
	HelpText    string      `json:"help_text,omitempty"`
}

// TemplateSection represents a section in the template structure
type TemplateSection struct {
	ID          string          `json:"id"`
	Title       string          `json:"title"`
	Description string          `json:"description,omitempty"`
	Fields      []TemplateField `json:"fields"`
	Order       int             `json:"order"`
	Collapsible bool            `json:"collapsible"`
	Collapsed   bool            `json:"collapsed"`
}

// TemplateStructure represents the complete template structure
type TemplateStructure struct {
	Sections []TemplateSection `json:"sections"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// Value implements driver.Valuer interface
func (ts TemplateStructure) Value() (driver.Value, error) {
	return json.Marshal(ts)
}

// Scan implements sql.Scanner interface
func (ts *TemplateStructure) Scan(value interface{}) error {
	if value == nil {
		*ts = TemplateStructure{}
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		bytes = []byte(value.(string))
	}
	
	return json.Unmarshal(bytes, ts)
}

// BlogPostTemplate represents a reusable template for blog posts
type BlogPostTemplate struct {
	ID        uint `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID  *uint `gorm:"index" json:"tenant_id,omitempty"` // Nullable for system templates
	WebsiteID *uint `gorm:"index" json:"website_id,omitempty"` // Nullable for tenant/system templates
	CreatedBy uint  `gorm:"not null;index" json:"created_by"`

	// Basic Information
	Name        string               `gorm:"type:varchar(255);not null" json:"name"`
	Slug        string               `gorm:"type:varchar(255);not null;uniqueIndex:idx_template_slug_scope" json:"slug"`
	Description string               `gorm:"type:text" json:"description,omitempty"`
	Type        BlogPostTemplateType `gorm:"type:varchar(50);not null;default:'standard'" json:"type"`
	Scope       BlogPostTemplateScope `gorm:"type:varchar(20);not null;default:'user';uniqueIndex:idx_template_slug_scope" json:"scope"`
	
	// Template Configuration
	Icon       string `gorm:"type:varchar(100)" json:"icon,omitempty"` // Icon class or URL
	Color      string `gorm:"type:varchar(7)" json:"color,omitempty"`  // Hex color
	IsActive   bool   `gorm:"default:true" json:"is_active"`
	IsFeatured bool   `gorm:"default:false" json:"is_featured"`
	
	// Template Content
	TitleTemplate   string            `gorm:"type:varchar(500)" json:"title_template,omitempty"`   // Template with variables
	ContentTemplate string            `gorm:"type:longtext" json:"content_template,omitempty"`     // Markdown/HTML template
	ExcerptTemplate string            `gorm:"type:text" json:"excerpt_template,omitempty"`
	Structure       TemplateStructure `gorm:"type:json" json:"structure"`                          // Dynamic fields structure
	
	// Default Settings
	DefaultType          BlogPostType   `gorm:"type:varchar(20)" json:"default_type,omitempty"`
	DefaultStatus        BlogPostStatus `gorm:"type:varchar(20)" json:"default_status,omitempty"`
	DefaultCategoryID    *uint          `json:"default_category_id,omitempty"`
	DefaultTags          JSON           `gorm:"type:json" json:"default_tags,omitempty"`
	DefaultAllowComments bool           `gorm:"default:true" json:"default_allow_comments"`
	
	// SEO Templates
	SEOTitleTemplate       string `gorm:"type:varchar(500)" json:"seo_title_template,omitempty"`
	SEODescriptionTemplate string `gorm:"type:text" json:"seo_description_template,omitempty"`
	SEOKeywordsTemplate    string `gorm:"type:text" json:"seo_keywords_template,omitempty"`
	
	// Usage Statistics
	UsageCount uint      `gorm:"default:0" json:"usage_count"`
	LastUsedAt *time.Time `json:"last_used_at,omitempty"`
	
	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName returns the table name for the BlogPostTemplate model
func (BlogPostTemplate) TableName() string {
	return "blog_post_templates"
}

// BlogPostTemplateCreateRequest represents the request to create a blog post template
type BlogPostTemplateCreateRequest struct {
	TenantID             *uint                 `json:"tenant_id,omitempty"`
	WebsiteID            *uint                 `json:"website_id,omitempty"`
	Name                 string                `json:"name" validate:"required,min=1,max=255"`
	Slug                 string                `json:"slug" validate:"required,min=1,max=255"`
	Description          string                `json:"description,omitempty"`
	Type                 BlogPostTemplateType  `json:"type" validate:"required"`
	Scope                BlogPostTemplateScope `json:"scope" validate:"required"`
	Icon                 string                `json:"icon,omitempty"`
	Color                string                `json:"color,omitempty" validate:"omitempty,hexcolor"`
	TitleTemplate        string                `json:"title_template,omitempty"`
	ContentTemplate      string                `json:"content_template,omitempty"`
	ExcerptTemplate      string                `json:"excerpt_template,omitempty"`
	Structure            TemplateStructure     `json:"structure"`
	DefaultType          BlogPostType          `json:"default_type,omitempty"`
	DefaultStatus        BlogPostStatus        `json:"default_status,omitempty"`
	DefaultCategoryID    *uint                 `json:"default_category_id,omitempty"`
	DefaultTags          []string              `json:"default_tags,omitempty"`
	DefaultAllowComments bool                  `json:"default_allow_comments"`
	SEOTitleTemplate     string                `json:"seo_title_template,omitempty"`
	SEODescriptionTemplate string              `json:"seo_description_template,omitempty"`
	SEOKeywordsTemplate  string                `json:"seo_keywords_template,omitempty"`
}

// BlogPostTemplateUpdateRequest represents the request to update a blog post template
type BlogPostTemplateUpdateRequest struct {
	Name                 string                `json:"name,omitempty" validate:"omitempty,min=1,max=255"`
	Description          string                `json:"description,omitempty"`
	Icon                 string                `json:"icon,omitempty"`
	Color                string                `json:"color,omitempty" validate:"omitempty,hexcolor"`
	IsActive             *bool                 `json:"is_active,omitempty"`
	IsFeatured           *bool                 `json:"is_featured,omitempty"`
	TitleTemplate        string                `json:"title_template,omitempty"`
	ContentTemplate      string                `json:"content_template,omitempty"`
	ExcerptTemplate      string                `json:"excerpt_template,omitempty"`
	Structure            *TemplateStructure    `json:"structure,omitempty"`
	DefaultType          BlogPostType          `json:"default_type,omitempty"`
	DefaultStatus        BlogPostStatus        `json:"default_status,omitempty"`
	DefaultCategoryID    *uint                 `json:"default_category_id,omitempty"`
	DefaultTags          []string              `json:"default_tags,omitempty"`
	DefaultAllowComments *bool                 `json:"default_allow_comments,omitempty"`
	SEOTitleTemplate     string                `json:"seo_title_template,omitempty"`
	SEODescriptionTemplate string              `json:"seo_description_template,omitempty"`
	SEOKeywordsTemplate  string                `json:"seo_keywords_template,omitempty"`
}

// BlogPostTemplateResponse represents the response for a blog post template
type BlogPostTemplateResponse struct {
	ID                   uint                  `json:"id"`
	TenantID             *uint                 `json:"tenant_id,omitempty"`
	WebsiteID            *uint                 `json:"website_id,omitempty"`
	CreatedBy            uint                  `json:"created_by"`
	Name                 string                `json:"name"`
	Slug                 string                `json:"slug"`
	Description          string                `json:"description,omitempty"`
	Type                 BlogPostTemplateType  `json:"type"`
	Scope                BlogPostTemplateScope `json:"scope"`
	Icon                 string                `json:"icon,omitempty"`
	Color                string                `json:"color,omitempty"`
	IsActive             bool                  `json:"is_active"`
	IsFeatured           bool                  `json:"is_featured"`
	TitleTemplate        string                `json:"title_template,omitempty"`
	ContentTemplate      string                `json:"content_template,omitempty"`
	ExcerptTemplate      string                `json:"excerpt_template,omitempty"`
	Structure            TemplateStructure     `json:"structure"`
	DefaultType          BlogPostType          `json:"default_type,omitempty"`
	DefaultStatus        BlogPostStatus        `json:"default_status,omitempty"`
	DefaultCategoryID    *uint                 `json:"default_category_id,omitempty"`
	DefaultTags          []string              `json:"default_tags,omitempty"`
	DefaultAllowComments bool                  `json:"default_allow_comments"`
	SEOTitleTemplate     string                `json:"seo_title_template,omitempty"`
	SEODescriptionTemplate string              `json:"seo_description_template,omitempty"`
	SEOKeywordsTemplate  string                `json:"seo_keywords_template,omitempty"`
	UsageCount           uint                  `json:"usage_count"`
	LastUsedAt           *time.Time            `json:"last_used_at,omitempty"`
	CreatedAt            time.Time             `json:"created_at"`
	UpdatedAt            time.Time             `json:"updated_at"`
}

// FromBlogPostTemplate converts BlogPostTemplate to response
func (r *BlogPostTemplateResponse) FromBlogPostTemplate(template *BlogPostTemplate) {
	r.ID = template.ID
	r.TenantID = template.TenantID
	r.WebsiteID = template.WebsiteID
	r.CreatedBy = template.CreatedBy
	r.Name = template.Name
	r.Slug = template.Slug
	r.Description = template.Description
	r.Type = template.Type
	r.Scope = template.Scope
	r.Icon = template.Icon
	r.Color = template.Color
	r.IsActive = template.IsActive
	r.IsFeatured = template.IsFeatured
	r.TitleTemplate = template.TitleTemplate
	r.ContentTemplate = template.ContentTemplate
	r.ExcerptTemplate = template.ExcerptTemplate
	r.Structure = template.Structure
	r.DefaultType = template.DefaultType
	r.DefaultStatus = template.DefaultStatus
	r.DefaultCategoryID = template.DefaultCategoryID
	r.DefaultAllowComments = template.DefaultAllowComments
	r.SEOTitleTemplate = template.SEOTitleTemplate
	r.SEODescriptionTemplate = template.SEODescriptionTemplate
	r.SEOKeywordsTemplate = template.SEOKeywordsTemplate
	r.UsageCount = template.UsageCount
	r.LastUsedAt = template.LastUsedAt
	r.CreatedAt = template.CreatedAt
	r.UpdatedAt = template.UpdatedAt
	
	// Parse default tags from JSON
	if template.DefaultTags != nil {
		// Convert JSON to byte array first
		data, err := json.Marshal(template.DefaultTags)
		if err == nil {
			var tags []string
			if err := json.Unmarshal(data, &tags); err == nil {
				r.DefaultTags = tags
			}
		}
	}
}

// BlogPostTemplateFilter represents filter options for listing templates
type BlogPostTemplateFilter struct {
	TenantID  *uint                 `json:"tenant_id,omitempty"`
	WebsiteID *uint                 `json:"website_id,omitempty"`
	Type      BlogPostTemplateType  `json:"type,omitempty"`
	Scope     BlogPostTemplateScope `json:"scope,omitempty"`
	IsActive  *bool                 `json:"is_active,omitempty"`
	IsFeatured *bool                `json:"is_featured,omitempty"`
	CreatedBy *uint                 `json:"created_by,omitempty"`
	Search    string                `json:"search,omitempty"`
	Limit     int                   `json:"limit,omitempty"`
	Offset    int                   `json:"offset,omitempty"`
}

// BlogPostFromTemplateRequest represents the request to create a post from template
type BlogPostFromTemplateRequest struct {
	TemplateID     uint                   `json:"template_id" validate:"required"`
	FieldValues    map[string]interface{} `json:"field_values"` // Values for template fields
	Title          string                 `json:"title,omitempty"` // Override template title
	Content        string                 `json:"content,omitempty"` // Override template content
	Excerpt        string                 `json:"excerpt,omitempty"` // Override template excerpt
	CategoryID     *uint                  `json:"category_id,omitempty"`
	TagIDs         []uint                 `json:"tag_ids,omitempty"`
	Status         BlogPostStatus         `json:"status,omitempty"`
	ScheduledAt    *time.Time             `json:"scheduled_at,omitempty"`
	IsFeatured     bool                   `json:"is_featured"`
	AllowComments  bool                   `json:"allow_comments"`
	FeaturedImage  string                 `json:"featured_image,omitempty"`
}
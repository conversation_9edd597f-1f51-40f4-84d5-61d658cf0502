package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"
)

type NewsletterFrequency string

const (
	FrequencyInstant NewsletterFrequency = "instant"
	FrequencyDaily   NewsletterFrequency = "daily"
	FrequencyWeekly  NewsletterFrequency = "weekly"
	FrequencyMonthly NewsletterFrequency = "monthly"
)

type NewsletterSubscriptionStatus string

const (
	SubscriptionStatusPending      NewsletterSubscriptionStatus = "pending"
	SubscriptionStatusConfirmed    NewsletterSubscriptionStatus = "confirmed"
	SubscriptionStatusUnsubscribed NewsletterSubscriptionStatus = "unsubscribed"
	SubscriptionStatusBounced      NewsletterSubscriptionStatus = "bounced"
	SubscriptionStatusDeleted      NewsletterSubscriptionStatus = "deleted"
)

type BlogNewsletterSubscription struct {
	ID           uint                         `json:"id" gorm:"primaryKey"`
	TenantID     uint                         `json:"tenant_id" gorm:"not null"`
	WebsiteID    uint                         `json:"website_id" gorm:"not null"`
	Email        string                       `json:"email" gorm:"not null;size:255"`
	Name         *string                      `json:"name,omitempty" gorm:"size:255"`
	Categories   CategoryIDArray              `json:"categories" gorm:"type:json;default:'[]'"`
	Tags         TagIDArray                   `json:"tags" gorm:"type:json;default:'[]'"`
	Frequency    NewsletterFrequency          `json:"frequency" gorm:"type:enum('instant','daily','weekly','monthly');default:'weekly'"`
	Token        string                       `json:"token" gorm:"not null;size:64;uniqueIndex"`
	ConfirmedAt  *time.Time                   `json:"confirmed_at,omitempty"`
	LastSentAt   *time.Time                   `json:"last_sent_at,omitempty"`
	CreatedAt    time.Time                    `json:"created_at"`
	UpdatedAt    time.Time                    `json:"updated_at"`
	Status       NewsletterSubscriptionStatus `json:"status" gorm:"type:enum('pending','confirmed','unsubscribed','bounced','deleted');default:'pending'"`
}

func (BlogNewsletterSubscription) TableName() string {
	return "blog_newsletter_subscriptions"
}

type CategoryIDArray []uint

func (c CategoryIDArray) Value() (driver.Value, error) {
	if c == nil {
		return "[]", nil
	}
	return json.Marshal(c)
}

func (c *CategoryIDArray) Scan(value interface{}) error {
	if value == nil {
		*c = []uint{}
		return nil
	}
	
	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, c)
	case string:
		return json.Unmarshal([]byte(v), c)
	default:
		*c = []uint{}
		return nil
	}
}

type TagIDArray []uint

func (t TagIDArray) Value() (driver.Value, error) {
	if t == nil {
		return "[]", nil
	}
	return json.Marshal(t)
}

func (t *TagIDArray) Scan(value interface{}) error {
	if value == nil {
		*t = []uint{}
		return nil
	}
	
	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, t)
	case string:
		return json.Unmarshal([]byte(v), t)
	default:
		*t = []uint{}
		return nil
	}
}

// BlogNewsletterSubscriptionFilter represents filter criteria for newsletter subscriptions
type BlogNewsletterSubscriptionFilter struct {
	TenantID  uint                         `json:"tenant_id"`
	WebsiteID uint                         `json:"website_id"`
	Status    NewsletterSubscriptionStatus `json:"status,omitempty"`
	Frequency NewsletterFrequency          `json:"frequency,omitempty"`
	Email     string                       `json:"email,omitempty"`
	Search    string                       `json:"search,omitempty"`
	SortBy    string                       `json:"sort_by,omitempty"`
	SortOrder string                       `json:"sort_order,omitempty"`
	Limit     int                          `json:"limit,omitempty"`
	Offset    int                          `json:"offset,omitempty"`
}

// NewsletterSubscriptionStats represents subscription statistics
type NewsletterSubscriptionStats struct {
	TotalSubscribers      int                             `json:"total_subscribers"`
	ConfirmedSubscribers  int                             `json:"confirmed_subscribers"`
	PendingSubscribers    int                             `json:"pending_subscribers"`
	UnsubscribedCount     int                             `json:"unsubscribed_count"`
	BouncedCount          int                             `json:"bounced_count"`
	ByFrequency           map[NewsletterFrequency]int     `json:"by_frequency"`
	ByStatus              map[NewsletterSubscriptionStatus]int `json:"by_status"`
}

// NewsletterGrowthData represents growth data for subscriptions
type NewsletterGrowthData struct {
	Date         time.Time `json:"date"`
	Subscribed   int       `json:"subscribed"`
	Unsubscribed int       `json:"unsubscribed"`
	NetGrowth    int       `json:"net_growth"`
	Total        int       `json:"total"`
}
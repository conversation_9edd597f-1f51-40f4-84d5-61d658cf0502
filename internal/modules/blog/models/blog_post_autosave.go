package models

import (
	"time"

	"gorm.io/gorm"
)

// BlogPostAutosave represents an auto-saved draft of a blog post
type BlogPostAutosave struct {
	ID        uint `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID  uint `gorm:"not null;index" json:"tenant_id"`
	PostID    uint `gorm:"not null;index" json:"post_id"`
	UserID    uint `gorm:"not null;index" json:"user_id"`
	WebsiteID uint `gorm:"not null;index" json:"website_id"`

	// Auto-saved content
	Title         string `gorm:"type:varchar(255)" json:"title,omitempty"`
	Content       string `gorm:"type:longtext" json:"content,omitempty"`
	Excerpt       string `gorm:"type:text" json:"excerpt,omitempty"`
	FeaturedImage string `gorm:"type:varchar(500)" json:"featured_image,omitempty"`

	// Metadata
	Version      uint      `gorm:"not null;default:1" json:"version"`
	IsConflicted bool      `gorm:"default:false" json:"is_conflicted"`
	LastSavedAt  time.Time `gorm:"not null" json:"last_saved_at"`

	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`

	// Relationships
	Post *BlogPost `gorm:"foreignKey:PostID" json:"post,omitempty"`
}

// TableName returns the table name for the BlogPostAutosave model
func (BlogPostAutosave) TableName() string {
	return "blog_post_autosaves"
}

// BlogPostAutosaveCreateRequest represents the request to create or update an auto-save
type BlogPostAutosaveCreateRequest struct {
	PostID        uint   `json:"post_id" validate:"required,min=1"`
	Title         string `json:"title,omitempty" validate:"max=255"`
	Content       string `json:"content,omitempty"`
	Excerpt       string `json:"excerpt,omitempty" validate:"max=1000"`
	FeaturedImage string `json:"featured_image,omitempty" validate:"omitempty,url"`
}

// BlogPostAutosaveResponse represents the response when returning auto-save data
type BlogPostAutosaveResponse struct {
	ID            uint      `json:"id"`
	TenantID      uint      `json:"tenant_id"`
	PostID        uint      `json:"post_id"`
	UserID        uint      `json:"user_id"`
	WebsiteID     uint      `json:"website_id"`
	Title         string    `json:"title,omitempty"`
	Content       string    `json:"content,omitempty"`
	Excerpt       string    `json:"excerpt,omitempty"`
	FeaturedImage string    `json:"featured_image,omitempty"`
	Version       uint      `json:"version"`
	IsConflicted  bool      `json:"is_conflicted"`
	LastSavedAt   time.Time `json:"last_saved_at"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// FromBlogPostAutosave converts a BlogPostAutosave model to BlogPostAutosaveResponse
func (bar *BlogPostAutosaveResponse) FromBlogPostAutosave(autosave *BlogPostAutosave) {
	bar.ID = autosave.ID
	bar.TenantID = autosave.TenantID
	bar.PostID = autosave.PostID
	bar.UserID = autosave.UserID
	bar.WebsiteID = autosave.WebsiteID
	bar.Title = autosave.Title
	bar.Content = autosave.Content
	bar.Excerpt = autosave.Excerpt
	bar.FeaturedImage = autosave.FeaturedImage
	bar.Version = autosave.Version
	bar.IsConflicted = autosave.IsConflicted
	bar.LastSavedAt = autosave.LastSavedAt
	bar.CreatedAt = autosave.CreatedAt
	bar.UpdatedAt = autosave.UpdatedAt
}

// AutosaveConflictResolution represents different ways to resolve conflicts
type AutosaveConflictResolution string

const (
	// Keep the current saved version
	ConflictResolutionKeepSaved AutosaveConflictResolution = "keep_saved"
	// Use the auto-saved version
	ConflictResolutionUseAutosave AutosaveConflictResolution = "use_autosave"
	// Merge both versions (requires manual review)
	ConflictResolutionMerge AutosaveConflictResolution = "merge"
)

// BlogPostAutosaveConflictRequest represents a request to resolve auto-save conflicts
type BlogPostAutosaveConflictRequest struct {
	PostID     uint                       `json:"post_id" validate:"required,min=1"`
	Resolution AutosaveConflictResolution `json:"resolution" validate:"required,oneof=keep_saved use_autosave merge"`
}

// BlogPostAutosaveStatus represents the current auto-save status
type BlogPostAutosaveStatus struct {
	PostID            uint       `json:"post_id"`
	HasAutosave       bool       `json:"has_autosave"`
	LastSavedAt       *time.Time `json:"last_saved_at,omitempty"`
	Version           uint       `json:"version"`
	IsConflicted      bool       `json:"is_conflicted"`
	SecondsSinceLastSave int     `json:"seconds_since_last_save,omitempty"`
}
package handlers

import (
	"context"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
	httpresponse "github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// BlogPostTemplateHandler handles blog post template operations
type BlogPostTemplateHandler struct {
	templateService services.BlogPostTemplateService
	logger          utils.Logger
}

// NewBlogPostTemplateHandler creates a new blog post template handler
func NewBlogPostTemplateHandler(templateService services.BlogPostTemplateService, logger utils.Logger) *BlogPostTemplateHandler {
	return &BlogPostTemplateHandler{
		templateService: templateService,
		logger:          logger,
	}
}

// CreateTemplate creates a new blog post template
// @Summary Create blog post template
// @Description Creates a new reusable template for blog posts
// @Tags Blog Post Templates
// @Accept json
// @Produce json
// @Param request body models.BlogPostTemplateCreateRequest true "Template creation request"
// @Success 201 {object} models.BlogPostTemplateResponse
// @Failure 400 {object} httpresponse.ErrorResponse
// @Failure 401 {object} httpresponse.ErrorResponse
// @Failure 500 {object} httpresponse.ErrorResponse
// @Router /blog/templates [post]
func (h *BlogPostTemplateHandler) CreateTemplate(c *gin.Context) {
	var req models.BlogPostTemplateCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, err.Error())
		return
	}
	
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "User ID not found")
		return
	}
	
	// Set user ID in context for service
	ctx := c.Request.Context()
	ctx = context.WithValue(ctx, "user_id", userID.(uint))
	
	// Create template
	template, err := h.templateService.Create(ctx, &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create blog post template")
		httpresponse.InternalServerError(c.Writer, "Failed to create template")
		return
	}
	
	httpresponse.Created(c.Writer, template)
}

// GetTemplate retrieves a blog post template by ID
// @Summary Get blog post template
// @Description Retrieves a specific blog post template by ID
// @Tags Blog Post Templates
// @Accept json
// @Produce json
// @Param id path int true "Template ID"
// @Success 200 {object} models.BlogPostTemplateResponse
// @Failure 400 {object} httpresponse.ErrorResponse
// @Failure 404 {object} httpresponse.ErrorResponse
// @Failure 500 {object} httpresponse.ErrorResponse
// @Router /blog/templates/{id} [get]
func (h *BlogPostTemplateHandler) GetTemplate(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid template ID")
		return
	}
	
	template, err := h.templateService.GetByID(c.Request.Context(), uint(id))
	if err != nil {
		httpresponse.NotFound(c.Writer, "Template not found")
		return
	}
	
	httpresponse.Success(c.Writer, template)
}

// GetTemplateBySlug retrieves a blog post template by slug
// @Summary Get blog post template by slug
// @Description Retrieves a specific blog post template by slug
// @Tags Blog Post Templates
// @Accept json
// @Produce json
// @Param slug path string true "Template slug"
// @Param scope query string true "Template scope (system, tenant, website, user)"
// @Param scope_id query int false "Scope ID (required for tenant/website/user scopes)"
// @Success 200 {object} models.BlogPostTemplateResponse
// @Failure 400 {object} httpresponse.ErrorResponse
// @Failure 404 {object} httpresponse.ErrorResponse
// @Failure 500 {object} httpresponse.ErrorResponse
// @Router /blog/templates/slug/{slug} [get]
func (h *BlogPostTemplateHandler) GetTemplateBySlug(c *gin.Context) {
	slug := c.Param("slug")
	scope := c.Query("scope")
	
	if scope == "" {
		httpresponse.BadRequest(c.Writer, "Scope parameter is required")
		return
	}
	
	var scopeID *uint
	if scopeIDStr := c.Query("scope_id"); scopeIDStr != "" {
		scopeIDUint, err := strconv.ParseUint(scopeIDStr, 10, 32)
		if err != nil {
			httpresponse.BadRequest(c.Writer, "Invalid scope ID")
			return
		}
		scopeIDVal := uint(scopeIDUint)
		scopeID = &scopeIDVal
	}
	
	template, err := h.templateService.GetBySlug(c.Request.Context(), slug, models.BlogPostTemplateScope(scope), scopeID)
	if err != nil {
		httpresponse.NotFound(c.Writer, "Template not found")
		return
	}
	
	httpresponse.Success(c.Writer, template)
}

// UpdateTemplate updates an existing blog post template
// @Summary Update blog post template
// @Description Updates an existing blog post template
// @Tags Blog Post Templates
// @Accept json
// @Produce json
// @Param id path int true "Template ID"
// @Param request body models.BlogPostTemplateUpdateRequest true "Template update request"
// @Success 200 {object} models.BlogPostTemplateResponse
// @Failure 400 {object} httpresponse.ErrorResponse
// @Failure 404 {object} httpresponse.ErrorResponse
// @Failure 500 {object} httpresponse.ErrorResponse
// @Router /blog/templates/{id} [put]
func (h *BlogPostTemplateHandler) UpdateTemplate(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid template ID")
		return
	}
	
	var req models.BlogPostTemplateUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, err.Error())
		return
	}
	
	template, err := h.templateService.Update(c.Request.Context(), uint(id), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update blog post template")
		httpresponse.InternalServerError(c.Writer, "Failed to update template")
		return
	}
	
	httpresponse.Success(c.Writer, template)
}

// DeleteTemplate deletes a blog post template
// @Summary Delete blog post template
// @Description Deletes a blog post template
// @Tags Blog Post Templates
// @Accept json
// @Produce json
// @Param id path int true "Template ID"
// @Success 200 {object} httpresponse.SuccessResponse
// @Failure 400 {object} httpresponse.ErrorResponse
// @Failure 404 {object} httpresponse.ErrorResponse
// @Failure 500 {object} httpresponse.ErrorResponse
// @Router /blog/templates/{id} [delete]
func (h *BlogPostTemplateHandler) DeleteTemplate(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid template ID")
		return
	}
	
	if err := h.templateService.Delete(c.Request.Context(), uint(id)); err != nil {
		h.logger.WithError(err).Error("Failed to delete blog post template")
		httpresponse.InternalServerError(c.Writer, "Failed to delete template")
		return
	}
	
	httpresponse.Success(c.Writer, gin.H{"message": "Template deleted successfully"})
}

// ListTemplates lists blog post templates with filters
// @Summary List blog post templates
// @Description Lists blog post templates with optional filters
// @Tags Blog Post Templates
// @Accept json
// @Produce json
// @Param tenant_id query int false "Tenant ID"
// @Param website_id query int false "Website ID"
// @Param type query string false "Template type"
// @Param scope query string false "Template scope"
// @Param is_active query bool false "Active status"
// @Param is_featured query bool false "Featured status"
// @Param search query string false "Search query"
// @Param limit query int false "Limit"
// @Param offset query int false "Offset"
// @Success 200 {array} models.BlogPostTemplateResponse
// @Failure 500 {object} httpresponse.ErrorResponse
// @Router /blog/templates [get]
func (h *BlogPostTemplateHandler) ListTemplates(c *gin.Context) {
	filter := &models.BlogPostTemplateFilter{}
	
	// Parse filter parameters
	if tenantIDStr := c.Query("tenant_id"); tenantIDStr != "" {
		if tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32); err == nil {
			tenantIDVal := uint(tenantID)
			filter.TenantID = &tenantIDVal
		}
	}
	
	if websiteIDStr := c.Query("website_id"); websiteIDStr != "" {
		if websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32); err == nil {
			websiteIDVal := uint(websiteID)
			filter.WebsiteID = &websiteIDVal
		}
	}
	
	filter.Type = models.BlogPostTemplateType(c.Query("type"))
	filter.Scope = models.BlogPostTemplateScope(c.Query("scope"))
	
	if isActiveStr := c.Query("is_active"); isActiveStr != "" {
		if isActive, err := strconv.ParseBool(isActiveStr); err == nil {
			filter.IsActive = &isActive
		}
	}
	
	if isFeaturedStr := c.Query("is_featured"); isFeaturedStr != "" {
		if isFeatured, err := strconv.ParseBool(isFeaturedStr); err == nil {
			filter.IsFeatured = &isFeatured
		}
	}
	
	filter.Search = c.Query("search")
	
	if limitStr := c.Query("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil {
			filter.Limit = limit
		}
	}
	
	if offsetStr := c.Query("offset"); offsetStr != "" {
		if offset, err := strconv.Atoi(offsetStr); err == nil {
			filter.Offset = offset
		}
	}
	
	templates, total, err := h.templateService.List(c.Request.Context(), filter)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list blog post templates")
		httpresponse.InternalServerError(c.Writer, "Failed to list templates")
		return
	}
	
	httpresponse.Success(c.Writer, map[string]interface{}{
		"templates": templates,
		"total":     total,
		"limit":     filter.Limit,
		"offset":    filter.Offset,
	})
}

// ListAccessibleTemplates lists all templates accessible by the current user
// @Summary List accessible templates
// @Description Lists all templates accessible by the current user based on scope
// @Tags Blog Post Templates
// @Accept json
// @Produce json
// @Success 200 {array} models.BlogPostTemplateResponse
// @Failure 401 {object} httpresponse.ErrorResponse
// @Failure 500 {object} httpresponse.ErrorResponse
// @Router /blog/templates/accessible [get]
func (h *BlogPostTemplateHandler) ListAccessibleTemplates(c *gin.Context) {
	// Get IDs from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}
	
	userID, exists := c.Get("user_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "User ID not found")
		return
	}
	
	websiteID, exists := c.Get("website_id")
	if !exists {
		// Try to get from header or query
		websiteIDStr := c.GetHeader("X-Website-ID")
		if websiteIDStr == "" {
			websiteIDStr = c.Query("website_id")
		}
		if websiteIDStr == "" {
			httpresponse.BadRequest(c.Writer, "Website ID not found")
			return
		}
		websiteIDUint, err := strconv.ParseUint(websiteIDStr, 10, 32)
		if err != nil {
			httpresponse.BadRequest(c.Writer, "Invalid website ID")
			return
		}
		websiteID = uint(websiteIDUint)
	}
	
	templates, err := h.templateService.ListAccessible(
		c.Request.Context(),
		tenantID.(uint),
		websiteID.(uint),
		userID.(uint),
	)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list accessible templates")
		httpresponse.InternalServerError(c.Writer, "Failed to list templates")
		return
	}
	
	httpresponse.Success(c.Writer, templates)
}

// GetFeaturedTemplates retrieves featured templates
// @Summary Get featured templates
// @Description Retrieves featured blog post templates
// @Tags Blog Post Templates
// @Accept json
// @Produce json
// @Param limit query int false "Limit (default 10)"
// @Success 200 {array} models.BlogPostTemplateResponse
// @Failure 500 {object} httpresponse.ErrorResponse
// @Router /blog/templates/featured [get]
func (h *BlogPostTemplateHandler) GetFeaturedTemplates(c *gin.Context) {
	limit := 10
	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}
	
	// Get tenant ID from context
	var tenantID *uint
	if tid, exists := c.Get("tenant_id"); exists {
		t := tid.(uint)
		tenantID = &t
	}
	
	templates, err := h.templateService.GetFeatured(c.Request.Context(), tenantID, limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get featured templates")
		httpresponse.InternalServerError(c.Writer, "Failed to get templates")
		return
	}
	
	httpresponse.Success(c.Writer, templates)
}

// GetPopularTemplates retrieves popular templates
// @Summary Get popular templates
// @Description Retrieves most used blog post templates
// @Tags Blog Post Templates
// @Accept json
// @Produce json
// @Param limit query int false "Limit (default 10)"
// @Success 200 {array} models.BlogPostTemplateResponse
// @Failure 500 {object} httpresponse.ErrorResponse
// @Router /blog/templates/popular [get]
func (h *BlogPostTemplateHandler) GetPopularTemplates(c *gin.Context) {
	limit := 10
	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}
	
	// Get tenant ID from context
	var tenantID *uint
	if tid, exists := c.Get("tenant_id"); exists {
		t := tid.(uint)
		tenantID = &t
	}
	
	templates, err := h.templateService.GetPopular(c.Request.Context(), tenantID, limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get popular templates")
		httpresponse.InternalServerError(c.Writer, "Failed to get templates")
		return
	}
	
	httpresponse.Success(c.Writer, templates)
}

// DuplicateTemplate creates a copy of an existing template
// @Summary Duplicate template
// @Description Creates a copy of an existing blog post template
// @Tags Blog Post Templates
// @Accept json
// @Produce json
// @Param id path int true "Template ID to duplicate"
// @Param request body struct{Name string `json:"name"`; Slug string `json:"slug"`; Scope string `json:"scope"`; ScopeID *uint `json:"scope_id,omitempty"`} true "Duplication request"
// @Success 201 {object} models.BlogPostTemplateResponse
// @Failure 400 {object} httpresponse.ErrorResponse
// @Failure 404 {object} httpresponse.ErrorResponse
// @Failure 500 {object} httpresponse.ErrorResponse
// @Router /blog/templates/{id}/duplicate [post]
func (h *BlogPostTemplateHandler) DuplicateTemplate(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid template ID")
		return
	}
	
	var req struct {
		Name    string                        `json:"name" binding:"required"`
		Slug    string                        `json:"slug" binding:"required"`
		Scope   models.BlogPostTemplateScope  `json:"scope" binding:"required"`
		ScopeID *uint                         `json:"scope_id,omitempty"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, err.Error())
		return
	}
	
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "User ID not found")
		return
	}
	
	template, err := h.templateService.Duplicate(
		c.Request.Context(),
		uint(id),
		req.Name,
		req.Slug,
		req.Scope,
		req.ScopeID,
		userID.(uint),
	)
	if err != nil {
		h.logger.WithError(err).Error("Failed to duplicate template")
		httpresponse.InternalServerError(c.Writer, "Failed to duplicate template")
		return
	}
	
	httpresponse.Created(c.Writer, template)
}

// CreatePostFromTemplate creates a blog post from a template
// @Summary Create post from template
// @Description Creates a new blog post using a template
// @Tags Blog Post Templates
// @Accept json
// @Produce json
// @Param request body models.BlogPostFromTemplateRequest true "Post creation from template request"
// @Success 201 {object} models.BlogPostResponse
// @Failure 400 {object} httpresponse.ErrorResponse
// @Failure 401 {object} httpresponse.ErrorResponse
// @Failure 500 {object} httpresponse.ErrorResponse
// @Router /blog/templates/create-post [post]
func (h *BlogPostTemplateHandler) CreatePostFromTemplate(c *gin.Context) {
	var req models.BlogPostFromTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, err.Error())
		return
	}
	
	// Get IDs from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}
	
	userID, exists := c.Get("user_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "User ID not found")
		return
	}
	
	websiteID, exists := c.Get("website_id")
	if !exists {
		// Try to get from header or query
		websiteIDStr := c.GetHeader("X-Website-ID")
		if websiteIDStr == "" {
			websiteIDStr = c.Query("website_id")
		}
		if websiteIDStr == "" {
			httpresponse.BadRequest(c.Writer, "Website ID not found")
			return
		}
		websiteIDUint, err := strconv.ParseUint(websiteIDStr, 10, 32)
		if err != nil {
			httpresponse.BadRequest(c.Writer, "Invalid website ID")
			return
		}
		websiteID = uint(websiteIDUint)
	}
	
	post, err := h.templateService.CreatePostFromTemplate(
		c.Request.Context(),
		tenantID.(uint),
		websiteID.(uint),
		userID.(uint),
		&req,
	)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create post from template")
		httpresponse.InternalServerError(c.Writer, "Failed to create post")
		return
	}
	
	httpresponse.Created(c.Writer, post)
}
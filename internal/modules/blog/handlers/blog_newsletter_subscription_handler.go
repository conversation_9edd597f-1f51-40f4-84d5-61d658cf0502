package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
	httpresponse "github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// BlogNewsletterSubscriptionHandler handles newsletter subscription requests
type BlogNewsletterSubscriptionHandler struct {
	service services.BlogNewsletterSubscriptionService
	logger  utils.Logger
}

// NewBlogNewsletterSubscriptionHandler creates a new newsletter subscription handler
func NewBlogNewsletterSubscriptionHandler(service services.BlogNewsletterSubscriptionService, logger utils.Logger) *BlogNewsletterSubscriptionHandler {
	return &BlogNewsletterSubscriptionHandler{
		service: service,
		logger:  logger,
	}
}

// Subscribe handles newsletter subscription requests
// @Summary Subscribe to newsletter
// @Description Subscribe to blog newsletter with optional category and tag preferences
// @Tags Blog Newsletter
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int true "Tenant ID"
// @Param X-Website-ID header int true "Website ID"
// @Param request body dto.CreateNewsletterSubscriptionRequest true "Subscription details"
// @Success 201 {object} response.Response{data=dto.NewsletterSubscriptionResponse}
// @Failure 400 {object} response.Response
// @Failure 409 {object} response.Response
// @Router /blog/newsletter/subscribe [post]
func (h *BlogNewsletterSubscriptionHandler) Subscribe(c *gin.Context) {
	tenantID := c.GetUint("tenantID")
	websiteID := c.GetUint("websiteID")

	var req dto.CreateNewsletterSubscriptionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, err.Error())
		return
	}

	result, err := h.service.Subscribe(c.Request.Context(), tenantID, websiteID, &req)
	if err != nil {
		if err.Error() == "email already subscribed" {
			httpresponse.Conflict(c.Writer, "Email already subscribed")
			return
		}
		h.logger.Error("Failed to subscribe", err)
		httpresponse.InternalServerError(c.Writer, "Failed to subscribe")
		return
	}

	httpresponse.Created(c.Writer, result)
}

// Confirm handles subscription confirmation
// @Summary Confirm newsletter subscription
// @Description Confirm newsletter subscription using token from email
// @Tags Blog Newsletter
// @Accept json
// @Produce json
// @Param request body dto.ConfirmNewsletterSubscriptionRequest true "Confirmation token"
// @Success 200 {object} response.Response{data=dto.NewsletterSubscriptionResponse}
// @Failure 400 {object} response.Response
// @Router /blog/newsletter/confirm [post]
func (h *BlogNewsletterSubscriptionHandler) Confirm(c *gin.Context) {
	var req dto.ConfirmNewsletterSubscriptionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, err.Error())
		return
	}

	result, err := h.service.ConfirmSubscription(c.Request.Context(), req.Token)
	if err != nil {
		if err.Error() == "invalid or expired token" {
			httpresponse.BadRequest(c.Writer, "Invalid or expired token")
			return
		}
		if err.Error() == "subscription already confirmed" {
			httpresponse.BadRequest(c.Writer, "Subscription already confirmed")
			return
		}
		h.logger.Error("Failed to confirm subscription", err)
		httpresponse.InternalServerError(c.Writer, "Failed to confirm subscription")
		return
	}

	httpresponse.OK(c.Writer, result)
}

// Unsubscribe handles unsubscribe requests
// @Summary Unsubscribe from newsletter
// @Description Unsubscribe from newsletter using token
// @Tags Blog Newsletter
// @Accept json
// @Produce json
// @Param request body dto.UnsubscribeNewsletterRequest true "Unsubscribe token"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Router /blog/newsletter/unsubscribe [post]
func (h *BlogNewsletterSubscriptionHandler) Unsubscribe(c *gin.Context) {
	var req dto.UnsubscribeNewsletterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, err.Error())
		return
	}

	err := h.service.Unsubscribe(c.Request.Context(), req.Token)
	if err != nil {
		if err.Error() == "invalid or expired token" {
			httpresponse.BadRequest(c.Writer, "Invalid or expired token")
			return
		}
		if err.Error() == "already unsubscribed" {
			httpresponse.BadRequest(c.Writer, "Already unsubscribed")
			return
		}
		h.logger.Error("Failed to unsubscribe", err)
		httpresponse.InternalServerError(c.Writer, "Failed to unsubscribe")
		return
	}

	httpresponse.OK(c.Writer, map[string]string{"message": "Successfully unsubscribed from newsletter"})
}

// GetByID retrieves a subscription by ID
// @Summary Get newsletter subscription by ID
// @Description Get newsletter subscription details by ID
// @Tags Blog Newsletter
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int true "Tenant ID"
// @Param id path int true "Subscription ID"
// @Success 200 {object} response.Response{data=dto.NewsletterSubscriptionResponse}
// @Failure 404 {object} response.Response
// @Router /blog/newsletter/subscriptions/{id} [get]
// @Security ApiKeyAuth
func (h *BlogNewsletterSubscriptionHandler) GetByID(c *gin.Context) {
	tenantID := c.GetUint("tenantID")
	
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid subscription ID")
		return
	}

	result, err := h.service.GetByID(c.Request.Context(), tenantID, uint(id))
	if err != nil {
		httpresponse.NotFound(c.Writer, "Subscription not found")
		return
	}

	httpresponse.OK(c.Writer, result)
}

// Update updates a subscription
// @Summary Update newsletter subscription
// @Description Update newsletter subscription preferences
// @Tags Blog Newsletter
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int true "Tenant ID"
// @Param id path int true "Subscription ID"
// @Param request body dto.UpdateNewsletterSubscriptionRequest true "Update details"
// @Success 200 {object} response.Response{data=dto.NewsletterSubscriptionResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Router /blog/newsletter/subscriptions/{id} [put]
// @Security ApiKeyAuth
func (h *BlogNewsletterSubscriptionHandler) Update(c *gin.Context) {
	tenantID := c.GetUint("tenantID")
	
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid subscription ID")
		return
	}

	var req dto.UpdateNewsletterSubscriptionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, err.Error())
		return
	}

	result, err := h.service.Update(c.Request.Context(), tenantID, uint(id), &req)
	if err != nil {
		httpresponse.NotFound(c.Writer, "Subscription not found")
		return
	}

	httpresponse.OK(c.Writer, result)
}

// Delete deletes a subscription
// @Summary Delete newsletter subscription
// @Description Delete newsletter subscription (admin only)
// @Tags Blog Newsletter
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int true "Tenant ID"
// @Param id path int true "Subscription ID"
// @Success 200 {object} response.Response
// @Failure 404 {object} response.Response
// @Router /blog/newsletter/subscriptions/{id} [delete]
// @Security ApiKeyAuth
func (h *BlogNewsletterSubscriptionHandler) Delete(c *gin.Context) {
	tenantID := c.GetUint("tenantID")
	
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid subscription ID")
		return
	}

	err = h.service.Delete(c.Request.Context(), tenantID, uint(id))
	if err != nil {
		httpresponse.NotFound(c.Writer, "Subscription not found")
		return
	}

	httpresponse.OK(c.Writer, map[string]string{"message": "Subscription deleted successfully"})
}

// List retrieves subscriptions with pagination
// @Summary List newsletter subscriptions
// @Description List newsletter subscriptions with cursor-based pagination
// @Tags Blog Newsletter
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int true "Tenant ID"
// @Param X-Website-ID header int true "Website ID"
// @Param cursor query string false "Cursor for pagination"
// @Param limit query int false "Limit results per page (default: 20, max: 100)"
// @Param status query string false "Filter by status (pending, confirmed, unsubscribed)"
// @Param frequency query string false "Filter by frequency (instant, daily, weekly, monthly)"
// @Param search query string false "Search by email or name"
// @Success 200 {object} response.Response{data=[]dto.NewsletterSubscriptionListResponse,meta=pagination.CursorResponse}
// @Router /blog/newsletter/subscriptions [get]
// @Security ApiKeyAuth
func (h *BlogNewsletterSubscriptionHandler) List(c *gin.Context) {
	tenantID := c.GetUint("tenantID")
	websiteID := c.GetUint("websiteID")

	// Parse cursor request
	cursor := c.Query("cursor")
	limit := 20
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	cursorReq := &pagination.CursorRequest{
		Cursor: cursor,
		Limit:  limit,
	}

	// Parse filters
	filters := make(map[string]interface{})
	if status := c.Query("status"); status != "" {
		filters["status"] = status
	}
	if frequency := c.Query("frequency"); frequency != "" {
		filters["frequency"] = frequency
	}
	if search := c.Query("search"); search != "" {
		filters["search"] = search
	}

	// Get subscriptions
	subscriptions, cursorResp, err := h.service.ListWithCursor(c.Request.Context(), tenantID, websiteID, cursorReq, filters)
	if err != nil {
		h.logger.Error("Failed to list subscriptions", err)
		httpresponse.InternalServerError(c.Writer, "Failed to list subscriptions")
		return
	}

	httpresponse.CursorPaginated(c.Writer, subscriptions, *cursorResp)
}

// GetStats retrieves subscription statistics
// @Summary Get newsletter subscription statistics
// @Description Get newsletter subscription statistics for a website
// @Tags Blog Newsletter
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int true "Tenant ID"
// @Param X-Website-ID header int true "Website ID"
// @Success 200 {object} response.Response{data=models.NewsletterSubscriptionStats}
// @Router /blog/newsletter/stats [get]
// @Security ApiKeyAuth
func (h *BlogNewsletterSubscriptionHandler) GetStats(c *gin.Context) {
	tenantID := c.GetUint("tenantID")
	websiteID := c.GetUint("websiteID")

	stats, err := h.service.GetStatsByWebsite(c.Request.Context(), tenantID, websiteID)
	if err != nil {
		h.logger.Error("Failed to get subscription stats", err)
		httpresponse.InternalServerError(c.Writer, "Failed to get subscription stats")
		return
	}

	httpresponse.OK(c.Writer, stats)
}

// GetGrowthData retrieves subscription growth data
// @Summary Get newsletter subscription growth data
// @Description Get newsletter subscription growth data for a website
// @Tags Blog Newsletter
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int true "Tenant ID"
// @Param X-Website-ID header int true "Website ID"
// @Param days query int false "Number of days to retrieve (default: 30)"
// @Success 200 {object} response.Response{data=[]models.NewsletterGrowthData}
// @Router /blog/newsletter/growth [get]
// @Security ApiKeyAuth
func (h *BlogNewsletterSubscriptionHandler) GetGrowthData(c *gin.Context) {
	tenantID := c.GetUint("tenantID")
	websiteID := c.GetUint("websiteID")

	days := 30
	if d := c.Query("days"); d != "" {
		if parsed, err := strconv.Atoi(d); err == nil && parsed > 0 {
			days = parsed
		}
	}

	growthData, err := h.service.GetGrowthData(c.Request.Context(), tenantID, websiteID, days)
	if err != nil {
		h.logger.Error("Failed to get growth data", err)
		httpresponse.InternalServerError(c.Writer, "Failed to get growth data")
		return
	}

	httpresponse.OK(c.Writer, growthData)
}

// ExportSubscribers exports subscribers
// @Summary Export newsletter subscribers
// @Description Export newsletter subscribers with given status
// @Tags Blog Newsletter
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int true "Tenant ID"
// @Param X-Website-ID header int true "Website ID"
// @Param status query string false "Filter by status (confirmed, pending, unsubscribed)"
// @Success 200 {object} response.Response{data=[]dto.NewsletterSubscriptionResponse}
// @Router /blog/newsletter/export [get]
// @Security ApiKeyAuth
func (h *BlogNewsletterSubscriptionHandler) ExportSubscribers(c *gin.Context) {
	tenantID := c.GetUint("tenantID")
	websiteID := c.GetUint("websiteID")

	// TODO: Check if user has admin access when middleware is available
	// For now, allow access to test the functionality

	status := models.SubscriptionStatusConfirmed
	if s := c.Query("status"); s != "" {
		status = models.NewsletterSubscriptionStatus(s)
	}

	subscribers, err := h.service.ExportSubscribers(c.Request.Context(), tenantID, websiteID, status)
	if err != nil {
		h.logger.Error("Failed to export subscribers", err)
		httpresponse.InternalServerError(c.Writer, "Failed to export subscribers")
		return
	}

	httpresponse.OK(c.Writer, subscribers)
}
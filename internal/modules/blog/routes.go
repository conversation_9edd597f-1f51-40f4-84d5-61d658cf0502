package blog

import (
	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
	seoServices "github.com/tranthanhloi/wn-api-v3/internal/modules/seo/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// RegisterBlogRoutes registers all blog module routes
func RegisterBlogRoutes(r *gin.RouterGroup, blogServices *services.BlogServices, seoMetaService seoServices.SEOMetaService, logger utils.Logger) {
	// Create handlers
	categoryHandler := handlers.NewBlogCategoryHandler(blogServices.CategoryService)
	postHandler := handlers.NewBlogPostHandler(blogServices.PostService)
	tagHandler := handlers.NewBlogTagHandler(blogServices.TagService)
	autosaveHandler := handlers.NewBlogPostAutosaveHandler(blogServices.PostAutosaveService, logger)
	templateHandler := handlers.NewBlogPostTemplateHandler(blogServices.PostTemplateService, logger)
	progressHandler := handlers.NewBlogPostReadingProgressHandler(blogServices.ReadingProgressService)
	newsletterHandler := handlers.NewBlogNewsletterSubscriptionHandler(blogServices.NewsletterSubscriptionService, logger)

	// Create SEO handler if SEO service is provided
	var blogPostSEOHandler *handlers.BlogPostSEOHandler
	if seoMetaService != nil {
		blogPostSEOHandler = handlers.NewBlogPostSEOHandler(seoMetaService, logger)
	}

	// Create blog routes group
	blog := r.Group("/blog")

	// Public read-only routes (no authentication required)
	publicBlog := blog.Group("")
	{
		// Public category routes
		publicCategories := publicBlog.Group("/categories")
		{
			publicCategories.GET("", categoryHandler.ListCategories)
			publicCategories.GET("/:id", categoryHandler.GetCategory)
			publicCategories.GET("/slug/:slug", categoryHandler.GetCategoryBySlug)
			publicCategories.GET("/hierarchy", categoryHandler.GetCategoryHierarchy)
		}
	}

	// Protected routes (require authentication)
	protectedBlog := blog.Group("")
	protectedBlog.Use(middleware.RequireAuthentication())
	{
		// Protected category routes
		categories := protectedBlog.Group("/categories")
		{
			categories.POST("", categoryHandler.CreateCategory)
			categories.PUT("/:id", categoryHandler.UpdateCategory)
			categories.DELETE("/:id", categoryHandler.DeleteCategory)
			categories.POST("/:id/move", categoryHandler.MoveCategory)
			categories.POST("/positions", categoryHandler.UpdateCategoryPositions)
		}

		// Public post routes (in publicBlog group above)
	}

	// Add public post routes
	publicPosts := publicBlog.Group("/posts")
	{
		publicPosts.GET("", postHandler.ListPosts)
		publicPosts.GET("/:id", postHandler.GetPost)
		publicPosts.GET("/slug/:slug", postHandler.GetPostBySlug)
		publicPosts.GET("/published", postHandler.GetPublishedPosts)
		publicPosts.GET("/featured", postHandler.GetFeaturedPosts)
		publicPosts.GET("/:id/related", postHandler.GetRelatedPosts)
		publicPosts.GET("/stats", postHandler.GetPostStats)
		publicPosts.GET("/popular", postHandler.GetPopularPosts)
	}

	// Continue with protected blog group
	protectedBlog2 := blog.Group("")
	protectedBlog2.Use(middleware.RequireAuthentication())
	{
		// Auto-save management routes (global)
		autosave := protectedBlog2.Group("/autosave")
		{
			autosave.POST("/resolve-conflict", autosaveHandler.ResolveConflict)   // Resolve auto-save conflict
			autosave.GET("/conflicted", autosaveHandler.GetConflictedAutosaves)  // Get all conflicted auto-saves
		}
		
		// Protected post routes
		posts := protectedBlog2.Group("/posts")
		{
			posts.POST("", postHandler.CreatePost)
			posts.PUT("/:id", postHandler.UpdatePost)
			posts.DELETE("/:id", postHandler.DeletePost)
			posts.POST("/:id/publish", postHandler.PublishPost)
			posts.POST("/:id/unpublish", postHandler.UnpublishPost)
			posts.POST("/:id/tags/attach", postHandler.AttachTags)
			posts.POST("/:id/tags/detach", postHandler.DetachTags)
			posts.POST("/:id/tags/sync", postHandler.SyncTags)

			// Auto-save endpoints
			posts.POST("/:id/autosave", autosaveHandler.AutoSave)                // Auto-save post
			posts.GET("/:id/autosave", autosaveHandler.GetAutosave)             // Get auto-save
			posts.DELETE("/:id/autosave", autosaveHandler.DeleteAutosave)       // Delete auto-save
			posts.GET("/:id/autosave/status", autosaveHandler.GetAutosaveStatus) // Get auto-save status
			posts.POST("/:id/autosave/restore", autosaveHandler.RestoreFromAutosave) // Restore from auto-save
			
			// Reading progress endpoints
			posts.POST("/:id/progress", progressHandler.UpdateReadingProgress)   // Update reading progress
			posts.GET("/:id/progress", progressHandler.GetReadingProgress)       // Get reading progress
			posts.GET("/:id/reading-stats", progressHandler.GetPostReadingStats) // Get post reading stats
			posts.GET("/:id/readers", progressHandler.GetPostReaders)            // Get post readers
			
			// SEO convenience endpoints for blog posts
			if blogPostSEOHandler != nil {
				posts.POST("/:id/seo", blogPostSEOHandler.CreatePostSEO)            // Create SEO for post
				posts.GET("/:id/seo", blogPostSEOHandler.GetPostSEO)                // Get SEO for post
				posts.PUT("/:id/seo", blogPostSEOHandler.UpdatePostSEO)             // Update SEO for post
				posts.DELETE("/:id/seo", blogPostSEOHandler.DeletePostSEO)          // Delete SEO for post
				posts.POST("/:id/seo/analyze", blogPostSEOHandler.AnalyzePostSEO)   // Analyze post SEO
				posts.POST("/:id/seo/validate", blogPostSEOHandler.ValidatePostSEO) // Validate post SEO
				posts.GET("/:id/seo/tags", blogPostSEOHandler.GeneratePostMetaTags) // Generate meta tags
			}
		}

		
		// Global reading progress endpoints
		reading := protectedBlog2.Group("/reading")
		{
			reading.GET("/stats", progressHandler.GetUserReadingStats)       // Get user reading stats
			reading.GET("/analytics", progressHandler.GetReadingAnalytics)   // Get reading analytics
			reading.GET("/history", progressHandler.GetUserReadingHistory)   // Get user reading history
		}
	}

	// Public tag routes
	publicTags := publicBlog.Group("/tags")
	{
		publicTags.GET("", tagHandler.ListTags)
		publicTags.GET("/:id", tagHandler.GetTag)
		publicTags.GET("/slug/:slug", tagHandler.GetTagBySlug)
		publicTags.GET("/most-used", tagHandler.GetMostUsedTags)
		publicTags.GET("/suggestions", tagHandler.GetTagSuggestions)
		publicTags.GET("/stats", tagHandler.GetTagStats)
	}

	// Protected tag routes
	protectedTags := protectedBlog2.Group("/tags")
	{
		protectedTags.POST("", tagHandler.CreateTag)
		protectedTags.PUT("/:id", tagHandler.UpdateTag)
		protectedTags.DELETE("/:id", tagHandler.DeleteTag)
	}
	
	// Public template routes
	publicTemplates := publicBlog.Group("/templates")
	{
		publicTemplates.GET("", templateHandler.ListTemplates)
		publicTemplates.GET("/:id", templateHandler.GetTemplate)
		publicTemplates.GET("/slug/:slug", templateHandler.GetTemplateBySlug)
		publicTemplates.GET("/featured", templateHandler.GetFeaturedTemplates)
		publicTemplates.GET("/popular", templateHandler.GetPopularTemplates)
	}
	
	// Protected template routes
	protectedTemplates := protectedBlog2.Group("/templates")
	{
		protectedTemplates.POST("", templateHandler.CreateTemplate)
		protectedTemplates.PUT("/:id", templateHandler.UpdateTemplate)
		protectedTemplates.DELETE("/:id", templateHandler.DeleteTemplate)
		protectedTemplates.POST("/:id/duplicate", templateHandler.DuplicateTemplate)
		protectedTemplates.GET("/accessible", templateHandler.ListAccessibleTemplates)
		protectedTemplates.POST("/create-post", templateHandler.CreatePostFromTemplate)
	}

	// Public newsletter routes
	publicNewsletter := publicBlog.Group("/newsletter")
	{
		publicNewsletter.POST("/subscribe", newsletterHandler.Subscribe)
		publicNewsletter.POST("/confirm", newsletterHandler.Confirm)
		publicNewsletter.POST("/unsubscribe", newsletterHandler.Unsubscribe)
	}

	// Protected newsletter routes
	protectedNewsletter := protectedBlog2.Group("/newsletter")
	{
		protectedNewsletter.GET("/subscriptions", newsletterHandler.List)
		protectedNewsletter.GET("/subscriptions/:id", newsletterHandler.GetByID)
		protectedNewsletter.PUT("/subscriptions/:id", newsletterHandler.Update)
		protectedNewsletter.DELETE("/subscriptions/:id", newsletterHandler.Delete)
		protectedNewsletter.GET("/stats", newsletterHandler.GetStats)
		protectedNewsletter.GET("/growth", newsletterHandler.GetGrowthData)
		protectedNewsletter.GET("/export", newsletterHandler.ExportSubscribers)
	}
}

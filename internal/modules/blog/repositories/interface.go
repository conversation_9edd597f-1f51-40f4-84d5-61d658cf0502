package repositories

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// BlogCategoryRepository defines the interface for blog category data access
type BlogCategoryRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, category *models.BlogCategory) error
	GetByID(ctx context.Context, tenantID, id uint) (*models.BlogCategory, error)
	GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.BlogCategory, error)
	Update(ctx context.Context, tenantID, id uint, category *models.BlogCategory) error
	Delete(ctx context.Context, tenantID, id uint) error

	// List and filter operations
	List(ctx context.Context, filter *models.BlogCategoryFilter) ([]models.BlogCategory, int64, error)
	ListWithCursor(ctx context.Context, tenantID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]models.BlogCategory, *pagination.CursorResponse, error)
	GetByParentID(ctx context.Context, tenantID, websiteID uint, parentID *uint) ([]models.BlogCategory, error)
	GetHierarchy(ctx context.Context, tenantID, websiteID uint) ([]models.BlogCategory, error)

	// Hierarchical operations (Nested Set Model)
	MoveCategory(ctx context.Context, tenantID, categoryID, newParentID uint) error
	GetDescendants(ctx context.Context, tenantID, categoryID uint) ([]models.BlogCategory, error)
	UpdatePositions(ctx context.Context, tenantID uint, positions []CategoryPosition) error
}

// BlogTagRepository defines the interface for blog tag data access
type BlogTagRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, tag *models.BlogTag) error
	GetByID(ctx context.Context, tenantID, id uint) (*models.BlogTag, error)
	GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.BlogTag, error)
	Update(ctx context.Context, tenantID, id uint, tag *models.BlogTag) error
	Delete(ctx context.Context, tenantID, id uint) error

	// List and filter operations
	List(ctx context.Context, filter *models.BlogTagFilter) ([]models.BlogTag, int64, error)
	ListWithCursor(ctx context.Context, tenantID, websiteID uint, cursor *pagination.CursorRequest, filters map[string]interface{}) ([]models.BlogTag, *pagination.CursorResponse, error)
	GetByNames(ctx context.Context, tenantID, websiteID uint, names []string) ([]models.BlogTag, error)
	GetMostUsed(ctx context.Context, tenantID, websiteID uint, limit int) ([]models.BlogTag, error)
	GetSuggestions(ctx context.Context, tenantID, websiteID uint, query string, limit int) ([]models.BlogTag, error)

	// Usage operations
	IncrementUsage(ctx context.Context, tenantID, tagID uint) error
	DecrementUsage(ctx context.Context, tenantID, tagID uint) error
	UpdateUsageCount(ctx context.Context, tenantID, tagID uint, count uint) error
}

// BlogPostRepository defines the interface for blog post data access
type BlogPostRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, post *models.BlogPost) error
	GetByID(ctx context.Context, tenantID, id uint) (*models.BlogPost, error)
	GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.BlogPost, error)
	Update(ctx context.Context, tenantID, id uint, post *models.BlogPost) error
	Delete(ctx context.Context, tenantID, id uint) error

	// List and filter operations
	List(ctx context.Context, filter *models.BlogPostFilter) ([]models.BlogPost, int64, error)
	ListWithCursor(ctx context.Context, tenantID, websiteID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]models.BlogPost, *pagination.CursorResponse, error)
	GetPublished(ctx context.Context, tenantID, websiteID uint, limit, offset int) ([]models.BlogPost, int64, error)
	GetFeatured(ctx context.Context, tenantID, websiteID uint, limit int) ([]models.BlogPost, error)
	GetRelated(ctx context.Context, tenantID, postID uint, limit int) ([]models.BlogPost, error)

	// Tag operations
	AttachTags(ctx context.Context, tenantID, postID uint, tagIDs []uint) error
	DetachTags(ctx context.Context, tenantID, postID uint, tagIDs []uint) error
	SyncTags(ctx context.Context, tenantID, postID uint, tagIDs []uint) error
	GetPostTags(ctx context.Context, tenantID, postID uint) ([]models.BlogTag, error)

	// Statistics and analytics
	IncrementViewCount(ctx context.Context, tenantID, postID uint) error
	GetStats(ctx context.Context, tenantID, websiteID uint) (*models.BlogPostStats, error)
	GetPopular(ctx context.Context, tenantID, websiteID uint, days int, limit int) ([]models.BlogPost, error)

	// Publishing operations
	Publish(ctx context.Context, tenantID, postID uint) error
	Unpublish(ctx context.Context, tenantID, postID uint) error
	Schedule(ctx context.Context, tenantID, postID uint, scheduledAt *time.Time) error
	GetScheduled(ctx context.Context) ([]models.BlogPost, error)
}

// BlogPostScheduleRepository defines the interface for blog post scheduling
type BlogPostScheduleRepository interface {
	Create(ctx context.Context, schedule *models.BlogPostSchedule) error
	GetByPostID(ctx context.Context, tenantID, postID uint) (*models.BlogPostSchedule, error)
	Update(ctx context.Context, tenantID, id uint, schedule *models.BlogPostSchedule) error
	Delete(ctx context.Context, tenantID, id uint) error
	GetPendingSchedules(ctx context.Context) ([]models.BlogPostSchedule, error)
	MarkAsExecuted(ctx context.Context, tenantID, scheduleID uint) error
}

// BlogPostRevisionRepository defines the interface for blog post revisions
type BlogPostRevisionRepository interface {
	Create(ctx context.Context, revision *models.BlogPostRevision) error
	GetByPostID(ctx context.Context, tenantID, postID uint, limit, offset int) ([]models.BlogPostRevision, int64, error)
	GetByID(ctx context.Context, tenantID, id uint) (*models.BlogPostRevision, error)
	Delete(ctx context.Context, tenantID, id uint) error
	DeleteOldRevisions(ctx context.Context, tenantID, postID uint, keepCount int) error
}

// HomepageBlockRepository defines the interface for homepage block data access
type HomepageBlockRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, block *models.HomepageBlock) error
	GetByID(ctx context.Context, tenantID, id uint) (*models.HomepageBlock, error)
	Update(ctx context.Context, tenantID, id uint, block *models.HomepageBlock) error
	Delete(ctx context.Context, tenantID, id uint) error

	// List and filter operations
	List(ctx context.Context, filter *models.HomepageBlockFilter) ([]models.HomepageBlock, int64, error)
	GetByWebsite(ctx context.Context, tenantID, websiteID uint) ([]models.HomepageBlock, error)
	GetActiveBlocks(ctx context.Context, tenantID, websiteID uint) ([]models.HomepageBlock, error)

	// Block organization
	Reorder(ctx context.Context, tenantID uint, blockIDs []uint) error
	UpdateSortOrder(ctx context.Context, tenantID, blockID uint, sortOrder uint) error

	// Block status
	Activate(ctx context.Context, tenantID, blockID uint) error
	Deactivate(ctx context.Context, tenantID, blockID uint) error
}

// BlockTemplateRepository defines the interface for block template data access
type BlockTemplateRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, template *models.BlockTemplate) error
	GetByID(ctx context.Context, tenantID, id uint) (*models.BlockTemplate, error)
	Update(ctx context.Context, tenantID, id uint, template *models.BlockTemplate) error
	Delete(ctx context.Context, tenantID, id uint) error

	// List and filter operations
	List(ctx context.Context, filter *models.BlockTemplateFilter) ([]models.BlockTemplate, int64, error)
	GetPublicTemplates(ctx context.Context, blockType models.HomepageBlockType) ([]models.BlockTemplate, error)
	GetByTenant(ctx context.Context, tenantID uint) ([]models.BlockTemplate, error)
}

// BlogPostAutosaveRepository defines the interface for blog post auto-save data access
type BlogPostAutosaveRepository interface {
	// Basic operations
	CreateOrUpdate(autosave *models.BlogPostAutosave) error
	GetByPostIDAndUserID(postID, userID uint) (*models.BlogPostAutosave, error)
	GetByPostID(postID uint) ([]*models.BlogPostAutosave, error)
	Delete(id uint) error
	DeleteByPostIDAndUserID(postID, userID uint) error
	DeleteByPostID(postID uint) error
	
	// Cleanup and maintenance
	CleanupOldAutosaves(olderThan time.Duration) error
	
	// Conflict management
	GetConflictedAutosaves(tenantID uint) ([]*models.BlogPostAutosave, error)
	ResolveConflict(id uint) error
	
	// Status
	GetAutosaveStatus(postID, userID uint) (*models.BlogPostAutosaveStatus, error)
}

// BlogPostTemplateRepository defines the interface for blog post template data access
type BlogPostTemplateRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, template *models.BlogPostTemplate) error
	GetByID(ctx context.Context, id uint) (*models.BlogPostTemplate, error)
	GetBySlug(ctx context.Context, slug string, scope models.BlogPostTemplateScope, scopeID *uint) (*models.BlogPostTemplate, error)
	Update(ctx context.Context, id uint, template *models.BlogPostTemplate) error
	Delete(ctx context.Context, id uint) error
	
	// List and filter operations
	List(ctx context.Context, filter *models.BlogPostTemplateFilter) ([]models.BlogPostTemplate, int64, error)
	ListAccessible(ctx context.Context, tenantID, websiteID, userID uint) ([]models.BlogPostTemplate, error)
	GetByType(ctx context.Context, templateType models.BlogPostTemplateType, tenantID *uint) ([]models.BlogPostTemplate, error)
	GetFeatured(ctx context.Context, tenantID *uint, limit int) ([]models.BlogPostTemplate, error)
	
	// Usage tracking
	IncrementUsage(ctx context.Context, id uint) error
	GetPopularTemplates(ctx context.Context, tenantID *uint, limit int) ([]models.BlogPostTemplate, error)
	
	// Template duplication
	Duplicate(ctx context.Context, id uint, newName, newSlug string, targetScope models.BlogPostTemplateScope, targetScopeID *uint) (*models.BlogPostTemplate, error)
}

// BlogPostReadingProgressRepository defines the interface for reading progress tracking
type BlogPostReadingProgressRepository interface {
	// Basic CRUD operations
	CreateOrUpdate(ctx context.Context, progress *models.BlogPostReadingProgress) error
	GetByUserAndPost(ctx context.Context, tenantID, userID, blogPostID uint) (*models.BlogPostReadingProgress, error)
	GetByID(ctx context.Context, tenantID, id uint) (*models.BlogPostReadingProgress, error)
	Update(ctx context.Context, tenantID, id uint, progress *models.BlogPostReadingProgress) error
	Delete(ctx context.Context, tenantID, id uint) error

	// List and filter operations
	List(ctx context.Context, filter *models.BlogPostReadingProgressFilter) ([]models.BlogPostReadingProgress, int64, error)
	ListByUser(ctx context.Context, tenantID, userID uint, limit, offset int) ([]models.BlogPostReadingProgress, int64, error)
	ListByPost(ctx context.Context, tenantID, blogPostID uint, limit, offset int) ([]models.BlogPostReadingProgress, int64, error)

	// Analytics and statistics
	GetPostReadingStats(ctx context.Context, tenantID, blogPostID uint) (*models.BlogPostReadingStats, error)
	GetUserReadingStats(ctx context.Context, tenantID, userID uint) (*models.UserReadingStats, error)
	GetReadingTrends(ctx context.Context, tenantID uint, blogPostID *uint, days int) ([]dto.ReadingTrendData, error)
	
	// Batch operations
	BatchUpdate(ctx context.Context, updates []models.BlogPostReadingProgress) error
	
	// Cleanup operations
	CleanupOldProgress(ctx context.Context, tenantID uint, olderThan time.Time) error
	DeleteByUser(ctx context.Context, tenantID, userID uint) error
	DeleteByPost(ctx context.Context, tenantID, blogPostID uint) error
}

// BlogNewsletterSubscriptionRepository defines the interface for newsletter subscription data access
type BlogNewsletterSubscriptionRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, subscription *models.BlogNewsletterSubscription) error
	GetByID(ctx context.Context, tenantID, id uint) (*models.BlogNewsletterSubscription, error)
	GetByEmail(ctx context.Context, tenantID, websiteID uint, email string) (*models.BlogNewsletterSubscription, error)
	GetByToken(ctx context.Context, token string) (*models.BlogNewsletterSubscription, error)
	Update(ctx context.Context, tenantID, id uint, subscription *models.BlogNewsletterSubscription) error
	Delete(ctx context.Context, tenantID, id uint) error

	// List and filter operations
	List(ctx context.Context, filter *models.BlogNewsletterSubscriptionFilter) ([]models.BlogNewsletterSubscription, int64, error)
	ListWithCursor(ctx context.Context, tenantID, websiteID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]models.BlogNewsletterSubscription, *pagination.CursorResponse, error)
	GetActiveSubscribers(ctx context.Context, tenantID, websiteID uint, frequency models.NewsletterFrequency) ([]models.BlogNewsletterSubscription, error)
	GetSubscribersByCategories(ctx context.Context, tenantID, websiteID uint, categoryIDs []uint) ([]models.BlogNewsletterSubscription, error)
	GetSubscribersByTags(ctx context.Context, tenantID, websiteID uint, tagIDs []uint) ([]models.BlogNewsletterSubscription, error)

	// Subscription management
	Confirm(ctx context.Context, token string) error
	Unsubscribe(ctx context.Context, token string) error
	UpdateLastSentAt(ctx context.Context, tenantID, id uint) error
	BatchUpdateLastSentAt(ctx context.Context, tenantID uint, subscriptionIDs []uint) error

	// Statistics
	GetStatsByWebsite(ctx context.Context, tenantID, websiteID uint) (*models.NewsletterSubscriptionStats, error)
	GetGrowthData(ctx context.Context, tenantID, websiteID uint, days int) ([]models.NewsletterGrowthData, error)

	// Cleanup
	CleanupUnconfirmed(ctx context.Context, olderThan time.Time) error
	CleanupBounced(ctx context.Context, olderThan time.Time) error
}

// CategoryPosition represents a category position for reordering
type CategoryPosition struct {
	ID       uint  `json:"id"`
	ParentID *uint `json:"parent_id"`
	Position uint  `json:"position"`
}

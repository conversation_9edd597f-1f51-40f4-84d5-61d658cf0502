package mysql

import (
	"errors"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"gorm.io/gorm"
)

// BlogPostAutosaveRepository handles database operations for blog post auto-saves
type BlogPostAutosaveRepository struct {
	db *gorm.DB
}

// NewBlogPostAutosaveRepository creates a new blog post auto-save repository
func NewBlogPostAutosaveRepository(db *gorm.DB) *BlogPostAutosaveRepository {
	return &BlogPostAutosaveRepository{
		db: db,
	}
}

// CreateOrUpdate creates or updates an auto-save for a blog post
func (r *BlogPostAutosaveRepository) CreateOrUpdate(autosave *models.BlogPostAutosave) error {
	// Check if auto-save already exists
	existing := &models.BlogPostAutosave{}
	err := r.db.Where("post_id = ? AND user_id = ?", autosave.PostID, autosave.UserID).First(existing).Error
	
	if err == nil {
		// Update existing auto-save
		autosave.ID = existing.ID
		autosave.Version = existing.Version + 1
		autosave.LastSavedAt = time.Now()
		
		// Check for conflicts (if post was updated by another user)
		var post models.BlogPost
		if err := r.db.First(&post, autosave.PostID).Error; err == nil {
			if post.UpdatedAt.After(existing.LastSavedAt) && post.AuthorID != autosave.UserID {
				autosave.IsConflicted = true
			}
		}
		
		return r.db.Save(autosave).Error
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		// Create new auto-save
		autosave.Version = 1
		autosave.LastSavedAt = time.Now()
		return r.db.Create(autosave).Error
	}
	
	return err
}

// GetByPostIDAndUserID retrieves an auto-save by post ID and user ID
func (r *BlogPostAutosaveRepository) GetByPostIDAndUserID(postID, userID uint) (*models.BlogPostAutosave, error) {
	var autosave models.BlogPostAutosave
	err := r.db.Where("post_id = ? AND user_id = ?", postID, userID).First(&autosave).Error
	if err != nil {
		return nil, err
	}
	return &autosave, nil
}

// GetByPostID retrieves all auto-saves for a post
func (r *BlogPostAutosaveRepository) GetByPostID(postID uint) ([]*models.BlogPostAutosave, error) {
	var autosaves []*models.BlogPostAutosave
	err := r.db.Where("post_id = ?", postID).Find(&autosaves).Error
	return autosaves, err
}

// Delete removes an auto-save
func (r *BlogPostAutosaveRepository) Delete(id uint) error {
	return r.db.Delete(&models.BlogPostAutosave{}, id).Error
}

// DeleteByPostIDAndUserID removes an auto-save by post ID and user ID
func (r *BlogPostAutosaveRepository) DeleteByPostIDAndUserID(postID, userID uint) error {
	return r.db.Where("post_id = ? AND user_id = ?", postID, userID).Delete(&models.BlogPostAutosave{}).Error
}

// DeleteByPostID removes all auto-saves for a post
func (r *BlogPostAutosaveRepository) DeleteByPostID(postID uint) error {
	return r.db.Where("post_id = ?", postID).Delete(&models.BlogPostAutosave{}).Error
}

// CleanupOldAutosaves removes auto-saves older than the specified duration
func (r *BlogPostAutosaveRepository) CleanupOldAutosaves(olderThan time.Duration) error {
	cutoffTime := time.Now().Add(-olderThan)
	return r.db.Where("last_saved_at < ? AND is_conflicted = ?", cutoffTime, false).Delete(&models.BlogPostAutosave{}).Error
}

// GetConflictedAutosaves retrieves all conflicted auto-saves
func (r *BlogPostAutosaveRepository) GetConflictedAutosaves(tenantID uint) ([]*models.BlogPostAutosave, error) {
	var autosaves []*models.BlogPostAutosave
	err := r.db.Where("tenant_id = ? AND is_conflicted = ?", tenantID, true).
		Preload("Post").
		Find(&autosaves).Error
	return autosaves, err
}

// ResolveConflict marks an auto-save as no longer conflicted
func (r *BlogPostAutosaveRepository) ResolveConflict(id uint) error {
	return r.db.Model(&models.BlogPostAutosave{}).
		Where("id = ?", id).
		Update("is_conflicted", false).Error
}

// GetAutosaveStatus retrieves the auto-save status for a post
func (r *BlogPostAutosaveRepository) GetAutosaveStatus(postID, userID uint) (*models.BlogPostAutosaveStatus, error) {
	status := &models.BlogPostAutosaveStatus{
		PostID:      postID,
		HasAutosave: false,
	}
	
	autosave, err := r.GetByPostIDAndUserID(postID, userID)
	if err == nil && autosave != nil {
		status.HasAutosave = true
		status.LastSavedAt = &autosave.LastSavedAt
		status.Version = autosave.Version
		status.IsConflicted = autosave.IsConflicted
		status.SecondsSinceLastSave = int(time.Since(autosave.LastSavedAt).Seconds())
	}
	
	return status, nil
}
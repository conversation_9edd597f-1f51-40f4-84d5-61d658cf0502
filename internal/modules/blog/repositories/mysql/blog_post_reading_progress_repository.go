package mysql

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
	
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/dto"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
)

// blogPostReadingProgressRepository implements the BlogPostReadingProgressRepository interface
type blogPostReadingProgressRepository struct {
	db *gorm.DB
}

// NewBlogPostReadingProgressRepository creates a new instance of BlogPostReadingProgressRepository
func NewBlogPostReadingProgressRepository(db *gorm.DB) repositories.BlogPostReadingProgressRepository {
	return &blogPostReadingProgressRepository{
		db: db,
	}
}

// CreateOrUpdate creates a new reading progress record or updates existing one
func (r *blogPostReadingProgressRepository) CreateOrUpdate(ctx context.Context, progress *models.BlogPostReadingProgress) error {
	// First, try to find existing record
	var existing models.BlogPostReadingProgress
	err := r.db.WithContext(ctx).Where(
		"tenant_id = ? AND blog_post_id = ? AND user_id = ?",
		progress.TenantID, progress.BlogPostID, progress.UserID,
	).First(&existing).Error

	if err == gorm.ErrRecordNotFound {
		// Create new record
		if progress.FirstReadAt == nil {
			now := time.Now()
			progress.FirstReadAt = &now
		}
		return r.db.WithContext(ctx).Create(progress).Error
	} else if err != nil {
		return err
	}

	// Update existing record
	progress.ID = existing.ID
	progress.CreatedAt = existing.CreatedAt
	
	// Increment session count if this is a new session (more than 30 minutes since last read)
	if time.Since(existing.LastReadAt) > 30*time.Minute {
		progress.SessionCount = existing.SessionCount + 1
	} else {
		progress.SessionCount = existing.SessionCount
	}

	// Keep original first_read_at
	if existing.FirstReadAt != nil {
		progress.FirstReadAt = existing.FirstReadAt
	}

	// If progress reaches 100%, mark as completed
	if progress.ReadingProgressPercentage >= 100.0 && !progress.IsCompleted {
		progress.IsCompleted = true
		now := time.Now()
		progress.CompletedAt = &now
	}

	return r.db.WithContext(ctx).Save(progress).Error
}

// GetByUserAndPost retrieves reading progress for a specific user and post
func (r *blogPostReadingProgressRepository) GetByUserAndPost(ctx context.Context, tenantID, userID, blogPostID uint) (*models.BlogPostReadingProgress, error) {
	var progress models.BlogPostReadingProgress
	err := r.db.WithContext(ctx).
		Preload("BlogPost").
		Where("tenant_id = ? AND user_id = ? AND blog_post_id = ?", tenantID, userID, blogPostID).
		First(&progress).Error
	
	if err != nil {
		return nil, err
	}
	
	return &progress, nil
}

// GetByID retrieves reading progress by ID
func (r *blogPostReadingProgressRepository) GetByID(ctx context.Context, tenantID, id uint) (*models.BlogPostReadingProgress, error) {
	var progress models.BlogPostReadingProgress
	err := r.db.WithContext(ctx).
		Preload("BlogPost").
		Where("tenant_id = ? AND id = ?", tenantID, id).
		First(&progress).Error
	
	if err != nil {
		return nil, err
	}
	
	return &progress, nil
}

// Update updates reading progress
func (r *blogPostReadingProgressRepository) Update(ctx context.Context, tenantID, id uint, progress *models.BlogPostReadingProgress) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Updates(progress).Error
}

// Delete deletes reading progress
func (r *blogPostReadingProgressRepository) Delete(ctx context.Context, tenantID, id uint) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Delete(&models.BlogPostReadingProgress{}).Error
}

// List retrieves reading progress records with filtering
func (r *blogPostReadingProgressRepository) List(ctx context.Context, filter *models.BlogPostReadingProgressFilter) ([]models.BlogPostReadingProgress, int64, error) {
	query := r.db.WithContext(ctx).Model(&models.BlogPostReadingProgress{}).Preload("BlogPost")
	
	// Apply filters
	if filter.TenantID != 0 {
		query = query.Where("tenant_id = ?", filter.TenantID)
	}
	if filter.BlogPostID != 0 {
		query = query.Where("blog_post_id = ?", filter.BlogPostID)
	}
	if filter.UserID != 0 {
		query = query.Where("user_id = ?", filter.UserID)
	}
	if filter.IsCompleted != nil {
		query = query.Where("is_completed = ?", *filter.IsCompleted)
	}
	if filter.DeviceType != "" {
		query = query.Where("device_type = ?", filter.DeviceType)
	}
	if filter.MinProgress != nil {
		query = query.Where("reading_progress_percentage >= ?", *filter.MinProgress)
	}
	if filter.MaxProgress != nil {
		query = query.Where("reading_progress_percentage <= ?", *filter.MaxProgress)
	}
	if filter.DateFrom != nil {
		query = query.Where("last_read_at >= ?", *filter.DateFrom)
	}
	if filter.DateTo != nil {
		query = query.Where("last_read_at <= ?", *filter.DateTo)
	}

	// Count total records
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	if filter.SortBy != "" {
		order := "ASC"
		if strings.ToUpper(filter.SortOrder) == "DESC" {
			order = "DESC"
		}
		query = query.Order(fmt.Sprintf("%s %s", filter.SortBy, order))
	} else {
		query = query.Order("last_read_at DESC")
	}

	// Apply pagination
	if filter.Page > 0 && filter.PageSize > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		query = query.Offset(offset).Limit(filter.PageSize)
	}

	var progress []models.BlogPostReadingProgress
	err := query.Find(&progress).Error
	return progress, total, err
}

// ListByUser retrieves reading progress records for a specific user
func (r *blogPostReadingProgressRepository) ListByUser(ctx context.Context, tenantID, userID uint, limit, offset int) ([]models.BlogPostReadingProgress, int64, error) {
	query := r.db.WithContext(ctx).
		Preload("BlogPost").
		Where("tenant_id = ? AND user_id = ?", tenantID, userID)

	// Count total records
	var total int64
	if err := query.Model(&models.BlogPostReadingProgress{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	var progress []models.BlogPostReadingProgress
	err := query.Order("last_read_at DESC").
		Limit(limit).Offset(offset).
		Find(&progress).Error
	
	return progress, total, err
}

// ListByPost retrieves reading progress records for a specific post
func (r *blogPostReadingProgressRepository) ListByPost(ctx context.Context, tenantID, blogPostID uint, limit, offset int) ([]models.BlogPostReadingProgress, int64, error) {
	query := r.db.WithContext(ctx).
		Preload("BlogPost").
		Where("tenant_id = ? AND blog_post_id = ?", tenantID, blogPostID)

	// Count total records
	var total int64
	if err := query.Model(&models.BlogPostReadingProgress{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	var progress []models.BlogPostReadingProgress
	err := query.Order("last_read_at DESC").
		Limit(limit).Offset(offset).
		Find(&progress).Error
	
	return progress, total, err
}

// GetPostReadingStats retrieves reading statistics for a specific blog post
func (r *blogPostReadingProgressRepository) GetPostReadingStats(ctx context.Context, tenantID, blogPostID uint) (*models.BlogPostReadingStats, error) {
	var stats models.BlogPostReadingStats
	stats.BlogPostID = blogPostID

	// Get basic statistics
	var result struct {
		TotalReaders        int     `json:"total_readers"`
		CompletedReaders    int     `json:"completed_readers"`
		AverageProgress     float64 `json:"average_progress"`
		AverageTimeSpent    float64 `json:"average_time_spent"`
		AverageReadSpeedWPM float64 `json:"average_read_speed_wpm"`
		AverageScrollDepth  float64 `json:"average_scroll_depth"`
		MobileReaders       int     `json:"mobile_readers"`
		DesktopReaders      int     `json:"desktop_readers"`
		TabletReaders       int     `json:"tablet_readers"`
	}

	err := r.db.WithContext(ctx).Raw(`
		SELECT 
			COUNT(*) as total_readers,
			SUM(CASE WHEN is_completed = TRUE THEN 1 ELSE 0 END) as completed_readers,
			AVG(reading_progress_percentage) as average_progress,
			AVG(time_spent_seconds) as average_time_spent,
			AVG(CASE WHEN read_speed_wpm IS NOT NULL THEN read_speed_wpm ELSE NULL END) as average_read_speed_wpm,
			AVG(scroll_depth_percentage) as average_scroll_depth,
			SUM(CASE WHEN device_type = 'mobile' THEN 1 ELSE 0 END) as mobile_readers,
			SUM(CASE WHEN device_type = 'desktop' THEN 1 ELSE 0 END) as desktop_readers,
			SUM(CASE WHEN device_type = 'tablet' THEN 1 ELSE 0 END) as tablet_readers
		FROM blog_post_reading_progress 
		WHERE tenant_id = ? AND blog_post_id = ?
	`, tenantID, blogPostID).Scan(&result).Error

	if err != nil {
		return nil, err
	}

	stats.TotalReaders = result.TotalReaders
	stats.CompletedReaders = result.CompletedReaders
	stats.AverageProgress = result.AverageProgress
	stats.AverageTimeSpent = result.AverageTimeSpent
	stats.AverageReadSpeedWPM = result.AverageReadSpeedWPM
	stats.AverageScrollDepth = result.AverageScrollDepth
	stats.MobileReaders = result.MobileReaders
	stats.DesktopReaders = result.DesktopReaders
	stats.TabletReaders = result.TabletReaders

	// Calculate completion rate
	if stats.TotalReaders > 0 {
		stats.CompletionRate = float64(stats.CompletedReaders) / float64(stats.TotalReaders) * 100.0
	}

	return &stats, nil
}

// GetUserReadingStats retrieves reading statistics for a specific user
func (r *blogPostReadingProgressRepository) GetUserReadingStats(ctx context.Context, tenantID, userID uint) (*models.UserReadingStats, error) {
	var stats models.UserReadingStats
	stats.UserID = userID

	// Get basic statistics
	var result struct {
		TotalPostsRead         int     `json:"total_posts_read"`
		TotalPostsCompleted    int     `json:"total_posts_completed"`
		TotalTimeSpent         uint    `json:"total_time_spent"`
		AverageReadingProgress float64 `json:"average_reading_progress"`
		AverageReadSpeedWPM    float64 `json:"average_read_speed_wpm"`
		TotalReadingSessions   int     `json:"total_reading_sessions"`
	}

	err := r.db.WithContext(ctx).Raw(`
		SELECT 
			COUNT(*) as total_posts_read,
			SUM(CASE WHEN is_completed = TRUE THEN 1 ELSE 0 END) as total_posts_completed,
			SUM(time_spent_seconds) as total_time_spent,
			AVG(reading_progress_percentage) as average_reading_progress,
			AVG(CASE WHEN read_speed_wpm IS NOT NULL THEN read_speed_wpm ELSE NULL END) as average_read_speed_wpm,
			SUM(session_count) as total_reading_sessions
		FROM blog_post_reading_progress 
		WHERE tenant_id = ? AND user_id = ?
	`, tenantID, userID).Scan(&result).Error

	if err != nil {
		return nil, err
	}

	stats.TotalPostsRead = result.TotalPostsRead
	stats.TotalPostsCompleted = result.TotalPostsCompleted
	stats.TotalTimeSpent = result.TotalTimeSpent
	stats.AverageReadingProgress = result.AverageReadingProgress
	stats.AverageReadSpeedWPM = result.AverageReadSpeedWPM
	stats.TotalReadingSessions = result.TotalReadingSessions

	// Calculate completion rate
	if stats.TotalPostsRead > 0 {
		stats.CompletionRate = float64(stats.TotalPostsCompleted) / float64(stats.TotalPostsRead) * 100.0
	}

	// Get preferred device type
	var deviceType string
	err = r.db.WithContext(ctx).Raw(`
		SELECT device_type
		FROM blog_post_reading_progress 
		WHERE tenant_id = ? AND user_id = ?
		GROUP BY device_type
		ORDER BY COUNT(*) DESC
		LIMIT 1
	`, tenantID, userID).Scan(&deviceType).Error

	if err == nil {
		stats.PreferredDeviceType = models.DeviceType(deviceType)
	} else {
		stats.PreferredDeviceType = models.DeviceTypeUnknown
	}

	return &stats, nil
}

// GetReadingTrends retrieves reading trends over a specified period
func (r *blogPostReadingProgressRepository) GetReadingTrends(ctx context.Context, tenantID uint, blogPostID *uint, days int) ([]dto.ReadingTrendData, error) {
	var trends []dto.ReadingTrendData

	query := `
		SELECT 
			DATE(last_read_at) as date,
			COUNT(DISTINCT user_id) as total_readers,
			SUM(CASE WHEN is_completed = TRUE THEN 1 ELSE 0 END) as completed_readers,
			AVG(reading_progress_percentage) as average_progress,
			AVG(time_spent_seconds) as average_time_spent
		FROM blog_post_reading_progress 
		WHERE tenant_id = ? 
			AND last_read_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
	`

	args := []interface{}{tenantID, days}

	if blogPostID != nil {
		query += " AND blog_post_id = ?"
		args = append(args, *blogPostID)
	}

	query += `
		GROUP BY DATE(last_read_at)
		ORDER BY date DESC
	`

	var results []struct {
		Date             string  `json:"date"`
		TotalReaders     int     `json:"total_readers"`
		CompletedReaders int     `json:"completed_readers"`
		AverageProgress  float64 `json:"average_progress"`
		AverageTimeSpent float64 `json:"average_time_spent"`
	}

	err := r.db.WithContext(ctx).Raw(query, args...).Scan(&results).Error
	if err != nil {
		return nil, err
	}

	for _, result := range results {
		trends = append(trends, dto.ReadingTrendData{
			Date:             result.Date,
			TotalReaders:     result.TotalReaders,
			CompletedReaders: result.CompletedReaders,
			AverageProgress:  result.AverageProgress,
			AverageTimeSpent: result.AverageTimeSpent,
		})
	}

	return trends, nil
}

// BatchUpdate performs batch update of reading progress records
func (r *blogPostReadingProgressRepository) BatchUpdate(ctx context.Context, updates []models.BlogPostReadingProgress) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, update := range updates {
			if err := r.CreateOrUpdate(ctx, &update); err != nil {
				return err
			}
		}
		return nil
	})
}

// CleanupOldProgress removes old reading progress records
func (r *blogPostReadingProgressRepository) CleanupOldProgress(ctx context.Context, tenantID uint, olderThan time.Time) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND last_read_at < ?", tenantID, olderThan).
		Delete(&models.BlogPostReadingProgress{}).Error
}

// DeleteByUser removes all reading progress records for a specific user
func (r *blogPostReadingProgressRepository) DeleteByUser(ctx context.Context, tenantID, userID uint) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND user_id = ?", tenantID, userID).
		Delete(&models.BlogPostReadingProgress{}).Error
}

// DeleteByPost removes all reading progress records for a specific post
func (r *blogPostReadingProgressRepository) DeleteByPost(ctx context.Context, tenantID, blogPostID uint) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND blog_post_id = ?", tenantID, blogPostID).
		Delete(&models.BlogPostReadingProgress{}).Error
}
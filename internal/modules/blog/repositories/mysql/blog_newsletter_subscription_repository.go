package mysql

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"gorm.io/gorm"
)

type blogNewsletterSubscriptionRepository struct {
	db *gorm.DB
}

// NewBlogNewsletterSubscriptionRepository creates a new instance of newsletter subscription repository
func NewBlogNewsletterSubscriptionRepository(db *gorm.DB) repositories.BlogNewsletterSubscriptionRepository {
	return &blogNewsletterSubscriptionRepository{db: db}
}

// Create creates a new newsletter subscription
func (r *blogNewsletterSubscriptionRepository) Create(ctx context.Context, subscription *models.BlogNewsletterSubscription) error {
	// Generate unique token
	token, err := generateToken()
	if err != nil {
		return err
	}
	subscription.Token = token

	// Set default values
	if subscription.Status == "" {
		subscription.Status = models.SubscriptionStatusPending
	}
	if subscription.Frequency == "" {
		subscription.Frequency = models.FrequencyWeekly
	}

	return r.db.WithContext(ctx).Create(subscription).Error
}

// GetByID retrieves a subscription by ID
func (r *blogNewsletterSubscriptionRepository) GetByID(ctx context.Context, tenantID, id uint) (*models.BlogNewsletterSubscription, error) {
	var subscription models.BlogNewsletterSubscription
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		First(&subscription).Error
	if err != nil {
		return nil, err
	}
	return &subscription, nil
}

// GetByEmail retrieves a subscription by email
func (r *blogNewsletterSubscriptionRepository) GetByEmail(ctx context.Context, tenantID, websiteID uint, email string) (*models.BlogNewsletterSubscription, error) {
	var subscription models.BlogNewsletterSubscription
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND email = ?", tenantID, websiteID, email).
		First(&subscription).Error
	if err != nil {
		return nil, err
	}
	return &subscription, nil
}

// GetByToken retrieves a subscription by token
func (r *blogNewsletterSubscriptionRepository) GetByToken(ctx context.Context, token string) (*models.BlogNewsletterSubscription, error) {
	var subscription models.BlogNewsletterSubscription
	err := r.db.WithContext(ctx).
		Where("token = ?", token).
		First(&subscription).Error
	if err != nil {
		return nil, err
	}
	return &subscription, nil
}

// Update updates a subscription
func (r *blogNewsletterSubscriptionRepository) Update(ctx context.Context, tenantID, id uint, subscription *models.BlogNewsletterSubscription) error {
	result := r.db.WithContext(ctx).
		Model(&models.BlogNewsletterSubscription{}).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Updates(subscription)
	
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}
	return nil
}

// Delete soft deletes a subscription
func (r *blogNewsletterSubscriptionRepository) Delete(ctx context.Context, tenantID, id uint) error {
	result := r.db.WithContext(ctx).
		Model(&models.BlogNewsletterSubscription{}).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Update("status", models.SubscriptionStatusDeleted)
	
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}
	return nil
}

// List retrieves subscriptions with filters
func (r *blogNewsletterSubscriptionRepository) List(ctx context.Context, filter *models.BlogNewsletterSubscriptionFilter) ([]models.BlogNewsletterSubscription, int64, error) {
	var subscriptions []models.BlogNewsletterSubscription
	var total int64

	query := r.db.WithContext(ctx).Model(&models.BlogNewsletterSubscription{})

	// Apply filters
	query = query.Where("tenant_id = ? AND website_id = ?", filter.TenantID, filter.WebsiteID)

	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}

	if filter.Frequency != "" {
		query = query.Where("frequency = ?", filter.Frequency)
	}

	if filter.Email != "" {
		query = query.Where("email = ?", filter.Email)
	}

	if filter.Search != "" {
		searchPattern := "%" + filter.Search + "%"
		query = query.Where("email LIKE ? OR name LIKE ?", searchPattern, searchPattern)
	}

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	sortBy := "created_at"
	if filter.SortBy != "" {
		sortBy = filter.SortBy
	}
	sortOrder := "DESC"
	if filter.SortOrder != "" {
		sortOrder = filter.SortOrder
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Apply pagination
	if filter.Limit > 0 {
		query = query.Limit(filter.Limit)
	}
	if filter.Offset > 0 {
		query = query.Offset(filter.Offset)
	}

	// Execute query
	if err := query.Find(&subscriptions).Error; err != nil {
		return nil, 0, err
	}

	return subscriptions, total, nil
}

// ListWithCursor retrieves subscriptions with cursor-based pagination
func (r *blogNewsletterSubscriptionRepository) ListWithCursor(ctx context.Context, tenantID, websiteID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]models.BlogNewsletterSubscription, *pagination.CursorResponse, error) {
	var subscriptions []models.BlogNewsletterSubscription

	query := r.db.WithContext(ctx).Model(&models.BlogNewsletterSubscription{}).
		Where("tenant_id = ? AND website_id = ? AND status != ?", tenantID, websiteID, models.SubscriptionStatusDeleted)

	// Apply filters
	for key, value := range filters {
		switch key {
		case "status":
			query = query.Where("status = ?", value)
		case "frequency":
			query = query.Where("frequency = ?", value)
		case "search":
			searchPattern := "%" + value.(string) + "%"
			query = query.Where("email LIKE ? OR name LIKE ?", searchPattern, searchPattern)
		}
	}

	// Apply cursor logic
	if req.Cursor != "" {
		cursorData, err := pagination.DecodeCursor(req.Cursor)
		if err == nil && cursorData != nil {
			query = query.Where("(created_at < ? OR (created_at = ? AND id < ?))",
				cursorData.Time, cursorData.Time, cursorData.ID)
		}
	}

	// Apply sorting and limit
	query = query.Order("created_at DESC, id DESC").
		Limit(req.Limit + 1) // +1 to check if there are more results

	// Execute query
	err := query.Find(&subscriptions).Error
	if err != nil {
		return nil, nil, err
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(subscriptions) > req.Limit,
		Limit:   req.Limit,
		Count:   len(subscriptions),
	}

	// Trim results if needed
	if response.HasNext {
		subscriptions = subscriptions[:req.Limit]
		// Set next cursor based on last item
		if len(subscriptions) > 0 {
			last := subscriptions[len(subscriptions)-1]
			response.NextCursor, _ = pagination.EncodeCursor(last.ID, last.CreatedAt)
		}
	}

	response.HasMore = response.HasNext

	return subscriptions, response, nil
}

// GetActiveSubscribers retrieves active subscribers by frequency
func (r *blogNewsletterSubscriptionRepository) GetActiveSubscribers(ctx context.Context, tenantID, websiteID uint, frequency models.NewsletterFrequency) ([]models.BlogNewsletterSubscription, error) {
	var subscriptions []models.BlogNewsletterSubscription
	
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND status = ? AND frequency = ?", 
			tenantID, websiteID, models.SubscriptionStatusConfirmed, frequency).
		Find(&subscriptions).Error
		
	return subscriptions, err
}

// GetSubscribersByCategories retrieves subscribers interested in specific categories
func (r *blogNewsletterSubscriptionRepository) GetSubscribersByCategories(ctx context.Context, tenantID, websiteID uint, categoryIDs []uint) ([]models.BlogNewsletterSubscription, error) {
	var subscriptions []models.BlogNewsletterSubscription
	
	if len(categoryIDs) == 0 {
		return subscriptions, nil
	}

	// Build JSON contains query for array of category IDs
	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND status = ?", 
			tenantID, websiteID, models.SubscriptionStatusConfirmed)

	// Check if any of the categoryIDs exist in the categories JSON array
	for _, id := range categoryIDs {
		query = query.Or("JSON_CONTAINS(categories, ?)", fmt.Sprintf("%d", id))
	}

	err := query.Find(&subscriptions).Error
	return subscriptions, err
}

// GetSubscribersByTags retrieves subscribers interested in specific tags
func (r *blogNewsletterSubscriptionRepository) GetSubscribersByTags(ctx context.Context, tenantID, websiteID uint, tagIDs []uint) ([]models.BlogNewsletterSubscription, error) {
	var subscriptions []models.BlogNewsletterSubscription
	
	if len(tagIDs) == 0 {
		return subscriptions, nil
	}

	// Build JSON contains query for array of tag IDs
	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND status = ?", 
			tenantID, websiteID, models.SubscriptionStatusConfirmed)

	// Check if any of the tagIDs exist in the tags JSON array
	for _, id := range tagIDs {
		query = query.Or("JSON_CONTAINS(tags, ?)", fmt.Sprintf("%d", id))
	}

	err := query.Find(&subscriptions).Error
	return subscriptions, err
}

// Confirm confirms a subscription
func (r *blogNewsletterSubscriptionRepository) Confirm(ctx context.Context, token string) error {
	now := time.Now()
	result := r.db.WithContext(ctx).
		Model(&models.BlogNewsletterSubscription{}).
		Where("token = ? AND status = ?", token, models.SubscriptionStatusPending).
		Updates(map[string]interface{}{
			"status":       models.SubscriptionStatusConfirmed,
			"confirmed_at": now,
		})
	
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}
	return nil
}

// Unsubscribe unsubscribes using token
func (r *blogNewsletterSubscriptionRepository) Unsubscribe(ctx context.Context, token string) error {
	result := r.db.WithContext(ctx).
		Model(&models.BlogNewsletterSubscription{}).
		Where("token = ?", token).
		Update("status", models.SubscriptionStatusUnsubscribed)
	
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}
	return nil
}

// UpdateLastSentAt updates the last sent timestamp
func (r *blogNewsletterSubscriptionRepository) UpdateLastSentAt(ctx context.Context, tenantID, id uint) error {
	result := r.db.WithContext(ctx).
		Model(&models.BlogNewsletterSubscription{}).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Update("last_sent_at", time.Now())
	
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}
	return nil
}

// BatchUpdateLastSentAt updates last sent timestamp for multiple subscriptions
func (r *blogNewsletterSubscriptionRepository) BatchUpdateLastSentAt(ctx context.Context, tenantID uint, subscriptionIDs []uint) error {
	if len(subscriptionIDs) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).
		Model(&models.BlogNewsletterSubscription{}).
		Where("tenant_id = ? AND id IN ?", tenantID, subscriptionIDs).
		Update("last_sent_at", time.Now()).Error
}

// GetStatsByWebsite retrieves subscription statistics for a website
func (r *blogNewsletterSubscriptionRepository) GetStatsByWebsite(ctx context.Context, tenantID, websiteID uint) (*models.NewsletterSubscriptionStats, error) {
	stats := &models.NewsletterSubscriptionStats{
		ByFrequency: make(map[models.NewsletterFrequency]int),
		ByStatus:    make(map[models.NewsletterSubscriptionStatus]int),
	}

	// Get total count
	var totalCount int64
	r.db.WithContext(ctx).
		Model(&models.BlogNewsletterSubscription{}).
		Where("tenant_id = ? AND website_id = ? AND status != ?", tenantID, websiteID, models.SubscriptionStatusDeleted).
		Count(&totalCount)
	stats.TotalSubscribers = int(totalCount)

	// Get counts by status
	var statusCounts []struct {
		Status models.NewsletterSubscriptionStatus
		Count  int
	}
	r.db.WithContext(ctx).
		Model(&models.BlogNewsletterSubscription{}).
		Select("status, COUNT(*) as count").
		Where("tenant_id = ? AND website_id = ?", tenantID, websiteID).
		Group("status").
		Scan(&statusCounts)

	for _, sc := range statusCounts {
		stats.ByStatus[sc.Status] = sc.Count
		switch sc.Status {
		case models.SubscriptionStatusConfirmed:
			stats.ConfirmedSubscribers = sc.Count
		case models.SubscriptionStatusPending:
			stats.PendingSubscribers = sc.Count
		case models.SubscriptionStatusUnsubscribed:
			stats.UnsubscribedCount = sc.Count
		case models.SubscriptionStatusBounced:
			stats.BouncedCount = sc.Count
		}
	}

	// Get counts by frequency for confirmed subscriptions
	var frequencyCounts []struct {
		Frequency models.NewsletterFrequency
		Count     int
	}
	r.db.WithContext(ctx).
		Model(&models.BlogNewsletterSubscription{}).
		Select("frequency, COUNT(*) as count").
		Where("tenant_id = ? AND website_id = ? AND status = ?", tenantID, websiteID, models.SubscriptionStatusConfirmed).
		Group("frequency").
		Scan(&frequencyCounts)

	for _, fc := range frequencyCounts {
		stats.ByFrequency[fc.Frequency] = fc.Count
	}

	return stats, nil
}

// GetGrowthData retrieves subscription growth data
func (r *blogNewsletterSubscriptionRepository) GetGrowthData(ctx context.Context, tenantID, websiteID uint, days int) ([]models.NewsletterGrowthData, error) {
	var growthData []models.NewsletterGrowthData
	
	startDate := time.Now().AddDate(0, 0, -days)
	
	// Query to get daily subscription data
	query := `
		SELECT 
			DATE(created_at) as date,
			COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as subscribed,
			COUNT(CASE WHEN status = 'unsubscribed' AND updated_at >= ? THEN 1 END) as unsubscribed
		FROM blog_newsletter_subscriptions
		WHERE tenant_id = ? AND website_id = ? AND created_at >= ?
		GROUP BY DATE(created_at)
		ORDER BY date ASC
	`
	
	rows, err := r.db.WithContext(ctx).Raw(query, startDate, tenantID, websiteID, startDate).Rows()
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var runningTotal int
	for rows.Next() {
		var data models.NewsletterGrowthData
		var subscribed, unsubscribed int
		
		err := rows.Scan(&data.Date, &subscribed, &unsubscribed)
		if err != nil {
			return nil, err
		}
		
		data.Subscribed = subscribed
		data.Unsubscribed = unsubscribed
		data.NetGrowth = subscribed - unsubscribed
		runningTotal += data.NetGrowth
		data.Total = runningTotal
		
		growthData = append(growthData, data)
	}

	return growthData, nil
}

// CleanupUnconfirmed removes old unconfirmed subscriptions
func (r *blogNewsletterSubscriptionRepository) CleanupUnconfirmed(ctx context.Context, olderThan time.Time) error {
	return r.db.WithContext(ctx).
		Where("status = ? AND created_at < ?", models.SubscriptionStatusPending, olderThan).
		Delete(&models.BlogNewsletterSubscription{}).Error
}

// CleanupBounced removes old bounced subscriptions
func (r *blogNewsletterSubscriptionRepository) CleanupBounced(ctx context.Context, olderThan time.Time) error {
	return r.db.WithContext(ctx).
		Where("status = ? AND updated_at < ?", models.SubscriptionStatusBounced, olderThan).
		Delete(&models.BlogNewsletterSubscription{}).Error
}

// generateToken generates a secure random token
func generateToken() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}
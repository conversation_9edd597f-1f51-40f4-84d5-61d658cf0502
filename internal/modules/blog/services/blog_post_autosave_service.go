package services

import (
	"context"
	"errors"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// BlogPostAutosaveService handles business logic for blog post auto-saves
type BlogPostAutosaveService interface {
	// Auto-save operations
	AutoSave(ctx context.Context, tenantID, postID, userID, websiteID uint, request *models.BlogPostAutosaveCreateRequest) (*models.BlogPostAutosaveResponse, error)
	GetAutosave(ctx context.Context, tenantID, postID, userID uint) (*models.BlogPostAutosaveResponse, error)
	DeleteAutosave(ctx context.Context, tenantID, postID, userID uint) error
	
	// Status and conflict management
	GetAutosaveStatus(ctx context.Context, tenantID, postID, userID uint) (*models.BlogPostAutosaveStatus, error)
	ResolveConflict(ctx context.Context, tenantID, userID uint, request *models.BlogPostAutosaveConflictRequest) error
	GetConflictedAutosaves(ctx context.Context, tenantID uint) ([]*models.BlogPostAutosaveResponse, error)
	
	// Maintenance
	CleanupOldAutosaves(ctx context.Context) error
	
	// Restore operations
	RestoreFromAutosave(ctx context.Context, tenantID, postID, userID uint) (*models.BlogPostResponse, error)
}

type blogPostAutosaveService struct {
	autosaveRepo repositories.BlogPostAutosaveRepository
	postRepo     repositories.BlogPostRepository
	logger       utils.Logger
}

// NewBlogPostAutosaveService creates a new blog post auto-save service
func NewBlogPostAutosaveService(
	autosaveRepo repositories.BlogPostAutosaveRepository,
	postRepo repositories.BlogPostRepository,
	logger utils.Logger,
) BlogPostAutosaveService {
	return &blogPostAutosaveService{
		autosaveRepo: autosaveRepo,
		postRepo:     postRepo,
		logger:       logger,
	}
}

// AutoSave creates or updates an auto-save for a blog post
func (s *blogPostAutosaveService) AutoSave(ctx context.Context, tenantID, postID, userID, websiteID uint, request *models.BlogPostAutosaveCreateRequest) (*models.BlogPostAutosaveResponse, error) {
	// Validate that the post exists and user has access
	_, err := s.postRepo.GetByID(ctx, tenantID, postID)
	if err != nil {
		return nil, err
	}
	
	// Create auto-save model
	autosave := &models.BlogPostAutosave{
		TenantID:      tenantID,
		PostID:        postID,
		UserID:        userID,
		WebsiteID:     websiteID,
		Title:         request.Title,
		Content:       request.Content,
		Excerpt:       request.Excerpt,
		FeaturedImage: request.FeaturedImage,
	}
	
	// Create or update auto-save
	if err := s.autosaveRepo.CreateOrUpdate(autosave); err != nil {
		s.logger.WithError(err).Error("Failed to create or update auto-save")
		return nil, err
	}
	
	// Log auto-save activity
	s.logger.WithFields(map[string]interface{}{
		"tenant_id": tenantID,
		"post_id":   postID,
		"user_id":   userID,
		"version":   autosave.Version,
	}).Info("Auto-saved blog post")
	
	// Convert to response
	response := &models.BlogPostAutosaveResponse{}
	response.FromBlogPostAutosave(autosave)
	
	return response, nil
}

// GetAutosave retrieves an auto-save for a specific user and post
func (s *blogPostAutosaveService) GetAutosave(ctx context.Context, tenantID, postID, userID uint) (*models.BlogPostAutosaveResponse, error) {
	autosave, err := s.autosaveRepo.GetByPostIDAndUserID(postID, userID)
	if err != nil {
		return nil, err
	}
	
	// Verify tenant access
	if autosave.TenantID != tenantID {
		return nil, errors.New("unauthorized access to auto-save")
	}
	
	response := &models.BlogPostAutosaveResponse{}
	response.FromBlogPostAutosave(autosave)
	
	return response, nil
}

// DeleteAutosave removes an auto-save
func (s *blogPostAutosaveService) DeleteAutosave(ctx context.Context, tenantID, postID, userID uint) error {
	// Verify the auto-save exists and belongs to the tenant
	autosave, err := s.autosaveRepo.GetByPostIDAndUserID(postID, userID)
	if err != nil {
		return err
	}
	
	if autosave.TenantID != tenantID {
		return errors.New("unauthorized access to auto-save")
	}
	
	return s.autosaveRepo.DeleteByPostIDAndUserID(postID, userID)
}

// GetAutosaveStatus retrieves the auto-save status for a post
func (s *blogPostAutosaveService) GetAutosaveStatus(ctx context.Context, tenantID, postID, userID uint) (*models.BlogPostAutosaveStatus, error) {
	// Verify post exists and belongs to tenant
	post, err := s.postRepo.GetByID(ctx, tenantID, postID)
	if err != nil {
		return nil, err
	}
	
	if post.TenantID != tenantID {
		return nil, errors.New("unauthorized access to post")
	}
	
	return s.autosaveRepo.GetAutosaveStatus(postID, userID)
}

// ResolveConflict handles conflict resolution for auto-saves
func (s *blogPostAutosaveService) ResolveConflict(ctx context.Context, tenantID, userID uint, request *models.BlogPostAutosaveConflictRequest) error {
	// Get the auto-save
	autosave, err := s.autosaveRepo.GetByPostIDAndUserID(request.PostID, userID)
	if err != nil {
		return err
	}
	
	// Verify tenant access
	if autosave.TenantID != tenantID {
		return errors.New("unauthorized access to auto-save")
	}
	
	// Handle conflict resolution based on the resolution type
	switch request.Resolution {
	case models.ConflictResolutionKeepSaved:
		// Simply delete the auto-save
		return s.autosaveRepo.Delete(autosave.ID)
		
	case models.ConflictResolutionUseAutosave:
		// Update the post with auto-save content
		post, err := s.postRepo.GetByID(ctx, tenantID, request.PostID)
		if err != nil {
			return err
		}
		
		// Update post with auto-save content
		post.Title = autosave.Title
		post.Content = autosave.Content
		post.Excerpt = autosave.Excerpt
		post.FeaturedImage = autosave.FeaturedImage
		
		if err := s.postRepo.Update(ctx, tenantID, post.ID, post); err != nil {
			return err
		}
		
		// Delete the auto-save after applying
		return s.autosaveRepo.Delete(autosave.ID)
		
	case models.ConflictResolutionMerge:
		// Mark as resolved but keep the auto-save for manual merge
		return s.autosaveRepo.ResolveConflict(autosave.ID)
		
	default:
		return errors.New("invalid conflict resolution type")
	}
}

// GetConflictedAutosaves retrieves all conflicted auto-saves for a tenant
func (s *blogPostAutosaveService) GetConflictedAutosaves(ctx context.Context, tenantID uint) ([]*models.BlogPostAutosaveResponse, error) {
	autosaves, err := s.autosaveRepo.GetConflictedAutosaves(tenantID)
	if err != nil {
		return nil, err
	}
	
	responses := make([]*models.BlogPostAutosaveResponse, len(autosaves))
	for i, autosave := range autosaves {
		response := &models.BlogPostAutosaveResponse{}
		response.FromBlogPostAutosave(autosave)
		responses[i] = response
	}
	
	return responses, nil
}

// CleanupOldAutosaves removes auto-saves older than 7 days
func (s *blogPostAutosaveService) CleanupOldAutosaves(ctx context.Context) error {
	// Clean up auto-saves older than 7 days
	olderThan := 7 * 24 * time.Hour
	
	if err := s.autosaveRepo.CleanupOldAutosaves(olderThan); err != nil {
		s.logger.WithError(err).Error("Failed to cleanup old auto-saves")
		return err
	}
	
	s.logger.Info("Cleaned up old auto-saves")
	return nil
}

// RestoreFromAutosave restores a blog post from an auto-save
func (s *blogPostAutosaveService) RestoreFromAutosave(ctx context.Context, tenantID, postID, userID uint) (*models.BlogPostResponse, error) {
	// Get the auto-save
	autosave, err := s.autosaveRepo.GetByPostIDAndUserID(postID, userID)
	if err != nil {
		return nil, err
	}
	
	// Verify tenant access
	if autosave.TenantID != tenantID {
		return nil, errors.New("unauthorized access to auto-save")
	}
	
	// Get the post
	post, err := s.postRepo.GetByID(ctx, tenantID, postID)
	if err != nil {
		return nil, err
	}
	
	// Update post with auto-save content
	post.Title = autosave.Title
	post.Content = autosave.Content
	post.Excerpt = autosave.Excerpt
	post.FeaturedImage = autosave.FeaturedImage
	
	// Save the updated post
	if err := s.postRepo.Update(ctx, tenantID, post.ID, post); err != nil {
		return nil, err
	}
	
	// Delete the auto-save after restoration
	if err := s.autosaveRepo.Delete(autosave.ID); err != nil {
		s.logger.WithError(err).Warn("Failed to delete auto-save after restoration")
	}
	
	// Convert to response
	response := &models.BlogPostResponse{}
	response.FromBlogPost(post)
	
	return response, nil
}
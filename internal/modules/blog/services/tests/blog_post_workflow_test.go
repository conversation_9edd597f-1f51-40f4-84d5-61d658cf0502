package tests

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
)

// MockBlogPostRepository is a mock implementation of the BlogPostRepository interface
type MockBlogPostRepository struct {
	mock.Mock
}

func (m *MockBlogPostRepository) GetByID(ctx context.Context, tenantID, id uint) (*models.BlogPost, error) {
	args := m.Called(ctx, tenantID, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.BlogPost), args.Error(1)
}

func (m *MockBlogPostRepository) Update(ctx context.Context, tenantID, id uint, post *models.BlogPost) error {
	args := m.Called(ctx, tenantID, id, post)
	return args.Error(0)
}

// Add other required methods to satisfy the interface
func (m *MockBlogPostRepository) Create(ctx context.Context, post *models.BlogPost) error {
	args := m.Called(ctx, post)
	return args.Error(0)
}

func (m *MockBlogPostRepository) GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.BlogPost, error) {
	args := m.Called(ctx, tenantID, websiteID, slug)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.BlogPost), args.Error(1)
}

func (m *MockBlogPostRepository) Delete(ctx context.Context, tenantID, id uint) error {
	args := m.Called(ctx, tenantID, id)
	return args.Error(0)
}

func (m *MockBlogPostRepository) List(ctx context.Context, filter *models.BlogPostFilter) ([]models.BlogPost, int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).([]models.BlogPost), args.Get(1).(int64), args.Error(2)
}

// Mock implementations for other required methods...
func (m *MockBlogPostRepository) Publish(ctx context.Context, tenantID, postID uint) error {
	args := m.Called(ctx, tenantID, postID)
	return args.Error(0)
}

func (m *MockBlogPostRepository) Unpublish(ctx context.Context, tenantID, postID uint) error {
	args := m.Called(ctx, tenantID, postID)
	return args.Error(0)
}

func (m *MockBlogPostRepository) Schedule(ctx context.Context, tenantID, postID uint, scheduledAt *time.Time) error {
	args := m.Called(ctx, tenantID, postID, scheduledAt)
	return args.Error(0)
}

func (m *MockBlogPostRepository) AttachTags(ctx context.Context, tenantID, postID uint, tagIDs []uint) error {
	args := m.Called(ctx, tenantID, postID, tagIDs)
	return args.Error(0)
}

func (m *MockBlogPostRepository) DetachTags(ctx context.Context, tenantID, postID uint, tagIDs []uint) error {
	args := m.Called(ctx, tenantID, postID, tagIDs)
	return args.Error(0)
}

func (m *MockBlogPostRepository) IncrementViewCount(ctx context.Context, tenantID, postID uint) error {
	args := m.Called(ctx, tenantID, postID)
	return args.Error(0)
}

func (m *MockBlogPostRepository) GetStats(ctx context.Context, tenantID, websiteID uint) (*models.BlogPostStats, error) {
	args := m.Called(ctx, tenantID, websiteID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.BlogPostStats), args.Error(1)
}

func (m *MockBlogPostRepository) GetPublished(ctx context.Context, tenantID, websiteID uint, limit, offset int) ([]models.BlogPost, int64, error) {
	args := m.Called(ctx, tenantID, websiteID, limit, offset)
	return args.Get(0).([]models.BlogPost), args.Get(1).(int64), args.Error(2)
}

func (m *MockBlogPostRepository) GetFeatured(ctx context.Context, tenantID, websiteID uint, limit int) ([]models.BlogPost, error) {
	args := m.Called(ctx, tenantID, websiteID, limit)
	return args.Get(0).([]models.BlogPost), args.Error(1)
}

func (m *MockBlogPostRepository) GetRelated(ctx context.Context, tenantID, postID uint, limit int) ([]models.BlogPost, error) {
	args := m.Called(ctx, tenantID, postID, limit)
	return args.Get(0).([]models.BlogPost), args.Error(1)
}

func (m *MockBlogPostRepository) GetPopular(ctx context.Context, tenantID, websiteID uint, days int, limit int) ([]models.BlogPost, error) {
	args := m.Called(ctx, tenantID, websiteID, days, limit)
	return args.Get(0).([]models.BlogPost), args.Error(1)
}

// Mock other repositories for completeness
type MockBlogCategoryRepository struct {
	mock.Mock
}

func (m *MockBlogCategoryRepository) IncrementUsage(ctx context.Context, tenantID, categoryID uint) error {
	args := m.Called(ctx, tenantID, categoryID)
	return args.Error(0)
}

type MockBlogTagRepository struct {
	mock.Mock
}

func (m *MockBlogTagRepository) IncrementUsage(ctx context.Context, tenantID, tagID uint) error {
	args := m.Called(ctx, tenantID, tagID)
	return args.Error(0)
}

type MockBlogPostRevisionRepository struct {
	mock.Mock
}

func (m *MockBlogPostRevisionRepository) Create(ctx context.Context, revision *models.BlogPostRevision) error {
	args := m.Called(ctx, revision)
	return args.Error(0)
}

func (m *MockBlogPostRevisionRepository) GetByID(ctx context.Context, tenantID, id uint) (*models.BlogPostRevision, error) {
	args := m.Called(ctx, tenantID, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.BlogPostRevision), args.Error(1)
}

// MockNotificationService for testing
type MockNotificationService struct {
	mock.Mock
}

func (m *MockNotificationService) SendNotification(ctx context.Context, req interface{}) error {
	args := m.Called(ctx, req)
	return args.Error(0)
}

// MockLogger for testing
type MockLogger struct {
	mock.Mock
}

func (m *MockLogger) Info(msg string, fields ...interface{}) {
	m.Called(msg, fields)
}

func (m *MockLogger) Error(msg string, fields ...interface{}) {
	m.Called(msg, fields)
}

func (m *MockLogger) Debug(msg string, fields ...interface{}) {
	m.Called(msg, fields)
}

func (m *MockLogger) Warn(msg string, fields ...interface{}) {
	m.Called(msg, fields)
}

// Test Suite for Workflow Functions
func TestBlogPostService_AssignToReviewer(t *testing.T) {
	tests := []struct {
		name          string
		tenantID      uint
		postID        uint
		assignedTo    uint
		notes         string
		setupMocks    func(*MockBlogPostRepository)
		expectedError string
	}{
		{
			name:       "successful assignment",
			tenantID:   1,
			postID:     123,
			assignedTo: 456,
			notes:      "Please review ASAP",
			setupMocks: func(mockRepo *MockBlogPostRepository) {
				post := &models.BlogPost{
					ID:            123,
					TenantID:      1,
					WorkflowState: models.WorkflowStateCreation,
				}
				
				mockRepo.On("GetByID", mock.Anything, uint(1), uint(123)).Return(post, nil)
				mockRepo.On("Update", mock.Anything, uint(1), uint(123), mock.MatchedBy(func(post *models.BlogPost) bool {
					return post.WorkflowState == models.WorkflowStatePendingReview &&
						   post.WorkflowAssignedTo != nil && *post.WorkflowAssignedTo == 456 &&
						   post.WorkflowNotes == "Please review ASAP" &&
						   post.WorkflowAssignedAt != nil
				})).Return(nil)
			},
			expectedError: "",
		},
		{
			name:       "post not found",
			tenantID:   1,
			postID:     999,
			assignedTo: 456,
			notes:      "Review notes",
			setupMocks: func(mockRepo *MockBlogPostRepository) {
				mockRepo.On("GetByID", mock.Anything, uint(1), uint(999)).Return(nil, gorm.ErrRecordNotFound)
			},
			expectedError: "post not found",
		},
		{
			name:       "database error on update",
			tenantID:   1,
			postID:     123,
			assignedTo: 456,
			notes:      "Review notes",
			setupMocks: func(mockRepo *MockBlogPostRepository) {
				post := &models.BlogPost{
					ID:            123,
					TenantID:      1,
					WorkflowState: models.WorkflowStateCreation,
				}
				
				mockRepo.On("GetByID", mock.Anything, uint(1), uint(123)).Return(post, nil)
				mockRepo.On("Update", mock.Anything, uint(1), uint(123), mock.Anything).Return(errors.New("database error"))
			},
			expectedError: "failed to assign reviewer: database error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockRepo := new(MockBlogPostRepository)
			mockCategoryRepo := new(MockBlogCategoryRepository)
			mockTagRepo := new(MockBlogTagRepository)
			mockRevisionRepo := new(MockBlogPostRevisionRepository)
			
			tt.setupMocks(mockRepo)

			// Create service instance
			mockNotificationService := new(MockNotificationService)
			mockLogger := new(MockLogger)
			service := services.NewBlogPostService(mockRepo, mockTagRepo, mockNotificationService, mockLogger)

			// Execute
			ctx := context.Background()
			err := service.AssignToReviewer(ctx, tt.tenantID, tt.postID, tt.assignedTo, tt.notes)

			// Assert
			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
			} else {
				assert.NoError(t, err)
			}

			mockRepo.AssertExpectations(t)
		})
	}
}

func TestBlogPostService_SubmitForReview(t *testing.T) {
	tests := []struct {
		name            string
		tenantID        uint
		postID          uint
		userID          uint
		initialState    models.WorkflowState
		setupMocks      func(*MockBlogPostRepository)
		expectedError   string
	}{
		{
			name:         "successful submission from creation state",
			tenantID:     1,
			postID:       123,
			userID:       789,
			initialState: models.WorkflowStateCreation,
			setupMocks: func(mockRepo *MockBlogPostRepository) {
				post := &models.BlogPost{
					ID:            123,
					TenantID:      1,
					WorkflowState: models.WorkflowStateCreation,
				}
				
				mockRepo.On("GetByID", mock.Anything, uint(1), uint(123)).Return(post, nil)
				mockRepo.On("Update", mock.Anything, uint(1), uint(123), mock.MatchedBy(func(post *models.BlogPost) bool {
					return post.WorkflowState == models.WorkflowStatePendingReview
				})).Return(nil)
			},
			expectedError: "",
		},
		{
			name:         "invalid state transition",
			tenantID:     1,
			postID:       123,
			userID:       789,
			initialState: models.WorkflowStateInReview,
			setupMocks: func(mockRepo *MockBlogPostRepository) {
				post := &models.BlogPost{
					ID:            123,
					TenantID:      1,
					WorkflowState: models.WorkflowStateInReview,
				}
				
				mockRepo.On("GetByID", mock.Anything, uint(1), uint(123)).Return(post, nil)
			},
			expectedError: "post is not in creation state",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockRepo := new(MockBlogPostRepository)
			mockCategoryRepo := new(MockBlogCategoryRepository)
			mockTagRepo := new(MockBlogTagRepository)
			mockRevisionRepo := new(MockBlogPostRevisionRepository)
			
			tt.setupMocks(mockRepo)

			mockNotificationService := new(MockNotificationService)
			mockLogger := new(MockLogger)
			service := services.NewBlogPostService(mockRepo, mockTagRepo, mockNotificationService, mockLogger)

			// Execute
			ctx := context.Background()
			err := service.SubmitForReview(ctx, tt.tenantID, tt.postID, tt.userID)

			// Assert
			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
			} else {
				assert.NoError(t, err)
			}

			mockRepo.AssertExpectations(t)
		})
	}
}

func TestBlogPostService_ApprovePost(t *testing.T) {
	tests := []struct {
		name          string
		tenantID      uint
		postID        uint
		userID        uint
		notes         string
		initialState  models.WorkflowState
		setupMocks    func(*MockBlogPostRepository)
		expectedError string
	}{
		{
			name:         "successful approval from in_review state",
			tenantID:     1,
			postID:       123,
			userID:       789,
			notes:        "Approved for publication",
			initialState: models.WorkflowStateInReview,
			setupMocks: func(mockRepo *MockBlogPostRepository) {
				post := &models.BlogPost{
					ID:            123,
					TenantID:      1,
					WorkflowState: models.WorkflowStateInReview,
				}
				
				mockRepo.On("GetByID", mock.Anything, uint(1), uint(123)).Return(post, nil)
				mockRepo.On("Update", mock.Anything, uint(1), uint(123), mock.MatchedBy(func(post *models.BlogPost) bool {
					return post.WorkflowState == models.WorkflowStateApproved &&
						   post.WorkflowNotes == "Approved for publication"
				})).Return(nil)
			},
			expectedError: "",
		},
		{
			name:         "successful approval from pending_approval state",
			tenantID:     1,
			postID:       123,
			userID:       789,
			notes:        "Final approval granted",
			initialState: models.WorkflowStatePendingApproval,
			setupMocks: func(mockRepo *MockBlogPostRepository) {
				post := &models.BlogPost{
					ID:            123,
					TenantID:      1,
					WorkflowState: models.WorkflowStatePendingApproval,
				}
				
				mockRepo.On("GetByID", mock.Anything, uint(1), uint(123)).Return(post, nil)
				mockRepo.On("Update", mock.Anything, uint(1), uint(123), mock.MatchedBy(func(post *models.BlogPost) bool {
					return post.WorkflowState == models.WorkflowStateApproved &&
						   post.WorkflowNotes == "Final approval granted"
				})).Return(nil)
			},
			expectedError: "",
		},
		{
			name:         "invalid state for approval",
			tenantID:     1,
			postID:       123,
			userID:       789,
			notes:        "Cannot approve",
			initialState: models.WorkflowStateCreation,
			setupMocks: func(mockRepo *MockBlogPostRepository) {
				post := &models.BlogPost{
					ID:            123,
					TenantID:      1,
					WorkflowState: models.WorkflowStateCreation,
				}
				
				mockRepo.On("GetByID", mock.Anything, uint(1), uint(123)).Return(post, nil)
			},
			expectedError: "post is not in reviewable state",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockRepo := new(MockBlogPostRepository)
			mockCategoryRepo := new(MockBlogCategoryRepository)
			mockTagRepo := new(MockBlogTagRepository)
			mockRevisionRepo := new(MockBlogPostRevisionRepository)
			
			tt.setupMocks(mockRepo)

			mockNotificationService := new(MockNotificationService)
			mockLogger := new(MockLogger)
			service := services.NewBlogPostService(mockRepo, mockTagRepo, mockNotificationService, mockLogger)

			// Execute
			ctx := context.Background()
			err := service.ApprovePost(ctx, tt.tenantID, tt.postID, tt.userID, tt.notes)

			// Assert
			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
			} else {
				assert.NoError(t, err)
			}

			mockRepo.AssertExpectations(t)
		})
	}
}

func TestBlogPostService_RejectPost(t *testing.T) {
	tests := []struct {
		name          string
		tenantID      uint
		postID        uint
		userID        uint
		reason        string
		setupMocks    func(*MockBlogPostRepository)
		expectedError string
	}{
		{
			name:     "successful rejection",
			tenantID: 1,
			postID:   123,
			userID:   789,
			reason:   "Content does not meet quality standards",
			setupMocks: func(mockRepo *MockBlogPostRepository) {
				post := &models.BlogPost{
					ID:            123,
					TenantID:      1,
					WorkflowState: models.WorkflowStateInReview,
				}
				
				mockRepo.On("GetByID", mock.Anything, uint(1), uint(123)).Return(post, nil)
				mockRepo.On("Update", mock.Anything, uint(1), uint(123), mock.MatchedBy(func(post *models.BlogPost) bool {
					return post.WorkflowState == models.WorkflowStateRejected &&
						   post.WorkflowNotes == "Content does not meet quality standards"
				})).Return(nil)
			},
			expectedError: "",
		},
		{
			name:     "post not found for rejection",
			tenantID: 1,
			postID:   999,
			userID:   789,
			reason:   "Rejection reason",
			setupMocks: func(mockRepo *MockBlogPostRepository) {
				mockRepo.On("GetByID", mock.Anything, uint(1), uint(999)).Return(nil, gorm.ErrRecordNotFound)
			},
			expectedError: "post not found",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockRepo := new(MockBlogPostRepository)
			mockCategoryRepo := new(MockBlogCategoryRepository)
			mockTagRepo := new(MockBlogTagRepository)
			mockRevisionRepo := new(MockBlogPostRevisionRepository)
			
			tt.setupMocks(mockRepo)

			mockNotificationService := new(MockNotificationService)
			mockLogger := new(MockLogger)
			service := services.NewBlogPostService(mockRepo, mockTagRepo, mockNotificationService, mockLogger)

			// Execute
			ctx := context.Background()
			err := service.RejectPost(ctx, tt.tenantID, tt.postID, tt.userID, tt.reason)

			// Assert
			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
			} else {
				assert.NoError(t, err)
			}

			mockRepo.AssertExpectations(t)
		})
	}
}

func TestBlogPostService_ReturnToAuthor(t *testing.T) {
	tests := []struct {
		name          string
		tenantID      uint
		postID        uint
		userID        uint
		feedback      string
		setupMocks    func(*MockBlogPostRepository)
		expectedError string
	}{
		{
			name:     "successful return to author",
			tenantID: 1,
			postID:   123,
			userID:   789,
			feedback: "Please add more examples and fix grammar issues",
			setupMocks: func(mockRepo *MockBlogPostRepository) {
				post := &models.BlogPost{
					ID:            123,
					TenantID:      1,
					WorkflowState: models.WorkflowStateInReview,
				}
				
				mockRepo.On("GetByID", mock.Anything, uint(1), uint(123)).Return(post, nil)
				mockRepo.On("Update", mock.Anything, uint(1), uint(123), mock.MatchedBy(func(post *models.BlogPost) bool {
					return post.WorkflowState == models.WorkflowStateReturned &&
						   post.WorkflowNotes == "Please add more examples and fix grammar issues" &&
						   post.WorkflowAssignedTo == nil // Should unassign
				})).Return(nil)
			},
			expectedError: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockRepo := new(MockBlogPostRepository)
			mockCategoryRepo := new(MockBlogCategoryRepository)
			mockTagRepo := new(MockBlogTagRepository)
			mockRevisionRepo := new(MockBlogPostRevisionRepository)
			
			tt.setupMocks(mockRepo)

			mockNotificationService := new(MockNotificationService)
			mockLogger := new(MockLogger)
			service := services.NewBlogPostService(mockRepo, mockTagRepo, mockNotificationService, mockLogger)

			// Execute
			ctx := context.Background()
			err := service.ReturnToAuthor(ctx, tt.tenantID, tt.postID, tt.userID, tt.feedback)

			// Assert
			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
			} else {
				assert.NoError(t, err)
			}

			mockRepo.AssertExpectations(t)
		})
	}
}

func TestBlogPostService_GetWorkflowHistory(t *testing.T) {
	// Setup
	mockRepo := new(MockBlogPostRepository)
	mockCategoryRepo := new(MockBlogCategoryRepository)
	mockTagRepo := new(MockBlogTagRepository)
	mockRevisionRepo := new(MockBlogPostRevisionRepository)
	
	service := services.NewBlogPostService(mockRepo, mockCategoryRepo, mockTagRepo, mockRevisionRepo)

	// Execute
	ctx := context.Background()
	history, err := service.GetWorkflowHistory(ctx, 1, 123)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, history)
	assert.Equal(t, 0, len(history)) // Currently returns empty slice
}

func TestBlogPostService_GetPostsByWorkflowState(t *testing.T) {
	tests := []struct {
		name            string
		tenantID        uint
		websiteID       uint
		state           models.WorkflowState
		assignedTo      *uint
		setupMocks      func(*MockBlogPostRepository)
		expectedCount   int
		expectedError   string
	}{
		{
			name:      "get posts in pending_review state",
			tenantID:  1,
			websiteID: 2,
			state:     models.WorkflowStatePendingReview,
			assignedTo: nil,
			setupMocks: func(mockRepo *MockBlogPostRepository) {
				posts := []models.BlogPost{
					{
						ID:            1,
						TenantID:      1,
						WebsiteID:     2,
						WorkflowState: models.WorkflowStatePendingReview,
					},
					{
						ID:            2,
						TenantID:      1,
						WebsiteID:     2,
						WorkflowState: models.WorkflowStateInReview,
					},
				}
				
				mockRepo.On("List", mock.Anything, mock.MatchedBy(func(filter *models.BlogPostFilter) bool {
					return filter.TenantID == 1 && filter.WebsiteID == 2
				})).Return(posts, int64(2), nil)
			},
			expectedCount: 1, // Only one post in pending_review state
			expectedError: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockRepo := new(MockBlogPostRepository)
			mockCategoryRepo := new(MockBlogCategoryRepository)
			mockTagRepo := new(MockBlogTagRepository)
			mockRevisionRepo := new(MockBlogPostRevisionRepository)
			
			tt.setupMocks(mockRepo)

			mockNotificationService := new(MockNotificationService)
			mockLogger := new(MockLogger)
			service := services.NewBlogPostService(mockRepo, mockTagRepo, mockNotificationService, mockLogger)

			// Execute
			ctx := context.Background()
			posts, err := service.GetPostsByWorkflowState(ctx, tt.tenantID, tt.websiteID, tt.state, tt.assignedTo)

			// Assert
			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedCount, len(posts))
			}

			mockRepo.AssertExpectations(t)
		})
	}
}
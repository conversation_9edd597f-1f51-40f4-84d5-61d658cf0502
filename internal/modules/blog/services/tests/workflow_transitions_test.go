package tests

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
)

// TestWorkflowStateTransitions tests the validity of workflow state transitions
func TestWorkflowStateTransitions(t *testing.T) {
	tests := []struct {
		name        string
		fromState   models.WorkflowState
		toState     models.WorkflowState
		isValid     bool
		description string
	}{
		// Valid transitions from creation
		{
			name:        "creation to pending_review",
			fromState:   models.WorkflowStateCreation,
			toState:     models.WorkflowStatePendingReview,
			isValid:     true,
			description: "Author submits draft for review",
		},

		// Valid transitions from pending_review
		{
			name:        "pending_review to in_review",
			fromState:   models.WorkflowStatePendingReview,
			toState:     models.WorkflowStateInReview,
			isValid:     true,
			description: "Editor starts reviewing",
		},
		{
			name:        "pending_review to returned",
			fromState:   models.WorkflowStatePendingReview,
			toState:     models.WorkflowStateReturned,
			isValid:     true,
			description: "Editor returns to author without reviewing",
		},
		{
			name:        "pending_review to rejected",
			fromState:   models.WorkflowStatePendingReview,
			toState:     models.WorkflowStateRejected,
			isValid:     true,
			description: "Editor rejects immediately",
		},

		// Valid transitions from in_review
		{
			name:        "in_review to pending_approval",
			fromState:   models.WorkflowStateInReview,
			toState:     models.WorkflowStatePendingApproval,
			isValid:     true,
			description: "Editor approves, sends to editorial secretary",
		},
		{
			name:        "in_review to returned",
			fromState:   models.WorkflowStateInReview,
			toState:     models.WorkflowStateReturned,
			isValid:     true,
			description: "Editor returns to author for revisions",
		},
		{
			name:        "in_review to rejected",
			fromState:   models.WorkflowStateInReview,
			toState:     models.WorkflowStateRejected,
			isValid:     true,
			description: "Editor rejects the content",
		},
		{
			name:        "in_review to approved",
			fromState:   models.WorkflowStateInReview,
			toState:     models.WorkflowStateApproved,
			isValid:     true,
			description: "Editor with approval authority approves directly",
		},

		// Valid transitions from pending_approval
		{
			name:        "pending_approval to pending_eic",
			fromState:   models.WorkflowStatePendingApproval,
			toState:     models.WorkflowStatePendingEIC,
			isValid:     true,
			description: "Editorial secretary sends to editor-in-chief",
		},
		{
			name:        "pending_approval to approved",
			fromState:   models.WorkflowStatePendingApproval,
			toState:     models.WorkflowStateApproved,
			isValid:     true,
			description: "Editorial secretary approves directly",
		},
		{
			name:        "pending_approval to returned",
			fromState:   models.WorkflowStatePendingApproval,
			toState:     models.WorkflowStateReturned,
			isValid:     true,
			description: "Editorial secretary returns for revisions",
		},
		{
			name:        "pending_approval to rejected",
			fromState:   models.WorkflowStatePendingApproval,
			toState:     models.WorkflowStateRejected,
			isValid:     true,
			description: "Editorial secretary rejects",
		},

		// Valid transitions from pending_eic
		{
			name:        "pending_eic to approved",
			fromState:   models.WorkflowStatePendingEIC,
			toState:     models.WorkflowStateApproved,
			isValid:     true,
			description: "Editor-in-chief approves",
		},
		{
			name:        "pending_eic to returned",
			fromState:   models.WorkflowStatePendingEIC,
			toState:     models.WorkflowStateReturned,
			isValid:     true,
			description: "Editor-in-chief returns for revisions",
		},
		{
			name:        "pending_eic to rejected",
			fromState:   models.WorkflowStatePendingEIC,
			toState:     models.WorkflowStateRejected,
			isValid:     true,
			description: "Editor-in-chief rejects",
		},

		// Valid transitions from approved
		{
			name:        "approved to completed",
			fromState:   models.WorkflowStateApproved,
			toState:     models.WorkflowStateCompleted,
			isValid:     true,
			description: "Content is published and workflow completed",
		},

		// Valid transitions from returned
		{
			name:        "returned to pending_review",
			fromState:   models.WorkflowStateReturned,
			toState:     models.WorkflowStatePendingReview,
			isValid:     true,
			description: "Author resubmits after making revisions",
		},
		{
			name:        "returned to creation",
			fromState:   models.WorkflowStateReturned,
			toState:     models.WorkflowStateCreation,
			isValid:     true,
			description: "Author returns to draft mode for major revisions",
		},

		// Invalid transitions - these should not be allowed
		{
			name:        "creation to approved",
			fromState:   models.WorkflowStateCreation,
			toState:     models.WorkflowStateApproved,
			isValid:     false,
			description: "Cannot skip review process",
		},
		{
			name:        "completed to pending_review",
			fromState:   models.WorkflowStateCompleted,
			toState:     models.WorkflowStatePendingReview,
			isValid:     false,
			description: "Cannot reopen completed workflow",
		},
		{
			name:        "rejected to approved",
			fromState:   models.WorkflowStateRejected,
			toState:     models.WorkflowStateApproved,
			isValid:     false,
			description: "Cannot approve already rejected content",
		},
		{
			name:        "pending_review to completed",
			fromState:   models.WorkflowStatePendingReview,
			toState:     models.WorkflowStateCompleted,
			isValid:     false,
			description: "Cannot complete without approval",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			isValid := isValidWorkflowTransition(tt.fromState, tt.toState)
			assert.Equal(t, tt.isValid, isValid, 
				"Transition from %s to %s should be %v: %s",
				tt.fromState, tt.toState, tt.isValid, tt.description)
		})
	}
}

// TestWorkflowStateProperties tests properties of workflow states
func TestWorkflowStateProperties(t *testing.T) {
	tests := []struct {
		name              string
		state             models.WorkflowState
		isEditable        bool
		requiresAssignment bool
		isTerminal        bool
		allowsReassignment bool
	}{
		{
			name:               "creation state",
			state:              models.WorkflowStateCreation,
			isEditable:         true,
			requiresAssignment: false,
			isTerminal:         false,
			allowsReassignment: false,
		},
		{
			name:               "pending_review state",
			state:              models.WorkflowStatePendingReview,
			isEditable:         false,
			requiresAssignment: true,
			isTerminal:         false,
			allowsReassignment: true,
		},
		{
			name:               "in_review state",
			state:              models.WorkflowStateInReview,
			isEditable:         false,
			requiresAssignment: true,
			isTerminal:         false,
			allowsReassignment: true,
		},
		{
			name:               "pending_approval state",
			state:              models.WorkflowStatePendingApproval,
			isEditable:         false,
			requiresAssignment: true,
			isTerminal:         false,
			allowsReassignment: true,
		},
		{
			name:               "pending_eic state",
			state:              models.WorkflowStatePendingEIC,
			isEditable:         false,
			requiresAssignment: true,
			isTerminal:         false,
			allowsReassignment: false,
		},
		{
			name:               "approved state",
			state:              models.WorkflowStateApproved,
			isEditable:         false,
			requiresAssignment: false,
			isTerminal:         false,
			allowsReassignment: false,
		},
		{
			name:               "returned state",
			state:              models.WorkflowStateReturned,
			isEditable:         true,
			requiresAssignment: false,
			isTerminal:         false,
			allowsReassignment: false,
		},
		{
			name:               "rejected state",
			state:              models.WorkflowStateRejected,
			isEditable:         false,
			requiresAssignment: false,
			isTerminal:         true,
			allowsReassignment: false,
		},
		{
			name:               "completed state",
			state:              models.WorkflowStateCompleted,
			isEditable:         false,
			requiresAssignment: false,
			isTerminal:         true,
			allowsReassignment: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.isEditable, isStateEditable(tt.state),
				"State %s editability should be %v", tt.state, tt.isEditable)
			
			assert.Equal(t, tt.requiresAssignment, requiresAssignment(tt.state),
				"State %s assignment requirement should be %v", tt.state, tt.requiresAssignment)
			
			assert.Equal(t, tt.isTerminal, isTerminalState(tt.state),
				"State %s terminal status should be %v", tt.state, tt.isTerminal)
			
			assert.Equal(t, tt.allowsReassignment, allowsReassignment(tt.state),
				"State %s reassignment allowance should be %v", tt.state, tt.allowsReassignment)
		})
	}
}

// TestWorkflowRolePermissions tests which roles can perform which actions
func TestWorkflowRolePermissions(t *testing.T) {
	tests := []struct {
		name      string
		role      string
		action    string
		state     models.WorkflowState
		canPerform bool
	}{
		// Reporter permissions
		{
			name:       "reporter can submit for review",
			role:       "reporter",
			action:     "submit_for_review",
			state:      models.WorkflowStateCreation,
			canPerform: true,
		},
		{
			name:       "reporter can edit returned content",
			role:       "reporter",
			action:     "edit",
			state:      models.WorkflowStateReturned,
			canPerform: true,
		},
		{
			name:       "reporter cannot approve",
			role:       "reporter",
			action:     "approve",
			state:      models.WorkflowStateInReview,
			canPerform: false,
		},

		// Editor permissions
		{
			name:       "editor can start review",
			role:       "editor",
			action:     "start_review",
			state:      models.WorkflowStatePendingReview,
			canPerform: true,
		},
		{
			name:       "editor can approve from review",
			role:       "editor",
			action:     "approve",
			state:      models.WorkflowStateInReview,
			canPerform: true,
		},
		{
			name:       "editor can return to author",
			role:       "editor",
			action:     "return_to_author",
			state:      models.WorkflowStateInReview,
			canPerform: true,
		},
		{
			name:       "editor can reject",
			role:       "editor",
			action:     "reject",
			state:      models.WorkflowStateInReview,
			canPerform: true,
		},

		// Editorial Secretary permissions
		{
			name:       "editorial_secretary can approve",
			role:       "editorial_secretary",
			action:     "approve",
			state:      models.WorkflowStatePendingApproval,
			canPerform: true,
		},
		{
			name:       "editorial_secretary can send to EIC",
			role:       "editorial_secretary",
			action:     "send_to_eic",
			state:      models.WorkflowStatePendingApproval,
			canPerform: true,
		},

		// Editor-in-Chief permissions
		{
			name:       "eic can approve",
			role:       "editor_in_chief",
			action:     "approve",
			state:      models.WorkflowStatePendingEIC,
			canPerform: true,
		},
		{
			name:       "eic can override any state",
			role:       "editor_in_chief",
			action:     "override",
			state:      models.WorkflowStateInReview,
			canPerform: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			canPerform := canRolePerformAction(tt.role, tt.action, tt.state)
			assert.Equal(t, tt.canPerform, canPerform,
				"Role %s should %s be able to perform %s in state %s",
				tt.role, map[bool]string{true: "", false: "not"}[tt.canPerform], tt.action, tt.state)
		})
	}
}

// Helper functions for workflow business logic
func isValidWorkflowTransition(from, to models.WorkflowState) bool {
	validTransitions := map[models.WorkflowState][]models.WorkflowState{
		models.WorkflowStateCreation: {
			models.WorkflowStatePendingReview,
		},
		models.WorkflowStatePendingReview: {
			models.WorkflowStateInReview,
			models.WorkflowStateReturned,
			models.WorkflowStateRejected,
		},
		models.WorkflowStateInReview: {
			models.WorkflowStatePendingApproval,
			models.WorkflowStateApproved,
			models.WorkflowStateReturned,
			models.WorkflowStateRejected,
		},
		models.WorkflowStatePendingApproval: {
			models.WorkflowStatePendingEIC,
			models.WorkflowStateApproved,
			models.WorkflowStateReturned,
			models.WorkflowStateRejected,
		},
		models.WorkflowStatePendingEIC: {
			models.WorkflowStateApproved,
			models.WorkflowStateReturned,
			models.WorkflowStateRejected,
		},
		models.WorkflowStateApproved: {
			models.WorkflowStateCompleted,
		},
		models.WorkflowStateReturned: {
			models.WorkflowStateCreation,
			models.WorkflowStatePendingReview,
		},
		// Terminal states have no valid transitions
		models.WorkflowStateRejected:  {},
		models.WorkflowStateCompleted: {},
	}

	allowedStates := validTransitions[from]
	for _, allowedState := range allowedStates {
		if allowedState == to {
			return true
		}
	}
	return false
}

func isStateEditable(state models.WorkflowState) bool {
	editableStates := []models.WorkflowState{
		models.WorkflowStateCreation,
		models.WorkflowStateReturned,
	}
	
	for _, editableState := range editableStates {
		if editableState == state {
			return true
		}
	}
	return false
}

func requiresAssignment(state models.WorkflowState) bool {
	assignmentRequired := []models.WorkflowState{
		models.WorkflowStatePendingReview,
		models.WorkflowStateInReview,
		models.WorkflowStatePendingApproval,
		models.WorkflowStatePendingEIC,
	}
	
	for _, requiredState := range assignmentRequired {
		if requiredState == state {
			return true
		}
	}
	return false
}

func isTerminalState(state models.WorkflowState) bool {
	terminalStates := []models.WorkflowState{
		models.WorkflowStateRejected,
		models.WorkflowStateCompleted,
	}
	
	for _, terminalState := range terminalStates {
		if terminalState == state {
			return true
		}
	}
	return false
}

func allowsReassignment(state models.WorkflowState) bool {
	reassignableStates := []models.WorkflowState{
		models.WorkflowStatePendingReview,
		models.WorkflowStateInReview,
		models.WorkflowStatePendingApproval,
	}
	
	for _, reassignableState := range reassignableStates {
		if reassignableState == state {
			return true
		}
	}
	return false
}

func canRolePerformAction(role, action string, state models.WorkflowState) bool {
	permissions := map[string]map[string][]models.WorkflowState{
		"reporter": {
			"submit_for_review": {models.WorkflowStateCreation},
			"edit": {models.WorkflowStateCreation, models.WorkflowStateReturned},
		},
		"editor": {
			"start_review": {models.WorkflowStatePendingReview},
			"approve": {models.WorkflowStateInReview},
			"return_to_author": {models.WorkflowStateInReview},
			"reject": {models.WorkflowStateInReview},
		},
		"editorial_secretary": {
			"approve": {models.WorkflowStatePendingApproval},
			"send_to_eic": {models.WorkflowStatePendingApproval},
			"return_to_author": {models.WorkflowStatePendingApproval},
			"reject": {models.WorkflowStatePendingApproval},
		},
		"editor_in_chief": {
			"approve": {models.WorkflowStatePendingEIC},
			"return_to_author": {models.WorkflowStatePendingEIC},
			"reject": {models.WorkflowStatePendingEIC},
			"override": {
				models.WorkflowStateCreation,
				models.WorkflowStatePendingReview,
				models.WorkflowStateInReview,
				models.WorkflowStatePendingApproval,
				models.WorkflowStatePendingEIC,
			},
		},
	}

	rolePermissions, roleExists := permissions[role]
	if !roleExists {
		return false
	}

	actionStates, actionExists := rolePermissions[action]
	if !actionExists {
		return false
	}

	for _, allowedState := range actionStates {
		if allowedState == state {
			return true
		}
	}
	return false
}
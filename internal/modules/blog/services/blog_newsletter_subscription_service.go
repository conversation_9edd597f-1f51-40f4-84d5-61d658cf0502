package services

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
	notificationmodels "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

type blogNewsletterSubscriptionService struct {
	repo               repositories.BlogNewsletterSubscriptionRepository
	notificationSvc    services.NotificationService
	logger             utils.Logger
}

// NewBlogNewsletterSubscriptionService creates a new instance of newsletter subscription service
func NewBlogNewsletterSubscriptionService(
	repo repositories.BlogNewsletterSubscriptionRepository,
	notificationSvc services.NotificationService,
	logger utils.Logger,
) BlogNewsletterSubscriptionService {
	return &blogNewsletterSubscriptionService{
		repo:            repo,
		notificationSvc: notificationSvc,
		logger:          logger,
	}
}

// Subscribe creates a new newsletter subscription
func (s *blogNewsletterSubscriptionService) Subscribe(ctx context.Context, tenantID, websiteID uint, req *dto.CreateNewsletterSubscriptionRequest) (*dto.NewsletterSubscriptionResponse, error) {
	// Check if subscription already exists
	existing, err := s.repo.GetByEmail(ctx, tenantID, websiteID, req.Email)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	// If subscription exists
	if existing != nil {
		// If already confirmed, return error
		if existing.Status == models.SubscriptionStatusConfirmed {
			return nil, errors.New("email already subscribed")
		}
		
		// If pending, resend confirmation email
		if existing.Status == models.SubscriptionStatusPending {
			// Send confirmation email
			if err := s.sendConfirmationEmail(ctx, tenantID, websiteID, existing.Email, existing.Token); err != nil {
				s.logger.Error("Failed to send confirmation email", err, "email", existing.Email)
			}
			return dto.ToNewsletterSubscriptionResponse(existing), nil
		}
		
		// If unsubscribed, allow re-subscription
		if existing.Status == models.SubscriptionStatusUnsubscribed {
			// Update existing subscription
			existing.Status = models.SubscriptionStatusPending
			existing.Name = req.Name
			existing.Categories = models.CategoryIDArray(req.Categories)
			existing.Tags = models.TagIDArray(req.Tags)
			if req.Frequency != "" {
				existing.Frequency = models.NewsletterFrequency(req.Frequency)
			}
			existing.ConfirmedAt = nil
			
			if err := s.repo.Update(ctx, tenantID, existing.ID, existing); err != nil {
				return nil, err
			}
			
			// Send confirmation email
			if err := s.sendConfirmationEmail(ctx, tenantID, websiteID, existing.Email, existing.Token); err != nil {
				s.logger.Error("Failed to send confirmation email", err, "email", existing.Email)
			}
			
			return dto.ToNewsletterSubscriptionResponse(existing), nil
		}
	}

	// Create new subscription
	subscription := &models.BlogNewsletterSubscription{
		TenantID:   tenantID,
		WebsiteID:  websiteID,
		Email:      strings.ToLower(req.Email),
		Name:       req.Name,
		Categories: models.CategoryIDArray(req.Categories),
		Tags:       models.TagIDArray(req.Tags),
		Frequency:  models.FrequencyWeekly,
		Status:     models.SubscriptionStatusPending,
	}

	if req.Frequency != "" {
		subscription.Frequency = models.NewsletterFrequency(req.Frequency)
	}

	// Create subscription
	if err := s.repo.Create(ctx, subscription); err != nil {
		return nil, err
	}

	// Send confirmation email
	if err := s.sendConfirmationEmail(ctx, tenantID, websiteID, subscription.Email, subscription.Token); err != nil {
		s.logger.Error("Failed to send confirmation email", err, "email", subscription.Email)
	}

	return dto.ToNewsletterSubscriptionResponse(subscription), nil
}

// ConfirmSubscription confirms a subscription using token
func (s *blogNewsletterSubscriptionService) ConfirmSubscription(ctx context.Context, token string) (*dto.NewsletterSubscriptionResponse, error) {
	// Get subscription by token
	subscription, err := s.repo.GetByToken(ctx, token)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("invalid or expired token")
		}
		return nil, err
	}

	// Check if already confirmed
	if subscription.Status == models.SubscriptionStatusConfirmed {
		return nil, errors.New("subscription already confirmed")
	}

	// Confirm subscription
	if err := s.repo.Confirm(ctx, token); err != nil {
		return nil, err
	}

	// Get updated subscription
	subscription, err = s.repo.GetByToken(ctx, token)
	if err != nil {
		return nil, err
	}

	// Send welcome email
	if err := s.sendWelcomeEmail(ctx, subscription.TenantID, subscription.WebsiteID, subscription.Email, subscription.Name); err != nil {
		s.logger.Error("Failed to send welcome email", err, "email", subscription.Email)
	}

	return dto.ToNewsletterSubscriptionResponse(subscription), nil
}

// Unsubscribe unsubscribes using token
func (s *blogNewsletterSubscriptionService) Unsubscribe(ctx context.Context, token string) error {
	// Get subscription by token
	subscription, err := s.repo.GetByToken(ctx, token)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("invalid or expired token")
		}
		return err
	}

	// Check if already unsubscribed
	if subscription.Status == models.SubscriptionStatusUnsubscribed {
		return errors.New("already unsubscribed")
	}

	// Unsubscribe
	if err := s.repo.Unsubscribe(ctx, token); err != nil {
		return err
	}

	// Send unsubscribe confirmation email
	if err := s.sendUnsubscribeEmail(ctx, subscription.TenantID, subscription.WebsiteID, subscription.Email, subscription.Name); err != nil {
		s.logger.Error("Failed to send unsubscribe email", err, "email", subscription.Email)
	}

	return nil
}

// GetByID retrieves a subscription by ID
func (s *blogNewsletterSubscriptionService) GetByID(ctx context.Context, tenantID, id uint) (*dto.NewsletterSubscriptionResponse, error) {
	subscription, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		return nil, err
	}
	return dto.ToNewsletterSubscriptionResponse(subscription), nil
}

// GetByEmail retrieves a subscription by email
func (s *blogNewsletterSubscriptionService) GetByEmail(ctx context.Context, tenantID, websiteID uint, email string) (*dto.NewsletterSubscriptionResponse, error) {
	subscription, err := s.repo.GetByEmail(ctx, tenantID, websiteID, strings.ToLower(email))
	if err != nil {
		return nil, err
	}
	return dto.ToNewsletterSubscriptionResponse(subscription), nil
}

// Update updates a subscription
func (s *blogNewsletterSubscriptionService) Update(ctx context.Context, tenantID, id uint, req *dto.UpdateNewsletterSubscriptionRequest) (*dto.NewsletterSubscriptionResponse, error) {
	// Get existing subscription
	existing, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		return nil, err
	}

	// Update fields
	if req.Name != nil {
		existing.Name = req.Name
	}
	if req.Categories != nil {
		existing.Categories = models.CategoryIDArray(req.Categories)
	}
	if req.Tags != nil {
		existing.Tags = models.TagIDArray(req.Tags)
	}
	if req.Frequency != "" {
		existing.Frequency = models.NewsletterFrequency(req.Frequency)
	}

	// Update subscription
	if err := s.repo.Update(ctx, tenantID, id, existing); err != nil {
		return nil, err
	}

	// Get updated subscription
	updated, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		return nil, err
	}

	return dto.ToNewsletterSubscriptionResponse(updated), nil
}

// Delete deletes a subscription
func (s *blogNewsletterSubscriptionService) Delete(ctx context.Context, tenantID, id uint) error {
	return s.repo.Delete(ctx, tenantID, id)
}

// List retrieves subscriptions with filters
func (s *blogNewsletterSubscriptionService) List(ctx context.Context, filter *models.BlogNewsletterSubscriptionFilter) ([]dto.NewsletterSubscriptionListResponse, int64, error) {
	subscriptions, total, err := s.repo.List(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	responses := make([]dto.NewsletterSubscriptionListResponse, len(subscriptions))
	for i, sub := range subscriptions {
		responses[i] = *dto.ToNewsletterSubscriptionListResponse(&sub)
	}

	return responses, total, nil
}

// ListWithCursor retrieves subscriptions with cursor-based pagination
func (s *blogNewsletterSubscriptionService) ListWithCursor(ctx context.Context, tenantID, websiteID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]dto.NewsletterSubscriptionListResponse, *pagination.CursorResponse, error) {
	subscriptions, cursorResp, err := s.repo.ListWithCursor(ctx, tenantID, websiteID, req, filters)
	if err != nil {
		return nil, nil, err
	}

	responses := make([]dto.NewsletterSubscriptionListResponse, len(subscriptions))
	for i, sub := range subscriptions {
		responses[i] = *dto.ToNewsletterSubscriptionListResponse(&sub)
	}

	return responses, cursorResp, nil
}

// GetActiveSubscribers retrieves active subscribers by frequency
func (s *blogNewsletterSubscriptionService) GetActiveSubscribers(ctx context.Context, tenantID, websiteID uint, frequency models.NewsletterFrequency) ([]dto.NewsletterSubscriptionResponse, error) {
	subscriptions, err := s.repo.GetActiveSubscribers(ctx, tenantID, websiteID, frequency)
	if err != nil {
		return nil, err
	}

	responses := make([]dto.NewsletterSubscriptionResponse, len(subscriptions))
	for i, sub := range subscriptions {
		responses[i] = *dto.ToNewsletterSubscriptionResponse(&sub)
	}

	return responses, nil
}

// GetSubscribersByCategories retrieves subscribers by categories
func (s *blogNewsletterSubscriptionService) GetSubscribersByCategories(ctx context.Context, tenantID, websiteID uint, categoryIDs []uint) ([]dto.NewsletterSubscriptionResponse, error) {
	subscriptions, err := s.repo.GetSubscribersByCategories(ctx, tenantID, websiteID, categoryIDs)
	if err != nil {
		return nil, err
	}

	responses := make([]dto.NewsletterSubscriptionResponse, len(subscriptions))
	for i, sub := range subscriptions {
		responses[i] = *dto.ToNewsletterSubscriptionResponse(&sub)
	}

	return responses, nil
}

// GetSubscribersByTags retrieves subscribers by tags
func (s *blogNewsletterSubscriptionService) GetSubscribersByTags(ctx context.Context, tenantID, websiteID uint, tagIDs []uint) ([]dto.NewsletterSubscriptionResponse, error) {
	subscriptions, err := s.repo.GetSubscribersByTags(ctx, tenantID, websiteID, tagIDs)
	if err != nil {
		return nil, err
	}

	responses := make([]dto.NewsletterSubscriptionResponse, len(subscriptions))
	for i, sub := range subscriptions {
		responses[i] = *dto.ToNewsletterSubscriptionResponse(&sub)
	}

	return responses, nil
}

// MarkAsSent marks subscriptions as sent
func (s *blogNewsletterSubscriptionService) MarkAsSent(ctx context.Context, tenantID uint, subscriptionIDs []uint) error {
	return s.repo.BatchUpdateLastSentAt(ctx, tenantID, subscriptionIDs)
}

// GetStatsByWebsite retrieves subscription statistics for a website
func (s *blogNewsletterSubscriptionService) GetStatsByWebsite(ctx context.Context, tenantID, websiteID uint) (*models.NewsletterSubscriptionStats, error) {
	return s.repo.GetStatsByWebsite(ctx, tenantID, websiteID)
}

// GetGrowthData retrieves subscription growth data
func (s *blogNewsletterSubscriptionService) GetGrowthData(ctx context.Context, tenantID, websiteID uint, days int) ([]models.NewsletterGrowthData, error) {
	return s.repo.GetGrowthData(ctx, tenantID, websiteID, days)
}

// ImportSubscribers imports multiple subscribers
func (s *blogNewsletterSubscriptionService) ImportSubscribers(ctx context.Context, tenantID, websiteID uint, subscribers []dto.CreateNewsletterSubscriptionRequest) (int, error) {
	imported := 0
	
	for _, sub := range subscribers {
		// Skip if already exists
		existing, err := s.repo.GetByEmail(ctx, tenantID, websiteID, sub.Email)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			s.logger.Error("Failed to check existing subscription", err, "email", sub.Email)
			continue
		}
		
		if existing != nil && existing.Status == models.SubscriptionStatusConfirmed {
			continue
		}
		
		// Create subscription
		_, err = s.Subscribe(ctx, tenantID, websiteID, &sub)
		if err != nil {
			s.logger.Error("Failed to import subscriber", err, "email", sub.Email)
			continue
		}
		
		imported++
	}
	
	return imported, nil
}

// ExportSubscribers exports subscribers with given status
func (s *blogNewsletterSubscriptionService) ExportSubscribers(ctx context.Context, tenantID, websiteID uint, status models.NewsletterSubscriptionStatus) ([]dto.NewsletterSubscriptionResponse, error) {
	filter := &models.BlogNewsletterSubscriptionFilter{
		TenantID:  tenantID,
		WebsiteID: websiteID,
		Status:    status,
		Limit:     10000, // Max export limit
	}
	
	subscriptions, _, err := s.repo.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	
	responses := make([]dto.NewsletterSubscriptionResponse, len(subscriptions))
	for i, sub := range subscriptions {
		responses[i] = *dto.ToNewsletterSubscriptionResponse(&sub)
	}
	
	return responses, nil
}

// CleanupInactive cleans up inactive subscriptions
func (s *blogNewsletterSubscriptionService) CleanupInactive(ctx context.Context, days int) error {
	olderThan := time.Now().AddDate(0, 0, -days)
	
	// Cleanup unconfirmed subscriptions
	if err := s.repo.CleanupUnconfirmed(ctx, olderThan); err != nil {
		return err
	}
	
	// Cleanup bounced subscriptions
	if err := s.repo.CleanupBounced(ctx, olderThan); err != nil {
		return err
	}
	
	return nil
}

// sendConfirmationEmail sends newsletter subscription confirmation email
func (s *blogNewsletterSubscriptionService) sendConfirmationEmail(ctx context.Context, tenantID, websiteID uint, email, token string) error {
	// TODO: Get website URL from website service
	confirmationURL := fmt.Sprintf("https://example.com/newsletter/confirm?token=%s", token)
	
	templateData := map[string]interface{}{
		"confirmation_url": confirmationURL,
		"email":           email,
	}
	
	// Create notification request
	req := notificationmodels.CreateNotificationRequest{
		Type:        "newsletter_confirmation",
		Channel:     notificationmodels.ChannelEmail,
		Priority:    notificationmodels.PriorityNormal,
		Subject:     "Confirm your newsletter subscription",
		TemplateID:  &[]uint{9}[0], // Template ID 9 for newsletter confirmation
		TemplateData: templateData,
		Recipients: []notificationmodels.CreateRecipientRequest{
			{
				RecipientType:    notificationmodels.RecipientTypeEmail,
				RecipientAddress: email,
			},
		},
	}
	
	notification, err := s.notificationSvc.CreateNotification(tenantID, req)
	if err != nil {
		return err
	}
	
	// Send notification immediately
	return s.notificationSvc.SendNotification(tenantID, notification.ID)
}

// sendWelcomeEmail sends newsletter welcome email
func (s *blogNewsletterSubscriptionService) sendWelcomeEmail(ctx context.Context, tenantID, websiteID uint, email string, name *string) error {
	templateData := map[string]interface{}{
		"email": email,
	}
	
	if name != nil {
		templateData["name"] = *name
	}
	
	// Create notification request
	req := notificationmodels.CreateNotificationRequest{
		Type:        "newsletter_welcome",
		Channel:     notificationmodels.ChannelEmail,
		Priority:    notificationmodels.PriorityNormal,
		Subject:     "Welcome to our newsletter",
		TemplateID:  &[]uint{10}[0], // Template ID 10 for newsletter welcome
		TemplateData: templateData,
		Recipients: []notificationmodels.CreateRecipientRequest{
			{
				RecipientType:    notificationmodels.RecipientTypeEmail,
				RecipientAddress: email,
			},
		},
	}
	
	notification, err := s.notificationSvc.CreateNotification(tenantID, req)
	if err != nil {
		return err
	}
	
	return s.notificationSvc.SendNotification(tenantID, notification.ID)
}

// sendUnsubscribeEmail sends unsubscribe confirmation email
func (s *blogNewsletterSubscriptionService) sendUnsubscribeEmail(ctx context.Context, tenantID, websiteID uint, email string, name *string) error {
	templateData := map[string]interface{}{
		"email": email,
	}
	
	if name != nil {
		templateData["name"] = *name
	}
	
	// Create notification request
	req := notificationmodels.CreateNotificationRequest{
		Type:        "newsletter_unsubscribe",
		Channel:     notificationmodels.ChannelEmail,
		Priority:    notificationmodels.PriorityNormal,
		Subject:     "You've been unsubscribed",
		TemplateID:  &[]uint{11}[0], // Template ID 11 for unsubscribe confirmation
		TemplateData: templateData,
		Recipients: []notificationmodels.CreateRecipientRequest{
			{
				RecipientType:    notificationmodels.RecipientTypeEmail,
				RecipientAddress: email,
			},
		},
	}
	
	notification, err := s.notificationSvc.CreateNotification(tenantID, req)
	if err != nil {
		return err
	}
	
	return s.notificationSvc.SendNotification(tenantID, notification.ID)
}
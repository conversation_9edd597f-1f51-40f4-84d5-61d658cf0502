package services

import (
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
	notificationServices "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// BlogServices contains all blog services
type BlogServices struct {
	CategoryService                BlogCategoryService
	TagService                     BlogTagService
	PostService                    BlogPostService
	PostScheduleService            BlogPostScheduleService
	PostRevisionService            BlogPostRevisionService
	PostAutosaveService            BlogPostAutosaveService
	PostTemplateService            BlogPostTemplateService
	HomepageBlockService           HomepageBlockService
	BlockTemplateService           BlockTemplateService
	NewsletterSubscriptionService  BlogNewsletterSubscriptionService
	ReadingProgressService         BlogPostReadingProgressService
}

// NewBlogServices creates a new instance of blog services
func NewBlogServices(
	categoryRepo repositories.BlogCategoryRepository,
	tagRepo repositories.BlogTagRepository,
	postRepo repositories.BlogPostRepository,
	scheduleRepo repositories.BlogPostScheduleRepository,
	revisionRepo repositories.BlogPostRevisionRepository,
	autosaveRepo repositories.BlogPostAutosaveRepository,
	templateRepo repositories.BlogPostTemplateRepository,
	homepageBlockRepo repositories.HomepageBlockRepository,
	blockTemplateRepo repositories.BlockTemplateRepository,
	newsletterRepo repositories.BlogNewsletterSubscriptionRepository,
	progressRepo repositories.BlogPostReadingProgressRepository,
	notificationService notificationServices.NotificationService,
	logger utils.Logger,
) *BlogServices {
	// Create tag service first as it's needed by post service
	tagService := NewBlogTagService(tagRepo)

	return &BlogServices{
		CategoryService:               NewBlogCategoryService(categoryRepo),
		TagService:                    tagService,
		PostService:                   NewBlogPostService(postRepo, tagRepo, notificationService, logger),
		PostScheduleService:           NewBlogPostScheduleService(scheduleRepo, postRepo),
		PostRevisionService:           NewBlogPostRevisionService(revisionRepo, postRepo),
		PostAutosaveService:           NewBlogPostAutosaveService(autosaveRepo, postRepo, logger),
		PostTemplateService:           NewBlogPostTemplateService(templateRepo, postRepo, tagRepo, logger),
		HomepageBlockService:          NewHomepageBlockService(homepageBlockRepo, blockTemplateRepo, logger),
		BlockTemplateService:          NewBlockTemplateService(blockTemplateRepo, logger),
		NewsletterSubscriptionService: NewBlogNewsletterSubscriptionService(newsletterRepo, notificationService, logger),
		ReadingProgressService:        NewBlogPostReadingProgressService(progressRepo, postRepo),
	}
}

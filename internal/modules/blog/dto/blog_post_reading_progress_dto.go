package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
)

// UpdateReadingProgressRequest represents the request to update reading progress
type UpdateReadingProgressRequest struct {
	ReadingProgressPercentage float64 `json:"reading_progress_percentage" validate:"min=0,max=100" example:"67.5"`
	TimeSpentSeconds          uint    `json:"time_spent_seconds" validate:"min=0" example:"180"`
	LastReadPosition          uint    `json:"last_read_position" validate:"min=0" example:"2100"`
	ScrollDepthPercentage     float64 `json:"scroll_depth_percentage" validate:"min=0,max=100" example:"85.2"`
	IsCompleted               *bool   `json:"is_completed,omitempty" example:"false"`
}

// ReadingProgressResponse represents the response when returning reading progress data
type ReadingProgressResponse struct {
	ID                        uint                    `json:"id"`
	BlogPostID                uint                    `json:"blog_post_id"`
	ReadingProgressPercentage float64                 `json:"reading_progress_percentage"`
	TimeSpentSeconds          uint                    `json:"time_spent_seconds"`
	LastReadPosition          uint                    `json:"last_read_position"`
	ScrollDepthPercentage     float64                 `json:"scroll_depth_percentage"`
	SessionCount              uint                    `json:"session_count"`
	FirstReadAt               *time.Time              `json:"first_read_at,omitempty"`
	LastReadAt                time.Time               `json:"last_read_at"`
	IsCompleted               bool                    `json:"is_completed"`
	CompletedAt               *time.Time              `json:"completed_at,omitempty"`
	ReadSpeedWPM              *float64                `json:"read_speed_wpm,omitempty"`
	DeviceType                models.DeviceType       `json:"device_type"`
	CreatedAt                 time.Time               `json:"created_at"`
	UpdatedAt                 time.Time               `json:"updated_at"`
}

// PostReadingStatsResponse represents reading statistics for a blog post
type PostReadingStatsResponse struct {
	BlogPostID           uint    `json:"blog_post_id"`
	TotalReaders         int     `json:"total_readers"`
	CompletedReaders     int     `json:"completed_readers"`
	AverageProgress      float64 `json:"average_progress"`
	AverageTimeSpent     float64 `json:"average_time_spent"`
	AverageReadSpeedWPM  float64 `json:"average_read_speed_wpm"`
	AverageScrollDepth   float64 `json:"average_scroll_depth"`
	CompletionRate       float64 `json:"completion_rate"`
	MobileReaders        int     `json:"mobile_readers"`
	DesktopReaders       int     `json:"desktop_readers"`
	TabletReaders        int     `json:"tablet_readers"`
}

// UserReadingStatsResponse represents reading statistics for a user
type UserReadingStatsResponse struct {
	UserID                     uint              `json:"user_id"`
	TotalPostsRead             int               `json:"total_posts_read"`
	TotalPostsCompleted        int               `json:"total_posts_completed"`
	TotalTimeSpent             uint              `json:"total_time_spent"`
	AverageReadingProgress     float64           `json:"average_reading_progress"`
	AverageReadSpeedWPM        float64           `json:"average_read_speed_wpm"`
	CompletionRate             float64           `json:"completion_rate"`
	PreferredDeviceType        models.DeviceType `json:"preferred_device_type"`
	TotalReadingSessions       int               `json:"total_reading_sessions"`
}

// ReadingProgressAnalyticsResponse represents comprehensive reading analytics
type ReadingProgressAnalyticsResponse struct {
	PostStats                 *PostReadingStatsResponse `json:"post_stats,omitempty"`
	UserStats                 *UserReadingStatsResponse `json:"user_stats,omitempty"`
	ReadingTrends             []ReadingTrendData        `json:"reading_trends,omitempty"`
	DeviceDistribution        map[string]int            `json:"device_distribution,omitempty"`
	CompletionRateByTimeRange map[string]float64        `json:"completion_rate_by_time_range,omitempty"`
}

// ReadingTrendData represents reading trend data over time
type ReadingTrendData struct {
	Date              string  `json:"date"`
	TotalReaders      int     `json:"total_readers"`
	CompletedReaders  int     `json:"completed_readers"`
	AverageProgress   float64 `json:"average_progress"`
	AverageTimeSpent  float64 `json:"average_time_spent"`
}

// BatchUpdateReadingProgressRequest represents a batch update request
type BatchUpdateReadingProgressRequest struct {
	Updates []UpdateReadingProgressRequest `json:"updates" validate:"required,min=1,max=10"`
}

// ReadingProgressListResponse represents a paginated list of reading progress records
type ReadingProgressListResponse struct {
	Data     []ReadingProgressResponse `json:"data"`
	Total    int                       `json:"total"`
	Page     int                       `json:"page"`
	PageSize int                       `json:"page_size"`
	HasMore  bool                      `json:"has_more"`
}
package handlers

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/services"
	tenantmodels "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	tenantservices "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/services"
	usermodels "github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	userservices "github.com/tranthanhloi/wn-api-v3/internal/modules/user/services"
	httpmiddleware "github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/events"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// Mock services
type mockOnboardingService struct {
	mock.Mock
}

func (m *mockOnboardingService) CreateOrganization(ctx context.Context, req *models.CreateOrganizationRequest, userID uint) (*models.OnboardingResponse, error) {
	args := m.Called(ctx, req, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.OnboardingResponse), args.Error(1)
}

func (m *mockOnboardingService) CompleteOnboarding(ctx context.Context, userID uint, completionData *models.OnboardingCompletionData) error {
	args := m.Called(ctx, userID, completionData)
	return args.Error(0)
}

func (m *mockOnboardingService) GetOnboardingStatus(ctx context.Context, userID uint) (*models.OnboardingStatusResponse, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.OnboardingStatusResponse), args.Error(1)
}

func (m *mockOnboardingService) GetUserTenants(ctx context.Context, userID uint) ([]*models.UserTenantResponse, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*models.UserTenantResponse), args.Error(1)
}

func (m *mockOnboardingService) SwitchTenant(ctx context.Context, userID uint, tenantID uint) (*models.SwitchTenantResponse, error) {
	args := m.Called(ctx, userID, tenantID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.SwitchTenantResponse), args.Error(1)
}

func (m *mockOnboardingService) ValidateInvitationCode(ctx context.Context, invitationCode string) (*models.InvitationValidationResponse, error) {
	args := m.Called(ctx, invitationCode)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.InvitationValidationResponse), args.Error(1)
}

func (m *mockOnboardingService) AcceptInvitation(ctx context.Context, userID uint, invitationCode string) (*models.InvitationAcceptanceResponse, error) {
	args := m.Called(ctx, userID, invitationCode)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.InvitationAcceptanceResponse), args.Error(1)
}

// Test helper to setup router
func setupTestRouter(handler *OnboardingHandler) *gin.Engine {
	gin.SetMode(gin.TestMode)
	router := gin.New()
	
	// Add auth middleware mock
	router.Use(func(c *gin.Context) {
		// Mock user ID in context
		c.Set("userID", uint(100))
		c.Next()
	})
	
	// Register routes
	api := router.Group("/api/onboarding")
	{
		api.POST("/create-organization", handler.CreateOrganization)
		api.POST("/complete", handler.CompleteOnboarding)
		api.GET("/status", handler.GetOnboardingStatus)
		api.GET("/tenants", handler.GetUserTenants)
		api.POST("/switch-tenant", handler.SwitchTenant)
		api.GET("/validate-invitation", handler.ValidateInvitationCode)
		api.POST("/accept-invitation", handler.AcceptInvitation)
	}
	
	return router
}

func TestCreateOrganization_WithEventPublishing(t *testing.T) {
	// Reset global event bus for testing
	events.ResetEventBusForTesting()
	eventBus := events.GetEventBus()
	
	t.Run("Successfully Create Organization and Publish Event", func(t *testing.T) {
		// Setup
		service := new(mockOnboardingService)
		logger := utils.NewTestLogger()
		handler := NewOnboardingHandler(service, logger)
		router := setupTestRouter(handler)
		
		// Track if event was published
		var eventReceived events.TenantCreatedEvent
		eventHandlerCalled := false
		
		// Subscribe to tenant created event
		eventBus.SubscribeTenantCreated("test_handler", func(ctx context.Context, event events.TenantCreatedEvent) error {
			eventHandlerCalled = true
			eventReceived = event
			return nil
		})
		
		// Prepare request
		reqBody := models.CreateOrganizationRequest{
			Name:   "Test Organization",
			Domain: "test.example.com",
			Type:   "business",
			Settings: map[string]interface{}{
				"timezone": "UTC",
			},
		}
		
		// Mock service response
		expectedResponse := &models.OnboardingResponse{
			Organization: tenantmodels.Tenant{
				ID:     1,
				Name:   "Test Organization",
				Domain: "test.example.com",
				Type:   "business",
			},
			User: usermodels.User{
				ID:    100,
				Email: "<EMAIL>",
			},
			TenantMembership: models.TenantMembership{
				ID:        1,
				UserID:    100,
				TenantID:  1,
				IsPrimary: true,
				Status:    "active",
			},
			NextSteps: []string{
				"Complete profile setup",
				"Invite team members",
			},
		}
		
		service.On("CreateOrganization", mock.Anything, &reqBody, uint(100)).Return(expectedResponse, nil)
		
		// Make request
		body, _ := json.Marshal(reqBody)
		req := httptest.NewRequest("POST", "/api/onboarding/create-organization", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		
		// Execute
		router.ServeHTTP(w, req)
		
		// Assert HTTP response
		assert.Equal(t, http.StatusCreated, w.Code)
		
		var response models.OnboardingResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)
		assert.Equal(t, expectedResponse.Organization.ID, response.Organization.ID)
		assert.Equal(t, expectedResponse.Organization.Name, response.Organization.Name)
		
		// Wait a bit for async event processing
		time.Sleep(100 * time.Millisecond)
		
		// Assert event was published
		assert.True(t, eventHandlerCalled, "Event handler should have been called")
		assert.Equal(t, uint(1), eventReceived.TenantID)
		assert.Equal(t, uint(100), eventReceived.OwnerID)
		assert.Equal(t, "test.example.com", eventReceived.Domain)
		
		// Verify mock expectations
		service.AssertExpectations(t)
	})
	
	t.Run("Event Publishing Failure Should Not Affect Response", func(t *testing.T) {
		// Reset event bus
		events.ResetEventBusForTesting()
		eventBus = events.GetEventBus()
		
		// Setup
		service := new(mockOnboardingService)
		logger := utils.NewTestLogger()
		handler := NewOnboardingHandler(service, logger)
		router := setupTestRouter(handler)
		
		// Subscribe handler that returns error
		eventBus.SubscribeTenantCreated("failing_handler", func(ctx context.Context, event events.TenantCreatedEvent) error {
			return assert.AnError
		})
		
		// Prepare request
		reqBody := models.CreateOrganizationRequest{
			Name:   "Test Organization 2",
			Domain: "test2.example.com",
			Type:   "nonprofit",
		}
		
		// Mock service response
		expectedResponse := &models.OnboardingResponse{
			Organization: tenantmodels.Tenant{
				ID:     2,
				Name:   "Test Organization 2",
				Domain: "test2.example.com",
				Type:   "nonprofit",
			},
			User: usermodels.User{
				ID:    100,
				Email: "<EMAIL>",
			},
			TenantMembership: models.TenantMembership{
				ID:        2,
				UserID:    100,
				TenantID:  2,
				IsPrimary: true,
				Status:    "active",
			},
		}
		
		service.On("CreateOrganization", mock.Anything, &reqBody, uint(100)).Return(expectedResponse, nil)
		
		// Make request
		body, _ := json.Marshal(reqBody)
		req := httptest.NewRequest("POST", "/api/onboarding/create-organization", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		
		// Execute
		router.ServeHTTP(w, req)
		
		// Assert HTTP response is still successful
		assert.Equal(t, http.StatusCreated, w.Code)
		
		var response models.OnboardingResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)
		assert.Equal(t, expectedResponse.Organization.ID, response.Organization.ID)
		
		// Verify mock expectations
		service.AssertExpectations(t)
	})
}

func TestCreateOrganization_ValidationErrors(t *testing.T) {
	t.Run("Invalid Request Body", func(t *testing.T) {
		// Setup
		service := new(mockOnboardingService)
		logger := utils.NewTestLogger()
		handler := NewOnboardingHandler(service, logger)
		router := setupTestRouter(handler)
		
		// Make request with invalid JSON
		req := httptest.NewRequest("POST", "/api/onboarding/create-organization", bytes.NewBuffer([]byte("invalid json")))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		
		// Execute
		router.ServeHTTP(w, req)
		
		// Assert
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
	
	t.Run("Missing Required Fields", func(t *testing.T) {
		// Setup
		service := new(mockOnboardingService)
		logger := utils.NewTestLogger()
		handler := NewOnboardingHandler(service, logger)
		router := setupTestRouter(handler)
		
		// Make request without required fields
		reqBody := models.CreateOrganizationRequest{
			// Missing Name and Domain
			Type: "business",
		}
		
		body, _ := json.Marshal(reqBody)
		req := httptest.NewRequest("POST", "/api/onboarding/create-organization", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		
		// Execute
		router.ServeHTTP(w, req)
		
		// Assert
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

func TestCreateOrganization_ServiceErrors(t *testing.T) {
	t.Run("Service Returns Error", func(t *testing.T) {
		// Setup
		service := new(mockOnboardingService)
		logger := utils.NewTestLogger()
		handler := NewOnboardingHandler(service, logger)
		router := setupTestRouter(handler)
		
		// Prepare request
		reqBody := models.CreateOrganizationRequest{
			Name:   "Test Organization",
			Domain: "test.example.com",
			Type:   "business",
		}
		
		// Mock service to return error
		service.On("CreateOrganization", mock.Anything, &reqBody, uint(100)).Return(nil, sql.ErrNoRows)
		
		// Make request
		body, _ := json.Marshal(reqBody)
		req := httptest.NewRequest("POST", "/api/onboarding/create-organization", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		
		// Execute
		router.ServeHTTP(w, req)
		
		// Assert
		assert.Equal(t, http.StatusInternalServerError, w.Code)
		
		// Verify mock expectations
		service.AssertExpectations(t)
	})
}

// Integration test that simulates the full flow
func TestTenantCreation_FullIntegrationFlow(t *testing.T) {
	// This test would require a full setup with database and all services
	// For now, we'll create a simpler version that tests the event flow
	
	t.Run("Complete Flow: Create Tenant -> Publish Event -> Initialize RBAC", func(t *testing.T) {
		// Reset event bus
		events.ResetEventBusForTesting()
		eventBus := events.GetEventBus()
		
		// Track RBAC initialization
		rbacInitialized := false
		var rbacTenantID, rbacOwnerID uint
		
		// Simulate RBAC event handler
		eventBus.SubscribeTenantCreated("rbac_handler", func(ctx context.Context, event events.TenantCreatedEvent) error {
			rbacInitialized = true
			rbacTenantID = event.TenantID
			rbacOwnerID = event.OwnerID
			// In real scenario, this would call initializeRBACForTenant
			return nil
		})
		
		// Setup
		service := new(mockOnboardingService)
		logger := utils.NewTestLogger()
		handler := NewOnboardingHandler(service, logger)
		router := setupTestRouter(handler)
		
		// Prepare request
		reqBody := models.CreateOrganizationRequest{
			Name:   "Integration Test Org",
			Domain: "integration.test.com",
			Type:   "enterprise",
		}
		
		// Mock service response
		expectedResponse := &models.OnboardingResponse{
			Organization: tenantmodels.Tenant{
				ID:     999,
				Name:   "Integration Test Org",
				Domain: "integration.test.com",
				Type:   "enterprise",
			},
			User: usermodels.User{
				ID:    100,
				Email: "<EMAIL>",
			},
			TenantMembership: models.TenantMembership{
				ID:        999,
				UserID:    100,
				TenantID:  999,
				IsPrimary: true,
				Status:    "active",
			},
		}
		
		service.On("CreateOrganization", mock.Anything, &reqBody, uint(100)).Return(expectedResponse, nil)
		
		// Make request
		body, _ := json.Marshal(reqBody)
		req := httptest.NewRequest("POST", "/api/onboarding/create-organization", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		
		// Execute
		router.ServeHTTP(w, req)
		
		// Assert HTTP response
		assert.Equal(t, http.StatusCreated, w.Code)
		
		// Wait for async event processing
		time.Sleep(100 * time.Millisecond)
		
		// Assert RBAC was initialized
		assert.True(t, rbacInitialized, "RBAC should have been initialized")
		assert.Equal(t, uint(999), rbacTenantID)
		assert.Equal(t, uint(100), rbacOwnerID)
		
		// Verify mock expectations
		service.AssertExpectations(t)
	})
}
package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	tenantServices "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/services"
	userServices "github.com/tranthanhloi/wn-api-v3/internal/modules/user/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/events"
	httpmiddleware "github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	httpresponse "github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
)

// OnboardingHandler handles onboarding-related HTTP requests
type OnboardingHandler struct {
	onboardingService       services.OnboardingService
	tenantService           tenantServices.TenantService
	tenantMembershipService userServices.TenantMembershipService
	validator               validator.Validator
	logger                  utils.Logger
	tracer                  trace.Tracer
}

// NewOnboardingHandler creates a new onboarding handler
func NewOnboardingHandler(
	onboardingService services.OnboardingService,
	tenantService tenantServices.TenantService,
	tenantMembershipService userServices.TenantMembershipService,
	validator validator.Validator,
	logger utils.Logger,
) *OnboardingHandler {
	return &OnboardingHandler{
		onboardingService:       onboardingService,
		tenantService:           tenantService,
		tenantMembershipService: tenantMembershipService,
		validator:               validator,
		logger:                  logger,
		tracer:                  otel.Tracer("onboarding-handler"),
	}
}

// UpdateStep godoc
// @Summary      Update onboarding step
// @Description  Updates the current step in the onboarding process for the authenticated user
// @Tags         onboarding
// @Accept       json
// @Produce      json
// @Param        body body dto.UpdateOnboardingStepRequest true "Update step request"
// @Success      200 {object} response.Response{data=dto.UpdateOnboardingStepResponse} "Onboarding step updated successfully"
// @Failure      400 {object} response.Response "Invalid request format"
// @Failure      401 {object} response.Response "Unauthorized"
// @Failure      404 {object} response.Response "Onboarding progress not found"
// @Failure      409 {object} response.Response "Onboarding already completed"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/onboarding/step [put]
func (h *OnboardingHandler) UpdateStep(c *gin.Context) {
	ctx, span := h.tracer.Start(c.Request.Context(), "OnboardingHandler.UpdateStep")
	defer span.End()

	// Get user ID from JWT context
	userID, exists := c.Get(httpmiddleware.UserIDKey)
	if !exists {
		// This should not happen if middleware is properly configured
		httpresponse.InternalServerError(c.Writer, "User context not found", "MISSING_USER_CONTEXT")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		httpresponse.Unauthorized(c.Writer, "Invalid user context")
		return
	}

	var req dto.UpdateOnboardingStepRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request format", err.Error())
		return
	}

	// Set user ID from auth context
	req.UserID = userIDUint

	// Validate request
	if err := h.validator.Validate(ctx, &req); err != nil {
		httpresponse.ValidationError(c.Writer, err)
		return
	}

	response, err := h.onboardingService.UpdateStep(ctx, &req)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": req.UserID,
			"step":    req.Step,
		}).Error("Failed to update onboarding step")

		if err.Error() == "onboarding already completed" {
			httpresponse.Conflict(c.Writer, "Onboarding already completed")
			return
		}

		if err.Error() == "failed to get onboarding progress: onboarding progress not found for user" {
			httpresponse.NotFound(c.Writer, "Onboarding progress not found")
			return
		}

		httpresponse.InternalServerError(c.Writer, "Failed to update onboarding step")
		return
	}

	httpresponse.Success(c.Writer, response)
}

// CompleteOnboarding godoc
// @Summary      Complete onboarding
// @Description  Marks the onboarding process as completed for the authenticated user
// @Tags         onboarding
// @Accept       json
// @Produce      json
// @Param        body body dto.CompleteOnboardingRequest true "Complete onboarding request"
// @Success      200 {object} response.Response{data=dto.CompleteOnboardingResponse} "Onboarding completed successfully"
// @Failure      400 {object} response.Response "Invalid request format"
// @Failure      401 {object} response.Response "Unauthorized"
// @Failure      404 {object} response.Response "Onboarding progress not found"
// @Failure      409 {object} response.Response "Onboarding already completed"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/onboarding/complete [post]
func (h *OnboardingHandler) CompleteOnboarding(c *gin.Context) {
	ctx, span := h.tracer.Start(c.Request.Context(), "OnboardingHandler.CompleteOnboarding")
	defer span.End()

	// Get user ID from JWT context
	userID, exists := c.Get(httpmiddleware.UserIDKey)
	if !exists {
		// This should not happen if middleware is properly configured
		httpresponse.InternalServerError(c.Writer, "User context not found", "MISSING_USER_CONTEXT")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		httpresponse.Unauthorized(c.Writer, "Invalid user context")
		return
	}

	var req dto.CompleteOnboardingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request format", err.Error())
		return
	}

	// Set user ID from auth context
	req.UserID = userIDUint

	// Validate request
	if err := h.validator.Validate(ctx, &req); err != nil {
		httpresponse.ValidationError(c.Writer, err)
		return
	}

	response, err := h.onboardingService.CompleteOnboarding(ctx, &req)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": req.UserID,
		}).Error("Failed to complete onboarding")

		if err.Error() == "onboarding already completed" {
			httpresponse.Conflict(c.Writer, "Onboarding already completed")
			return
		}

		if err.Error() == "failed to get onboarding progress: onboarding progress not found for user" {
			httpresponse.NotFound(c.Writer, "Onboarding progress not found")
			return
		}

		httpresponse.InternalServerError(c.Writer, "Failed to complete onboarding")
		return
	}

	httpresponse.Success(c.Writer, response)
}

// ListProgress godoc
// @Summary      List onboarding progress
// @Description  Lists onboarding progress with optional filters
// @Tags         onboarding
// @Accept       json
// @Produce      json
// @Param        status query string false "Filter by status (pending, processing, completed)"
// @Param        step query string false "Filter by step (create_tenant, create_website, completed)"
// @Param        limit query int false "Limit number of results (default: 10, max: 100)"
// @Param        offset query int false "Offset for pagination (default: 0)"
// @Success      200 {object} response.Response{data=dto.ListOnboardingProgressResponse} "Onboarding progress list retrieved successfully"
// @Failure      400 {object} response.Response "Invalid query parameters"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/onboarding/progress [get]
func (h *OnboardingHandler) ListProgress(c *gin.Context) {
	ctx, span := h.tracer.Start(c.Request.Context(), "OnboardingHandler.ListProgress")
	defer span.End()

	var req dto.ListOnboardingProgressRequest

	// Parse query parameters
	req.Status = c.Query("status")
	req.Step = c.Query("step")

	if limitStr := c.Query("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil {
			req.Limit = limit
		}
	}

	if offsetStr := c.Query("offset"); offsetStr != "" {
		if offset, err := strconv.Atoi(offsetStr); err == nil {
			req.Offset = offset
		}
	}

	// Validate request
	if err := h.validator.Validate(ctx, &req); err != nil {
		httpresponse.ValidationError(c.Writer, err)
		return
	}

	response, err := h.onboardingService.ListProgress(ctx, &req)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"status": req.Status,
			"step":   req.Step,
			"limit":  req.Limit,
			"offset": req.Offset,
		}).Error("Failed to list onboarding progress")

		httpresponse.InternalServerError(c.Writer, "Failed to list onboarding progress")
		return
	}

	httpresponse.Success(c.Writer, response)
}

// GetStats godoc
// @Summary      Get onboarding statistics
// @Description  Retrieves statistics about onboarding progress
// @Tags         onboarding
// @Accept       json
// @Produce      json
// @Success      200 {object} response.Response{data=dto.OnboardingStatsResponse} "Onboarding statistics retrieved successfully"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/onboarding/stats [get]
func (h *OnboardingHandler) GetStats(c *gin.Context) {
	ctx, span := h.tracer.Start(c.Request.Context(), "OnboardingHandler.GetStats")
	defer span.End()

	response, err := h.onboardingService.GetStats(ctx)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get onboarding statistics")
		httpresponse.InternalServerError(c.Writer, "Failed to get onboarding statistics")
		return
	}

	httpresponse.Success(c.Writer, response)
}

// GetCurrentStep godoc
// @Summary      Get current onboarding step and status
// @Description  Retrieves the current onboarding step and status for the authenticated user
// @Tags         onboarding
// @Accept       json
// @Produce      json
// @Success      200 {object} response.Response{data=map[string]string} "Current onboarding step and status retrieved successfully"
// @Failure      401 {object} response.Response "Unauthorized"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/onboarding/step [get]
func (h *OnboardingHandler) GetCurrentStep(c *gin.Context) {
	ctx, span := h.tracer.Start(c.Request.Context(), "OnboardingHandler.GetCurrentStep")
	defer span.End()

	// Get user ID from JWT context
	userID, exists := c.Get(httpmiddleware.UserIDKey)
	if !exists {
		// This should not happen if middleware is properly configured
		httpresponse.InternalServerError(c.Writer, "User context not found", "MISSING_USER_CONTEXT")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		httpresponse.Unauthorized(c.Writer, "Invalid user context")
		return
	}

	step, status, err := h.onboardingService.GetCurrentStepWithStatus(ctx, userIDUint)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": userID,
		}).Error("Failed to get current onboarding step")

		httpresponse.InternalServerError(c.Writer, "Failed to get current onboarding step")
		return
	}

	response := map[string]string{
		"step":   step,
		"status": status,
	}

	httpresponse.Success(c.Writer, response)
}

// CreateOrganization godoc
// @Summary      Create organization during onboarding
// @Description  Creates a new organization (tenant) for the authenticated user during onboarding process
// @Tags         onboarding
// @Accept       json
// @Produce      json
// @Param        body body dto.CreateOrganizationRequest true "Create organization request"
// @Success      201 {object} response.Response{data=dto.CreateOrganizationResponse} "Organization created successfully"
// @Failure      400 {object} response.Response "Invalid request format"
// @Failure      401 {object} response.Response "Unauthorized"
// @Failure      409 {object} response.Response "Organization already exists"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/onboarding/create-organization [post]
func (h *OnboardingHandler) CreateOrganization(c *gin.Context) {
	ctx, span := h.tracer.Start(c.Request.Context(), "OnboardingHandler.CreateOrganization")
	defer span.End()

	// Get user ID from JWT context
	userID, exists := c.Get(httpmiddleware.UserIDKey)
	if !exists {
		// This should not happen if middleware is properly configured
		httpresponse.InternalServerError(c.Writer, "User context not found", "MISSING_USER_CONTEXT")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		httpresponse.Unauthorized(c.Writer, "Invalid user context")
		return
	}

	var req dto.CreateOrganizationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request format", err.Error())
		return
	}

	// Set user ID from auth context
	req.UserID = userIDUint

	// Validate request
	if err := h.validator.Validate(ctx, &req); err != nil {
		httpresponse.ValidationError(c.Writer, err)
		return
	}

	// Convert to tenant service input
	tenantInput := tenantServices.CreateTenantInput{
		Name:        req.Name,
		Domain:      req.Domain,
		PlanID:      req.PlanID,
		AdminEmail:  req.ContactEmail,
		AdminName:   req.CompanyName,
		CompanyInfo: make(map[string]interface{}),
	}

	// Add company info
	if req.CompanyName != "" {
		tenantInput.CompanyInfo["company_name"] = req.CompanyName
	}
	if req.CompanyAddress != "" {
		tenantInput.CompanyInfo["company_address"] = req.CompanyAddress
	}
	if req.ContactPhone != "" {
		tenantInput.CompanyInfo["contact_phone"] = req.ContactPhone
	}
	tenantInput.CompanyInfo["contact_email"] = req.ContactEmail

	// Create tenant
	tenant, err := h.tenantService.Create(ctx, tenantInput)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": userIDUint,
			"domain":  req.Domain,
		}).Error("Failed to create organization")

		httpresponse.InternalError(c.Writer, "Failed to create organization", err.Error())
		return
	}

	// Add user as primary member of the tenant
	memberInput := userServices.AddTenantMemberInput{
		UserID:   userIDUint,
		TenantID: tenant.ID,
		Status:   "active",
	}

	membership, err := h.tenantMembershipService.AddTenantMember(ctx, memberInput)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id":   userIDUint,
			"tenant_id": tenant.ID,
		}).Error("Failed to add user to tenant")

		httpresponse.InternalError(c.Writer, "Failed to add user to organization")
		return
	}

	// TODO: Set as primary tenant membership
	// membership.IsPrimary = true - requires adding UpdateMembership method to service
	_ = membership // Use the membership variable to avoid unused variable error

	// Publish tenant created event for RBAC initialization
	if err := events.PublishTenantCreatedEvent(ctx, tenant.ID, userIDUint, tenant.Domain); err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"tenant_id": tenant.ID,
			"user_id":   userIDUint,
		}).Error("Failed to publish tenant created event")
		// Don't fail the request, just log the error
	}

	// Check if user has onboarding progress, if not create it
	currentStep, err := h.onboardingService.GetCurrentStep(ctx, userIDUint)
	if err != nil || currentStep == "" {
		// User doesn't have onboarding progress, create it
		err = h.onboardingService.CreateOnboardingForUser(ctx, userIDUint)
		if err != nil {
			h.logger.WithError(err).WithFields(map[string]interface{}{
				"user_id": userIDUint,
			}).Error("Failed to create onboarding for user")
		}
	}

	// Update onboarding step
	stepReq := &dto.UpdateOnboardingStepRequest{
		UserID: userIDUint,
		Step:   "create_website",
		Metadata: map[string]interface{}{
			"tenant_id": tenant.ID,
			"domain":    tenant.Domain,
		},
	}

	_, err = h.onboardingService.UpdateStep(ctx, stepReq)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id":   userIDUint,
			"tenant_id": tenant.ID,
		}).Error("Failed to update onboarding step")
		// Don't fail the request, just log the error
	}

	// Emit event when tenant is created
	if err := events.PublishTenantCreatedEvent(ctx, tenant.ID, userIDUint, tenant.Domain); err != nil {
		// Log error but don't fail the request
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"tenant_id": tenant.ID,
			"user_id":   userIDUint,
			"domain":    tenant.Domain,
		}).Error("Failed to publish tenant created event")
	}

	response := &dto.CreateOrganizationResponse{
		TenantID:     tenant.ID,
		Name:         tenant.Name,
		Domain:       tenant.Domain,
		Status:       string(tenant.Status),
		ContactEmail: req.ContactEmail,
		CompanyName:  req.CompanyName,
		Message:      "Organization created successfully",
	}

	httpresponse.Created(c.Writer, response)
}

// GetMyOrganization godoc
// @Summary      Get current user's organization
// @Description  Gets the current authenticated user's primary organization
// @Tags         onboarding
// @Accept       json
// @Produce      json
// @Success      200 {object} response.Response{data=dto.GetMyOrganizationResponse} "Organization retrieved successfully"
// @Failure      401 {object} response.Response "Unauthorized"
// @Failure      404 {object} response.Response "Organization not found"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/onboarding/my/organization [get]
func (h *OnboardingHandler) GetMyOrganization(c *gin.Context) {
	ctx, span := h.tracer.Start(c.Request.Context(), "OnboardingHandler.GetMyOrganization")
	defer span.End()

	// Get user ID from JWT context
	userID, exists := c.Get(httpmiddleware.UserIDKey)
	if !exists {
		// This should not happen if middleware is properly configured
		httpresponse.InternalServerError(c.Writer, "User context not found", "MISSING_USER_CONTEXT")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		httpresponse.Unauthorized(c.Writer, "Invalid user context")
		return
	}

	// Get user's active memberships
	memberships, err := h.tenantMembershipService.GetActiveMemberships(ctx, userIDUint)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": userIDUint,
		}).Error("Failed to get user memberships")

		httpresponse.InternalError(c.Writer, "Failed to get organization")
		return
	}

	// Find primary tenant
	var primaryTenantID uint
	for _, membership := range memberships {
		if membership.IsPrimary {
			primaryTenantID = membership.TenantID
			break
		}
	}

	if primaryTenantID == 0 {
		httpresponse.NotFound(c.Writer, "Organization not found")
		return
	}

	// Get tenant details
	tenant, err := h.tenantService.GetByID(ctx, primaryTenantID)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id":   userIDUint,
			"tenant_id": primaryTenantID,
		}).Error("Failed to get tenant details")

		httpresponse.InternalError(c.Writer, "Failed to get organization details")
		return
	}

	// Extract company info from settings
	var contactEmail, contactPhone, companyName, companyAddress, logoURL string
	if tenant.Settings != nil {
		if email, ok := tenant.Settings["contact_email"].(string); ok {
			contactEmail = email
		}
		if phone, ok := tenant.Settings["contact_phone"].(string); ok {
			contactPhone = phone
		}
		if name, ok := tenant.Settings["company_name"].(string); ok {
			companyName = name
		}
		if address, ok := tenant.Settings["company_address"].(string); ok {
			companyAddress = address
		}
		if url, ok := tenant.Settings["logo_url"].(string); ok {
			logoURL = url
		}
	}

	response := &dto.GetMyOrganizationResponse{
		TenantID:       tenant.ID,
		Name:           tenant.Name,
		Domain:         tenant.Domain,
		Status:         string(tenant.Status),
		ContactEmail:   contactEmail,
		ContactPhone:   contactPhone,
		CompanyName:    companyName,
		CompanyAddress: companyAddress,
		LogoURL:        logoURL,
		IsPrimary:      true,
	}

	httpresponse.Success(c.Writer, response)
}

// GetMyWebsite godoc
// @Summary      Get current user's website (placeholder)
// @Description  Gets the current authenticated user's website information (placeholder implementation)
// @Tags         onboarding
// @Accept       json
// @Produce      json
// @Success      200 {object} response.Response{data=dto.GetMyWebsiteResponse} "Website retrieved successfully"
// @Failure      401 {object} response.Response "Unauthorized"
// @Failure      404 {object} response.Response "Website not found"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/onboarding/my/website [get]
func (h *OnboardingHandler) GetMyWebsite(c *gin.Context) {
	_, span := h.tracer.Start(c.Request.Context(), "OnboardingHandler.GetMyWebsite")
	defer span.End()

	// Get user ID from JWT context
	userID, exists := c.Get(httpmiddleware.UserIDKey)
	if !exists {
		// This should not happen if middleware is properly configured
		httpresponse.InternalServerError(c.Writer, "User context not found", "MISSING_USER_CONTEXT")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		httpresponse.Unauthorized(c.Writer, "Invalid user context")
		return
	}

	// TODO: Implement website service integration
	// For now, return a placeholder response
	h.logger.WithFields(map[string]interface{}{
		"user_id": userIDUint,
	}).Info("GetMyWebsite called - placeholder implementation")

	response := &dto.GetMyWebsiteResponse{
		WebsiteID:   1, // Placeholder
		Name:        "My Website",
		Description: "Default website",
		Domain:      "",
		Language:    "en",
		Timezone:    "UTC",
		Status:      "active",
	}

	httpresponse.Success(c.Writer, response)
}

// CreateMyOrganization godoc
// @Summary      Create or update current user's organization
// @Description  Creates a new organization or updates existing organization for the authenticated user
// @Tags         onboarding
// @Accept       json
// @Produce      json
// @Param        body body dto.CreateOrganizationRequest true "Create organization request"
// @Success      201 {object} response.Response{data=dto.CreateOrganizationResponse} "Organization created successfully"
// @Success      200 {object} response.Response{data=dto.CreateOrganizationResponse} "Organization updated successfully"
// @Failure      400 {object} response.Response "Invalid request format"
// @Failure      401 {object} response.Response "Unauthorized"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/onboarding/my/organization [post]
func (h *OnboardingHandler) CreateMyOrganization(c *gin.Context) {
	ctx, span := h.tracer.Start(c.Request.Context(), "OnboardingHandler.CreateMyOrganization")
	defer span.End()

	// Get user ID from JWT context
	userID, exists := c.Get(httpmiddleware.UserIDKey)
	if !exists {
		// This should not happen if middleware is properly configured
		httpresponse.InternalServerError(c.Writer, "User context not found", "MISSING_USER_CONTEXT")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		httpresponse.Unauthorized(c.Writer, "Invalid user context")
		return
	}

	var req dto.CreateOrganizationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request format", err.Error())
		return
	}

	// Set user ID from auth context
	req.UserID = userIDUint

	// Validate request
	if err := h.validator.Validate(ctx, &req); err != nil {
		httpresponse.ValidationError(c.Writer, err)
		return
	}

	// Check if user already has a primary organization
	memberships, err := h.tenantMembershipService.GetActiveMemberships(ctx, userIDUint)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": userIDUint,
		}).Error("Failed to get user memberships")

		httpresponse.InternalError(c.Writer, "Failed to check existing organization")
		return
	}

	// Find primary tenant if exists
	var primaryTenantID uint
	for _, membership := range memberships {
		if membership.IsPrimary {
			primaryTenantID = membership.TenantID
			break
		}
	}

	var tenant *models.Tenant
	var isUpdate bool

	if primaryTenantID > 0 {
		// Update existing organization
		isUpdate = true
		updateInput := tenantServices.UpdateTenantInput{
			Name:        &req.Name,
			Domain:      &req.Domain,
			CompanyInfo: make(map[string]interface{}),
		}

		// Add company info
		if req.CompanyName != "" {
			updateInput.CompanyInfo["company_name"] = req.CompanyName
		}
		if req.CompanyAddress != "" {
			updateInput.CompanyInfo["company_address"] = req.CompanyAddress
		}
		if req.ContactPhone != "" {
			updateInput.CompanyInfo["contact_phone"] = req.ContactPhone
		}
		updateInput.CompanyInfo["contact_email"] = req.ContactEmail

		tenant, err = h.tenantService.Update(ctx, primaryTenantID, updateInput)
		if err != nil {
			h.logger.WithError(err).WithFields(map[string]interface{}{
				"user_id":   userIDUint,
				"tenant_id": primaryTenantID,
			}).Error("Failed to update organization")

			httpresponse.InternalError(c.Writer, "Failed to update organization", err.Error())
			return
		}
	} else {
		// Create new organization
		tenantInput := tenantServices.CreateTenantInput{
			Name:        req.Name,
			Domain:      req.Domain,
			PlanID:      req.PlanID,
			AdminEmail:  req.ContactEmail,
			AdminName:   req.CompanyName,
			CompanyInfo: make(map[string]interface{}),
		}

		// Add company info
		if req.CompanyName != "" {
			tenantInput.CompanyInfo["company_name"] = req.CompanyName
		}
		if req.CompanyAddress != "" {
			tenantInput.CompanyInfo["company_address"] = req.CompanyAddress
		}
		if req.ContactPhone != "" {
			tenantInput.CompanyInfo["contact_phone"] = req.ContactPhone
		}
		tenantInput.CompanyInfo["contact_email"] = req.ContactEmail

		// Create tenant
		tenant, err = h.tenantService.Create(ctx, tenantInput)
		if err != nil {
			h.logger.WithError(err).WithFields(map[string]interface{}{
				"user_id": userIDUint,
				"domain":  req.Domain,
			}).Error("Failed to create organization")

			httpresponse.InternalError(c.Writer, "Failed to create organization", err.Error())
			return
		}

		// Add user as primary member of the tenant
		memberInput := userServices.AddTenantMemberInput{
			UserID:   userIDUint,
			TenantID: tenant.ID,
			Status:   "active",
		}

		membership, err := h.tenantMembershipService.AddTenantMember(ctx, memberInput)
		if err != nil {
			h.logger.WithError(err).WithFields(map[string]interface{}{
				"user_id":   userIDUint,
				"tenant_id": tenant.ID,
			}).Error("Failed to add user to tenant")

			httpresponse.InternalError(c.Writer, "Failed to add user to organization")
			return
		}

		// TODO: Set as primary tenant membership
		_ = membership // Use the membership variable to avoid unused variable error

		// Emit event when tenant is created (only for new tenants, not updates)
		if err := events.PublishTenantCreatedEvent(ctx, tenant.ID, userIDUint, tenant.Domain); err != nil {
			// Log error but don't fail the request
			h.logger.WithError(err).WithFields(map[string]interface{}{
				"tenant_id": tenant.ID,
				"user_id":   userIDUint,
				"domain":    tenant.Domain,
			}).Error("Failed to publish tenant created event")
		}
	}

	// Check if user has onboarding progress, if not create it
	currentStep, err := h.onboardingService.GetCurrentStep(ctx, userIDUint)
	if err != nil || currentStep == "" {
		// User doesn't have onboarding progress, create it
		err = h.onboardingService.CreateOnboardingForUser(ctx, userIDUint)
		if err != nil {
			h.logger.WithError(err).WithFields(map[string]interface{}{
				"user_id": userIDUint,
			}).Error("Failed to create onboarding for user")
		}
	}

	// Update onboarding step
	stepReq := &dto.UpdateOnboardingStepRequest{
		UserID: userIDUint,
		Step:   "create_website",
		Metadata: map[string]interface{}{
			"tenant_id": tenant.ID,
			"domain":    tenant.Domain,
		},
	}

	_, err = h.onboardingService.UpdateStep(ctx, stepReq)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id":   userIDUint,
			"tenant_id": tenant.ID,
		}).Error("Failed to update onboarding step")
		// Don't fail the request, just log the error
	}

	response := &dto.CreateOrganizationResponse{
		TenantID:     tenant.ID,
		Name:         tenant.Name,
		Domain:       tenant.Domain,
		Status:       string(tenant.Status),
		ContactEmail: req.ContactEmail,
		CompanyName:  req.CompanyName,
		Message:      "Organization " + map[bool]string{true: "updated", false: "created"}[isUpdate] + " successfully",
	}

	if isUpdate {
		httpresponse.Success(c.Writer, response)
	} else {
		httpresponse.Created(c.Writer, response)
	}
}

// CreateMyWebsite godoc
// @Summary      Create or update current user's website
// @Description  Creates a new website or updates existing website for the authenticated user
// @Tags         onboarding
// @Accept       json
// @Produce      json
// @Param        body body dto.CreateWebsiteRequest true "Create website request"
// @Success      201 {object} response.Response{data=dto.CreateWebsiteResponse} "Website created successfully"
// @Success      200 {object} response.Response{data=dto.CreateWebsiteResponse} "Website updated successfully"
// @Failure      400 {object} response.Response "Invalid request format"
// @Failure      401 {object} response.Response "Unauthorized"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/onboarding/my/website [post]
func (h *OnboardingHandler) CreateMyWebsite(c *gin.Context) {
	ctx, span := h.tracer.Start(c.Request.Context(), "OnboardingHandler.CreateMyWebsite")
	defer span.End()

	// Get user ID from JWT context
	userID, exists := c.Get(httpmiddleware.UserIDKey)
	if !exists {
		// This should not happen if middleware is properly configured
		httpresponse.InternalServerError(c.Writer, "User context not found", "MISSING_USER_CONTEXT")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		httpresponse.Unauthorized(c.Writer, "Invalid user context")
		return
	}

	var req dto.CreateWebsiteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request format", err.Error())
		return
	}

	// Set user ID from auth context
	req.UserID = userIDUint

	// Validate request
	if err := h.validator.Validate(ctx, &req); err != nil {
		httpresponse.ValidationError(c.Writer, err)
		return
	}

	// Get user's primary tenant
	memberships, err := h.tenantMembershipService.GetActiveMemberships(ctx, userIDUint)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": userIDUint,
		}).Error("Failed to get user memberships")

		httpresponse.InternalError(c.Writer, "Failed to get organization")
		return
	}

	// Find primary tenant
	var primaryTenantID uint
	for _, membership := range memberships {
		if membership.IsPrimary {
			primaryTenantID = membership.TenantID
			break
		}
	}

	if primaryTenantID == 0 {
		httpresponse.BadRequest(c.Writer, "No organization found", "Please create an organization first")
		return
	}

	// TODO: Implement website service integration
	// For now, we'll store website info in tenant settings
	tenant, err := h.tenantService.GetByID(ctx, primaryTenantID)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id":   userIDUint,
			"tenant_id": primaryTenantID,
		}).Error("Failed to get tenant details")

		httpresponse.InternalError(c.Writer, "Failed to get organization details")
		return
	}

	// Update tenant settings with website info
	if tenant.Settings == nil {
		tenant.Settings = make(map[string]interface{})
	}

	websiteInfo := map[string]interface{}{
		"name":        req.Name,
		"description": req.Description,
		"domain":      req.Domain,
		"language":    req.Language,
		"timezone":    req.Timezone,
		"status":      "active",
	}

	tenant.Settings["website"] = websiteInfo

	updateInput := tenantServices.UpdateTenantInput{
		CompanyInfo: tenant.Settings,
	}

	_, err = h.tenantService.Update(ctx, primaryTenantID, updateInput)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id":   userIDUint,
			"tenant_id": primaryTenantID,
		}).Error("Failed to update tenant with website info")

		httpresponse.InternalError(c.Writer, "Failed to save website information")
		return
	}

	// Update onboarding step to completed
	stepReq := &dto.UpdateOnboardingStepRequest{
		UserID: userIDUint,
		Step:   "completed",
		Metadata: map[string]interface{}{
			"website_name": req.Name,
			"domain":       req.Domain,
		},
	}

	_, err = h.onboardingService.UpdateStep(ctx, stepReq)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": userIDUint,
		}).Error("Failed to update onboarding step")
		// Don't fail the request, just log the error
	}

	// Complete onboarding
	completeReq := &dto.CompleteOnboardingRequest{
		UserID: userIDUint,
		Metadata: map[string]interface{}{
			"tenant_id":      primaryTenantID,
			"website_name":   req.Name,
			"website_domain": req.Domain,
		},
	}

	_, err = h.onboardingService.CompleteOnboarding(ctx, completeReq)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": userIDUint,
		}).Error("Failed to complete onboarding")
		// Don't fail the request, just log the error
	}

	response := &dto.CreateWebsiteResponse{
		WebsiteID:   1, // Placeholder ID
		Name:        req.Name,
		Description: req.Description,
		Domain:      req.Domain,
		Status:      "active",
		Message:     "Website created successfully",
	}

	httpresponse.Created(c.Writer, response)
}

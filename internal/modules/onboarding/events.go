package onboarding

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/pkg/events"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

// InitializeOnboardingEventHandlers sets up event handlers for onboarding
func InitializeOnboardingEventHandlers(db *gorm.DB, logger utils.Logger) {
	eventBus := events.GetEventBus()

	// Create onboarding service
	onboardingService := GetOnboardingService(db, logger)

	// Register handler for email verified events
	eventBus.SubscribeEmailVerified("email_verified", func(ctx context.Context, event events.EmailVerifiedEvent) error {
		logger.WithFields(map[string]interface{}{
			"user_id": event.UserID,
		}).Info("Handling email verified event - creating onboarding record")

		if err := onboardingService.CreateOnboardingForUser(ctx, event.UserID); err != nil {
			logger.WithError(err).WithFields(map[string]interface{}{
				"user_id": event.UserID,
			}).Error("Failed to create onboarding record for verified user")
			return err
		}

		logger.WithFields(map[string]interface{}{
			"user_id": event.UserID,
		}).Info("Successfully created onboarding record for verified user")

		return nil
	})
}

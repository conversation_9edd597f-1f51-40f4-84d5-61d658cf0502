package onboarding

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

// EmailVerificationIntegration handles onboarding creation during email verification
type EmailVerificationIntegration struct {
	onboardingService services.OnboardingService
	logger            utils.Logger
}

// NewEmailVerificationIntegration creates a new email verification integration
func NewEmailVerificationIntegration(db *gorm.DB, logger utils.Logger) *EmailVerificationIntegration {
	onboardingService := GetOnboardingService(db, logger)

	return &EmailVerificationIntegration{
		onboardingService: onboardingService,
		logger:            logger,
	}
}

// HandleEmailVerified is called when a user verifies their email
// This function automatically creates an onboarding record for the user
func (e *EmailVerificationIntegration) HandleEmailVerified(ctx context.Context, userID uint) error {
	e.logger.WithFields(map[string]interface{}{
		"user_id": userID,
	}).Info("Creating onboarding record for verified user")

	if err := e.onboardingService.CreateOnboardingForUser(ctx, userID); err != nil {
		e.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": userID,
		}).Error("Failed to create onboarding record for verified user")
		return err
	}

	e.logger.WithFields(map[string]interface{}{
		"user_id": userID,
	}).Info("Successfully created onboarding record for verified user")

	return nil
}

// GetOnboardingIntegration creates and returns an email verification integration instance
// This function should be called from the auth module during email verification
func GetOnboardingIntegration(db *gorm.DB, logger utils.Logger) *EmailVerificationIntegration {
	return NewEmailVerificationIntegration(db, logger)
}

package mysql

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

type onboardingProgressRepository struct {
	db     *gorm.DB
	logger utils.Logger
}

// NewOnboardingProgressRepository creates a new MySQL onboarding progress repository
func NewOnboardingProgressRepository(db *gorm.DB, logger utils.Logger) repositories.OnboardingProgressRepository {
	return &onboardingProgressRepository{
		db:     db,
		logger: logger,
	}
}

// <PERSON><PERSON> creates a new onboarding progress record
func (r *onboardingProgressRepository) Create(ctx context.Context, progress *models.OnboardingProgress) error {
	if err := r.db.WithContext(ctx).Create(progress).Error; err != nil {
		if strings.Contains(err.Error(), "Duplicate entry") {
			r.logger.WithError(err).Error("Onboarding progress already exists for user")
			return fmt.Errorf("onboarding progress already exists for user")
		}
		r.logger.WithError(err).Error("Failed to create onboarding progress")
		return fmt.Errorf("failed to create onboarding progress: %w", err)
	}
	return nil
}

// GetByID retrieves onboarding progress by ID
func (r *onboardingProgressRepository) GetByID(ctx context.Context, id uint) (*models.OnboardingProgress, error) {
	var progress models.OnboardingProgress
	if err := r.db.WithContext(ctx).First(&progress, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("onboarding progress not found")
		}
		r.logger.WithError(err).Error("Failed to get onboarding progress by ID")
		return nil, fmt.Errorf("failed to get onboarding progress: %w", err)
	}
	return &progress, nil
}

// GetByUserID retrieves onboarding progress by user ID
func (r *onboardingProgressRepository) GetByUserID(ctx context.Context, userID uint) (*models.OnboardingProgress, error) {
	var progress models.OnboardingProgress
	if err := r.db.WithContext(ctx).Where("user_id = ?", userID).First(&progress).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("onboarding progress not found for user")
		}
		r.logger.WithError(err).Error("Failed to get onboarding progress by user ID")
		return nil, fmt.Errorf("failed to get onboarding progress: %w", err)
	}
	return &progress, nil
}

// Update updates an onboarding progress record
func (r *onboardingProgressRepository) Update(ctx context.Context, progress *models.OnboardingProgress) error {
	if err := r.db.WithContext(ctx).Save(progress).Error; err != nil {
		r.logger.WithError(err).Error("Failed to update onboarding progress")
		return fmt.Errorf("failed to update onboarding progress: %w", err)
	}
	return nil
}

// Delete deletes an onboarding progress record
func (r *onboardingProgressRepository) Delete(ctx context.Context, id uint) error {
	if err := r.db.WithContext(ctx).Delete(&models.OnboardingProgress{}, id).Error; err != nil {
		r.logger.WithError(err).Error("Failed to delete onboarding progress")
		return fmt.Errorf("failed to delete onboarding progress: %w", err)
	}
	return nil
}

// UpdateStatus updates the status of onboarding progress
func (r *onboardingProgressRepository) UpdateStatus(ctx context.Context, userID uint, status models.OnboardingStatus) error {
	if err := r.db.WithContext(ctx).Model(&models.OnboardingProgress{}).
		Where("user_id = ?", userID).
		Update("status", status).Error; err != nil {
		r.logger.WithError(err).Error("Failed to update onboarding progress status")
		return fmt.Errorf("failed to update onboarding progress status: %w", err)
	}
	return nil
}

// UpdateStep updates the step of onboarding progress
func (r *onboardingProgressRepository) UpdateStep(ctx context.Context, userID uint, step models.OnboardingStep) error {
	if err := r.db.WithContext(ctx).Model(&models.OnboardingProgress{}).
		Where("user_id = ?", userID).
		Update("step", step).Error; err != nil {
		r.logger.WithError(err).Error("Failed to update onboarding progress step")
		return fmt.Errorf("failed to update onboarding progress step: %w", err)
	}
	return nil
}

// UpdateMetadata updates the metadata of onboarding progress
func (r *onboardingProgressRepository) UpdateMetadata(ctx context.Context, userID uint, metadata models.OnboardingMetadata) error {
	if err := r.db.WithContext(ctx).Model(&models.OnboardingProgress{}).
		Where("user_id = ?", userID).
		Update("metadata", metadata).Error; err != nil {
		r.logger.WithError(err).Error("Failed to update onboarding progress metadata")
		return fmt.Errorf("failed to update onboarding progress metadata: %w", err)
	}
	return nil
}

// GetByIDs retrieves multiple onboarding progress records by IDs
func (r *onboardingProgressRepository) GetByIDs(ctx context.Context, ids []uint) ([]*models.OnboardingProgress, error) {
	var progressList []*models.OnboardingProgress
	if err := r.db.WithContext(ctx).Where("id IN ?", ids).Find(&progressList).Error; err != nil {
		r.logger.WithError(err).Error("Failed to get onboarding progress by IDs")
		return nil, fmt.Errorf("failed to get onboarding progress: %w", err)
	}
	return progressList, nil
}

// GetByUserIDs retrieves multiple onboarding progress records by user IDs
func (r *onboardingProgressRepository) GetByUserIDs(ctx context.Context, userIDs []uint) ([]*models.OnboardingProgress, error) {
	var progressList []*models.OnboardingProgress
	if err := r.db.WithContext(ctx).Where("user_id IN ?", userIDs).Find(&progressList).Error; err != nil {
		r.logger.WithError(err).Error("Failed to get onboarding progress by user IDs")
		return nil, fmt.Errorf("failed to get onboarding progress: %w", err)
	}
	return progressList, nil
}

// List retrieves onboarding progress records with filters
func (r *onboardingProgressRepository) List(ctx context.Context, filters *repositories.OnboardingProgressFilters) ([]*models.OnboardingProgress, error) {
	query := r.db.WithContext(ctx)

	// Apply filters
	if filters != nil {
		if filters.Status != nil {
			query = query.Where("status = ?", *filters.Status)
		}
		if filters.Step != nil {
			query = query.Where("step = ?", *filters.Step)
		}
		if len(filters.UserIDs) > 0 {
			query = query.Where("user_id IN ?", filters.UserIDs)
		}
		if filters.StartDate != nil {
			query = query.Where("created_at >= ?", *filters.StartDate)
		}
		if filters.EndDate != nil {
			query = query.Where("created_at <= ?", *filters.EndDate)
		}

		// Apply ordering
		orderBy := "created_at"
		orderDir := "DESC"
		if filters.OrderBy != "" {
			orderBy = filters.OrderBy
		}
		if filters.OrderDir != "" {
			orderDir = filters.OrderDir
		}
		query = query.Order(fmt.Sprintf("%s %s", orderBy, orderDir))

		// Apply pagination
		if filters.Limit > 0 {
			query = query.Limit(filters.Limit)
		}
		if filters.Offset > 0 {
			query = query.Offset(filters.Offset)
		}
	}

	var progressList []*models.OnboardingProgress
	if err := query.Find(&progressList).Error; err != nil {
		r.logger.WithError(err).Error("Failed to list onboarding progress")
		return nil, fmt.Errorf("failed to list onboarding progress: %w", err)
	}
	return progressList, nil
}

// Count counts onboarding progress records with filters
func (r *onboardingProgressRepository) Count(ctx context.Context, filters *repositories.OnboardingProgressFilters) (int64, error) {
	query := r.db.WithContext(ctx).Model(&models.OnboardingProgress{})

	// Apply filters
	if filters != nil {
		if filters.Status != nil {
			query = query.Where("status = ?", *filters.Status)
		}
		if filters.Step != nil {
			query = query.Where("step = ?", *filters.Step)
		}
		if len(filters.UserIDs) > 0 {
			query = query.Where("user_id IN ?", filters.UserIDs)
		}
		if filters.StartDate != nil {
			query = query.Where("created_at >= ?", *filters.StartDate)
		}
		if filters.EndDate != nil {
			query = query.Where("created_at <= ?", *filters.EndDate)
		}
	}

	var count int64
	if err := query.Count(&count).Error; err != nil {
		r.logger.WithError(err).Error("Failed to count onboarding progress")
		return 0, fmt.Errorf("failed to count onboarding progress: %w", err)
	}
	return count, nil
}

// GetByStatus retrieves onboarding progress records by status
func (r *onboardingProgressRepository) GetByStatus(ctx context.Context, status models.OnboardingStatus) ([]*models.OnboardingProgress, error) {
	var progressList []*models.OnboardingProgress
	if err := r.db.WithContext(ctx).Where("status = ?", status).Find(&progressList).Error; err != nil {
		r.logger.WithError(err).Error("Failed to get onboarding progress by status")
		return nil, fmt.Errorf("failed to get onboarding progress: %w", err)
	}
	return progressList, nil
}

// GetByStep retrieves onboarding progress records by step
func (r *onboardingProgressRepository) GetByStep(ctx context.Context, step models.OnboardingStep) ([]*models.OnboardingProgress, error) {
	var progressList []*models.OnboardingProgress
	if err := r.db.WithContext(ctx).Where("step = ?", step).Find(&progressList).Error; err != nil {
		r.logger.WithError(err).Error("Failed to get onboarding progress by step")
		return nil, fmt.Errorf("failed to get onboarding progress: %w", err)
	}
	return progressList, nil
}

// GetStats retrieves statistics about onboarding progress
func (r *onboardingProgressRepository) GetStats(ctx context.Context) (*repositories.OnboardingStats, error) {
	var stats repositories.OnboardingStats

	// Count total users with onboarding progress
	var totalUsers int64
	if err := r.db.WithContext(ctx).Model(&models.OnboardingProgress{}).Count(&totalUsers).Error; err != nil {
		r.logger.WithError(err).Error("Failed to count total users")
		return nil, fmt.Errorf("failed to get stats: %w", err)
	}

	// Count by status
	var pendingCount, processingCount, completedCount int64

	if err := r.db.WithContext(ctx).Model(&models.OnboardingProgress{}).
		Where("status = ?", models.OnboardingStatusPending).Count(&pendingCount).Error; err != nil {
		r.logger.WithError(err).Error("Failed to count pending users")
		return nil, fmt.Errorf("failed to get stats: %w", err)
	}

	if err := r.db.WithContext(ctx).Model(&models.OnboardingProgress{}).
		Where("status = ?", models.OnboardingStatusProcessing).Count(&processingCount).Error; err != nil {
		r.logger.WithError(err).Error("Failed to count processing users")
		return nil, fmt.Errorf("failed to get stats: %w", err)
	}

	if err := r.db.WithContext(ctx).Model(&models.OnboardingProgress{}).
		Where("status = ?", models.OnboardingStatusCompleted).Count(&completedCount).Error; err != nil {
		r.logger.WithError(err).Error("Failed to count completed users")
		return nil, fmt.Errorf("failed to get stats: %w", err)
	}

	stats.TotalUsers = uint(totalUsers)
	stats.PendingUsers = uint(pendingCount)
	stats.ProcessingUsers = uint(processingCount)
	stats.CompletedUsers = uint(completedCount)

	// Calculate completion rate
	if stats.TotalUsers > 0 {
		stats.CompletionRate = float64(stats.CompletedUsers) / float64(stats.TotalUsers) * 100
	}

	return &stats, nil
}

// GetCompletionRate retrieves the completion rate
func (r *onboardingProgressRepository) GetCompletionRate(ctx context.Context) (float64, error) {
	stats, err := r.GetStats(ctx)
	if err != nil {
		return 0, err
	}
	return stats.CompletionRate, nil
}

// Exists checks if onboarding progress exists for a user
func (r *onboardingProgressRepository) Exists(ctx context.Context, userID uint) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.OnboardingProgress{}).
		Where("user_id = ?", userID).Count(&count).Error; err != nil {
		r.logger.WithError(err).Error("Failed to check if onboarding progress exists")
		return false, fmt.Errorf("failed to check existence: %w", err)
	}
	return count > 0, nil
}

// ExistsByID checks if onboarding progress exists by ID
func (r *onboardingProgressRepository) ExistsByID(ctx context.Context, id uint) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.OnboardingProgress{}).
		Where("id = ?", id).Count(&count).Error; err != nil {
		r.logger.WithError(err).Error("Failed to check if onboarding progress exists by ID")
		return false, fmt.Errorf("failed to check existence: %w", err)
	}
	return count > 0, nil
}

package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// SEOMetaHandler handles SEO metadata HTTP requests
type SEOMetaHandler struct {
	seoMetaService services.SEOMetaService
	validator      validator.Validator
	logger         utils.Logger
}

// NewSEOMetaHandler creates a new SEO meta handler instance
func NewSEOMetaHandler(
	seoMetaService services.SEOMetaService,
	validator validator.Validator,
	logger utils.Logger,
) *SEOMetaHandler {
	return &SEOMetaHandler{
		seoMetaService: seoMetaService,
		validator:      validator,
		logger:         logger,
	}
}

// CreateMeta creates new SEO metadata
// @Summary Create SEO metadata
// @Description Create new SEO metadata for a page
// @Tags SEO Meta
// @Accept json
// @Produce json
// @Param request body models.CreateSEOMetaRequest true "Create SEO meta request"
// @Success 201 {object} response.Response{data=models.SEOMetaResponse}
// @Failure 400 {object} response.Response
// @Failure 422 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /seo/meta [post]
func (h *SEOMetaHandler) CreateMeta(c *gin.Context) {
	var req models.CreateSEOMetaRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body")
		return
	}

	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Get tenant and website ID from context
	// Get tenant context
	tenantCtx, exists := c.Get("tenant_context")
	if !exists {
		response.BadRequest(c.Writer, "Tenant context is required")
		return
	}

	tenantInfo, ok := tenantCtx.(map[string]interface{})
	if !ok {
		response.BadRequest(c.Writer, "Invalid tenant context")
		return
	}

	tenantID, ok := tenantInfo["tenant_id"].(uint)
	if !ok || tenantID == 0 {
		response.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}
	if tenantID == 0 {
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	seoMeta, err := h.seoMetaService.CreateMeta(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("Failed to create SEO meta", "error", err, "tenant_id", tenantID)
		response.InternalError(c.Writer, "Failed to create SEO metadata", err.Error())
		return
	}

	response.Created(c.Writer, seoMeta)
}

// GetMeta retrieves SEO metadata by ID
// @Summary Get SEO metadata by ID
// @Description Get SEO metadata by its ID
// @Tags SEO Meta
// @Accept json
// @Produce json
// @Param id path int true "SEO Meta ID"
// @Success 200 {object} response.Response{data=models.SEOMetaResponse}
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /seo/meta/{id} [get]
func (h *SEOMetaHandler) GetMeta(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid ID parameter")
		return
	}

	// Get tenant context
	tenantCtx, exists := c.Get("tenant_context")
	if !exists {
		response.BadRequest(c.Writer, "Tenant context is required")
		return
	}

	tenantInfo, ok := tenantCtx.(map[string]interface{})
	if !ok {
		response.BadRequest(c.Writer, "Invalid tenant context")
		return
	}

	tenantID, ok := tenantInfo["tenant_id"].(uint)
	if !ok || tenantID == 0 {
		response.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}
	seoMeta, err := h.seoMetaService.GetMetaByID(c.Request.Context(), uint(id), tenantID)
	if err != nil {
		h.logger.Error("Failed to get SEO meta", "error", err, "id", id, "tenant_id", tenantID)
		response.NotFound(c.Writer, "SEO metadata not found")
		return
	}

	response.Success(c.Writer, seoMeta)
}

// UpdateMeta updates existing SEO metadata
// @Summary Update SEO metadata
// @Description Update existing SEO metadata
// @Tags SEO Meta
// @Accept json
// @Produce json
// @Param id path int true "SEO Meta ID"
// @Param request body models.UpdateSEOMetaRequest true "Update SEO meta request"
// @Success 200 {object} response.Response{data=models.SEOMetaResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 422 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /seo/meta/{id} [put]
func (h *SEOMetaHandler) UpdateMeta(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid ID parameter")
		return
	}

	var req models.UpdateSEOMetaRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body")
		return
	}

	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Get tenant context
	tenantCtx, exists := c.Get("tenant_context")
	if !exists {
		response.BadRequest(c.Writer, "Tenant context is required")
		return
	}

	tenantInfo, ok := tenantCtx.(map[string]interface{})
	if !ok {
		response.BadRequest(c.Writer, "Invalid tenant context")
		return
	}

	tenantID, ok := tenantInfo["tenant_id"].(uint)
	if !ok || tenantID == 0 {
		response.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}
	seoMeta, err := h.seoMetaService.UpdateMeta(c.Request.Context(), uint(id), &req, tenantID)
	if err != nil {
		h.logger.Error("Failed to update SEO meta", "error", err, "id", id, "tenant_id", tenantID)
		response.InternalError(c.Writer, "Failed to update SEO metadata", err.Error())
		return
	}

	response.Success(c.Writer, seoMeta)
}

// DeleteMeta deletes SEO metadata
// @Summary Delete SEO metadata
// @Description Delete SEO metadata by ID
// @Tags SEO Meta
// @Accept json
// @Produce json
// @Param id path int true "SEO Meta ID"
// @Success 200 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /seo/meta/{id} [delete]
func (h *SEOMetaHandler) DeleteMeta(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid ID parameter")
		return
	}

	// Get tenant context
	tenantCtx, exists := c.Get("tenant_context")
	if !exists {
		response.BadRequest(c.Writer, "Tenant context is required")
		return
	}

	tenantInfo, ok := tenantCtx.(map[string]interface{})
	if !ok {
		response.BadRequest(c.Writer, "Invalid tenant context")
		return
	}

	tenantID, ok := tenantInfo["tenant_id"].(uint)
	if !ok || tenantID == 0 {
		response.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}
	err = h.seoMetaService.DeleteMeta(c.Request.Context(), uint(id), tenantID)
	if err != nil {
		h.logger.Error("Failed to delete SEO meta", "error", err, "id", id, "tenant_id", tenantID)
		response.InternalError(c.Writer, "Failed to delete SEO metadata", err.Error())
		return
	}

	response.NoContent(c.Writer)
}

// ListMeta lists SEO metadata with filtering
// @Summary List SEO metadata
// @Description List SEO metadata with optional filtering
// @Tags SEO Meta
// @Accept json
// @Produce json
// @Param website_id query int false "Website ID"
// @Param page_type query string false "Page type (page, post, category, tag, product, custom)"
// @Param status query string false "Status (active, inactive, deleted)"
// @Param is_indexed query bool false "Is indexed"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} response.Response{data=[]models.SEOMetaResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /seo/meta [get]
func (h *SEOMetaHandler) ListMeta(c *gin.Context) {
	// Parse query parameters
	filter := &models.SEOMetaFilter{}

	if websiteID := c.Query("website_id"); websiteID != "" {
		if id, err := strconv.ParseUint(websiteID, 10, 32); err == nil {
			uid := uint(id)
			filter.WebsiteID = &uid
		}
	}

	if pageType := c.Query("page_type"); pageType != "" {
		filter.PageType = &pageType
	}

	if status := c.Query("status"); status != "" {
		filter.Status = &status
	}

	if isIndexed := c.Query("is_indexed"); isIndexed != "" {
		if indexed, err := strconv.ParseBool(isIndexed); err == nil {
			filter.IsIndexed = &indexed
		}
	}

	// Parse pagination
	page := 1
	if p := c.Query("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
			page = parsed
		}
	}

	limit := 20
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	// Get tenant context
	tenantCtx, exists := c.Get("tenant_context")
	if !exists {
		response.BadRequest(c.Writer, "Tenant context is required")
		return
	}

	tenantInfo, ok := tenantCtx.(map[string]interface{})
	if !ok {
		response.BadRequest(c.Writer, "Invalid tenant context")
		return
	}

	tenantID, ok := tenantInfo["tenant_id"].(uint)
	if !ok || tenantID == 0 {
		response.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}

	result, err := h.seoMetaService.ListMeta(c.Request.Context(), filter, page, limit)
	if err != nil {
		h.logger.Error("Failed to list SEO meta", "error", err, "tenant_id", tenantID)
		response.InternalError(c.Writer, "Failed to list SEO metadata", err.Error())
		return
	}

	response.Success(c.Writer, result)
}

// GetMetaByPage retrieves SEO metadata by page identifier
// @Summary Get SEO metadata by page
// @Description Get SEO metadata by page type and page ID
// @Tags SEO Meta
// @Accept json
// @Produce json
// @Param page_type query string true "Page type (page, post, category, tag, product, custom)"
// @Param page_id query int false "Page ID"
// @Param page_url query string false "Page URL"
// @Success 200 {object} response.Response{data=models.SEOMetaResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /seo/meta/by-page [get]
func (h *SEOMetaHandler) GetMetaByPage(c *gin.Context) {
	pageType := c.Query("page_type")
	if pageType == "" {
		response.BadRequest(c.Writer, "page_type is required")
		return
	}

	var pageID *uint
	if pid := c.Query("page_id"); pid != "" {
		if id, err := strconv.ParseUint(pid, 10, 32); err == nil {
			uid := uint(id)
			pageID = &uid
		}
	}

	pageURL := c.Query("page_url")

	// Get tenant context
	tenantCtx, exists := c.Get("tenant_context")
	if !exists {
		response.BadRequest(c.Writer, "Tenant context is required")
		return
	}

	tenantInfo, ok := tenantCtx.(map[string]interface{})
	if !ok {
		response.BadRequest(c.Writer, "Invalid tenant context")
		return
	}

	tenantID, ok := tenantInfo["tenant_id"].(uint)
	if !ok || tenantID == 0 {
		response.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}
	seoMeta, err := h.seoMetaService.GetMetaByPage(c.Request.Context(), pageType, pageID, pageURL, tenantID)
	if err != nil {
		h.logger.Error("Failed to get SEO meta by page", "error", err, "page_type", pageType, "page_id", pageID, "tenant_id", tenantID)
		response.NotFound(c.Writer, "SEO metadata not found")
		return
	}

	response.Success(c.Writer, seoMeta)
}

// BulkCreateMeta creates multiple SEO metadata entries
// @Summary Bulk create SEO metadata
// @Description Create multiple SEO metadata entries at once
// @Tags SEO Meta
// @Accept json
// @Produce json
// @Param request body []models.CreateSEOMetaRequest true "Bulk create SEO meta requests"
// @Success 201 {object} response.Response{data=[]models.SEOMetaResponse}
// @Failure 400 {object} response.Response
// @Failure 422 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /seo/meta/bulk [post]
func (h *SEOMetaHandler) BulkCreateMeta(c *gin.Context) {
	var requests []models.CreateSEOMetaRequest
	if err := c.ShouldBindJSON(&requests); err != nil {
		response.BadRequest(c.Writer, "Invalid request body")
		return
	}

	if len(requests) == 0 {
		response.BadRequest(c.Writer, "At least one request is required")
		return
	}

	if len(requests) > 100 {
		response.BadRequest(c.Writer, "Maximum 100 requests allowed per bulk operation")
		return
	}

	// Get tenant context
	tenantCtx, exists := c.Get("tenant_context")
	if !exists {
		response.BadRequest(c.Writer, "Tenant context is required")
		return
	}

	tenantInfo, ok := tenantCtx.(map[string]interface{})
	if !ok {
		response.BadRequest(c.Writer, "Invalid tenant context")
		return
	}

	tenantID, ok := tenantInfo["tenant_id"].(uint)
	if !ok || tenantID == 0 {
		response.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}

	for i := range requests {
		if err := h.validator.Validate(c.Request.Context(), &requests[i]); err != nil {
			response.ValidationError(c.Writer, err)
			return
		}
	}

	result, err := h.seoMetaService.BulkCreateMeta(c.Request.Context(), requests)
	if err != nil {
		h.logger.Error("Failed to bulk create SEO meta", "error", err, "count", len(requests), "tenant_id", tenantID)
		response.InternalError(c.Writer, "Failed to create SEO metadata", err.Error())
		return
	}

	response.Created(c.Writer, result)
}

// AnalyzeMeta analyzes SEO metadata and provides recommendations
// @Summary Analyze SEO metadata
// @Description Analyze SEO metadata and provide optimization recommendations
// @Tags SEO Meta
// @Accept json
// @Produce json
// @Param id path int true "SEO Meta ID"
// @Success 200 {object} response.Response{data=models.SEOAnalysis}
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /seo/meta/{id}/analyze [post]
func (h *SEOMetaHandler) AnalyzeMeta(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid ID parameter")
		return
	}

	// Get tenant context
	tenantCtx, exists := c.Get("tenant_context")
	if !exists {
		response.BadRequest(c.Writer, "Tenant context is required")
		return
	}

	tenantInfo, ok := tenantCtx.(map[string]interface{})
	if !ok {
		response.BadRequest(c.Writer, "Invalid tenant context")
		return
	}

	tenantID, ok := tenantInfo["tenant_id"].(uint)
	if !ok || tenantID == 0 {
		response.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}
	analysis, err := h.seoMetaService.AnalyzeMeta(c.Request.Context(), uint(id), tenantID)
	if err != nil {
		h.logger.Error("Failed to analyze SEO meta", "error", err, "id", id, "tenant_id", tenantID)
		response.InternalError(c.Writer, "Failed to analyze SEO metadata", err.Error())
		return
	}

	response.Success(c.Writer, analysis)
}

// ValidateMeta validates SEO metadata
// @Summary Validate SEO metadata
// @Description Validate SEO metadata and check for issues
// @Tags SEO Meta
// @Accept json
// @Produce json
// @Param id path int true "SEO Meta ID"
// @Success 200 {object} response.Response{data=models.SEOValidationResult}
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /seo/meta/{id}/validate [post]
func (h *SEOMetaHandler) ValidateMeta(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid ID parameter")
		return
	}

	// Get tenant context
	tenantCtx, exists := c.Get("tenant_context")
	if !exists {
		response.BadRequest(c.Writer, "Tenant context is required")
		return
	}

	tenantInfo, ok := tenantCtx.(map[string]interface{})
	if !ok {
		response.BadRequest(c.Writer, "Invalid tenant context")
		return
	}

	tenantID, ok := tenantInfo["tenant_id"].(uint)
	if !ok || tenantID == 0 {
		response.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}
	result, err := h.seoMetaService.ValidateMeta(c.Request.Context(), uint(id), tenantID)
	if err != nil {
		h.logger.Error("Failed to validate SEO meta", "error", err, "id", id, "tenant_id", tenantID)
		response.InternalError(c.Writer, "Failed to validate SEO metadata", err.Error())
		return
	}

	response.Success(c.Writer, result)
}

// GenerateMetaTags generates HTML meta tags for a page
// @Summary Generate meta tags
// @Description Generate HTML meta tags for SEO metadata
// @Tags SEO Meta
// @Accept json
// @Produce json
// @Param id path int true "SEO Meta ID"
// @Success 200 {object} response.Response{data=map[string]interface{}}
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /seo/meta/{id}/tags [get]
func (h *SEOMetaHandler) GenerateMetaTags(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid ID parameter")
		return
	}

	// Get tenant context
	tenantCtx, exists := c.Get("tenant_context")
	if !exists {
		response.BadRequest(c.Writer, "Tenant context is required")
		return
	}

	tenantInfo, ok := tenantCtx.(map[string]interface{})
	if !ok {
		response.BadRequest(c.Writer, "Invalid tenant context")
		return
	}

	tenantID, ok := tenantInfo["tenant_id"].(uint)
	if !ok || tenantID == 0 {
		response.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}
	tags, structuredData, err := h.seoMetaService.GenerateMetaTags(c.Request.Context(), uint(id), tenantID)
	if err != nil {
		h.logger.Error("Failed to generate meta tags", "error", err, "id", id, "tenant_id", tenantID)
		response.InternalError(c.Writer, "Failed to generate meta tags", err.Error())
		return
	}

	result := map[string]interface{}{
		"meta_tags":       tags,
		"structured_data": structuredData,
	}

	response.Success(c.Writer, result)
}

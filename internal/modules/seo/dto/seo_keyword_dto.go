package dto

import (
	"time"
)

// SEOKeywordCreateRequest represents request to create SEO keyword
type SEOKeywordCreateRequest struct {
	WebsiteID uint   `json:"website_id" validate:"required" example:"1"`
	Keyword   string `json:"keyword" validate:"required,max=255" example:"best web development company"`

	// Keyword Properties
	KeywordType  string   `json:"keyword_type" validate:"required,oneof=primary secondary long_tail branded competitor" example:"primary"`
	Difficulty   *int     `json:"difficulty" validate:"omitempty,min=1,max=100" example:"75"`
	SearchVolume *int     `json:"search_volume" validate:"omitempty,min=0" example:"5400"`
	CPC          *float64 `json:"cpc" validate:"omitempty,min=0" example:"2.35"`
	Competition  *float64 `json:"competition" validate:"omitempty,min=0,max=1" example:"0.68"`

	// Position Tracking
	CurrentPosition *int `json:"current_position" validate:"omitempty,min=1" example:"12"`
	TargetPosition  *int `json:"target_position" validate:"omitempty,min=1" example:"5"`
	BestPosition    *int `json:"best_position" validate:"omitempty,min=1" example:"8"`
	WorstPosition   *int `json:"worst_position" validate:"omitempty,min=1" example:"25"`

	// Tracking Settings
	TrackingURL     *string `json:"tracking_url" validate:"omitempty,url" example:"https://example.com/services/web-development"`
	TrackingEnabled bool    `json:"tracking_enabled" example:"true"`
	Priority        string  `json:"priority" validate:"omitempty,oneof=low medium high critical" example:"high"`

	// SEO Context
	SearchIntent *string `json:"search_intent" validate:"omitempty,oneof=informational navigational transactional commercial" example:"commercial"`
	Location     *string `json:"location" validate:"omitempty,max=100" example:"United States"`
	Language     *string `json:"language" validate:"omitempty,max=10" example:"en"`
	Device       *string `json:"device" validate:"omitempty,oneof=desktop mobile tablet" example:"desktop"`

	// Additional Data
	Tags       []string               `json:"tags" example:"[\"web-dev\",\"services\",\"primary\"]"`
	Notes      *string                `json:"notes" validate:"omitempty,max=1000" example:"Primary keyword for web development services page"`
	CustomData map[string]interface{} `json:"custom_data" example:"{\"campaign_id\":\"camp123\",\"content_group\":\"services\"}"`

	// Status
	Status string `json:"status" validate:"omitempty,oneof=active inactive paused deleted" example:"active"`
}

// SEOKeywordUpdateRequest represents request to update SEO keyword
type SEOKeywordUpdateRequest struct {
	Keyword *string `json:"keyword" validate:"omitempty,max=255" example:"updated web development company"`

	// Keyword Properties
	KeywordType  *string  `json:"keyword_type" validate:"omitempty,oneof=primary secondary long_tail branded competitor" example:"secondary"`
	Difficulty   *int     `json:"difficulty" validate:"omitempty,min=1,max=100" example:"80"`
	SearchVolume *int     `json:"search_volume" validate:"omitempty,min=0" example:"6200"`
	CPC          *float64 `json:"cpc" validate:"omitempty,min=0" example:"2.80"`
	Competition  *float64 `json:"competition" validate:"omitempty,min=0,max=1" example:"0.72"`

	// Position Tracking
	CurrentPosition *int `json:"current_position" validate:"omitempty,min=1" example:"10"`
	TargetPosition  *int `json:"target_position" validate:"omitempty,min=1" example:"3"`
	BestPosition    *int `json:"best_position" validate:"omitempty,min=1" example:"6"`
	WorstPosition   *int `json:"worst_position" validate:"omitempty,min=1" example:"30"`

	// Tracking Settings
	TrackingURL     *string `json:"tracking_url" validate:"omitempty,url" example:"https://example.com/services/web-development-updated"`
	TrackingEnabled *bool   `json:"tracking_enabled" example:"true"`
	Priority        *string `json:"priority" validate:"omitempty,oneof=low medium high critical" example:"critical"`

	// SEO Context
	SearchIntent *string `json:"search_intent" validate:"omitempty,oneof=informational navigational transactional commercial" example:"transactional"`
	Location     *string `json:"location" validate:"omitempty,max=100" example:"United States"`
	Language     *string `json:"language" validate:"omitempty,max=10" example:"en"`
	Device       *string `json:"device" validate:"omitempty,oneof=desktop mobile tablet" example:"mobile"`

	// Additional Data
	Tags       []string               `json:"tags" example:"[\"web-dev\",\"services\",\"updated\"]"`
	Notes      *string                `json:"notes" validate:"omitempty,max=1000" example:"Updated keyword focus"`
	CustomData map[string]interface{} `json:"custom_data"`

	// Status
	Status *string `json:"status" validate:"omitempty,oneof=active inactive paused deleted" example:"active"`
}

// SEOKeywordResponse represents SEO keyword information in responses
type SEOKeywordResponse struct {
	ID        uint   `json:"id" example:"1"`
	WebsiteID uint   `json:"website_id" example:"1"`
	TenantID  uint   `json:"tenant_id" example:"1"`
	Keyword   string `json:"keyword" example:"best web development company"`

	// Keyword Properties
	KeywordType  string   `json:"keyword_type" example:"primary"`
	Difficulty   *int     `json:"difficulty,omitempty" example:"75"`
	SearchVolume *int     `json:"search_volume,omitempty" example:"5400"`
	CPC          *float64 `json:"cpc,omitempty" example:"2.35"`
	Competition  *float64 `json:"competition,omitempty" example:"0.68"`

	// Position Tracking
	CurrentPosition *int `json:"current_position,omitempty" example:"12"`
	TargetPosition  *int `json:"target_position,omitempty" example:"5"`
	BestPosition    *int `json:"best_position,omitempty" example:"8"`
	WorstPosition   *int `json:"worst_position,omitempty" example:"25"`
	PositionChange  *int `json:"position_change,omitempty" example:"-3"`

	// Tracking Settings
	TrackingURL     *string `json:"tracking_url,omitempty" example:"https://example.com/services/web-development"`
	TrackingEnabled bool    `json:"tracking_enabled" example:"true"`
	Priority        string  `json:"priority" example:"high"`

	// SEO Context
	SearchIntent *string `json:"search_intent,omitempty" example:"commercial"`
	Location     *string `json:"location,omitempty" example:"United States"`
	Language     *string `json:"language,omitempty" example:"en"`
	Device       *string `json:"device,omitempty" example:"desktop"`

	// Performance Metrics
	Clicks          *int     `json:"clicks,omitempty" example:"150"`
	Impressions     *int     `json:"impressions,omitempty" example:"2500"`
	CTR             *float64 `json:"ctr,omitempty" example:"6.0"`
	AveragePosition *float64 `json:"average_position,omitempty" example:"11.5"`

	// Additional Data
	Tags       []string               `json:"tags,omitempty" example:"[\"web-dev\",\"services\"]"`
	Notes      *string                `json:"notes,omitempty" example:"Primary keyword"`
	CustomData map[string]interface{} `json:"custom_data,omitempty"`

	// Tracking History
	LastTracked     *time.Time               `json:"last_tracked,omitempty"`
	NextCheck       *time.Time               `json:"next_check,omitempty"`
	TrackingHistory []KeywordPositionHistory `json:"tracking_history,omitempty"`

	// Status and Timestamps
	Status    string    `json:"status" example:"active"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// KeywordPositionHistory represents historical position data
type KeywordPositionHistory struct {
	Date         time.Time `json:"date" example:"2024-01-15T00:00:00Z"`
	Position     int       `json:"position" example:"12"`
	URL          string    `json:"url" example:"https://example.com/services/web-development"`
	SearchEngine string    `json:"search_engine" example:"google"`
	Location     string    `json:"location" example:"United States"`
	Device       string    `json:"device" example:"desktop"`
}

// SEOKeywordListResponse represents response for listing SEO keywords
type SEOKeywordListResponse struct {
	Keywords   []SEOKeywordResponse `json:"keywords"`
	Total      int64                `json:"total" example:"150"`
	Page       int                  `json:"page" example:"1"`
	PageSize   int                  `json:"page_size" example:"20"`
	TotalPages int                  `json:"total_pages" example:"8"`
}

// SEOKeywordFilter represents filter parameters for listing keywords
type SEOKeywordFilter struct {
	WebsiteID       *uint      `json:"website_id" example:"1"`
	KeywordType     *string    `json:"keyword_type" validate:"omitempty,oneof=primary secondary long_tail branded competitor" example:"primary"`
	Status          *string    `json:"status" validate:"omitempty,oneof=active inactive paused deleted" example:"active"`
	Priority        *string    `json:"priority" validate:"omitempty,oneof=low medium high critical" example:"high"`
	SearchIntent    *string    `json:"search_intent" validate:"omitempty,oneof=informational navigational transactional commercial" example:"commercial"`
	MinPosition     *int       `json:"min_position" example:"1"`
	MaxPosition     *int       `json:"max_position" example:"20"`
	MinVolume       *int       `json:"min_volume" example:"1000"`
	MaxVolume       *int       `json:"max_volume" example:"10000"`
	MinDifficulty   *int       `json:"min_difficulty" example:"50"`
	MaxDifficulty   *int       `json:"max_difficulty" example:"100"`
	Location        *string    `json:"location" example:"United States"`
	Language        *string    `json:"language" example:"en"`
	Device          *string    `json:"device" validate:"omitempty,oneof=desktop mobile tablet" example:"desktop"`
	TrackingEnabled *bool      `json:"tracking_enabled" example:"true"`
	Tags            []string   `json:"tags" example:"[\"web-dev\",\"services\"]"`
	Search          string     `json:"search" example:"web development"`
	DateFrom        *time.Time `json:"date_from" example:"2024-01-01T00:00:00Z"`
	DateTo          *time.Time `json:"date_to" example:"2024-12-31T23:59:59Z"`
	Page            int        `json:"page" validate:"min=1" example:"1"`
	PageSize        int        `json:"page_size" validate:"min=1,max=100" example:"20"`
	SortBy          string     `json:"sort_by" validate:"omitempty,oneof=id keyword created_at updated_at current_position search_volume difficulty ctr" example:"current_position"`
	SortOrder       string     `json:"sort_order" validate:"omitempty,oneof=asc desc" example:"asc"`
}

// SEOKeywordStatsResponse represents keyword statistics
type SEOKeywordStatsResponse struct {
	TotalKeywords        int                         `json:"total_keywords" example:"150"`
	ActiveKeywords       int                         `json:"active_keywords" example:"120"`
	TrackedKeywords      int                         `json:"tracked_keywords" example:"100"`
	RankingKeywords      int                         `json:"ranking_keywords" example:"80"`
	TopTenKeywords       int                         `json:"top_ten_keywords" example:"25"`
	AveragePosition      float64                     `json:"average_position" example:"15.5"`
	AverageVolume        float64                     `json:"average_volume" example:"3500.0"`
	TotalVolume          int                         `json:"total_volume" example:"525000"`
	ByType               map[string]KeywordTypeStats `json:"by_type"`
	ByPriority           map[string]int              `json:"by_priority"`
	ByIntent             map[string]int              `json:"by_intent"`
	PositionDistribution map[string]int              `json:"position_distribution"`
	RecentChanges        KeywordChangeStats          `json:"recent_changes"`
	TopPerformers        []SEOKeywordResponse        `json:"top_performers"`
	NeedsAttention       []SEOKeywordResponse        `json:"needs_attention"`
}

// KeywordTypeStats represents statistics by keyword type
type KeywordTypeStats struct {
	Type            string  `json:"type" example:"primary"`
	Count           int     `json:"count" example:"50"`
	AveragePosition float64 `json:"average_position" example:"12.5"`
	AverageVolume   float64 `json:"average_volume" example:"4500.0"`
	RankingCount    int     `json:"ranking_count" example:"40"`
}

// KeywordChangeStats represents recent changes in keyword performance
type KeywordChangeStats struct {
	Improved     int `json:"improved" example:"15"`
	Declined     int `json:"declined" example:"8"`
	NewRankings  int `json:"new_rankings" example:"5"`
	LostRankings int `json:"lost_rankings" example:"3"`
	BiggestGain  int `json:"biggest_gain" example:"25"`
	BiggestLoss  int `json:"biggest_loss" example:"-18"`
}

// SEOKeywordTrackRequest represents request to track keyword positions
type SEOKeywordTrackRequest struct {
	KeywordIDs       []uint   `json:"keyword_ids" validate:"required,min=1" example:"1,2,3"`
	SearchEngines    []string `json:"search_engines" validate:"required,min=1" example:"[\"google\",\"bing\"]"`
	Locations        []string `json:"locations" example:"[\"United States\",\"Canada\"]"`
	Devices          []string `json:"devices" validate:"required,min=1" example:"[\"desktop\",\"mobile\"]"`
	ForceRefresh     bool     `json:"force_refresh" example:"false"`
	HighPriorityOnly bool     `json:"high_priority_only" example:"false"`
}

// SEOKeywordTrackResponse represents response for keyword tracking
type SEOKeywordTrackResponse struct {
	Success       bool                 `json:"success" example:"true"`
	Message       string               `json:"message" example:"Keyword tracking started successfully"`
	TrackedCount  int                  `json:"tracked_count" example:"15"`
	SkippedCount  int                  `json:"skipped_count" example:"2"`
	FailedCount   int                  `json:"failed_count" example:"1"`
	TaskID        string               `json:"task_id" example:"task_track_abc123"`
	EstimatedTime int                  `json:"estimated_time" example:"300"`
	Results       []KeywordTrackResult `json:"results,omitempty"`
}

// KeywordTrackResult represents tracking result for individual keyword
type KeywordTrackResult struct {
	KeywordID        uint      `json:"keyword_id" example:"1"`
	Keyword          string    `json:"keyword" example:"web development"`
	Success          bool      `json:"success" example:"true"`
	PreviousPosition *int      `json:"previous_position,omitempty" example:"15"`
	CurrentPosition  *int      `json:"current_position,omitempty" example:"12"`
	PositionChange   *int      `json:"position_change,omitempty" example:"-3"`
	URL              string    `json:"url,omitempty" example:"https://example.com/services"`
	SearchEngine     string    `json:"search_engine" example:"google"`
	Location         string    `json:"location" example:"United States"`
	Device           string    `json:"device" example:"desktop"`
	TrackedAt        time.Time `json:"tracked_at"`
	Error            string    `json:"error,omitempty"`
}

// SEOKeywordResearchRequest represents request for keyword research
type SEOKeywordResearchRequest struct {
	SeedKeywords      []string `json:"seed_keywords" validate:"required,min=1" example:"[\"web development\",\"website design\"]"`
	Location          string   `json:"location" validate:"required" example:"United States"`
	Language          string   `json:"language" validate:"required" example:"en"`
	SearchVolume      *int     `json:"min_search_volume" validate:"omitempty,min=0" example:"100"`
	MaxDifficulty     *int     `json:"max_difficulty" validate:"omitempty,min=1,max=100" example:"80"`
	MaxResults        int      `json:"max_results" validate:"omitempty,min=1,max=1000" example:"100"`
	IncludeLongTail   bool     `json:"include_long_tail" example:"true"`
	IncludeQuestions  bool     `json:"include_questions" example:"true"`
	ExcludeCompetitor bool     `json:"exclude_competitor" example:"false"`
	Sources           []string `json:"sources" example:"[\"google_keyword_planner\",\"semrush\",\"ahrefs\"]"`
}

// SEOKeywordResearchResponse represents response for keyword research
type SEOKeywordResearchResponse struct {
	Success           bool                    `json:"success" example:"true"`
	Message           string                  `json:"message" example:"Keyword research completed successfully"`
	ResearchID        string                  `json:"research_id" example:"research_abc123"`
	TotalFound        int                     `json:"total_found" example:"250"`
	Keywords          []KeywordResearchResult `json:"keywords"`
	RelatedKeywords   []string                `json:"related_keywords" example:"[\"responsive design\",\"mobile development\"]"`
	SearchSuggestions []string                `json:"search_suggestions" example:"[\"web development company\",\"web development services\"]"`
	ResearchedAt      time.Time               `json:"researched_at"`
}

// KeywordResearchResult represents individual keyword research result
type KeywordResearchResult struct {
	Keyword      string   `json:"keyword" example:"web development services"`
	SearchVolume int      `json:"search_volume" example:"5400"`
	Difficulty   int      `json:"difficulty" example:"75"`
	CPC          float64  `json:"cpc" example:"2.35"`
	Competition  float64  `json:"competition" example:"0.68"`
	SearchIntent string   `json:"search_intent" example:"commercial"`
	KeywordType  string   `json:"keyword_type" example:"long_tail"`
	Relevance    float64  `json:"relevance" example:"0.85"`
	Opportunity  float64  `json:"opportunity" example:"0.72"`
	Source       string   `json:"source" example:"google_keyword_planner"`
	RelatedTerms []string `json:"related_terms,omitempty" example:"[\"web design\",\"development company\"]"`
}

// SEOKeywordImportRequest represents request for importing keywords
type SEOKeywordImportRequest struct {
	Format            string                `json:"format" validate:"required,oneof=csv json excel semrush ahrefs" example:"csv"`
	Data              string                `json:"data" validate:"required" example:"keyword,volume,difficulty\nweb development,5400,75\nwebsite design,3200,60"`
	OverwriteExisting bool                  `json:"overwrite_existing" example:"false"`
	DefaultSettings   KeywordImportDefaults `json:"default_settings"`
	SkipValidation    bool                  `json:"skip_validation" example:"false"`
	AutoTrack         bool                  `json:"auto_track" example:"true"`
}

// KeywordImportDefaults represents default settings for imported keywords
type KeywordImportDefaults struct {
	KeywordType     string `json:"keyword_type" validate:"required,oneof=primary secondary long_tail branded competitor" example:"secondary"`
	Priority        string `json:"priority" validate:"required,oneof=low medium high critical" example:"medium"`
	Status          string `json:"status" validate:"required,oneof=active inactive paused" example:"active"`
	TrackingEnabled bool   `json:"tracking_enabled" example:"true"`
	Location        string `json:"location" example:"United States"`
	Language        string `json:"language" example:"en"`
	Device          string `json:"device" validate:"required,oneof=desktop mobile tablet" example:"desktop"`
}

// SEOKeywordImportResponse represents response for keyword import
type SEOKeywordImportResponse struct {
	Success          bool                 `json:"success" example:"true"`
	Message          string               `json:"message" example:"Keywords imported successfully"`
	ImportedCount    int                  `json:"imported_count" example:"25"`
	SkippedCount     int                  `json:"skipped_count" example:"2"`
	ErrorCount       int                  `json:"error_count" example:"1"`
	Errors           []string             `json:"errors,omitempty"`
	SkippedItems     []string             `json:"skipped_items,omitempty"`
	ImportedKeywords []SEOKeywordResponse `json:"imported_keywords,omitempty"`
}

// SEOKeywordExportRequest represents request for exporting keywords
type SEOKeywordExportRequest struct {
	Format         string     `json:"format" validate:"required,oneof=csv json excel pdf" example:"csv"`
	KeywordIDs     []uint     `json:"keyword_ids" example:"1,2,3"`
	KeywordType    *string    `json:"keyword_type" validate:"omitempty,oneof=primary secondary long_tail branded competitor" example:"primary"`
	Status         *string    `json:"status" validate:"omitempty,oneof=active inactive paused deleted" example:"active"`
	IncludeHistory bool       `json:"include_history" example:"true"`
	IncludeStats   bool       `json:"include_stats" example:"true"`
	DateRange      *DateRange `json:"date_range"`
}

// DateRange represents a date range filter
type DateRange struct {
	From *time.Time `json:"from" example:"2024-01-01T00:00:00Z"`
	To   *time.Time `json:"to" example:"2024-12-31T23:59:59Z"`
}

// SEOKeywordExportResponse represents response for keyword export
type SEOKeywordExportResponse struct {
	Success     bool      `json:"success" example:"true"`
	Message     string    `json:"message" example:"Keywords exported successfully"`
	Format      string    `json:"format" example:"csv"`
	Data        string    `json:"data,omitempty" example:"keyword,position,volume,difficulty\nweb development,12,5400,75"`
	DownloadURL string    `json:"download_url" example:"https://api.example.com/exports/keywords_123.csv"`
	ExportID    string    `json:"export_id" example:"export_abc123"`
	FileSize    int       `json:"file_size" example:"51200"`
	ExpiresAt   time.Time `json:"expires_at"`
}

// SEOKeywordBulkActionRequest represents request for bulk operations on keywords
type SEOKeywordBulkActionRequest struct {
	KeywordIDs []uint                 `json:"keyword_ids" validate:"required,min=1" example:"1,2,3"`
	Action     string                 `json:"action" validate:"required,oneof=track pause resume delete update_priority update_type" example:"track"`
	Settings   map[string]interface{} `json:"settings,omitempty" example:"{\"priority\":\"high\",\"tracking_enabled\":true}"`
	Reason     string                 `json:"reason,omitempty" example:"Bulk tracking activation for campaign launch"`
}

// SEOKeywordBulkActionResponse represents response for bulk operations
type SEOKeywordBulkActionResponse struct {
	Success      bool                  `json:"success" example:"true"`
	Message      string                `json:"message" example:"Bulk action completed successfully"`
	ProcessedIDs []uint                `json:"processed_ids" example:"1,2,3"`
	FailedIDs    []uint                `json:"failed_ids,omitempty"`
	SkippedIDs   []uint                `json:"skipped_ids,omitempty"`
	Results      []KeywordActionResult `json:"results"`
	TaskID       string                `json:"task_id,omitempty" example:"task_bulk_keyword_abc123"`
}

// KeywordActionResult represents result for individual keyword action
type KeywordActionResult struct {
	KeywordID uint   `json:"keyword_id" example:"1"`
	Keyword   string `json:"keyword" example:"web development"`
	Action    string `json:"action" example:"track"`
	Success   bool   `json:"success" example:"true"`
	Message   string `json:"message" example:"Keyword tracking activated"`
	Error     string `json:"error,omitempty"`
}

// ToServiceModel converts SEOKeywordCreateRequest to service model (when available)
func (r *SEOKeywordCreateRequest) ToServiceModel() interface{} {
	// Note: This would be implemented when the keyword service model is available
	return map[string]interface{}{
		"website_id":       r.WebsiteID,
		"keyword":          r.Keyword,
		"keyword_type":     r.KeywordType,
		"difficulty":       r.Difficulty,
		"search_volume":    r.SearchVolume,
		"cpc":              r.CPC,
		"competition":      r.Competition,
		"current_position": r.CurrentPosition,
		"target_position":  r.TargetPosition,
		"best_position":    r.BestPosition,
		"worst_position":   r.WorstPosition,
		"tracking_url":     r.TrackingURL,
		"tracking_enabled": r.TrackingEnabled,
		"priority":         r.Priority,
		"search_intent":    r.SearchIntent,
		"location":         r.Location,
		"language":         r.Language,
		"device":           r.Device,
		"tags":             r.Tags,
		"notes":            r.Notes,
		"custom_data":      r.CustomData,
		"status":           r.Status,
	}
}

// ToServiceModel converts SEOKeywordUpdateRequest to service model (when available)
func (r *SEOKeywordUpdateRequest) ToServiceModel() interface{} {
	// Note: This would be implemented when the keyword service model is available
	return map[string]interface{}{
		"keyword":          r.Keyword,
		"keyword_type":     r.KeywordType,
		"difficulty":       r.Difficulty,
		"search_volume":    r.SearchVolume,
		"cpc":              r.CPC,
		"competition":      r.Competition,
		"current_position": r.CurrentPosition,
		"target_position":  r.TargetPosition,
		"best_position":    r.BestPosition,
		"worst_position":   r.WorstPosition,
		"tracking_url":     r.TrackingURL,
		"tracking_enabled": r.TrackingEnabled,
		"priority":         r.Priority,
		"search_intent":    r.SearchIntent,
		"location":         r.Location,
		"language":         r.Language,
		"device":           r.Device,
		"tags":             r.Tags,
		"notes":            r.Notes,
		"custom_data":      r.CustomData,
		"status":           r.Status,
	}
}

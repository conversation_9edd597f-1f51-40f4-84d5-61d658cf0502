package services

import (
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
)

// Note: DTO to model conversion functions removed as we now work directly with DTOs
// and create models directly in the service layer

// convertCampaignModelToResponse converts model to DTO response
func convertCampaignModelToResponse(campaign *models.Campaign) *dto.CampaignResponse {
	return &dto.CampaignResponse{
		ID:          campaign.ID,
		TenantID:    campaign.TenantID,
		Name:        campaign.Name,
		Description: campaign.Description,
		Budget:      campaign.Budget,
		Status:      campaign.Status,
		StartDate:   campaign.StartDate,
		EndDate:     campaign.EndDate,
		CreatedAt:   campaign.CreatedAt,
		UpdatedAt:   campaign.UpdatedAt,
		IsActive:    campaign.IsActive(),
		IsRunning:   campaign.IsRunning(),
	}
}

// convertCampaignModelsToResponses converts slice of models to slice of DTO responses
func convertCampaignModelsToResponses(campaigns []*models.Campaign) []dto.CampaignResponse {
	responses := make([]dto.CampaignResponse, len(campaigns))
	for i, campaign := range campaigns {
		responses[i] = *convertCampaignModelToResponse(campaign)
	}
	return responses
}

// convertCampaignListFilterToServiceFilter converts DTO filter to service filter
func convertCampaignListFilterToServiceFilter(filter *dto.CampaignListFilter) *CampaignFilters {
	if filter == nil {
		return &CampaignFilters{}
	}

	serviceFilter := &CampaignFilters{}

	if filter.Status != nil {
		status := string(*filter.Status)
		serviceFilter.Status = &status
	}
	if filter.StartDate != nil {
		serviceFilter.StartDate = filter.StartDate
	}
	if filter.EndDate != nil {
		serviceFilter.EndDate = filter.EndDate
	}

	// Set pagination defaults
	serviceFilter.Limit = 20
	serviceFilter.Offset = 0

	if filter.Pagination != nil {
		if filter.Pagination.Limit > 0 {
			serviceFilter.Limit = filter.Pagination.Limit
		}
		// For cursor pagination, we can parse the cursor to get offset-like behavior
		// For now, we'll keep it simple and use limit only
	}

	return serviceFilter
}

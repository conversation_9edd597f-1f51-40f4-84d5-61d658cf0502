package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// AdvertisementCreateRequest represents the request to create an advertisement
// @Description Request structure for creating a new advertisement
type AdvertisementCreateRequest struct {
	TenantID        uint                   `json:"tenant_id" validate:"required" example:"1"`
	CampaignID      uint                   `json:"campaign_id" validate:"required" example:"1"`
	Title           string                 `json:"title" validate:"required,min=1,max=255" example:"Summer Sale Banner"`
	Description     string                 `json:"description,omitempty" validate:"max=1000" example:"Exclusive summer discounts up to 50% off"`
	ImageURL        string                 `json:"image_url,omitempty" validate:"omitempty,url" example:"https://example.com/banner.jpg"`
	LinkURL         string                 `json:"link_url" validate:"required,url" example:"https://example.com/summer-sale"`
	AdType          models.AdType          `json:"ad_type,omitempty" validate:"omitempty,oneof=banner text rich_media native video" example:"banner"`
	DeviceTargeting models.DeviceTargeting `json:"device_targeting,omitempty" validate:"omitempty,oneof=web_pc web_mobile both" example:"both"`
	PageTargeting   []string               `json:"page_targeting,omitempty" example:"homepage,category"`
	Position        models.AdPosition      `json:"position,omitempty" validate:"omitempty,oneof=header sidebar footer inline popup" example:"sidebar"`
	Priority        int                    `json:"priority,omitempty" validate:"omitempty,min=1,max=10" example:"5"`
	MediaFileIDs    []uint                 `json:"media_file_ids,omitempty" example:"1,2,3"`
}

// AdvertisementUpdateRequest represents the request to update an advertisement
// @Description Request structure for updating an existing advertisement
type AdvertisementUpdateRequest struct {
	Title           *string                     `json:"title,omitempty" validate:"omitempty,min=1,max=255" example:"Updated Summer Sale Banner"`
	Description     *string                     `json:"description,omitempty" validate:"omitempty,max=1000" example:"Updated summer discounts up to 60% off"`
	ImageURL        *string                     `json:"image_url,omitempty" validate:"omitempty,url" example:"https://example.com/updated-banner.jpg"`
	LinkURL         *string                     `json:"link_url,omitempty" validate:"omitempty,url" example:"https://example.com/updated-summer-sale"`
	AdType          *models.AdType              `json:"ad_type,omitempty" validate:"omitempty,oneof=banner text rich_media native video" example:"banner"`
	DeviceTargeting *models.DeviceTargeting     `json:"device_targeting,omitempty" validate:"omitempty,oneof=web_pc web_mobile both" example:"both"`
	PageTargeting   []string                    `json:"page_targeting,omitempty" example:"homepage,category,article"`
	Position        *models.AdPosition          `json:"position,omitempty" validate:"omitempty,oneof=header sidebar footer inline popup" example:"header"`
	Priority        *int                        `json:"priority,omitempty" validate:"omitempty,min=1,max=10" example:"7"`
	Status          *models.AdvertisementStatus `json:"status,omitempty" validate:"omitempty,oneof=draft active paused expired deleted" example:"active"`
	MediaFileIDs    []uint                      `json:"media_file_ids,omitempty" example:"4,5,6"`
}

// AdvertisementResponse represents the response structure for advertisement
// @Description Response structure for advertisement data
type AdvertisementResponse struct {
	ID              uint                       `json:"id" example:"1"`
	TenantID        uint                       `json:"tenant_id" example:"1"`
	CampaignID      uint                       `json:"campaign_id" example:"1"`
	Title           string                     `json:"title" example:"Summer Sale Banner"`
	Description     string                     `json:"description,omitempty" example:"Exclusive summer discounts up to 50% off"`
	ImageURL        string                     `json:"image_url,omitempty" example:"https://example.com/banner.jpg"`
	LinkURL         string                     `json:"link_url" example:"https://example.com/summer-sale"`
	AdType          models.AdType              `json:"ad_type" example:"banner"`
	DeviceTargeting models.DeviceTargeting     `json:"device_targeting" example:"both"`
	PageTargeting   []string                   `json:"page_targeting,omitempty" example:"homepage,category"`
	Position        models.AdPosition          `json:"position" example:"sidebar"`
	Priority        int                        `json:"priority" example:"5"`
	Status          models.AdvertisementStatus `json:"status" example:"active"`
	CreatedAt       time.Time                  `json:"created_at" example:"2023-01-01T00:00:00Z"`
	UpdatedAt       time.Time                  `json:"updated_at" example:"2023-01-01T00:00:00Z"`
	IsActive        bool                       `json:"is_active" example:"true"`
	MediaFileIDs    []uint                     `json:"media_file_ids,omitempty" example:"1,2,3"`
}

// AdvertisementListFilter represents filter options for listing advertisements
// @Description Filter options for listing advertisements
type AdvertisementListFilter struct {
	TenantID        uint                         `json:"tenant_id" validate:"required" example:"1"`
	CampaignID      *uint                        `json:"campaign_id,omitempty" example:"1"`
	Status          *models.AdvertisementStatus  `json:"status,omitempty" validate:"omitempty,oneof=draft active paused expired deleted" example:"active"`
	AdType          *models.AdType               `json:"ad_type,omitempty" validate:"omitempty,oneof=banner text rich_media native video" example:"banner"`
	DeviceTargeting *models.DeviceTargeting      `json:"device_targeting,omitempty" validate:"omitempty,oneof=web_pc web_mobile both" example:"both"`
	Position        *models.AdPosition           `json:"position,omitempty" validate:"omitempty,oneof=header sidebar footer inline popup" example:"sidebar"`
	Priority        *int                         `json:"priority,omitempty" validate:"omitempty,min=1,max=10" example:"5"`
	Search          string                       `json:"search,omitempty" example:"summer sale"`
	Pagination      *pagination.CursorPagination `json:"pagination,omitempty"`
}

// AdvertisementListResponse represents the response for listing advertisements
// @Description Response structure for advertisement listing
type AdvertisementListResponse struct {
	Advertisements []AdvertisementResponse    `json:"advertisements"`
	Pagination     *pagination.CursorResponse `json:"pagination,omitempty"`
}

// AdServingRequest represents a request to serve advertisements
// @Description Request structure for serving advertisements to visitors
type AdServingRequest struct {
	TenantID   uint              `json:"tenant_id" validate:"required" example:"1"`
	PageType   string            `json:"page_type" validate:"required,oneof=homepage category article tag custom" example:"homepage"`
	Position   models.AdPosition `json:"position" validate:"required,oneof=header sidebar footer inline popup" example:"sidebar"`
	DeviceType string            `json:"device_type,omitempty" validate:"omitempty,oneof=web_pc web_mobile" example:"web_pc"`
	PageURL    string            `json:"page_url,omitempty" validate:"omitempty,url" example:"https://example.com/homepage"`
	Limit      int               `json:"limit,omitempty" validate:"omitempty,min=1,max=10" example:"3"`
}

// AdServingResponse represents the response for ad serving
// @Description Response structure for served advertisements
type AdServingResponse struct {
	ID          uint              `json:"id" example:"1"`
	Title       string            `json:"title" example:"Summer Sale Banner"`
	Description string            `json:"description,omitempty" example:"Exclusive summer discounts up to 50% off"`
	ImageURL    string            `json:"image_url,omitempty" example:"https://example.com/banner.jpg"`
	LinkURL     string            `json:"link_url" example:"https://example.com/summer-sale"`
	AdType      models.AdType     `json:"ad_type" example:"banner"`
	Position    models.AdPosition `json:"position" example:"sidebar"`
	TrackingID  string            `json:"tracking_id" example:"ad_123_impression_456"`
}

// AdvertisementStatsRequest represents a request for advertisement statistics
// @Description Request structure for retrieving advertisement statistics
type AdvertisementStatsRequest struct {
	TenantID        uint       `json:"tenant_id" validate:"required" example:"1"`
	AdvertisementID *uint      `json:"advertisement_id,omitempty" example:"1"`
	CampaignID      *uint      `json:"campaign_id,omitempty" example:"1"`
	StartDate       *time.Time `json:"start_date,omitempty" example:"2023-01-01T00:00:00Z"`
	EndDate         *time.Time `json:"end_date,omitempty" example:"2023-12-31T23:59:59Z"`
	Granularity     string     `json:"granularity,omitempty" validate:"omitempty,oneof=hour day week month" example:"day"`
}

// AdvertisementStatsResponse represents the response for advertisement statistics
// @Description Response structure for advertisement statistics
type AdvertisementStatsResponse struct {
	AdvertisementID uint    `json:"advertisement_id" example:"1"`
	Impressions     int64   `json:"impressions" example:"10000"`
	Clicks          int64   `json:"clicks" example:"250"`
	CTR             float64 `json:"ctr" example:"2.5"`
	Revenue         float64 `json:"revenue" example:"125.50"`
	CPC             float64 `json:"cpc" example:"0.50"`
	CPM             float64 `json:"cpm" example:"12.55"`
}

// AdvertisementBulkUpdateRequest represents a request for bulk updating advertisements
// @Description Request structure for bulk updating multiple advertisements
type AdvertisementBulkUpdateRequest struct {
	TenantID         uint                        `json:"tenant_id" validate:"required" example:"1"`
	AdvertisementIDs []uint                      `json:"advertisement_ids" validate:"required,min=1" example:"1,2,3"`
	Status           *models.AdvertisementStatus `json:"status,omitempty" validate:"omitempty,oneof=draft active paused expired deleted" example:"paused"`
	Priority         *int                        `json:"priority,omitempty" validate:"omitempty,min=1,max=10" example:"3"`
	DeviceTargeting  *models.DeviceTargeting     `json:"device_targeting,omitempty" validate:"omitempty,oneof=web_pc web_mobile both" example:"web_mobile"`
}

// AdvertisementBulkUpdateResponse represents the response for bulk updating advertisements
// @Description Response structure for bulk advertisement updates
type AdvertisementBulkUpdateResponse struct {
	UpdatedCount int    `json:"updated_count" example:"3"`
	FailedCount  int    `json:"failed_count" example:"0"`
	FailedIDs    []uint `json:"failed_ids,omitempty" example:""`
	Message      string `json:"message" example:"Successfully updated 3 advertisements"`
}

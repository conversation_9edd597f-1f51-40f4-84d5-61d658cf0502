package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// ScheduleCreateRequest represents the request to create a schedule
// @Description Request structure for creating a new advertisement schedule
type ScheduleCreateRequest struct {
	TenantID         uint                     `json:"tenant_id" validate:"required" example:"1"`
	AdvertisementID  uint                     `json:"advertisement_id" validate:"required" example:"1"`
	StartTime        time.Time                `json:"start_time" validate:"required" example:"2023-07-01T09:00:00Z"`
	EndTime          time.Time                `json:"end_time" validate:"required" example:"2023-07-31T18:00:00Z"`
	Timezone         string                   `json:"timezone,omitempty" validate:"omitempty,max=50" example:"America/New_York"`
	RecurringPattern *models.RecurringPattern `json:"recurring_pattern,omitempty"`
}

// ScheduleUpdateRequest represents the request to update a schedule
// @Description Request structure for updating an existing schedule
type ScheduleUpdateRequest struct {
	StartTime        *time.Time               `json:"start_time,omitempty" example:"2023-07-01T10:00:00Z"`
	EndTime          *time.Time               `json:"end_time,omitempty" example:"2023-07-31T19:00:00Z"`
	Timezone         *string                  `json:"timezone,omitempty" validate:"omitempty,max=50" example:"Europe/London"`
	RecurringPattern *models.RecurringPattern `json:"recurring_pattern,omitempty"`
	Status           *string                  `json:"status,omitempty" validate:"omitempty,oneof=active paused expired deleted" example:"paused"`
}

// ScheduleResponse represents the response structure for schedule
// @Description Response structure for schedule data
type ScheduleResponse struct {
	ID                uint                     `json:"id" example:"1"`
	TenantID          uint                     `json:"tenant_id" example:"1"`
	AdvertisementID   uint                     `json:"advertisement_id" example:"1"`
	StartTime         time.Time                `json:"start_time" example:"2023-07-01T09:00:00Z"`
	EndTime           time.Time                `json:"end_time" example:"2023-07-31T18:00:00Z"`
	Timezone          string                   `json:"timezone" example:"America/New_York"`
	RecurringPattern  *models.RecurringPattern `json:"recurring_pattern,omitempty"`
	Status            string                   `json:"status" example:"active"`
	CreatedAt         time.Time                `json:"created_at" example:"2023-01-01T00:00:00Z"`
	UpdatedAt         time.Time                `json:"updated_at" example:"2023-01-01T00:00:00Z"`
	IsActive          bool                     `json:"is_active" example:"true"`
	IsCurrentlyActive bool                     `json:"is_currently_active" example:"true"`
}

// ScheduleListFilter represents filter options for listing schedules
// @Description Filter options for listing schedules
type ScheduleListFilter struct {
	TenantID        uint                         `json:"tenant_id" validate:"required" example:"1"`
	AdvertisementID *uint                        `json:"advertisement_id,omitempty" example:"1"`
	CampaignID      *uint                        `json:"campaign_id,omitempty" example:"1"`
	Status          *string                      `json:"status,omitempty" validate:"omitempty,oneof=active paused expired deleted" example:"active"`
	Timezone        *string                      `json:"timezone,omitempty" example:"America/New_York"`
	StartDate       *time.Time                   `json:"start_date,omitempty" example:"2023-07-01T00:00:00Z"`
	EndDate         *time.Time                   `json:"end_date,omitempty" example:"2023-07-31T23:59:59Z"`
	IsActive        *bool                        `json:"is_active,omitempty" example:"true"`
	Search          string                       `json:"search,omitempty" example:"summer campaign"`
	Pagination      *pagination.CursorPagination `json:"pagination,omitempty"`
}

// ScheduleListResponse represents the response for listing schedules
// @Description Response structure for schedule listing
type ScheduleListResponse struct {
	Schedules  []ScheduleResponse         `json:"schedules"`
	Pagination *pagination.CursorResponse `json:"pagination,omitempty"`
}

// ScheduleStatusRequest represents a request to check schedule status
// @Description Request structure for checking schedule status
type ScheduleStatusRequest struct {
	TenantID   uint      `json:"tenant_id" validate:"required" example:"1"`
	ScheduleID uint      `json:"schedule_id" validate:"required" example:"1"`
	CheckTime  time.Time `json:"check_time,omitempty" example:"2023-07-15T14:30:00Z"`
}

// ScheduleStatusResponse represents the response for schedule status
// @Description Response structure for schedule status
type ScheduleStatusResponse struct {
	ScheduleID        uint       `json:"schedule_id" example:"1"`
	CurrentTime       time.Time  `json:"current_time" example:"2023-07-15T14:30:00Z"`
	IsActive          bool       `json:"is_active" example:"true"`
	IsCurrentlyActive bool       `json:"is_currently_active" example:"true"`
	NextScheduledTime *time.Time `json:"next_scheduled_time,omitempty" example:"2023-07-16T09:00:00Z"`
	TimeUntilNext     *string    `json:"time_until_next,omitempty" example:"18h30m"`
	Status            string     `json:"status" example:"active"`
	StatusReason      string     `json:"status_reason,omitempty" example:"Schedule is within active time range"`
}

// ScheduleActiveRequest represents a request to get active schedules
// @Description Request structure for retrieving active schedules
type ScheduleActiveRequest struct {
	TenantID        uint       `json:"tenant_id" validate:"required" example:"1"`
	AdvertisementID *uint      `json:"advertisement_id,omitempty" example:"1"`
	CampaignID      *uint      `json:"campaign_id,omitempty" example:"1"`
	CheckTime       *time.Time `json:"check_time,omitempty" example:"2023-07-15T14:30:00Z"`
	Timezone        *string    `json:"timezone,omitempty" example:"America/New_York"`
}

// ScheduleActiveResponse represents the response for active schedules
// @Description Response structure for active schedules
type ScheduleActiveResponse struct {
	CurrentTime  time.Time          `json:"current_time" example:"2023-07-15T14:30:00Z"`
	Timezone     string             `json:"timezone" example:"America/New_York"`
	Schedules    []ScheduleResponse `json:"schedules"`
	TotalActive  int                `json:"total_active" example:"5"`
	TotalMatched int                `json:"total_matched" example:"3"`
}

// ScheduleValidationRequest represents a request to validate schedule
// @Description Request structure for validating schedule configuration
type ScheduleValidationRequest struct {
	TenantID         uint                     `json:"tenant_id" validate:"required" example:"1"`
	AdvertisementID  uint                     `json:"advertisement_id" validate:"required" example:"1"`
	StartTime        time.Time                `json:"start_time" validate:"required" example:"2023-07-01T09:00:00Z"`
	EndTime          time.Time                `json:"end_time" validate:"required" example:"2023-07-31T18:00:00Z"`
	Timezone         string                   `json:"timezone,omitempty" example:"America/New_York"`
	RecurringPattern *models.RecurringPattern `json:"recurring_pattern,omitempty"`
}

// ScheduleValidationIssue represents a validation issue
// @Description Schedule validation issue
type ScheduleValidationIssue struct {
	Type       string `json:"type" example:"time_conflict"`
	Severity   string `json:"severity" example:"error"`
	Field      string `json:"field" example:"end_time"`
	Message    string `json:"message" example:"End time must be after start time"`
	Suggestion string `json:"suggestion,omitempty" example:"Set end time to be at least 1 hour after start time"`
}

// ScheduleValidationResponse represents the response for schedule validation
// @Description Response structure for schedule validation
type ScheduleValidationResponse struct {
	IsValid              bool                      `json:"is_valid" example:"false"`
	Issues               []ScheduleValidationIssue `json:"issues,omitempty"`
	Warnings             []ScheduleValidationIssue `json:"warnings,omitempty"`
	ConflictingSchedules []uint                    `json:"conflicting_schedules,omitempty" example:"2,3"`
	Recommendations      []string                  `json:"recommendations,omitempty" example:"Consider adjusting time range to avoid conflicts"`
}

// ScheduleBulkUpdateRequest represents a request for bulk updating schedules
// @Description Request structure for bulk updating multiple schedules
type ScheduleBulkUpdateRequest struct {
	TenantID    uint    `json:"tenant_id" validate:"required" example:"1"`
	ScheduleIDs []uint  `json:"schedule_ids" validate:"required,min=1" example:"1,2,3"`
	Status      *string `json:"status,omitempty" validate:"omitempty,oneof=active paused expired deleted" example:"paused"`
	Timezone    *string `json:"timezone,omitempty" validate:"omitempty,max=50" example:"UTC"`
}

// ScheduleBulkUpdateResponse represents the response for bulk updating schedules
// @Description Response structure for bulk schedule updates
type ScheduleBulkUpdateResponse struct {
	UpdatedCount int    `json:"updated_count" example:"3"`
	FailedCount  int    `json:"failed_count" example:"0"`
	FailedIDs    []uint `json:"failed_ids,omitempty" example:""`
	Message      string `json:"message" example:"Successfully updated 3 schedules"`
}

// ScheduleAnalyticsRequest represents a request for schedule analytics
// @Description Request structure for schedule performance analytics
type ScheduleAnalyticsRequest struct {
	TenantID        uint      `json:"tenant_id" validate:"required" example:"1"`
	ScheduleID      *uint     `json:"schedule_id,omitempty" example:"1"`
	AdvertisementID *uint     `json:"advertisement_id,omitempty" example:"1"`
	CampaignID      *uint     `json:"campaign_id,omitempty" example:"1"`
	StartDate       time.Time `json:"start_date" validate:"required" example:"2023-07-01T00:00:00Z"`
	EndDate         time.Time `json:"end_date" validate:"required" example:"2023-07-31T23:59:59Z"`
	Granularity     string    `json:"granularity,omitempty" validate:"omitempty,oneof=hour day week month" example:"day"`
	Timezone        string    `json:"timezone,omitempty" example:"America/New_York"`
}

// SchedulePerformanceMetrics represents performance metrics for a schedule
// @Description Performance metrics for schedule analysis
type SchedulePerformanceMetrics struct {
	ScheduleID      uint    `json:"schedule_id" example:"1"`
	ActiveHours     float64 `json:"active_hours" example:"720.5"`
	PlannedHours    float64 `json:"planned_hours" example:"744.0"`
	UtilizationRate float64 `json:"utilization_rate" example:"0.968"`
	Impressions     int64   `json:"impressions" example:"15000"`
	Clicks          int64   `json:"clicks" example:"375"`
	CTR             float64 `json:"ctr" example:"0.025"`
	Revenue         float64 `json:"revenue" example:"187.50"`
	CostEfficiency  float64 `json:"cost_efficiency" example:"0.50"`
	PeakHours       []struct {
		Hour        int     `json:"hour" example:"14"`
		Impressions int64   `json:"impressions" example:"1250"`
		Performance float64 `json:"performance" example:"8.5"`
	} `json:"peak_hours,omitempty"`
}

// ScheduleAnalyticsResponse represents the response for schedule analytics
// @Description Response structure for schedule analytics
type ScheduleAnalyticsResponse struct {
	AnalysisPeriod struct {
		StartDate time.Time `json:"start_date" example:"2023-07-01T00:00:00Z"`
		EndDate   time.Time `json:"end_date" example:"2023-07-31T23:59:59Z"`
		Timezone  string    `json:"timezone" example:"America/New_York"`
	} `json:"analysis_period"`
	Summary struct {
		TotalSchedules     int     `json:"total_schedules" example:"5"`
		ActiveSchedules    int     `json:"active_schedules" example:"4"`
		AverageUtilization float64 `json:"average_utilization" example:"0.85"`
		TotalActiveHours   float64 `json:"total_active_hours" example:"3602.5"`
	} `json:"summary"`
	ScheduleMetrics []SchedulePerformanceMetrics `json:"schedule_metrics"`
	Insights        []string                     `json:"insights,omitempty" example:"Peak performance occurs between 2-4 PM"`
	Recommendations []string                     `json:"recommendations,omitempty" example:"Consider extending schedule to capture evening traffic"`
}

// ScheduleConflictRequest represents a request to check schedule conflicts
// @Description Request structure for checking schedule conflicts
type ScheduleConflictRequest struct {
	TenantID         uint                     `json:"tenant_id" validate:"required" example:"1"`
	AdvertisementID  uint                     `json:"advertisement_id" validate:"required" example:"1"`
	StartTime        time.Time                `json:"start_time" validate:"required" example:"2023-07-01T09:00:00Z"`
	EndTime          time.Time                `json:"end_time" validate:"required" example:"2023-07-31T18:00:00Z"`
	Timezone         string                   `json:"timezone,omitempty" example:"America/New_York"`
	RecurringPattern *models.RecurringPattern `json:"recurring_pattern,omitempty"`
	ExcludeSchedule  *uint                    `json:"exclude_schedule,omitempty" example:"2"`
}

// ScheduleConflict represents a schedule conflict
// @Description Schedule conflict information
type ScheduleConflict struct {
	ConflictingScheduleID uint      `json:"conflicting_schedule_id" example:"2"`
	ConflictType          string    `json:"conflict_type" example:"overlap"`
	ConflictStart         time.Time `json:"conflict_start" example:"2023-07-15T14:00:00Z"`
	ConflictEnd           time.Time `json:"conflict_end" example:"2023-07-15T16:00:00Z"`
	Severity              string    `json:"severity" example:"high"`
	Description           string    `json:"description" example:"Schedules overlap for 2 hours"`
	Resolution            string    `json:"resolution,omitempty" example:"Adjust start/end times to avoid overlap"`
}

// ScheduleConflictResponse represents the response for schedule conflict check
// @Description Response structure for schedule conflict check
type ScheduleConflictResponse struct {
	HasConflicts     bool               `json:"has_conflicts" example:"true"`
	ConflictCount    int                `json:"conflict_count" example:"2"`
	Conflicts        []ScheduleConflict `json:"conflicts,omitempty"`
	Suggestions      []string           `json:"suggestions,omitempty" example:"Adjust start time by 2 hours"`
	AlternativeSlots []struct {
		StartTime time.Time `json:"start_time" example:"2023-07-15T11:00:00Z"`
		EndTime   time.Time `json:"end_time" example:"2023-07-15T13:00:00Z"`
		Score     float64   `json:"score" example:"8.5"`
	} `json:"alternative_slots,omitempty"`
}

package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// CreateChatSessionRequest represents the request to create a chat session
type CreateChatSessionRequest struct {
	TenantID     uint           `json:"tenant_id" validate:"required,min=1" example:"1"`
	WebsiteID    *uint          `json:"website_id,omitempty" validate:"omitempty,min=1" example:"1"`
	UserID       *uint          `json:"user_id,omitempty" validate:"omitempty,min=1" example:"1"`
	Title        string         `json:"title" validate:"required,min=1,max=255" example:"Chat about AI development"`
	ModelID      uint           `json:"model_id" validate:"required,min=1" example:"1"`
	SystemPrompt *string        `json:"system_prompt,omitempty" validate:"omitempty,max=10000" example:"You are a helpful AI assistant."`
	Context      models.JSONMap `json:"context,omitempty" example:"{}"`
}

// UpdateChatSessionRequest represents the request to update a chat session
type UpdateChatSessionRequest struct {
	Title        *string                   `json:"title,omitempty" validate:"omitempty,min=1,max=255" example:"Updated chat title"`
	SystemPrompt *string                   `json:"system_prompt,omitempty" validate:"omitempty,max=10000" example:"You are an expert developer."`
	Status       *models.ChatSessionStatus `json:"status,omitempty" validate:"omitempty,oneof=active archived" example:"active"`
	Context      models.JSONMap            `json:"context,omitempty" example:"{}"`
}

// ChatSessionResponse represents the chat session response
type ChatSessionResponse struct {
	ID           uint                     `json:"id" example:"1"`
	TenantID     uint                     `json:"tenant_id" example:"1"`
	WebsiteID    *uint                    `json:"website_id,omitempty" example:"1"`
	UserID       *uint                    `json:"user_id,omitempty" example:"1"`
	Title        string                   `json:"title" example:"Chat about AI development"`
	ModelID      uint                     `json:"model_id" example:"1"`
	Context      models.JSONMap           `json:"context,omitempty" example:"{}"`
	SystemPrompt *string                  `json:"system_prompt,omitempty" example:"You are a helpful AI assistant."`
	Status       models.ChatSessionStatus `json:"status" example:"active"`
	MessageCount int                      `json:"message_count" example:"5"`
	CreatedAt    time.Time                `json:"created_at" example:"2024-01-15T10:30:00Z"`
	UpdatedAt    time.Time                `json:"updated_at" example:"2024-01-15T14:30:00Z"`

	// Relationships
	Tenant  *TenantResponse  `json:"tenant,omitempty"`
	Website *WebsiteResponse `json:"website,omitempty"`
	User    *UserResponse    `json:"user,omitempty"`
	Model   *AIModelResponse `json:"model,omitempty"`
}

// FromChatSession converts a chat session model to response DTO
func (r *ChatSessionResponse) FromChatSession(session *models.AIChatSession) {
	r.ID = session.ID
	r.TenantID = session.TenantID
	r.WebsiteID = session.WebsiteID
	r.UserID = session.UserID
	r.Title = session.Title
	r.ModelID = session.ModelID
	r.Context = session.Context
	r.SystemPrompt = session.SystemPrompt
	r.Status = session.Status
	r.MessageCount = session.MessageCount
	r.CreatedAt = session.CreatedAt
	r.UpdatedAt = session.UpdatedAt
}

// ChatSessionListResponse represents the response for listing chat sessions
type ChatSessionListResponse struct {
	Sessions   []ChatSessionResponse      `json:"sessions"`
	Pagination *pagination.CursorResponse `json:"pagination"`
	TotalCount int64                      `json:"total_count" example:"50"`
}

// ChatSessionFilter represents filters for querying chat sessions
type ChatSessionFilter struct {
	TenantID       *uint                        `json:"tenant_id,omitempty" validate:"omitempty,min=1" example:"1"`
	WebsiteID      *uint                        `json:"website_id,omitempty" validate:"omitempty,min=1" example:"1"`
	UserID         *uint                        `json:"user_id,omitempty" validate:"omitempty,min=1" example:"1"`
	Status         *models.ChatSessionStatus    `json:"status,omitempty" validate:"omitempty,oneof=active archived deleted" example:"active"`
	ModelID        *uint                        `json:"model_id,omitempty" validate:"omitempty,min=1" example:"1"`
	SearchQuery    string                       `json:"search_query,omitempty" validate:"omitempty,max=500" example:"AI development"`
	IncludeDeleted bool                         `json:"include_deleted,omitempty" example:"false"`
	CreatedAfter   *time.Time                   `json:"created_after,omitempty" example:"2024-01-01T00:00:00Z"`
	CreatedBefore  *time.Time                   `json:"created_before,omitempty" example:"2024-01-31T23:59:59Z"`
	MinMessages    *int                         `json:"min_messages,omitempty" validate:"omitempty,min=0" example:"1"`
	MaxMessages    *int                         `json:"max_messages,omitempty" validate:"omitempty,min=0" example:"100"`
	Pagination     *pagination.CursorPagination `json:"pagination,omitempty"`
}

// ChatSessionStatsResponse represents chat session statistics
type ChatSessionStatsResponse struct {
	TotalSessions    int64              `json:"total_sessions" example:"100"`
	ActiveSessions   int64              `json:"active_sessions" example:"75"`
	ArchivedSessions int64              `json:"archived_sessions" example:"20"`
	DeletedSessions  int64              `json:"deleted_sessions" example:"5"`
	TotalMessages    int64              `json:"total_messages" example:"2500"`
	AverageMessages  float64            `json:"average_messages" example:"25.0"`
	SessionsByModel  map[string]int64   `json:"sessions_by_model"`
	SessionsByDay    map[string]int64   `json:"sessions_by_day"`
	SessionsByUser   []UserSessionStats `json:"sessions_by_user"`
	MostActiveUsers  []UserSessionStats `json:"most_active_users"`
}

// UserSessionStats represents session statistics for a user
type UserSessionStats struct {
	UserID          uint       `json:"user_id" example:"1"`
	Username        string     `json:"username" example:"johndoe"`
	Email           string     `json:"email" example:"<EMAIL>"`
	SessionCount    int64      `json:"session_count" example:"10"`
	MessageCount    int64      `json:"message_count" example:"250"`
	AverageMessages float64    `json:"average_messages" example:"25.0"`
	LastSessionAt   *time.Time `json:"last_session_at,omitempty" example:"2024-01-15T14:30:00Z"`
}

// ArchiveSessionRequest represents the request to archive a session
type ArchiveSessionRequest struct {
	SessionID uint `json:"session_id" validate:"required,min=1" example:"1"`
}

// DeleteSessionRequest represents the request to delete a session
type DeleteSessionRequest struct {
	SessionID uint `json:"session_id" validate:"required,min=1" example:"1"`
	Hard      bool `json:"hard,omitempty" example:"false"`
}

// RestoreSessionRequest represents the request to restore a deleted session
type RestoreSessionRequest struct {
	SessionID uint `json:"session_id" validate:"required,min=1" example:"1"`
}

// BulkSessionActionRequest represents the request for bulk session actions
type BulkSessionActionRequest struct {
	SessionIDs []uint `json:"session_ids" validate:"required,min=1,max=100" example:"[1,2,3]"`
	Action     string `json:"action" validate:"required,oneof=archive delete restore" example:"archive"`
}

// BulkSessionActionResponse represents the response for bulk session actions
type BulkSessionActionResponse struct {
	Successful []uint   `json:"successful" example:"[1,2]"`
	Failed     []uint   `json:"failed" example:"[3]"`
	Errors     []string `json:"errors,omitempty" example:"['Session 3 not found']"`
}

// ExportSessionRequest represents the request to export a session
type ExportSessionRequest struct {
	SessionID       uint   `json:"session_id" validate:"required,min=1" example:"1"`
	Format          string `json:"format" validate:"required,oneof=json csv markdown pdf" example:"json"`
	IncludeMetadata bool   `json:"include_metadata,omitempty" example:"true"`
}

// ChatSessionSummaryResponse represents a summary of a chat session
type ChatSessionSummaryResponse struct {
	ID            uint                     `json:"id" example:"1"`
	Title         string                   `json:"title" example:"Chat about AI development"`
	Status        models.ChatSessionStatus `json:"status" example:"active"`
	MessageCount  int                      `json:"message_count" example:"5"`
	LastMessageAt *time.Time               `json:"last_message_at,omitempty" example:"2024-01-15T14:30:00Z"`
	CreatedAt     time.Time                `json:"created_at" example:"2024-01-15T10:30:00Z"`
	UpdatedAt     time.Time                `json:"updated_at" example:"2024-01-15T14:30:00Z"`
}

// RecentSessionsResponse represents recent sessions for a user
type RecentSessionsResponse struct {
	Sessions []ChatSessionSummaryResponse `json:"sessions"`
	Count    int                          `json:"count" example:"5"`
}

package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/models"
)

// AnalyticsTimeRange represents different time ranges for analytics
type AnalyticsTimeRange string

const (
	TimeRangeLastHour   AnalyticsTimeRange = "last_hour"
	TimeRangeToday      AnalyticsTimeRange = "today"
	TimeRangeYesterday  AnalyticsTimeRange = "yesterday"
	TimeRangeLast7Days  AnalyticsTimeRange = "last_7_days"
	TimeRangeLast30Days AnalyticsTimeRange = "last_30_days"
	TimeRangeThisMonth  AnalyticsTimeRange = "this_month"
	TimeRangeLastMonth  AnalyticsTimeRange = "last_month"
	TimeRangeCustom     AnalyticsTimeRange = "custom"
)

// AnalyticsRequest represents the request for analytics data
type AnalyticsRequest struct {
	TenantID    uint               `json:"tenant_id" validate:"required,min=1" example:"1"`
	WebsiteID   *uint              `json:"website_id,omitempty" validate:"omitempty,min=1" example:"1"`
	UserID      *uint              `json:"user_id,omitempty" validate:"omitempty,min=1" example:"1"`
	TimeRange   AnalyticsTimeRange `json:"time_range" validate:"required,oneof=last_hour today yesterday last_7_days last_30_days this_month last_month custom" example:"last_7_days"`
	StartDate   *time.Time         `json:"start_date,omitempty" example:"2024-01-01T00:00:00Z"`
	EndDate     *time.Time         `json:"end_date,omitempty" example:"2024-01-31T23:59:59Z"`
	Granularity string             `json:"granularity,omitempty" validate:"omitempty,oneof=hour day week month" example:"day"`
	Metrics     []string           `json:"metrics,omitempty" validate:"omitempty,dive,oneof=requests tokens cost sessions messages" example:"['requests','tokens','cost']"`
}

// OverallAnalyticsResponse represents comprehensive analytics data
type OverallAnalyticsResponse struct {
	TimeRange AnalyticsTimeRange `json:"time_range" example:"last_7_days"`
	StartDate time.Time          `json:"start_date" example:"2024-01-09T00:00:00Z"`
	EndDate   time.Time          `json:"end_date" example:"2024-01-15T23:59:59Z"`

	// Request Analytics
	RequestAnalytics RequestAnalyticsData `json:"request_analytics"`

	// Session Analytics
	SessionAnalytics SessionAnalyticsData `json:"session_analytics"`

	// Message Analytics
	MessageAnalytics MessageAnalyticsData `json:"message_analytics"`

	// Usage Analytics
	UsageAnalytics UsageAnalyticsData `json:"usage_analytics"`

	// Performance Analytics
	PerformanceAnalytics PerformanceAnalyticsData `json:"performance_analytics"`

	// Time Series Data
	TimeSeries TimeSeriesData `json:"time_series"`
}

// RequestAnalyticsData represents AI request analytics
type RequestAnalyticsData struct {
	TotalRequests     int64            `json:"total_requests" example:"500"`
	CompletedRequests int64            `json:"completed_requests" example:"450"`
	FailedRequests    int64            `json:"failed_requests" example:"30"`
	TimeoutRequests   int64            `json:"timeout_requests" example:"20"`
	SuccessRate       float64          `json:"success_rate" example:"90.0"`
	RequestsByType    map[string]int64 `json:"requests_by_type"`
	RequestsByStatus  map[string]int64 `json:"requests_by_status"`
	TopRequestTypes   []TypeUsageStats `json:"top_request_types"`
}

// SessionAnalyticsData represents chat session analytics
type SessionAnalyticsData struct {
	TotalSessions    int64            `json:"total_sessions" example:"100"`
	ActiveSessions   int64            `json:"active_sessions" example:"75"`
	ArchivedSessions int64            `json:"archived_sessions" example:"20"`
	DeletedSessions  int64            `json:"deleted_sessions" example:"5"`
	AverageMessages  float64          `json:"average_messages_per_session" example:"25.0"`
	SessionsByStatus map[string]int64 `json:"sessions_by_status"`
	SessionsByModel  map[string]int64 `json:"sessions_by_model"`
}

// MessageAnalyticsData represents chat message analytics
type MessageAnalyticsData struct {
	TotalMessages     int64            `json:"total_messages" example:"2500"`
	UserMessages      int64            `json:"user_messages" example:"1250"`
	AssistantMessages int64            `json:"assistant_messages" example:"1200"`
	SystemMessages    int64            `json:"system_messages" example:"50"`
	MessagesByRole    map[string]int64 `json:"messages_by_role"`
	AverageLength     float64          `json:"average_message_length" example:"125.5"`
}

// UsageAnalyticsData represents usage and cost analytics
type UsageAnalyticsData struct {
	TotalTokensUsed  int64             `json:"total_tokens_used" example:"75000"`
	AverageTokens    float64           `json:"average_tokens_per_request" example:"150.0"`
	TotalCostCents   int64             `json:"total_cost_cents" example:"2500"`
	TotalCostDollars float64           `json:"total_cost_dollars" example:"25.00"`
	AverageCost      float64           `json:"average_cost_per_request" example:"0.05"`
	TokensByModel    map[string]int64  `json:"tokens_by_model"`
	CostByModel      map[string]int64  `json:"cost_by_model"`
	TopModels        []ModelUsageStats `json:"top_models"`
}

// PerformanceAnalyticsData represents performance metrics
type PerformanceAnalyticsData struct {
	AverageProcessingTimeMs float64            `json:"average_processing_time_ms" example:"1250.5"`
	MedianProcessingTimeMs  float64            `json:"median_processing_time_ms" example:"1100.0"`
	P95ProcessingTimeMs     float64            `json:"p95_processing_time_ms" example:"2500.0"`
	P99ProcessingTimeMs     float64            `json:"p99_processing_time_ms" example:"4000.0"`
	ProcessingTimeByModel   map[string]float64 `json:"processing_time_by_model"`
	ThroughputByHour        map[string]float64 `json:"throughput_by_hour"`
}

// TimeSeriesData represents time-based analytics
type TimeSeriesData struct {
	RequestsByTime map[string]int64   `json:"requests_by_time"`
	TokensByTime   map[string]int64   `json:"tokens_by_time"`
	CostByTime     map[string]float64 `json:"cost_by_time"`
	SessionsByTime map[string]int64   `json:"sessions_by_time"`
	MessagesByTime map[string]int64   `json:"messages_by_time"`
}

// TypeUsageStats represents usage statistics for a request type
type TypeUsageStats struct {
	RequestType  models.AIRequestType `json:"request_type" example:"chat"`
	RequestCount int64                `json:"request_count" example:"300"`
	TokensUsed   int64                `json:"tokens_used" example:"45000"`
	CostCents    int64                `json:"cost_cents" example:"1500"`
	CostDollars  float64              `json:"cost_dollars" example:"15.00"`
	SuccessRate  float64              `json:"success_rate" example:"95.0"`
}

// UserAnalyticsResponse represents analytics data for a specific user
type UserAnalyticsResponse struct {
	UserID    uint               `json:"user_id" example:"1"`
	Username  string             `json:"username" example:"johndoe"`
	Email     string             `json:"email" example:"<EMAIL>"`
	TimeRange AnalyticsTimeRange `json:"time_range" example:"last_7_days"`
	StartDate time.Time          `json:"start_date" example:"2024-01-09T00:00:00Z"`
	EndDate   time.Time          `json:"end_date" example:"2024-01-15T23:59:59Z"`

	// User-specific metrics
	TotalRequests int64   `json:"total_requests" example:"50"`
	TotalSessions int64   `json:"total_sessions" example:"10"`
	TotalMessages int64   `json:"total_messages" example:"250"`
	TokensUsed    int64   `json:"tokens_used" example:"7500"`
	CostCents     int64   `json:"cost_cents" example:"250"`
	CostDollars   float64 `json:"cost_dollars" example:"2.50"`

	// Activity patterns
	RequestsByType map[string]int64 `json:"requests_by_type"`
	ActivityByHour map[string]int64 `json:"activity_by_hour"`
	ActivityByDay  map[string]int64 `json:"activity_by_day"`

	// Usage trends
	UsageTrend []DailyUsageStats `json:"usage_trend"`
}

// DailyUsageStats represents daily usage statistics
type DailyUsageStats struct {
	Date        string  `json:"date" example:"2024-01-15"`
	Requests    int64   `json:"requests" example:"10"`
	Sessions    int64   `json:"sessions" example:"2"`
	Messages    int64   `json:"messages" example:"50"`
	TokensUsed  int64   `json:"tokens_used" example:"1500"`
	CostCents   int64   `json:"cost_cents" example:"50"`
	CostDollars float64 `json:"cost_dollars" example:"0.50"`
}

// ModelAnalyticsResponse represents analytics data for AI models
type ModelAnalyticsResponse struct {
	TimeRange AnalyticsTimeRange `json:"time_range" example:"last_7_days"`
	StartDate time.Time          `json:"start_date" example:"2024-01-09T00:00:00Z"`
	EndDate   time.Time          `json:"end_date" example:"2024-01-15T23:59:59Z"`

	Models     []ModelPerformanceStats `json:"models"`
	Comparison ModelComparisonData     `json:"comparison"`
}

// ModelPerformanceStats represents performance statistics for a model
type ModelPerformanceStats struct {
	ModelID             uint    `json:"model_id" example:"1"`
	ModelName           string  `json:"model_name" example:"GPT-4"`
	Provider            string  `json:"provider" example:"OpenAI"`
	RequestCount        int64   `json:"request_count" example:"200"`
	SessionCount        int64   `json:"session_count" example:"40"`
	MessageCount        int64   `json:"message_count" example:"1000"`
	TokensUsed          int64   `json:"tokens_used" example:"30000"`
	CostCents           int64   `json:"cost_cents" example:"1000"`
	CostDollars         float64 `json:"cost_dollars" example:"10.00"`
	SuccessRate         float64 `json:"success_rate" example:"98.0"`
	AverageProcessingMs float64 `json:"average_processing_ms" example:"1200.5"`
	AverageTokensPerReq float64 `json:"average_tokens_per_request" example:"150.0"`
	AverageCostPerReq   float64 `json:"average_cost_per_request" example:"0.05"`
}

// ModelComparisonData represents comparison data between models
type ModelComparisonData struct {
	MostUsed          ModelPerformanceStats `json:"most_used"`
	FastestResponse   ModelPerformanceStats `json:"fastest_response"`
	HighestSuccess    ModelPerformanceStats `json:"highest_success_rate"`
	MostCostEffective ModelPerformanceStats `json:"most_cost_effective"`
}

// TrendAnalyticsResponse represents trend analysis over time
type TrendAnalyticsResponse struct {
	TimeRange   AnalyticsTimeRange `json:"time_range" example:"last_30_days"`
	StartDate   time.Time          `json:"start_date" example:"2024-01-01T00:00:00Z"`
	EndDate     time.Time          `json:"end_date" example:"2024-01-30T23:59:59Z"`
	Granularity string             `json:"granularity" example:"day"`

	// Growth metrics
	RequestGrowth GrowthMetrics `json:"request_growth"`
	SessionGrowth GrowthMetrics `json:"session_growth"`
	UserGrowth    GrowthMetrics `json:"user_growth"`
	CostGrowth    GrowthMetrics `json:"cost_growth"`

	// Trends
	DailyTrends []DailyTrendData `json:"daily_trends"`
	Predictions PredictionData   `json:"predictions"`
}

// GrowthMetrics represents growth metrics
type GrowthMetrics struct {
	Current    float64 `json:"current" example:"500.0"`
	Previous   float64 `json:"previous" example:"450.0"`
	Growth     float64 `json:"growth" example:"11.11"`
	GrowthRate float64 `json:"growth_rate" example:"0.1111"`
	Trend      string  `json:"trend" example:"increasing"`
}

// DailyTrendData represents daily trend data
type DailyTrendData struct {
	Date        string  `json:"date" example:"2024-01-15"`
	Requests    int64   `json:"requests" example:"25"`
	Sessions    int64   `json:"sessions" example:"5"`
	Messages    int64   `json:"messages" example:"125"`
	TokensUsed  int64   `json:"tokens_used" example:"3750"`
	CostCents   int64   `json:"cost_cents" example:"125"`
	CostDollars float64 `json:"cost_dollars" example:"1.25"`
	Users       int64   `json:"active_users" example:"8"`
}

// PredictionData represents prediction data
type PredictionData struct {
	NextPeriodRequests float64 `json:"next_period_requests" example:"550.0"`
	NextPeriodSessions float64 `json:"next_period_sessions" example:"110.0"`
	NextPeriodCost     float64 `json:"next_period_cost_dollars" example:"27.50"`
	Confidence         float64 `json:"confidence" example:"0.85"`
	Method             string  `json:"method" example:"linear_regression"`
}

// ExportAnalyticsRequest represents the request to export analytics data
type ExportAnalyticsRequest struct {
	TenantID  uint               `json:"tenant_id" validate:"required,min=1" example:"1"`
	WebsiteID *uint              `json:"website_id,omitempty" validate:"omitempty,min=1" example:"1"`
	UserID    *uint              `json:"user_id,omitempty" validate:"omitempty,min=1" example:"1"`
	TimeRange AnalyticsTimeRange `json:"time_range" validate:"required" example:"last_7_days"`
	StartDate *time.Time         `json:"start_date,omitempty" example:"2024-01-01T00:00:00Z"`
	EndDate   *time.Time         `json:"end_date,omitempty" example:"2024-01-31T23:59:59Z"`
	Format    string             `json:"format" validate:"required,oneof=json csv excel pdf" example:"csv"`
	Metrics   []string           `json:"metrics" validate:"required,min=1" example:"['requests','tokens','cost']"`
}

// ExportAnalyticsResponse represents the response for analytics export
type ExportAnalyticsResponse struct {
	Format      string    `json:"format" example:"csv"`
	Filename    string    `json:"filename" example:"analytics_2024_01_15.csv"`
	DownloadURL string    `json:"download_url" example:"https://api.example.com/downloads/analytics_abc123"`
	ExpiresAt   time.Time `json:"expires_at" example:"2024-01-16T10:30:00Z"`
	FileSize    int64     `json:"file_size" example:"102400"`
	RecordCount int       `json:"record_count" example:"500"`
}

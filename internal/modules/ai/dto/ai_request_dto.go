package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// CreateAIRequestRequest represents the request to create a new AI request
type CreateAIRequestRequest struct {
	TenantID    uint                 `json:"tenant_id" validate:"required" example:"1"`
	WebsiteID   *uint                `json:"website_id,omitempty" validate:"omitempty,min=1" example:"1"`
	UserID      *uint                `json:"user_id,omitempty" validate:"omitempty,min=1" example:"1"`
	ModelID     uint                 `json:"model_id" validate:"required,min=1" example:"1"`
	RequestType models.AIRequestType `json:"request_type" validate:"required,oneof=content_generation chat design optimization analysis" example:"chat"`
	PromptText  string               `json:"prompt_text" validate:"required,min=1,max=10000" example:"Generate a blog post about AI"`
	Metadata    models.JSONMap       `json:"metadata,omitempty" example:"{}"`
}

// UpdateAIRequestRequest represents the request to update an AI request
type UpdateAIRequestRequest struct {
	Status           *models.AIRequestStatus `json:"status,omitempty" validate:"omitempty,oneof=pending completed failed timeout" example:"completed"`
	ResponseText     *string                 `json:"response_text,omitempty" validate:"omitempty,min=1" example:"Here is your generated content..."`
	TokensUsed       *int                    `json:"tokens_used,omitempty" validate:"omitempty,min=0" example:"150"`
	ProcessingTimeMs *int                    `json:"processing_time_ms,omitempty" validate:"omitempty,min=0" example:"1500"`
	CostCents        *int                    `json:"cost_cents,omitempty" validate:"omitempty,min=0" example:"5"`
	ErrorMessage     *string                 `json:"error_message,omitempty" validate:"omitempty,min=1" example:"Processing failed"`
	Metadata         models.JSONMap          `json:"metadata,omitempty" example:"{}"`
}

// AIRequestResponse represents the response when returning AI request data
type AIRequestResponse struct {
	ID               uint                   `json:"id" example:"1"`
	TenantID         uint                   `json:"tenant_id" example:"1"`
	WebsiteID        *uint                  `json:"website_id,omitempty" example:"1"`
	UserID           *uint                  `json:"user_id,omitempty" example:"1"`
	ModelID          uint                   `json:"model_id" example:"1"`
	RequestType      models.AIRequestType   `json:"request_type" example:"chat"`
	PromptText       string                 `json:"prompt_text" example:"Generate a blog post about AI"`
	ResponseText     *string                `json:"response_text,omitempty" example:"Here is your generated content..."`
	TokensUsed       int                    `json:"tokens_used" example:"150"`
	ProcessingTimeMs int                    `json:"processing_time_ms" example:"1500"`
	CostCents        int                    `json:"cost_cents" example:"5"`
	CostDollars      float64                `json:"cost_dollars" example:"0.05"`
	Status           models.AIRequestStatus `json:"status" example:"completed"`
	ErrorMessage     *string                `json:"error_message,omitempty" example:""`
	Metadata         models.JSONMap         `json:"metadata" example:"{}"`
	CreatedAt        time.Time              `json:"created_at" example:"2024-01-15T10:30:00Z"`

	// Relationships
	Tenant  *TenantResponse  `json:"tenant,omitempty"`
	Website *WebsiteResponse `json:"website,omitempty"`
	User    *UserResponse    `json:"user,omitempty"`
	Model   *AIModelResponse `json:"model,omitempty"`
}

// FromAIRequest converts an AI request model to response DTO
func (r *AIRequestResponse) FromAIRequest(req *models.AIRequest) {
	r.ID = req.ID
	r.TenantID = req.TenantID
	r.WebsiteID = req.WebsiteID
	r.UserID = req.UserID
	r.ModelID = req.ModelID
	r.RequestType = req.RequestType
	r.PromptText = req.PromptText
	r.ResponseText = req.ResponseText
	r.TokensUsed = req.TokensUsed
	r.ProcessingTimeMs = req.ProcessingTimeMs
	r.CostCents = req.CostCents
	r.CostDollars = req.GetCostDollars()
	r.Status = req.Status
	r.ErrorMessage = req.ErrorMessage
	r.Metadata = req.Metadata
	r.CreatedAt = req.CreatedAt
}

// AIRequestListResponse represents the response for listing AI requests
type AIRequestListResponse struct {
	Requests   []AIRequestResponse        `json:"requests"`
	Pagination *pagination.CursorResponse `json:"pagination"`
	Total      int64                      `json:"total" example:"100"`
}

// AIRequestFilter represents filter options for AI request queries
type AIRequestFilter struct {
	TenantID          *uint                        `json:"tenant_id,omitempty" validate:"omitempty,min=1" example:"1"`
	WebsiteID         *uint                        `json:"website_id,omitempty" validate:"omitempty,min=1" example:"1"`
	UserID            *uint                        `json:"user_id,omitempty" validate:"omitempty,min=1" example:"1"`
	ModelID           *uint                        `json:"model_id,omitempty" validate:"omitempty,min=1" example:"1"`
	RequestType       *models.AIRequestType        `json:"request_type,omitempty" validate:"omitempty,oneof=content_generation chat design optimization analysis" example:"chat"`
	Status            *models.AIRequestStatus      `json:"status,omitempty" validate:"omitempty,oneof=pending completed failed timeout" example:"completed"`
	CreatedAfter      *time.Time                   `json:"created_after,omitempty" example:"2024-01-01T00:00:00Z"`
	CreatedBefore     *time.Time                   `json:"created_before,omitempty" example:"2024-01-31T23:59:59Z"`
	MinTokens         *int                         `json:"min_tokens,omitempty" validate:"omitempty,min=0" example:"10"`
	MaxTokens         *int                         `json:"max_tokens,omitempty" validate:"omitempty,min=0" example:"1000"`
	MinCost           *int                         `json:"min_cost,omitempty" validate:"omitempty,min=0" example:"1"`
	MaxCost           *int                         `json:"max_cost,omitempty" validate:"omitempty,min=0" example:"100"`
	MinProcessingTime *int                         `json:"min_processing_time,omitempty" validate:"omitempty,min=0" example:"100"`
	MaxProcessingTime *int                         `json:"max_processing_time,omitempty" validate:"omitempty,min=0" example:"5000"`
	SearchQuery       string                       `json:"search_query,omitempty" validate:"omitempty,max=500" example:"blog post"`
	Pagination        *pagination.CursorPagination `json:"pagination,omitempty"`
}

// AIRequestStatsResponse represents AI request statistics
type AIRequestStatsResponse struct {
	TotalRequests           int64             `json:"total_requests" example:"500"`
	CompletedRequests       int64             `json:"completed_requests" example:"450"`
	FailedRequests          int64             `json:"failed_requests" example:"30"`
	TimeoutRequests         int64             `json:"timeout_requests" example:"20"`
	TotalTokensUsed         int64             `json:"total_tokens_used" example:"75000"`
	TotalCostCents          int64             `json:"total_cost_cents" example:"2500"`
	TotalCostDollars        float64           `json:"total_cost_dollars" example:"25.00"`
	AverageProcessingTimeMs float64           `json:"average_processing_time_ms" example:"1250.5"`
	RequestsByType          map[string]int64  `json:"requests_by_type"`
	RequestsByStatus        map[string]int64  `json:"requests_by_status"`
	RequestsByHour          map[string]int64  `json:"requests_by_hour"`
	RequestsByDay           map[string]int64  `json:"requests_by_day"`
	TopUsers                []UserUsageStats  `json:"top_users"`
	TopModels               []ModelUsageStats `json:"top_models"`
}

// UserUsageStats represents usage statistics for a user
type UserUsageStats struct {
	UserID       uint    `json:"user_id" example:"1"`
	Username     string  `json:"username" example:"johndoe"`
	Email        string  `json:"email" example:"<EMAIL>"`
	RequestCount int64   `json:"request_count" example:"25"`
	TokensUsed   int64   `json:"tokens_used" example:"3750"`
	CostCents    int64   `json:"cost_cents" example:"125"`
	CostDollars  float64 `json:"cost_dollars" example:"1.25"`
}

// ModelUsageStats represents usage statistics for a model
type ModelUsageStats struct {
	ModelID      uint    `json:"model_id" example:"1"`
	ModelName    string  `json:"model_name" example:"GPT-4"`
	Provider     string  `json:"provider" example:"OpenAI"`
	RequestCount int64   `json:"request_count" example:"100"`
	TokensUsed   int64   `json:"tokens_used" example:"15000"`
	CostCents    int64   `json:"cost_cents" example:"500"`
	CostDollars  float64 `json:"cost_dollars" example:"5.00"`
}

// MarkCompletedRequest represents the request to mark an AI request as completed
type MarkCompletedRequest struct {
	ResponseText     string `json:"response_text" validate:"required,min=1" example:"Here is your generated content..."`
	TokensUsed       int    `json:"tokens_used" validate:"required,min=0" example:"150"`
	ProcessingTimeMs int    `json:"processing_time_ms" validate:"required,min=0" example:"1500"`
	CostCents        int    `json:"cost_cents" validate:"required,min=0" example:"5"`
}

// MarkFailedRequest represents the request to mark an AI request as failed
type MarkFailedRequest struct {
	ErrorMessage string `json:"error_message" validate:"required,min=1" example:"Processing failed due to timeout"`
}

// Relationship response types (simplified versions)
type TenantResponse struct {
	ID   uint   `json:"id" example:"1"`
	Name string `json:"name" example:"My Company"`
}

type WebsiteResponse struct {
	ID   uint   `json:"id" example:"1"`
	Name string `json:"name" example:"My Website"`
}

type UserResponse struct {
	ID       uint   `json:"id" example:"1"`
	Username string `json:"username" example:"johndoe"`
	Email    string `json:"email" example:"<EMAIL>"`
}

type AIModelResponse struct {
	ID       uint   `json:"id" example:"1"`
	Name     string `json:"name" example:"GPT-4"`
	Provider string `json:"provider" example:"OpenAI"`
}

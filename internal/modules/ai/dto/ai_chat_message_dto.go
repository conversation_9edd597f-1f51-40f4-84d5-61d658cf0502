package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// CreateChatMessageRequest represents the request to create a chat message
type CreateChatMessageRequest struct {
	SessionID  uint                   `json:"session_id" validate:"required,min=1" example:"1"`
	Role       models.ChatMessageRole `json:"role" validate:"required,oneof=user assistant system" example:"user"`
	Content    string                 `json:"content" validate:"required,min=1,max=100000" example:"Hello, can you help me with coding?"`
	Metadata   models.JSONMap         `json:"metadata,omitempty" example:"{}"`
	TokensUsed *int                   `json:"tokens_used,omitempty" validate:"omitempty,min=0" example:"25"`
}

// UpdateChatMessageRequest represents the request to update a chat message
type UpdateChatMessageRequest struct {
	Content    *string        `json:"content,omitempty" validate:"omitempty,min=1,max=100000" example:"Updated message content"`
	Metadata   models.JSONMap `json:"metadata,omitempty" example:"{}"`
	TokensUsed *int           `json:"tokens_used,omitempty" validate:"omitempty,min=0" example:"30"`
}

// ChatMessageResponse represents the chat message response
type ChatMessageResponse struct {
	ID         uint                   `json:"id" example:"1"`
	SessionID  uint                   `json:"session_id" example:"1"`
	Role       models.ChatMessageRole `json:"role" example:"user"`
	Content    string                 `json:"content" example:"Hello, can you help me with coding?"`
	Metadata   models.JSONMap         `json:"metadata,omitempty" example:"{}"`
	TokensUsed int                    `json:"tokens_used" example:"25"`
	CreatedAt  time.Time              `json:"created_at" example:"2024-01-15T10:30:00Z"`

	// Relationships
	Session *ChatSessionSummaryResponse `json:"session,omitempty"`
}

// FromChatMessage converts a chat message model to response DTO
func (r *ChatMessageResponse) FromChatMessage(message *models.AIChatMessage) {
	r.ID = message.ID
	r.SessionID = message.SessionID
	r.Role = message.Role
	r.Content = message.Content
	r.Metadata = message.Metadata
	r.TokensUsed = message.TokensUsed
	r.CreatedAt = message.CreatedAt
}

// ChatMessageListResponse represents the response for listing chat messages
type ChatMessageListResponse struct {
	Messages   []ChatMessageResponse      `json:"messages"`
	Pagination *pagination.CursorResponse `json:"pagination"`
	TotalCount int64                      `json:"total_count" example:"100"`
}

// ChatMessageFilter represents filters for querying chat messages
type ChatMessageFilter struct {
	SessionID     *uint                        `json:"session_id,omitempty" validate:"omitempty,min=1" example:"1"`
	Role          *models.ChatMessageRole      `json:"role,omitempty" validate:"omitempty,oneof=user assistant system" example:"user"`
	Roles         []models.ChatMessageRole     `json:"roles,omitempty" validate:"omitempty,dive,oneof=user assistant system" example:"['user','assistant']"`
	SearchQuery   string                       `json:"search_query,omitempty" validate:"omitempty,max=500" example:"coding help"`
	MinTokens     *int                         `json:"min_tokens,omitempty" validate:"omitempty,min=0" example:"10"`
	MaxTokens     *int                         `json:"max_tokens,omitempty" validate:"omitempty,min=0" example:"1000"`
	CreatedAfter  *time.Time                   `json:"created_after,omitempty" example:"2024-01-01T00:00:00Z"`
	CreatedBefore *time.Time                   `json:"created_before,omitempty" example:"2024-01-31T23:59:59Z"`
	Pagination    *pagination.CursorPagination `json:"pagination,omitempty"`
}

// ChatConversationResponse represents a conversation view of messages
type ChatConversationResponse struct {
	SessionID    uint                  `json:"session_id" example:"1"`
	SessionTitle string                `json:"session_title" example:"Chat about AI development"`
	Messages     []ChatMessageResponse `json:"messages"`
	TotalTokens  int                   `json:"total_tokens" example:"250"`
	MessageCount int                   `json:"message_count" example:"10"`
	CreatedAt    time.Time             `json:"created_at" example:"2024-01-15T10:30:00Z"`
	UpdatedAt    time.Time             `json:"updated_at" example:"2024-01-15T14:30:00Z"`
}

// MessageStatsResponse represents statistics for messages
type MessageStatsResponse struct {
	TotalMessages     int64                 `json:"total_messages" example:"1000"`
	UserMessages      int64                 `json:"user_messages" example:"500"`
	AssistantMessages int64                 `json:"assistant_messages" example:"480"`
	SystemMessages    int64                 `json:"system_messages" example:"20"`
	TotalTokens       int64                 `json:"total_tokens" example:"25000"`
	AverageTokens     float64               `json:"average_tokens" example:"25.0"`
	MessagesByRole    map[string]int64      `json:"messages_by_role"`
	MessagesByHour    map[string]int64      `json:"messages_by_hour"`
	MessagesByDay     map[string]int64      `json:"messages_by_day"`
	TopSessions       []SessionMessageStats `json:"top_sessions"`
}

// SessionMessageStats represents message statistics for a session
type SessionMessageStats struct {
	SessionID     uint      `json:"session_id" example:"1"`
	SessionTitle  string    `json:"session_title" example:"Chat about AI development"`
	MessageCount  int64     `json:"message_count" example:"50"`
	TokenCount    int64     `json:"token_count" example:"1250"`
	AverageTokens float64   `json:"average_tokens" example:"25.0"`
	LastMessageAt time.Time `json:"last_message_at" example:"2024-01-15T14:30:00Z"`
}

// BulkCreateChatMessagesRequest represents the request to create multiple chat messages
type BulkCreateChatMessagesRequest struct {
	SessionID uint                       `json:"session_id" validate:"required,min=1" example:"1"`
	Messages  []CreateChatMessageRequest `json:"messages" validate:"required,min=1,max=100" example:"[{'role':'user','content':'Hello'},{'role':'assistant','content':'Hi there!'}]"`
}

// BulkCreateChatMessagesResponse represents the response for bulk message creation
type BulkCreateChatMessagesResponse struct {
	Messages     []ChatMessageResponse `json:"messages"`
	CreatedCount int                   `json:"created_count" example:"2"`
}

// SearchChatMessagesRequest represents the request to search chat messages
type SearchChatMessagesRequest struct {
	TenantID      uint                         `json:"tenant_id" validate:"required,min=1" example:"1"`
	WebsiteID     *uint                        `json:"website_id,omitempty" validate:"omitempty,min=1" example:"1"`
	UserID        *uint                        `json:"user_id,omitempty" validate:"omitempty,min=1" example:"1"`
	SessionID     *uint                        `json:"session_id,omitempty" validate:"omitempty,min=1" example:"1"`
	Query         string                       `json:"query" validate:"required,min=1,max=500" example:"coding help"`
	Role          *models.ChatMessageRole      `json:"role,omitempty" validate:"omitempty,oneof=user assistant system" example:"user"`
	Roles         []models.ChatMessageRole     `json:"roles,omitempty" validate:"omitempty,dive,oneof=user assistant system" example:"['user','assistant']"`
	CreatedAfter  *time.Time                   `json:"created_after,omitempty" example:"2024-01-01T00:00:00Z"`
	CreatedBefore *time.Time                   `json:"created_before,omitempty" example:"2024-01-31T23:59:59Z"`
	Pagination    *pagination.CursorPagination `json:"pagination,omitempty"`
}

// ExportChatMessagesRequest represents the request to export chat messages
type ExportChatMessagesRequest struct {
	SessionID       uint                     `json:"session_id" validate:"required,min=1" example:"1"`
	Format          string                   `json:"format" validate:"required,oneof=json csv markdown txt pdf" example:"json"`
	Role            *models.ChatMessageRole  `json:"role,omitempty" validate:"omitempty,oneof=user assistant system" example:"user"`
	Roles           []models.ChatMessageRole `json:"roles,omitempty" validate:"omitempty,dive,oneof=user assistant system" example:"['user','assistant']"`
	CreatedAfter    *time.Time               `json:"created_after,omitempty" example:"2024-01-01T00:00:00Z"`
	CreatedBefore   *time.Time               `json:"created_before,omitempty" example:"2024-01-31T23:59:59Z"`
	IncludeMetadata bool                     `json:"include_metadata,omitempty" example:"true"`
}

// ExportChatMessagesResponse represents the response for message export
type ExportChatMessagesResponse struct {
	Format       string    `json:"format" example:"json"`
	Filename     string    `json:"filename" example:"chat_session_1_messages.json"`
	DownloadURL  string    `json:"download_url" example:"https://api.example.com/downloads/abc123"`
	ExpiresAt    time.Time `json:"expires_at" example:"2024-01-16T10:30:00Z"`
	MessageCount int       `json:"message_count" example:"25"`
	FileSize     int64     `json:"file_size" example:"51200"`
}

// ChatMessageSummaryResponse represents a summary of a chat message
type ChatMessageSummaryResponse struct {
	ID         uint                   `json:"id" example:"1"`
	Role       models.ChatMessageRole `json:"role" example:"user"`
	Content    string                 `json:"content" example:"Hello, can you help me with coding?"`
	TokensUsed int                    `json:"tokens_used" example:"25"`
	CreatedAt  time.Time              `json:"created_at" example:"2024-01-15T10:30:00Z"`
}

// RecentMessagesResponse represents recent messages for a session or user
type RecentMessagesResponse struct {
	Messages []ChatMessageSummaryResponse `json:"messages"`
	Count    int                          `json:"count" example:"5"`
}

// BulkDeleteMessagesRequest represents the request to delete multiple messages
type BulkDeleteMessagesRequest struct {
	MessageIDs []uint `json:"message_ids" validate:"required,min=1,max=100" example:"[1,2,3]"`
}

// BulkDeleteMessagesResponse represents the response for bulk message deletion
type BulkDeleteMessagesResponse struct {
	Deleted []uint   `json:"deleted" example:"[1,2]"`
	Failed  []uint   `json:"failed" example:"[3]"`
	Errors  []string `json:"errors,omitempty" example:"['Message 3 not found']"`
}

// Note: ChatSessionSummaryResponse is defined in ai_chat_session_dto.go

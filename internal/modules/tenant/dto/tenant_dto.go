package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
)

// TenantCreateRequest represents the request to create a new tenant
type TenantCreateRequest struct {
	Name           string `json:"name" validate:"required,min=3,max=100" example:"My Company"`
	Domain         string `json:"domain" validate:"required,min=3,max=100" example:"mycompany"`
	ContactEmail   string `json:"contact_email" validate:"required,email" example:"<EMAIL>"`
	ContactPhone   string `json:"contact_phone" validate:"omitempty,e164" example:"+1234567890"`
	CompanyName    string `json:"company_name" validate:"omitempty,max=200" example:"My Company Inc."`
	CompanyAddress string `json:"company_address" validate:"omitempty,max=500" example:"123 Business St, City, State"`
	CompanyTaxID   string `json:"company_tax_id" validate:"omitempty,max=50" example:"TAX123456"`
	PlanID         uint   `json:"plan_id" validate:"required" example:"1"`
}

// TenantUpdateRequest represents the request to update a tenant
type TenantUpdateRequest struct {
	Name           string  `json:"name" validate:"omitempty,min=3,max=100" example:"Updated Company"`
	ContactEmail   string  `json:"contact_email" validate:"omitempty,email" example:"<EMAIL>"`
	ContactPhone   string  `json:"contact_phone" validate:"omitempty,e164" example:"+9876543210"`
	CompanyName    *string `json:"company_name" validate:"omitempty,max=200" example:"Updated Company Inc."`
	CompanyAddress *string `json:"company_address" validate:"omitempty,max=500" example:"456 New Address St"`
	CompanyTaxID   *string `json:"company_tax_id" validate:"omitempty,max=50" example:"NEWTAX789"`
	LogoURL        *string `json:"logo_url" validate:"omitempty,url" example:"https://example.com/logo.png"`
	FaviconURL     *string `json:"favicon_url" validate:"omitempty,url" example:"https://example.com/favicon.ico"`
	PrimaryColor   *string `json:"primary_color" validate:"omitempty,hexcolor" example:"#3B82F6"`
	CustomCSS      *string `json:"custom_css" validate:"omitempty,max=10000" example:"body { font-family: Arial; }"`
}

// TenantUpdateStatusRequest represents the request to update tenant status
type TenantUpdateStatusRequest struct {
	Status string `json:"status" validate:"required,oneof=active suspended inactive trial deleted" example:"active"`
}

// TenantAssignPlanRequest represents the request to assign a plan to a tenant
type TenantAssignPlanRequest struct {
	PlanID uint `json:"plan_id" validate:"required" example:"2"`
}

// TenantListFilter represents the filter for listing tenants
type TenantListFilter struct {
	Search    string              `json:"search,omitempty" example:"company"`
	Status    models.TenantStatus `json:"status,omitempty" example:"active"`
	PlanID    uint                `json:"plan_id,omitempty" example:"1"`
	Page      int                 `json:"page,omitempty" example:"1"`
	PageSize  int                 `json:"page_size,omitempty" example:"20"`
	SortBy    string              `json:"sort_by,omitempty" example:"created_at"`
	SortOrder string              `json:"sort_order,omitempty" example:"desc"`
}

// TenantResponse represents the response for tenant operations
// KEEP_OMITEMPTY: Optional company data, branding, and nullable subscription dates
type TenantResponse struct {
	ID               uint                `json:"id" example:"1"`
	Name             string              `json:"name" example:"My Company"`
	Domain           string              `json:"domain" example:"mycompany"`
	Status           models.TenantStatus `json:"status" example:"active"`
	PlanID           uint                `json:"plan_id" example:"1"`
	ContactEmail     string              `json:"contact_email" example:"<EMAIL>"`
	ContactPhone     string              `json:"contact_phone,omitempty" example:"+1234567890"`                   // KEEP_OMITEMPTY: Optional contact
	CompanyName      string              `json:"company_name,omitempty" example:"My Company Inc."`                // KEEP_OMITEMPTY: Optional company data
	CompanyAddress   string              `json:"company_address,omitempty" example:"123 Business St"`             // KEEP_OMITEMPTY: Optional company data
	CompanyTaxID     string              `json:"company_tax_id,omitempty" example:"TAX123456"`                    // KEEP_OMITEMPTY: Optional company data
	LogoURL          string              `json:"logo_url,omitempty" example:"https://example.com/logo.png"`       // KEEP_OMITEMPTY: Optional branding
	FaviconURL       string              `json:"favicon_url,omitempty" example:"https://example.com/favicon.ico"` // KEEP_OMITEMPTY: Optional branding
	PrimaryColor     string              `json:"primary_color,omitempty" example:"#3B82F6"`                       // KEEP_OMITEMPTY: Optional branding
	CustomCSS        string              `json:"custom_css,omitempty"`                                            // KEEP_OMITEMPTY: Optional branding
	SubscriptionEnds *time.Time          `json:"subscription_ends,omitempty"`                                     // KEEP_OMITEMPTY: Nullable subscription date
	TrialEnds        *time.Time          `json:"trial_ends,omitempty"`                                            // KEEP_OMITEMPTY: Nullable trial date
	CreatedAt        time.Time           `json:"created_at"`
	UpdatedAt        time.Time           `json:"updated_at"`
}

// TenantListResponse represents the response for listing tenants
type TenantListResponse struct {
	Tenants    []TenantResponse `json:"tenants"`
	Total      int64            `json:"total" example:"50"`
	Page       int              `json:"page" example:"1"`
	PageSize   int              `json:"page_size" example:"20"`
	TotalPages int              `json:"total_pages" example:"3"`
}

// TenantStatsResponse represents tenant statistics
// KEEP_OMITEMPTY: Optional plan info that may not be loaded
type TenantStatsResponse struct {
	UserCount      int64                  `json:"user_count" example:"25"`
	ProjectCount   int64                  `json:"project_count" example:"5"`
	StorageUsed    int64                  `json:"storage_used" example:"1073741824"`
	APICallsToday  int64                  `json:"api_calls_today" example:"1250"`
	ResourceLimits map[string]interface{} `json:"resource_limits"`
	PlanInfo       *TenantPlanResponse    `json:"plan_info,omitempty"` // KEEP_OMITEMPTY: Optional nested object
}

// TenantDomainCheckRequest represents request to check domain availability
type TenantDomainCheckRequest struct {
	Domain string `json:"domain" validate:"required,min=3,max=100" example:"newcompany"`
}

// TenantDomainCheckResponse represents response for domain availability check
type TenantDomainCheckResponse struct {
	Domain    string `json:"domain" example:"newcompany"`
	Available bool   `json:"available" example:"true"`
	Message   string `json:"message" example:"Domain is available"`
}

// TenantUsageResponse represents tenant resource usage
type TenantUsageResponse struct {
	TenantID     uint                   `json:"tenant_id" example:"1"`
	PlanLimits   map[string]interface{} `json:"plan_limits"`
	CurrentUsage map[string]interface{} `json:"current_usage"`
	Percentage   map[string]float64     `json:"percentage"`
	Warnings     []string               `json:"warnings"`
}

// MyTenantItem represents a single tenant in the current user's tenant list
type MyTenantItem struct {
	ID               uint      `json:"id" example:"1"`
	Name             string    `json:"name" example:"My Company"`
	Domain           string    `json:"domain" example:"mycompany"`
	Status           string    `json:"status" example:"active"`
	MembershipStatus string    `json:"membership_status" example:"active"`
	IsPrimary        bool      `json:"is_primary" example:"true"`
	JoinedAt         time.Time `json:"joined_at"`
	LogoURL          string    `json:"logo_url,omitempty" example:"https://example.com/logo.png"`
}

// MyTenantsResponse represents the response for GET /tenants/my with cursor pagination
// DEPRECATED: Use standard response format with CursorResponse instead
type MyTenantsResponse struct {
	Tenants []MyTenantItem `json:"tenants"`
	Meta    struct {
		HasMore    bool   `json:"has_more" example:"true"`
		NextCursor string `json:"next_cursor,omitempty" example:"eyJpZCI6MTUsInRpbWUiOiIyMDI0LTAxLTE1VDA5OjMwOjAwWiJ9"`
		Total      int64  `json:"total" example:"3"`
	} `json:"meta"`
}

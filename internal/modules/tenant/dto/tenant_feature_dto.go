package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
)

// FeatureCatalogCreateRequest represents the request to create a new feature in catalog
type FeatureCatalogCreateRequest struct {
	FeatureKey       string `json:"feature_key" validate:"required,lowercase,alphanum" example:"advanced_analytics"`
	FeatureName      string `json:"feature_name" validate:"required" example:"Advanced Analytics"`
	Description      string `json:"description" example:"Advanced analytics and reporting capabilities"`
	Category         string `json:"category" validate:"required" example:"analytics"`
	RequiredPlans    []uint `json:"required_plans" example:"2,3"`
	ExcludedPlans    []uint `json:"excluded_plans" example:"1"`
	IsBeta           bool   `json:"is_beta" example:"false"`
	IsExperimental   bool   `json:"is_experimental" example:"false"`
	DocumentationURL string `json:"documentation_url" validate:"omitempty,url" example:"https://docs.example.com/advanced-analytics"`
}

// FeatureCatalogUpdateRequest represents the request to update a feature in catalog
type FeatureCatalogUpdateRequest struct {
	FeatureName      *string               `json:"feature_name" example:"Updated Analytics"`
	Description      *string               `json:"description" example:"Updated description"`
	Category         *string               `json:"category" example:"reporting"`
	RequiredPlans    []uint                `json:"required_plans" example:"2,3,4"`
	ExcludedPlans    []uint                `json:"excluded_plans" example:"1"`
	IsBeta           *bool                 `json:"is_beta" example:"true"`
	IsExperimental   *bool                 `json:"is_experimental" example:"false"`
	DocumentationURL *string               `json:"documentation_url" validate:"omitempty,url" example:"https://docs.example.com/updated-analytics"`
	Status           *models.FeatureStatus `json:"status" validate:"omitempty,oneof=active deprecated disabled" example:"active"`
}

// TenantFeatureToggleRequest represents request to toggle feature for tenant
type TenantFeatureToggleRequest struct {
	FeatureKey string `json:"feature_key" validate:"required" example:"advanced_analytics"`
	Enabled    bool   `json:"enabled" example:"true"`
}

// TenantFeatureBulkToggleRequest represents request to bulk toggle features
type TenantFeatureBulkToggleRequest struct {
	FeatureKeys []string `json:"feature_keys" validate:"required,min=1" example:"feature1,feature2,feature3"`
	Enabled     bool     `json:"enabled" example:"true"`
}

// TenantFeatureConfigurationRequest represents request to update feature configuration
type TenantFeatureConfigurationRequest struct {
	FeatureKey    string                 `json:"feature_key" validate:"required" example:"advanced_analytics"`
	Configuration map[string]interface{} `json:"configuration" example:"{\"retention_days\": 90, \"export_formats\": [\"csv\", \"pdf\"]}"`
}

// FeatureCatalogResponse represents the response for feature catalog operations
type FeatureCatalogResponse struct {
	ID               uint                 `json:"id" example:"1"`
	FeatureKey       string               `json:"feature_key" example:"advanced_analytics"`
	FeatureName      string               `json:"feature_name" example:"Advanced Analytics"`
	Description      string               `json:"description" example:"Advanced analytics and reporting capabilities"`
	Category         string               `json:"category" example:"analytics"`
	RequiredPlans    []uint               `json:"required_plans" example:"2,3"`
	ExcludedPlans    []uint               `json:"excluded_plans" example:"1"`
	IsBeta           bool                 `json:"is_beta" example:"false"`
	IsExperimental   bool                 `json:"is_experimental" example:"false"`
	DocumentationURL string               `json:"documentation_url" example:"https://docs.example.com/advanced-analytics"`
	Status           models.FeatureStatus `json:"status" example:"active"`
	CreatedAt        time.Time            `json:"created_at"`
	UpdatedAt        time.Time            `json:"updated_at"`
}

// TenantFeatureResponse represents tenant-specific feature status
type TenantFeatureResponse struct {
	ID             uint                   `json:"id" example:"1"`
	TenantID       uint                   `json:"tenant_id" example:"1"`
	FeatureKey     string                 `json:"feature_key" example:"advanced_analytics"`
	FeatureName    string                 `json:"feature_name" example:"Advanced Analytics"`
	Category       string                 `json:"category" example:"analytics"`
	IsEnabled      bool                   `json:"is_enabled" example:"true"`
	Configuration  map[string]interface{} `json:"configuration,omitempty"`
	EnabledAt      *time.Time             `json:"enabled_at,omitempty"`
	EnabledBy      *uint                  `json:"enabled_by,omitempty"`
	IsBeta         bool                   `json:"is_beta" example:"false"`
	IsExperimental bool                   `json:"is_experimental" example:"false"`
}

// FeatureListResponse represents the response for listing features
type FeatureListResponse struct {
	Features   []FeatureCatalogResponse `json:"features"`
	Total      int64                    `json:"total" example:"15"`
	Page       int                      `json:"page" example:"1"`
	PageSize   int                      `json:"page_size" example:"20"`
	TotalPages int                      `json:"total_pages" example:"1"`
}

// TenantFeatureListResponse represents the response for listing tenant features
type TenantFeatureListResponse struct {
	Features []TenantFeatureResponse `json:"features"`
	TenantID uint                    `json:"tenant_id" example:"1"`
	Total    int64                   `json:"total" example:"8"`
}

// FeatureUsageResponse represents feature usage statistics
type FeatureUsageResponse struct {
	FeatureKey  string                 `json:"feature_key" example:"advanced_analytics"`
	TenantID    uint                   `json:"tenant_id" example:"1"`
	EnabledAt   *time.Time             `json:"enabled_at,omitempty"`
	TotalUsers  int64                  `json:"total_users" example:"5"`
	ActiveUsers int64                  `json:"active_users" example:"3"`
	UsageStats  map[string]interface{} `json:"usage_stats"`
}

// FeatureAdoptionStatsResponse represents feature adoption statistics
type FeatureAdoptionStatsResponse struct {
	FeatureKey          string  `json:"feature_key" example:"advanced_analytics"`
	FeatureName         string  `json:"feature_name" example:"Advanced Analytics"`
	Category            string  `json:"category" example:"analytics"`
	EnabledTenantsCount int     `json:"enabled_tenants_count" example:"15"`
	TotalTenants        int     `json:"total_tenants" example:"50"`
	AdoptionRate        float64 `json:"adoption_rate" example:"0.30"`
}

// FeatureCatalogFilter represents filter for listing feature catalog
type FeatureCatalogFilter struct {
	Category       string                `json:"category,omitempty" example:"analytics"`
	Status         *models.FeatureStatus `json:"status,omitempty" example:"active"`
	IsBeta         *bool                 `json:"is_beta,omitempty" example:"false"`
	IsExperimental *bool                 `json:"is_experimental,omitempty" example:"false"`
	PlanID         *uint                 `json:"plan_id,omitempty" example:"2"`
	Page           int                   `json:"page,omitempty" example:"1"`
	PageSize       int                   `json:"page_size,omitempty" example:"20"`
	SortBy         string                `json:"sort_by,omitempty" example:"feature_name"`
	SortOrder      string                `json:"sort_order,omitempty" example:"asc"`
}

// FeatureAvailabilityResponse represents feature availability for a tenant/plan
type FeatureAvailabilityResponse struct {
	FeatureKey        string `json:"feature_key" example:"advanced_analytics"`
	FeatureName       string `json:"feature_name" example:"Advanced Analytics"`
	Available         bool   `json:"available" example:"true"`
	RequiredPlan      string `json:"required_plan,omitempty" example:"Professional"`
	CurrentPlan       string `json:"current_plan" example:"Basic"`
	UpgradeRequired   bool   `json:"upgrade_required" example:"true"`
	RestrictionReason string `json:"restriction_reason,omitempty" example:"Feature requires Professional plan or higher"`
}

package services

import (
	"context"
	"fmt"
	"strings"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// websiteTemplateService implements WebsiteTemplateService
type websiteTemplateService struct {
	templateRepo repositories.WebsiteTemplateRepository
	categoryRepo repositories.TemplateCategoryRepository
	logger       utils.Logger
}

// NewWebsiteTemplateService creates a new website template service
func NewWebsiteTemplateService(
	templateRepo repositories.WebsiteTemplateRepository,
	categoryRepo repositories.TemplateCategoryRepository,
	logger utils.Logger,
) WebsiteTemplateService {
	return &websiteTemplateService{
		templateRepo: templateRepo,
		categoryRepo: categoryRepo,
		logger:       logger,
	}
}

// CreateTemplate creates a new website template
func (s *websiteTemplateService) CreateTemplate(ctx context.Context, req *dto.CreateTemplateRequest) (*dto.TemplateResponse, error) {
	// Validate slug uniqueness
	if err := s.ValidateTemplateSlug(ctx, req.Slug, 0); err != nil {
		return nil, err
	}

	// Validate category if provided
	if req.CategoryID != nil {
		if _, err := s.categoryRepo.GetByID(ctx, *req.CategoryID); err != nil {
			if err == gorm.ErrRecordNotFound {
				return nil, &WebsiteServiceError{
					Code:    ErrCodeCategoryNotFound,
					Message: "Category not found",
					Field:   "category_id",
				}
			}
			return nil, err
		}
	}

	// Create template model
	template := &models.WebsiteTemplate{
		Name:            req.Name,
		Slug:            req.Slug,
		Description:     req.Description,
		PreviewImageURL: req.PreviewImageURL,
		CategoryID:      req.CategoryID,
		IsPremium:       req.IsPremium,
		IsPublished:     req.IsPublished,
		IsFeatured:      req.IsFeatured,
		Version:         req.Version,
		TemplateType:    models.TemplateType(req.TemplateType),
		Industry:        req.Industry,
		Status:          models.TemplateStatusDraft,
	}

	// Set default version if empty
	if template.Version == "" {
		template.Version = "1.0.0"
	}

	// Convert JSON fields
	if req.TemplateData != nil {
		template.TemplateData = models.TemplateData(req.TemplateData)
	}
	if req.Sections != nil {
		template.Sections = models.TemplateSections(req.Sections)
	}
	if req.Styles != nil {
		template.Styles = models.TemplateStyles(req.Styles)
	}
	if req.Settings != nil {
		template.Settings = models.TemplateSettings(req.Settings)
	}
	if req.Tags != nil {
		template.Tags = models.TemplateTags(req.Tags)
	}

	// Set published status and time if published
	if req.IsPublished {
		template.MarkAsPublished()
	}

	// Create template
	if err := s.templateRepo.Create(ctx, template); err != nil {
		s.logger.WithError(err).Error("Failed to create template")
		return nil, &WebsiteServiceError{
			Code:    ErrCodeInternalError,
			Message: "Failed to create template",
		}
	}

	// Update category template count if category is set
	if template.CategoryID != nil {
		if err := s.categoryRepo.IncrementTemplateCount(ctx, *template.CategoryID); err != nil {
			s.logger.WithError(err).Warn("Failed to increment category template count")
		}
	}

	// Get template with category for response
	templateWithCategory, err := s.templateRepo.GetWithCategory(ctx, template.ID)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to get template with category")
		templateWithCategory = template
	}

	return dto.NewTemplateResponse(templateWithCategory), nil
}

// GetTemplate retrieves a template by ID or slug
func (s *websiteTemplateService) GetTemplate(ctx context.Context, req *dto.GetTemplateRequest) (*dto.TemplateResponse, error) {
	var template *models.WebsiteTemplate
	var err error

	if req.ID != nil {
		template, err = s.templateRepo.GetWithCategory(ctx, *req.ID)
	} else if req.Slug != "" {
		template, err = s.templateRepo.GetBySlug(ctx, req.Slug)
		if err == nil {
			// Get with category for complete response
			template, err = s.templateRepo.GetWithCategory(ctx, template.ID)
		}
	} else {
		return nil, &WebsiteServiceError{
			Code:    ErrCodeValidationFailed,
			Message: "Either ID or slug must be provided",
		}
	}

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, &WebsiteServiceError{
				Code:    ErrCodeTemplateNotFound,
				Message: "Template not found",
			}
		}
		return nil, err
	}

	return dto.NewTemplateResponse(template), nil
}

// UpdateTemplate updates a template
func (s *websiteTemplateService) UpdateTemplate(ctx context.Context, req *dto.UpdateTemplateRequest) (*dto.TemplateResponse, error) {
	// Get existing template
	template, err := s.templateRepo.GetByID(ctx, req.ID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, &WebsiteServiceError{
				Code:    ErrCodeTemplateNotFound,
				Message: "Template not found",
			}
		}
		return nil, err
	}

	updates := make(map[string]interface{})

	// Validate slug uniqueness if changed
	if req.Slug != nil && *req.Slug != template.Slug {
		if err := s.ValidateTemplateSlug(ctx, *req.Slug, req.ID); err != nil {
			return nil, err
		}
		updates["slug"] = *req.Slug
	}

	// Validate category if changed
	if req.CategoryID != nil && (template.CategoryID == nil || *req.CategoryID != *template.CategoryID) {
		if _, err := s.categoryRepo.GetByID(ctx, *req.CategoryID); err != nil {
			if err == gorm.ErrRecordNotFound {
				return nil, &WebsiteServiceError{
					Code:    ErrCodeCategoryNotFound,
					Message: "Category not found",
					Field:   "category_id",
				}
			}
			return nil, err
		}

		// Update category counts
		if template.CategoryID != nil {
			s.categoryRepo.DecrementTemplateCount(ctx, *template.CategoryID)
		}
		s.categoryRepo.IncrementTemplateCount(ctx, *req.CategoryID)
		updates["category_id"] = *req.CategoryID
	}

	// Update other fields
	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.PreviewImageURL != nil {
		updates["preview_image_url"] = *req.PreviewImageURL
	}
	if req.IsPremium != nil {
		updates["is_premium"] = *req.IsPremium
	}
	if req.IsPublished != nil {
		updates["is_published"] = *req.IsPublished
		if *req.IsPublished {
			updates["status"] = models.TemplateStatusPublished
		}
	}
	if req.IsFeatured != nil {
		updates["is_featured"] = *req.IsFeatured
	}
	if req.Version != nil {
		updates["version"] = *req.Version
	}
	if req.TemplateType != nil {
		updates["template_type"] = *req.TemplateType
	}
	if req.Industry != nil {
		updates["industry"] = *req.Industry
	}

	// Update JSON fields
	if req.TemplateData != nil {
		updates["template_data"] = models.TemplateData(req.TemplateData)
	}
	if req.Sections != nil {
		updates["sections"] = models.TemplateSections(req.Sections)
	}
	if req.Styles != nil {
		updates["styles"] = models.TemplateStyles(req.Styles)
	}
	if req.Settings != nil {
		updates["settings"] = models.TemplateSettings(req.Settings)
	}
	if req.Tags != nil {
		updates["tags"] = models.TemplateTags(req.Tags)
	}

	// Apply updates
	if len(updates) > 0 {
		if err := s.templateRepo.Update(ctx, req.ID, updates); err != nil {
			s.logger.WithError(err).Error("Failed to update template")
			return nil, &WebsiteServiceError{
				Code:    ErrCodeInternalError,
				Message: "Failed to update template",
			}
		}
	}

	// Get updated template with category
	updatedTemplate, err := s.templateRepo.GetWithCategory(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	return dto.NewTemplateResponse(updatedTemplate), nil
}

// DeleteTemplate soft deletes a template
func (s *websiteTemplateService) DeleteTemplate(ctx context.Context, id uint) error {
	// Get template to check category
	template, err := s.templateRepo.GetByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return &WebsiteServiceError{
				Code:    ErrCodeTemplateNotFound,
				Message: "Template not found",
			}
		}
		return err
	}

	// Delete template
	if err := s.templateRepo.Delete(ctx, id); err != nil {
		s.logger.WithError(err).Error("Failed to delete template")
		return &WebsiteServiceError{
			Code:    ErrCodeInternalError,
			Message: "Failed to delete template",
		}
	}

	// Update category template count
	if template.CategoryID != nil {
		if err := s.categoryRepo.DecrementTemplateCount(ctx, *template.CategoryID); err != nil {
			s.logger.WithError(err).Warn("Failed to decrement category template count")
		}
	}

	return nil
}

// ListTemplates retrieves templates with filtering and pagination
func (s *websiteTemplateService) ListTemplates(ctx context.Context, req *dto.ListTemplatesRequest) (*dto.TemplateListResponse, error) {
	// Set defaults
	if req.Limit == 0 {
		req.Limit = 20
	}

	// Build filter for cursor-based pagination
	filter := models.TemplateFilter{
		CategoryID: req.CategoryID,
		Industry:   nil,
		IsPremium:  req.IsPremium,
		IsFeatured: req.IsFeatured,
		Search:     nil,
		SortBy:     req.SortBy,
		SortOrder:  req.SortOrder,
		Cursor:     req.Cursor,
		Limit:      req.Limit,
		// Keep offset for backward compatibility if cursor is not provided
		Offset:     (req.Page - 1) * req.Limit,
	}

	// Set industry filter only if not empty
	if req.Industry != "" {
		filter.Industry = &req.Industry
	}

	// Set search filter only if not empty
	if req.Search != "" {
		filter.Search = &req.Search
	}

	// Set template type
	if req.TemplateType != "" {
		templateType := models.TemplateType(req.TemplateType)
		filter.TemplateType = &templateType
	}

	// Set status
	if req.Status != "" {
		status := models.TemplateStatus(req.Status)
		filter.Status = &status
	}

	// Parse tags
	if req.Tags != "" {
		filter.Tags = strings.Split(req.Tags, ",")
		// Trim whitespace
		for i, tag := range filter.Tags {
			filter.Tags[i] = strings.TrimSpace(tag)
		}
	}

	// Get templates
	templates, _, err := s.templateRepo.List(ctx, filter)
	if err != nil {
		s.logger.WithError(err).Error("Failed to list templates")
		return nil, &WebsiteServiceError{
			Code:    ErrCodeInternalError,
			Message: "Failed to list templates",
		}
	}

	// Convert to DTOs
	templateResponses := make([]dto.TemplateResponse, len(templates))
	for i, template := range templates {
		templateResponses[i] = *dto.NewTemplateResponse(template)
	}

	// Always use cursor-based pagination
	hasMore := len(templates) == req.Limit
	var nextCursor string
	
	if hasMore && len(templates) > 0 {
		// Create cursor from last item using global function
		lastTemplate := templates[len(templates)-1]
		nextCursor, err = pagination.EncodeCursor(lastTemplate.ID, lastTemplate.CreatedAt)
		if err != nil {
			return nil, &WebsiteServiceError{
				Code:    ErrCodeInternalError,
				Message: "Failed to encode cursor",
			}
		}
	}
	
	cursorResponse := &pagination.CursorResponse{
		HasNext:    hasMore,
		NextCursor: nextCursor,
		HasMore:    hasMore,
		Count:      len(templates),
		Limit:      req.Limit,
	}

	return &dto.TemplateListResponse{
		Templates:  templateResponses,
		Pagination: cursorResponse,
	}, nil
}

// GetFeaturedTemplates retrieves featured templates
func (s *websiteTemplateService) GetFeaturedTemplates(ctx context.Context, limit int) ([]*dto.TemplateResponse, error) {
	templates, err := s.templateRepo.GetFeatured(ctx, limit)
	if err != nil {
		return nil, err
	}

	responses := make([]*dto.TemplateResponse, len(templates))
	for i, template := range templates {
		responses[i] = dto.NewTemplateResponse(template)
	}

	return responses, nil
}

// GetPopularTemplates retrieves popular templates
func (s *websiteTemplateService) GetPopularTemplates(ctx context.Context, limit int) ([]*dto.TemplateResponse, error) {
	templates, err := s.templateRepo.GetPopular(ctx, limit)
	if err != nil {
		return nil, err
	}

	responses := make([]*dto.TemplateResponse, len(templates))
	for i, template := range templates {
		responses[i] = dto.NewTemplateResponse(template)
	}

	return responses, nil
}

// SearchTemplates searches templates by query
func (s *websiteTemplateService) SearchTemplates(ctx context.Context, query string, limit int) ([]*dto.TemplateResponse, error) {
	templates, err := s.templateRepo.Search(ctx, query, limit)
	if err != nil {
		return nil, err
	}

	responses := make([]*dto.TemplateResponse, len(templates))
	for i, template := range templates {
		responses[i] = dto.NewTemplateResponse(template)
	}

	return responses, nil
}

// GetTemplatesByCategory retrieves templates by category
func (s *websiteTemplateService) GetTemplatesByCategory(ctx context.Context, categoryID uint) ([]*dto.TemplateResponse, error) {
	templates, err := s.templateRepo.GetByCategory(ctx, categoryID)
	if err != nil {
		return nil, err
	}

	responses := make([]*dto.TemplateResponse, len(templates))
	for i, template := range templates {
		responses[i] = dto.NewTemplateResponse(template)
	}

	return responses, nil
}

// GetTemplatesByType retrieves templates by type
func (s *websiteTemplateService) GetTemplatesByType(ctx context.Context, templateType string) ([]*dto.TemplateResponse, error) {
	templates, err := s.templateRepo.GetByType(ctx, models.TemplateType(templateType))
	if err != nil {
		return nil, err
	}

	responses := make([]*dto.TemplateResponse, len(templates))
	for i, template := range templates {
		responses[i] = dto.NewTemplateResponse(template)
	}

	return responses, nil
}

// GetTemplateStats retrieves template statistics
func (s *websiteTemplateService) GetTemplateStats(ctx context.Context) (*dto.TemplateStatsResponse, error) {
	stats, err := s.templateRepo.GetStats(ctx)
	if err != nil {
		return nil, err
	}

	// Get popular templates
	popularTemplates, err := s.GetPopularTemplates(ctx, 5)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to get popular templates for stats")
		popularTemplates = []*dto.TemplateResponse{}
	}

	// Convert pointers to values
	popular := make([]dto.TemplateResponse, len(popularTemplates))
	for i, template := range popularTemplates {
		popular[i] = *template
	}

	// Get categories with template counts
	categories, err := s.categoryRepo.GetMainCategories(ctx)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to get categories for stats")
		categories = []*models.TemplateCategory{}
	}

	byCategory := make([]dto.TemplateCategoryStats, len(categories))
	for i, category := range categories {
		count, _ := s.templateRepo.CountByCategory(ctx, category.ID)
		byCategory[i] = dto.TemplateCategoryStats{
			CategoryID:    category.ID,
			CategoryName:  category.Name,
			CategorySlug:  category.Slug,
			TemplateCount: count,
		}
	}

	return &dto.TemplateStatsResponse{
		TotalTemplates:     stats.TotalTemplates,
		PublishedTemplates: stats.PublishedTemplates,
		PremiumTemplates:   stats.PremiumTemplates,
		FeaturedTemplates:  stats.FeaturedTemplates,
		ByType:             stats.ByType,
		ByCategory:         byCategory,
		MostPopular:        popular,
	}, nil
}

// IncrementDownloadCount increments template download count
func (s *websiteTemplateService) IncrementDownloadCount(ctx context.Context, id uint) error {
	return s.templateRepo.UpdateDownloadCount(ctx, id)
}

// IncrementUsageCount increments template usage count
func (s *websiteTemplateService) IncrementUsageCount(ctx context.Context, id uint) error {
	return s.templateRepo.UpdateUsage(ctx, id)
}

// UpdateTemplateRating updates template rating
func (s *websiteTemplateService) UpdateTemplateRating(ctx context.Context, id uint, rating float64) error {
	if rating < 1 || rating > 5 {
		return &WebsiteServiceError{
			Code:    ErrCodeValidationFailed,
			Message: "Rating must be between 1 and 5",
			Field:   "rating",
		}
	}
	return s.templateRepo.UpdateRating(ctx, id, rating)
}

// PublishTemplate publishes a template
func (s *websiteTemplateService) PublishTemplate(ctx context.Context, id uint) error {
	return s.templateRepo.UpdateStatus(ctx, id, models.TemplateStatusPublished)
}

// ArchiveTemplate archives a template
func (s *websiteTemplateService) ArchiveTemplate(ctx context.Context, id uint) error {
	return s.templateRepo.UpdateStatus(ctx, id, models.TemplateStatusArchived)
}

// ValidateTemplateSlug validates template slug uniqueness
func (s *websiteTemplateService) ValidateTemplateSlug(ctx context.Context, slug string, excludeID uint) error {
	exists, err := s.templateRepo.CheckSlugExists(ctx, slug, excludeID)
	if err != nil {
		return err
	}
	if exists {
		return &WebsiteServiceError{
			Code:    ErrCodeTemplateSlugExists,
			Message: "Template slug already exists",
			Field:   "slug",
		}
	}
	return nil
}

// CloneTemplate creates a copy of an existing template
func (s *websiteTemplateService) CloneTemplate(ctx context.Context, sourceID uint, newSlug, newName string) (*dto.TemplateResponse, error) {
	// Get source template
	source, err := s.templateRepo.GetByID(ctx, sourceID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, &WebsiteServiceError{
				Code:    ErrCodeTemplateNotFound,
				Message: "Source template not found",
			}
		}
		return nil, err
	}

	// Validate new slug
	if err := s.ValidateTemplateSlug(ctx, newSlug, 0); err != nil {
		return nil, err
	}

	// Create cloned template
	cloned := &models.WebsiteTemplate{
		Name:            newName,
		Slug:            newSlug,
		Description:     fmt.Sprintf("Cloned from %s", source.Name),
		PreviewImageURL: source.PreviewImageURL,
		TemplateData:    source.TemplateData,
		Sections:        source.Sections,
		Styles:          source.Styles,
		Settings:        source.Settings,
		CategoryID:      source.CategoryID,
		Tags:            source.Tags,
		IsPremium:       source.IsPremium,
		IsPublished:     false, // Cloned templates start as unpublished
		IsFeatured:      false, // Cloned templates are not featured by default
		Version:         "1.0.0",
		TemplateType:    source.TemplateType,
		Industry:        source.Industry,
		Status:          models.TemplateStatusDraft,
	}

	if err := s.templateRepo.Create(ctx, cloned); err != nil {
		s.logger.WithError(err).Error("Failed to clone template")
		return nil, &WebsiteServiceError{
			Code:    ErrCodeInternalError,
			Message: "Failed to clone template",
		}
	}

	// Get cloned template with category
	clonedWithCategory, err := s.templateRepo.GetWithCategory(ctx, cloned.ID)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to get cloned template with category")
		clonedWithCategory = cloned
	}

	return dto.NewTemplateResponse(clonedWithCategory), nil
}

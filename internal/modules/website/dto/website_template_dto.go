package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// ListTemplatesRequest represents the request for listing templates
type ListTemplatesRequest struct {
	// Filters
	CategoryID   *uint  `form:"category_id" validate:"omitempty,min=1"`
	TemplateType string `form:"template_type" validate:"omitempty,oneof=business portfolio blog ecommerce landing personal nonprofit education event restaurant"`
	Industry     string `form:"industry" validate:"omitempty,max=100"`
	IsPremium    *bool  `form:"is_premium"`
	IsFeatured   *bool  `form:"is_featured"`
	Status       string `form:"status" validate:"omitempty,oneof=draft review published archived"`
	Search       string `form:"search" validate:"omitempty,max=255"`
	Tags         string `form:"tags" validate:"omitempty,max=500"` // Comma-separated tags

	// Sorting
	SortBy    string `form:"sort_by" validate:"omitempty,oneof=name created_at updated_at download_count usage_count rating_average"`
	SortOrder string `form:"sort_order" validate:"omitempty,oneof=asc desc"`

	// Cursor-based pagination (preferred)
	Cursor string `form:"cursor"`
	Limit  int    `form:"limit,default=20" binding:"min=1,max=100"`
	
	// Offset-based pagination (deprecated, for backward compatibility)
	Page int `form:"page,default=1" binding:"min=1"`
}

// GetTemplateRequest represents the request for getting a single template
type GetTemplateRequest struct {
	ID   *uint  `json:"id,omitempty" validate:"omitempty,min=1"`
	Slug string `json:"slug,omitempty" validate:"omitempty,min=1,max=255"`
}

// CreateTemplateRequest represents the request for creating a template
type CreateTemplateRequest struct {
	Name        string `json:"name" validate:"required,min=1,max=255"`
	Slug        string `json:"slug" validate:"required,min=1,max=255"`
	Description string `json:"description,omitempty" validate:"omitempty,max=5000"`

	// Template Content
	PreviewImageURL string                   `json:"preview_image_url,omitempty" validate:"omitempty,url"`
	TemplateData    map[string]interface{}   `json:"template_data,omitempty"`
	Sections        []map[string]interface{} `json:"sections,omitempty"`
	Styles          map[string]interface{}   `json:"styles,omitempty"`
	Settings        map[string]interface{}   `json:"settings,omitempty"`

	// Categorization
	CategoryID *uint    `json:"category_id,omitempty" validate:"omitempty,min=1"`
	Tags       []string `json:"tags,omitempty" validate:"omitempty,dive,min=1,max=50"`

	// Template Properties
	IsPremium   bool   `json:"is_premium"`
	IsPublished bool   `json:"is_published"`
	IsFeatured  bool   `json:"is_featured"`
	Version     string `json:"version,omitempty" validate:"omitempty,max=20"`

	// Template Type and Industry
	TemplateType string `json:"template_type" validate:"required,oneof=business portfolio blog ecommerce landing personal nonprofit education event restaurant"`
	Industry     string `json:"industry,omitempty" validate:"omitempty,max=100"`
}

// UpdateTemplateRequest represents the request for updating a template
type UpdateTemplateRequest struct {
	ID          uint    `json:"-" validate:"required,min=1"`
	Name        *string `json:"name,omitempty" validate:"omitempty,min=1,max=255"`
	Slug        *string `json:"slug,omitempty" validate:"omitempty,min=1,max=255"`
	Description *string `json:"description,omitempty" validate:"omitempty,max=5000"`

	// Template Content
	PreviewImageURL *string                  `json:"preview_image_url,omitempty" validate:"omitempty,url"`
	TemplateData    map[string]interface{}   `json:"template_data,omitempty"`
	Sections        []map[string]interface{} `json:"sections,omitempty"`
	Styles          map[string]interface{}   `json:"styles,omitempty"`
	Settings        map[string]interface{}   `json:"settings,omitempty"`

	// Categorization
	CategoryID *uint    `json:"category_id,omitempty" validate:"omitempty,min=1"`
	Tags       []string `json:"tags,omitempty" validate:"omitempty,dive,min=1,max=50"`

	// Template Properties
	IsPremium   *bool   `json:"is_premium,omitempty"`
	IsPublished *bool   `json:"is_published,omitempty"`
	IsFeatured  *bool   `json:"is_featured,omitempty"`
	Version     *string `json:"version,omitempty" validate:"omitempty,max=20"`

	// Template Type and Industry
	TemplateType *string `json:"template_type,omitempty" validate:"omitempty,oneof=business portfolio blog ecommerce landing personal nonprofit education event restaurant"`
	Industry     *string `json:"industry,omitempty" validate:"omitempty,max=100"`
}

// TemplateResponse represents the response for template operations
type TemplateResponse struct {
	ID          uint   `json:"id"`
	Name        string `json:"name"`
	Slug        string `json:"slug"`
	Description string `json:"description,omitempty"`

	// Template Content
	PreviewImageURL string                   `json:"preview_image_url,omitempty"`
	TemplateData    map[string]interface{}   `json:"template_data,omitempty"`
	Sections        []map[string]interface{} `json:"sections,omitempty"`
	Styles          map[string]interface{}   `json:"styles,omitempty"`
	Settings        map[string]interface{}   `json:"settings,omitempty"`

	// Categorization
	CategoryID *uint                     `json:"category_id,omitempty"`
	Category   *TemplateCategoryResponse `json:"category,omitempty"`
	Tags       []string                  `json:"tags,omitempty"`

	// Template Properties
	IsPremium   bool   `json:"is_premium"`
	IsPublished bool   `json:"is_published"`
	IsFeatured  bool   `json:"is_featured"`
	Version     string `json:"version"`

	// Usage Statistics
	DownloadCount uint    `json:"download_count"`
	UsageCount    uint    `json:"usage_count"`
	RatingAverage float64 `json:"rating_average"`
	RatingCount   uint    `json:"rating_count"`

	// Template Type and Industry
	TemplateType string `json:"template_type"`
	Industry     string `json:"industry,omitempty"`

	// Status and Lifecycle
	Status      string     `json:"status"`
	PublishedAt *time.Time `json:"published_at,omitempty"`
	ArchivedAt  *time.Time `json:"archived_at,omitempty"`

	// Timestamps
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// TemplateListResponse represents the response for listing templates
type TemplateListResponse struct {
	Templates  []TemplateResponse        `json:"templates"`
	Pagination *pagination.CursorResponse `json:"pagination,omitempty"`
}

// TemplateStatsResponse represents template statistics
type TemplateStatsResponse struct {
	TotalTemplates     int64                   `json:"total_templates"`
	PublishedTemplates int64                   `json:"published_templates"`
	PremiumTemplates   int64                   `json:"premium_templates"`
	FeaturedTemplates  int64                   `json:"featured_templates"`
	ByType             map[string]int64        `json:"by_type"`
	ByCategory         []TemplateCategoryStats `json:"by_category"`
	MostPopular        []TemplateResponse      `json:"most_popular"`
}

// TemplateCategoryStats represents category statistics
type TemplateCategoryStats struct {
	CategoryID    uint   `json:"category_id"`
	CategoryName  string `json:"category_name"`
	CategorySlug  string `json:"category_slug"`
	TemplateCount int64  `json:"template_count"`
}

// NewTemplateResponse creates a TemplateResponse from a WebsiteTemplate model
func NewTemplateResponse(template *models.WebsiteTemplate) *TemplateResponse {
	response := &TemplateResponse{
		ID:              template.ID,
		Name:            template.Name,
		Slug:            template.Slug,
		Description:     template.Description,
		PreviewImageURL: template.PreviewImageURL,
		CategoryID:      template.CategoryID,
		IsPremium:       template.IsPremium,
		IsPublished:     template.IsPublished,
		IsFeatured:      template.IsFeatured,
		Version:         template.Version,
		DownloadCount:   template.DownloadCount,
		UsageCount:      template.UsageCount,
		RatingAverage:   template.RatingAverage,
		RatingCount:     template.RatingCount,
		TemplateType:    template.GetTemplateType(),
		Industry:        template.Industry,
		Status:          template.GetStatus(),
		PublishedAt:     template.PublishedAt,
		ArchivedAt:      template.ArchivedAt,
		CreatedAt:       template.CreatedAt,
		UpdatedAt:       template.UpdatedAt,
	}

	// Convert JSON fields
	if template.TemplateData != nil {
		response.TemplateData = map[string]interface{}(template.TemplateData)
	}
	if template.Sections != nil {
		response.Sections = []map[string]interface{}(template.Sections)
	}
	if template.Styles != nil {
		response.Styles = map[string]interface{}(template.Styles)
	}
	if template.Settings != nil {
		response.Settings = map[string]interface{}(template.Settings)
	}
	if template.Tags != nil {
		response.Tags = []string(template.Tags)
	}

	// Include category if loaded
	if template.Category != nil {
		response.Category = NewTemplateCategoryResponse(template.Category)
	}

	return response
}

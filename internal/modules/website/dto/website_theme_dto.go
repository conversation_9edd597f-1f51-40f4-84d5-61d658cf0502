package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
)

// WebsiteThemeCreateRequest represents the request to create a new website theme
type WebsiteThemeCreateRequest struct {
	Name            string         `json:"name" validate:"required,min=1,max=100" example:"Modern Theme"`
	Version         string         `json:"version,omitempty" validate:"omitempty,max=20" example:"1.0.0"`
	Author          string         `json:"author,omitempty" validate:"omitempty,max=255" example:"Theme Author"`
	Description     string         `json:"description,omitempty" example:"A modern and clean theme"`
	Config          models.JSONMap `json:"config,omitempty"`
	TemplateFiles   models.JSONMap `json:"template_files,omitempty"`
	AssetFiles      models.JSONMap `json:"asset_files,omitempty"`
	DemoContent     models.JSONMap `json:"demo_content,omitempty"`
	SupportedLayout []string       `json:"supported_layout,omitempty" example:"blog,portfolio,ecommerce"`
	IsDefault       bool           `json:"is_default" example:"false"`
	IsCustom        bool           `json:"is_custom" example:"true"`
	ParentThemeID   *uint          `json:"parent_theme_id,omitempty" example:"1"`
}

// WebsiteThemeUpdateRequest represents the request to update a website theme
type WebsiteThemeUpdateRequest struct {
	Name            *string                    `json:"name,omitempty" validate:"omitempty,min=1,max=100" example:"Updated Theme"`
	Version         *string                    `json:"version,omitempty" validate:"omitempty,max=20" example:"1.1.0"`
	Author          *string                    `json:"author,omitempty" validate:"omitempty,max=255" example:"Updated Author"`
	Description     *string                    `json:"description,omitempty" example:"Updated description"`
	Config          *models.JSONMap            `json:"config,omitempty"`
	TemplateFiles   *models.JSONMap            `json:"template_files,omitempty"`
	AssetFiles      *models.JSONMap            `json:"asset_files,omitempty"`
	DemoContent     *models.JSONMap            `json:"demo_content,omitempty"`
	SupportedLayout *[]string                  `json:"supported_layout,omitempty"`
	IsDefault       *bool                      `json:"is_default,omitempty" example:"true"`
	IsCustom        *bool                      `json:"is_custom,omitempty" example:"false"`
	ParentThemeID   *uint                      `json:"parent_theme_id,omitempty" example:"2"`
	Status          *models.WebsiteThemeStatus `json:"status,omitempty" validate:"omitempty,oneof=active inactive" example:"active"`
}

// WebsiteThemeActivateRequest represents the request to activate a theme
type WebsiteThemeActivateRequest struct {
	ThemeID uint `json:"theme_id" validate:"required,min=1" example:"1"`
}

// WebsiteThemeCloneRequest represents the request to clone a theme
type WebsiteThemeCloneRequest struct {
	SourceThemeID uint   `json:"source_theme_id" validate:"required,min=1" example:"1"`
	NewName       string `json:"new_name" validate:"required,min=1,max=100" example:"Cloned Theme"`
	CloneAsChild  bool   `json:"clone_as_child" example:"true"`
}

// WebsiteThemeResponse represents the response for website theme operations
type WebsiteThemeResponse struct {
	ID              uint                      `json:"id" example:"1"`
	WebsiteID       uint                      `json:"website_id" example:"1"`
	TenantID        uint                      `json:"tenant_id" example:"1"`
	Name            string                    `json:"name" example:"Modern Theme"`
	Version         string                    `json:"version,omitempty" example:"1.0.0"`
	Author          string                    `json:"author,omitempty" example:"Theme Author"`
	Description     string                    `json:"description,omitempty" example:"A modern and clean theme"`
	Config          models.JSONMap            `json:"config,omitempty"`
	TemplateFiles   models.JSONMap            `json:"template_files,omitempty"`
	AssetFiles      models.JSONMap            `json:"asset_files,omitempty"`
	DemoContent     models.JSONMap            `json:"demo_content,omitempty"`
	SupportedLayout []string                  `json:"supported_layout,omitempty"`
	IsDefault       bool                      `json:"is_default" example:"false"`
	IsCustom        bool                      `json:"is_custom" example:"true"`
	ParentThemeID   *uint                     `json:"parent_theme_id,omitempty" example:"1"`
	Status          models.WebsiteThemeStatus `json:"status" example:"active"`
	CreatedAt       time.Time                 `json:"created_at"`
	UpdatedAt       time.Time                 `json:"updated_at"`
}

// WebsiteThemeListResponse represents the response for listing website themes
type WebsiteThemeListResponse struct {
	Themes     []WebsiteThemeResponse `json:"themes"`
	TotalCount int64                  `json:"total_count" example:"10"`
	Page       int                    `json:"page" example:"1"`
	PageSize   int                    `json:"page_size" example:"20"`
	TotalPages int                    `json:"total_pages" example:"1"`
}

package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
)

// ListTemplateCategoriesRequest represents the request for listing template categories
type ListTemplateCategoriesRequest struct {
	// Filters
	ParentID   *uint  `form:"parent_id" validate:"omitempty,min=1"`
	Level      *uint  `form:"level" validate:"omitempty,min=0"`
	IsActive   *bool  `form:"is_active"`
	IsFeatured *bool  `form:"is_featured"`
	Status     string `form:"status" validate:"omitempty,oneof=active inactive"`
	Search     string `form:"search" validate:"omitempty,max=255"`

	// Sorting
	SortBy    string `form:"sort_by" validate:"omitempty,oneof=name sort_order created_at template_count"`
	SortOrder string `form:"sort_order" validate:"omitempty,oneof=asc desc"`

	// Pagination
	Page  int `form:"page" validate:"omitempty,min=1"`
	Limit int `form:"limit" validate:"omitempty,min=1,max=100"`

	// Include relations
	IncludeChildren  bool `form:"include_children"`
	IncludeTemplates bool `form:"include_templates"`
	IncludeParent    bool `form:"include_parent"`
}

// GetTemplateCategoryRequest represents the request for getting a single category
type GetTemplateCategoryRequest struct {
	ID   *uint  `json:"id,omitempty" validate:"omitempty,min=1"`
	Slug string `json:"slug,omitempty" validate:"omitempty,min=1,max=255"`

	// Include relations
	IncludeChildren  bool `form:"include_children"`
	IncludeTemplates bool `form:"include_templates"`
	IncludeParent    bool `form:"include_parent"`
}

// CreateTemplateCategoryRequest represents the request for creating a category
type CreateTemplateCategoryRequest struct {
	Name        string `json:"name" validate:"required,min=1,max=255"`
	Slug        string `json:"slug" validate:"required,min=1,max=255"`
	Description string `json:"description,omitempty" validate:"omitempty,max=5000"`

	// Category Properties
	Icon      string `json:"icon,omitempty" validate:"omitempty,max=100"`
	Color     string `json:"color,omitempty" validate:"omitempty,hexcolor"`
	SortOrder uint   `json:"sort_order,omitempty"`

	// Category Status
	IsActive   bool `json:"is_active"`
	IsFeatured bool `json:"is_featured"`

	// Hierarchy Support
	ParentID *uint `json:"parent_id,omitempty" validate:"omitempty,min=1"`

	// SEO
	MetaTitle       string `json:"meta_title,omitempty" validate:"omitempty,max=255"`
	MetaDescription string `json:"meta_description,omitempty" validate:"omitempty,max=500"`
}

// UpdateTemplateCategoryRequest represents the request for updating a category
type UpdateTemplateCategoryRequest struct {
	ID          uint    `json:"-" validate:"required,min=1"`
	Name        *string `json:"name,omitempty" validate:"omitempty,min=1,max=255"`
	Slug        *string `json:"slug,omitempty" validate:"omitempty,min=1,max=255"`
	Description *string `json:"description,omitempty" validate:"omitempty,max=5000"`

	// Category Properties
	Icon      *string `json:"icon,omitempty" validate:"omitempty,max=100"`
	Color     *string `json:"color,omitempty" validate:"omitempty,hexcolor"`
	SortOrder *uint   `json:"sort_order,omitempty"`

	// Category Status
	IsActive   *bool `json:"is_active,omitempty"`
	IsFeatured *bool `json:"is_featured,omitempty"`

	// Hierarchy Support
	ParentID *uint `json:"parent_id,omitempty" validate:"omitempty,min=1"`

	// SEO
	MetaTitle       *string `json:"meta_title,omitempty" validate:"omitempty,max=255"`
	MetaDescription *string `json:"meta_description,omitempty" validate:"omitempty,max=500"`
}

// TemplateCategoryResponse represents the response for category operations
type TemplateCategoryResponse struct {
	ID          uint   `json:"id"`
	Name        string `json:"name"`
	Slug        string `json:"slug"`
	Description string `json:"description,omitempty"`

	// Category Properties
	Icon      string `json:"icon,omitempty"`
	Color     string `json:"color,omitempty"`
	SortOrder uint   `json:"sort_order"`

	// Category Status
	IsActive   bool `json:"is_active"`
	IsFeatured bool `json:"is_featured"`

	// Hierarchy Support
	ParentID *uint                      `json:"parent_id,omitempty"`
	Parent   *TemplateCategoryResponse  `json:"parent,omitempty"`
	Children []TemplateCategoryResponse `json:"children,omitempty"`
	Level    uint                       `json:"level"`
	Path     string                     `json:"path"`

	// Statistics
	TemplateCount uint `json:"template_count"`

	// SEO
	MetaTitle       string `json:"meta_title,omitempty"`
	MetaDescription string `json:"meta_description,omitempty"`

	// Status
	Status string `json:"status"`

	// Timestamps
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Relations (optional)
	Templates []TemplateResponse `json:"templates,omitempty"`
}

// TemplateCategoryListResponse represents the response for listing categories
type TemplateCategoryListResponse struct {
	Categories []TemplateCategoryResponse `json:"categories"`
	Total      int64                      `json:"total"`
	Page       int                        `json:"page"`
	Limit      int                        `json:"limit"`
	TotalPages int                        `json:"total_pages"`
}

// TemplateCategoryTreeResponse represents a hierarchical category tree
type TemplateCategoryTreeResponse struct {
	Categories []TemplateCategoryResponse `json:"categories"`
}

// TemplateCategoryStatsResponse represents category statistics
type TemplateCategoryStatsResponse struct {
	TotalCategories    int64                      `json:"total_categories"`
	ActiveCategories   int64                      `json:"active_categories"`
	FeaturedCategories int64                      `json:"featured_categories"`
	MainCategories     int64                      `json:"main_categories"`
	Subcategories      int64                      `json:"subcategories"`
	MostPopular        []TemplateCategoryResponse `json:"most_popular"`
	ByLevel            map[uint]int64             `json:"by_level"`
}

// NewTemplateCategoryResponse creates a TemplateCategoryResponse from a TemplateCategory model
func NewTemplateCategoryResponse(category *models.TemplateCategory) *TemplateCategoryResponse {
	response := &TemplateCategoryResponse{
		ID:              category.ID,
		Name:            category.Name,
		Slug:            category.Slug,
		Description:     category.Description,
		Icon:            category.Icon,
		Color:           category.Color,
		SortOrder:       category.SortOrder,
		IsActive:        category.IsActive,
		IsFeatured:      category.IsFeatured,
		ParentID:        category.ParentID,
		Level:           category.Level,
		Path:            category.Path,
		TemplateCount:   category.TemplateCount,
		MetaTitle:       category.MetaTitle,
		MetaDescription: category.MetaDescription,
		Status:          category.GetStatus(),
		CreatedAt:       category.CreatedAt,
		UpdatedAt:       category.UpdatedAt,
	}

	// Include parent if loaded
	if category.Parent != nil {
		response.Parent = NewTemplateCategoryResponse(category.Parent)
	}

	// Include children if loaded
	if len(category.Children) > 0 {
		response.Children = make([]TemplateCategoryResponse, len(category.Children))
		for i, child := range category.Children {
			response.Children[i] = *NewTemplateCategoryResponse(&child)
		}
	}

	// Include templates if loaded
	if len(category.Templates) > 0 {
		response.Templates = make([]TemplateResponse, len(category.Templates))
		for i, template := range category.Templates {
			response.Templates[i] = *NewTemplateResponse(&template)
		}
	}

	return response
}

// BuildCategoryTree builds a hierarchical tree from flat category list
func BuildCategoryTree(categories []models.TemplateCategory) []TemplateCategoryResponse {
	categoryMap := make(map[uint]*TemplateCategoryResponse)
	var rootCategories []TemplateCategoryResponse

	// First pass: create all category responses
	for _, category := range categories {
		response := NewTemplateCategoryResponse(&category)
		categoryMap[category.ID] = response
	}

	// Second pass: build hierarchy
	for _, category := range categories {
		if category.ParentID == nil {
			// Root category
			rootCategories = append(rootCategories, *categoryMap[category.ID])
		} else {
			// Child category
			parent := categoryMap[*category.ParentID]
			if parent != nil {
				parent.Children = append(parent.Children, *categoryMap[category.ID])
			}
		}
	}

	return rootCategories
}

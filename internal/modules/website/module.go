package website

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/repositories/mysql"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// RegisterRoutes registers all website module routes and dependencies
func RegisterRoutes(r *gin.RouterGroup, db *gorm.DB, v validator.Validator, logger utils.Logger) {
	// Create repositories
	templateRepo := mysql.NewWebsiteTemplateRepository(db)
	categoryRepo := mysql.NewTemplateCategoryRepository(db)

	// Create services
	templateService := services.NewWebsiteTemplateService(templateRepo, categoryRepo, logger)
	categoryService := services.NewTemplateCategoryService(categoryRepo, templateRepo, logger)

	// Create handlers
	templateHandler := handlers.NewWebsiteTemplateHandler(templateService, v, logger)
	categoryHandler := handlers.NewTemplateCategoryHandler(categoryService, v, logger)

	// Register routes
	RegisterWebsiteRoutes(r, nil, nil, templateHandler, categoryHandler)
}

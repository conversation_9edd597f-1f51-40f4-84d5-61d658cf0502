package mysql

import (
	"context"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/repositories"
)

// templateCategoryRepository implements TemplateCategoryRepository using MySQL/GORM
type templateCategoryRepository struct {
	db *gorm.DB
}

// NewTemplateCategoryRepository creates a new template category repository
func NewTemplateCategoryRepository(db *gorm.DB) repositories.TemplateCategoryRepository {
	return &templateCategoryRepository{db: db}
}

// Create creates a new template category
func (r *templateCategoryRepository) Create(ctx context.Context, category *models.TemplateCategory) error {
	return r.db.WithContext(ctx).Create(category).Error
}

// GetByID retrieves a category by ID
func (r *templateCategoryRepository) GetByID(ctx context.Context, id uint) (*models.TemplateCategory, error) {
	var category models.TemplateCategory
	err := r.db.WithContext(ctx).First(&category, id).Error
	if err != nil {
		return nil, err
	}
	return &category, nil
}

// GetBySlug retrieves a category by slug
func (r *templateCategoryRepository) GetBySlug(ctx context.Context, slug string) (*models.TemplateCategory, error) {
	var category models.TemplateCategory
	err := r.db.WithContext(ctx).Where("slug = ?", slug).First(&category).Error
	if err != nil {
		return nil, err
	}
	return &category, nil
}

// Update updates a category
func (r *templateCategoryRepository) Update(ctx context.Context, id uint, updates map[string]interface{}) error {
	return r.db.WithContext(ctx).Model(&models.TemplateCategory{}).Where("id = ?", id).Updates(updates).Error
}

// Delete soft deletes a category (sets status to deleted)
func (r *templateCategoryRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&models.TemplateCategory{}).Where("id = ?", id).Update("status", models.CategoryStatusDeleted).Error
}

// List retrieves categories based on filter
func (r *templateCategoryRepository) List(ctx context.Context, filter models.CategoryFilter) ([]*models.TemplateCategory, int64, error) {
	var categories []*models.TemplateCategory
	var total int64

	query := r.db.WithContext(ctx).Model(&models.TemplateCategory{})

	// Apply filters
	query = r.applyFilters(query, filter)

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	if filter.SortBy != "" {
		order := filter.SortBy
		if filter.SortOrder == "desc" {
			order += " DESC"
		} else {
			order += " ASC"
		}
		query = query.Order(order)
	} else {
		query = query.Order("sort_order ASC, name ASC")
	}

	// Apply pagination
	if filter.Limit > 0 {
		query = query.Limit(filter.Limit)
	}
	if filter.Offset > 0 {
		query = query.Offset(filter.Offset)
	}

	err := query.Preload("Parent").Find(&categories).Error
	return categories, total, err
}

// GetMainCategories retrieves all main categories (level 0)
func (r *templateCategoryRepository) GetMainCategories(ctx context.Context) ([]*models.TemplateCategory, error) {
	var categories []*models.TemplateCategory
	err := r.db.WithContext(ctx).Where("level = ? AND status = ?", 0, models.CategoryStatusActive).Order("sort_order ASC, name ASC").Find(&categories).Error
	return categories, err
}

// GetSubcategories retrieves subcategories by parent ID
func (r *templateCategoryRepository) GetSubcategories(ctx context.Context, parentID uint) ([]*models.TemplateCategory, error) {
	var categories []*models.TemplateCategory
	err := r.db.WithContext(ctx).Where("parent_id = ? AND status = ?", parentID, models.CategoryStatusActive).Order("sort_order ASC, name ASC").Find(&categories).Error
	return categories, err
}

// GetCategoryTree retrieves the category tree
func (r *templateCategoryRepository) GetCategoryTree(ctx context.Context) ([]*models.TemplateCategory, error) {
	var categories []*models.TemplateCategory
	err := r.db.WithContext(ctx).Where("status = ?", models.CategoryStatusActive).Order("level ASC, sort_order ASC, name ASC").Preload("Parent").Find(&categories).Error
	return categories, err
}

// GetFeatured retrieves featured categories
func (r *templateCategoryRepository) GetFeatured(ctx context.Context) ([]*models.TemplateCategory, error) {
	var categories []*models.TemplateCategory
	err := r.db.WithContext(ctx).Where("is_featured = ? AND status = ?", true, models.CategoryStatusActive).Order("sort_order ASC, name ASC").Find(&categories).Error
	return categories, err
}

// GetWithChildren retrieves a category with its children
func (r *templateCategoryRepository) GetWithChildren(ctx context.Context, id uint) (*models.TemplateCategory, error) {
	var category models.TemplateCategory
	err := r.db.WithContext(ctx).Preload("Children", "status = ?", models.CategoryStatusActive).First(&category, id).Error
	if err != nil {
		return nil, err
	}
	return &category, nil
}

// GetWithParent retrieves a category with its parent
func (r *templateCategoryRepository) GetWithParent(ctx context.Context, id uint) (*models.TemplateCategory, error) {
	var category models.TemplateCategory
	err := r.db.WithContext(ctx).Preload("Parent").First(&category, id).Error
	if err != nil {
		return nil, err
	}
	return &category, nil
}

// GetWithTemplates retrieves a category with its templates
func (r *templateCategoryRepository) GetWithTemplates(ctx context.Context, id uint) (*models.TemplateCategory, error) {
	var category models.TemplateCategory
	err := r.db.WithContext(ctx).Preload("Templates", "status = ?", models.TemplateStatusPublished).First(&category, id).Error
	if err != nil {
		return nil, err
	}
	return &category, nil
}

// UpdateTemplateCount updates the template count for a category
func (r *templateCategoryRepository) UpdateTemplateCount(ctx context.Context, id uint, count int) error {
	return r.db.WithContext(ctx).Model(&models.TemplateCategory{}).Where("id = ?", id).Update("template_count", count).Error
}

// IncrementTemplateCount increments template count
func (r *templateCategoryRepository) IncrementTemplateCount(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&models.TemplateCategory{}).Where("id = ?", id).UpdateColumn("template_count", gorm.Expr("template_count + 1")).Error
}

// DecrementTemplateCount decrements template count
func (r *templateCategoryRepository) DecrementTemplateCount(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&models.TemplateCategory{}).Where("id = ? AND template_count > 0", id).UpdateColumn("template_count", gorm.Expr("template_count - 1")).Error
}

// CheckSlugExists checks if a slug already exists
func (r *templateCategoryRepository) CheckSlugExists(ctx context.Context, slug string, excludeID uint) (bool, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&models.TemplateCategory{}).Where("slug = ?", slug)
	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}
	err := query.Count(&count).Error
	return count > 0, err
}

// UpdateStatus updates category status
func (r *templateCategoryRepository) UpdateStatus(ctx context.Context, id uint, status models.CategoryStatus) error {
	updates := map[string]interface{}{"status": status}
	if status == models.CategoryStatusActive {
		updates["is_active"] = true
	} else {
		updates["is_active"] = false
	}
	return r.db.WithContext(ctx).Model(&models.TemplateCategory{}).Where("id = ?", id).Updates(updates).Error
}

// GetStats retrieves category statistics
func (r *templateCategoryRepository) GetStats(ctx context.Context) (*models.CategoryStats, error) {
	stats := &models.CategoryStats{
		ByLevel:  make(map[uint]int64),
		ByStatus: make(map[string]int64),
	}

	// Total categories
	r.db.WithContext(ctx).Model(&models.TemplateCategory{}).Where("status != ?", models.CategoryStatusDeleted).Count(&stats.TotalCategories)

	// Active categories
	r.db.WithContext(ctx).Model(&models.TemplateCategory{}).Where("status = ?", models.CategoryStatusActive).Count(&stats.ActiveCategories)

	// Featured categories
	r.db.WithContext(ctx).Model(&models.TemplateCategory{}).Where("is_featured = ? AND status != ?", true, models.CategoryStatusDeleted).Count(&stats.FeaturedCategories)

	// Main categories
	r.db.WithContext(ctx).Model(&models.TemplateCategory{}).Where("level = 0 AND status != ?", models.CategoryStatusDeleted).Count(&stats.MainCategories)

	// Subcategories
	r.db.WithContext(ctx).Model(&models.TemplateCategory{}).Where("level > 0 AND status != ?", models.CategoryStatusDeleted).Count(&stats.Subcategories)

	// By level
	var levelStats []struct {
		Level uint  `json:"level"`
		Count int64 `json:"count"`
	}
	r.db.WithContext(ctx).Model(&models.TemplateCategory{}).Select("level, count(*) as count").Where("status != ?", models.CategoryStatusDeleted).Group("level").Find(&levelStats)
	for _, stat := range levelStats {
		stats.ByLevel[stat.Level] = stat.Count
	}

	// By status
	var statusStats []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}
	r.db.WithContext(ctx).Model(&models.TemplateCategory{}).Select("status, count(*) as count").Where("status != ?", models.CategoryStatusDeleted).Group("status").Find(&statusStats)
	for _, stat := range statusStats {
		stats.ByStatus[stat.Status] = stat.Count
	}

	return stats, nil
}

// MoveCategoryToParent moves a category to a new parent
func (r *templateCategoryRepository) MoveCategoryToParent(ctx context.Context, categoryID uint, newParentID *uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var category models.TemplateCategory
		if err := tx.First(&category, categoryID).Error; err != nil {
			return err
		}

		// Update parent
		category.ParentID = newParentID

		// Update path and level
		if newParentID == nil {
			category.UpdatePath("/")
		} else {
			var parent models.TemplateCategory
			if err := tx.First(&parent, *newParentID).Error; err != nil {
				return err
			}
			category.UpdatePath(parent.Path)
		}

		return tx.Save(&category).Error
	})
}

// GetCategoryPath retrieves the path from root to category
func (r *templateCategoryRepository) GetCategoryPath(ctx context.Context, id uint) ([]*models.TemplateCategory, error) {
	var path []*models.TemplateCategory
	var category models.TemplateCategory

	if err := r.db.WithContext(ctx).First(&category, id).Error; err != nil {
		return nil, err
	}

	// Build path from current category up to root
	current := &category
	for current != nil {
		path = append([]*models.TemplateCategory{current}, path...)
		if current.ParentID == nil {
			break
		}

		var parent models.TemplateCategory
		if err := r.db.WithContext(ctx).First(&parent, *current.ParentID).Error; err != nil {
			break
		}
		current = &parent
	}

	return path, nil
}

// applyFilters applies filters to the GORM query
func (r *templateCategoryRepository) applyFilters(query *gorm.DB, filter models.CategoryFilter) *gorm.DB {
	if filter.ParentID != nil {
		query = query.Where("parent_id = ?", *filter.ParentID)
	}

	if filter.Level != nil {
		query = query.Where("level = ?", *filter.Level)
	}

	if filter.IsActive != nil {
		query = query.Where("is_active = ?", *filter.IsActive)
	}

	if filter.IsFeatured != nil {
		query = query.Where("is_featured = ?", *filter.IsFeatured)
	}

	if filter.Status != nil {
		query = query.Where("status = ?", *filter.Status)
	} else {
		// Default: exclude deleted categories
		query = query.Where("status != ?", models.CategoryStatusDeleted)
	}

	if filter.Search != nil && *filter.Search != "" {
		searchTerm := "%" + *filter.Search + "%"
		query = query.Where("name LIKE ? OR description LIKE ?", searchTerm, searchTerm)
	}

	return query
}

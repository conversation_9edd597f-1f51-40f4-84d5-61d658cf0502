package mysql

import (
	"context"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// websiteTemplateRepository implements WebsiteTemplateRepository using MySQL/GORM
type websiteTemplateRepository struct {
	db *gorm.DB
}

// NewWebsiteTemplateRepository creates a new website template repository
func NewWebsiteTemplateRepository(db *gorm.DB) repositories.WebsiteTemplateRepository {
	return &websiteTemplateRepository{db: db}
}

// Create creates a new website template
func (r *websiteTemplateRepository) Create(ctx context.Context, template *models.WebsiteTemplate) error {
	return r.db.WithContext(ctx).Create(template).Error
}

// GetByID retrieves a template by ID
func (r *websiteTemplateRepository) GetByID(ctx context.Context, id uint) (*models.WebsiteTemplate, error) {
	var template models.WebsiteTemplate
	err := r.db.WithContext(ctx).First(&template, id).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

// GetBySlug retrieves a template by slug
func (r *websiteTemplateRepository) GetBySlug(ctx context.Context, slug string) (*models.WebsiteTemplate, error) {
	var template models.WebsiteTemplate
	err := r.db.WithContext(ctx).Where("slug = ?", slug).First(&template).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

// Update updates a template
func (r *websiteTemplateRepository) Update(ctx context.Context, id uint, updates map[string]interface{}) error {
	return r.db.WithContext(ctx).Model(&models.WebsiteTemplate{}).Where("id = ?", id).Updates(updates).Error
}

// Delete soft deletes a template (sets status to deleted)
func (r *websiteTemplateRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&models.WebsiteTemplate{}).Where("id = ?", id).Update("status", models.TemplateStatusDeleted).Error
}

// List retrieves templates based on filter
func (r *websiteTemplateRepository) List(ctx context.Context, filter models.TemplateFilter) ([]*models.WebsiteTemplate, int64, error) {
	var templates []*models.WebsiteTemplate
	var total int64

	query := r.db.WithContext(ctx).Model(&models.WebsiteTemplate{})

	// Apply filters
	query = r.applyFilters(query, filter)

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	if filter.SortBy != "" {
		order := filter.SortBy
		if filter.SortOrder == "desc" {
			order += " DESC"
		} else {
			order += " ASC"
		}
		query = query.Order(order)
	} else {
		query = query.Order("created_at DESC")
	}

	// Apply pagination
	if filter.Limit > 0 {
		query = query.Limit(filter.Limit)
	}
	if filter.Offset > 0 {
		query = query.Offset(filter.Offset)
	}

	err := query.Preload("Category").Find(&templates).Error
	return templates, total, err
}

// GetByCategory retrieves templates by category
func (r *websiteTemplateRepository) GetByCategory(ctx context.Context, categoryID uint) ([]*models.WebsiteTemplate, error) {
	var templates []*models.WebsiteTemplate
	err := r.db.WithContext(ctx).Where("category_id = ? AND status = ?", categoryID, models.TemplateStatusPublished).Find(&templates).Error
	return templates, err
}

// GetByType retrieves templates by type
func (r *websiteTemplateRepository) GetByType(ctx context.Context, templateType models.TemplateType) ([]*models.WebsiteTemplate, error) {
	var templates []*models.WebsiteTemplate
	err := r.db.WithContext(ctx).Where("template_type = ? AND status = ?", templateType, models.TemplateStatusPublished).Find(&templates).Error
	return templates, err
}

// GetFeatured retrieves featured templates
func (r *websiteTemplateRepository) GetFeatured(ctx context.Context, limit int) ([]*models.WebsiteTemplate, error) {
	var templates []*models.WebsiteTemplate
	query := r.db.WithContext(ctx).Where("is_featured = ? AND status = ?", true, models.TemplateStatusPublished).Order("created_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	err := query.Preload("Category").Find(&templates).Error
	return templates, err
}

// GetPopular retrieves popular templates (by usage count)
func (r *websiteTemplateRepository) GetPopular(ctx context.Context, limit int) ([]*models.WebsiteTemplate, error) {
	var templates []*models.WebsiteTemplate
	query := r.db.WithContext(ctx).Where("status = ?", models.TemplateStatusPublished).Order("usage_count DESC, rating_average DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	err := query.Preload("Category").Find(&templates).Error
	return templates, err
}

// GetPublished retrieves all published templates
func (r *websiteTemplateRepository) GetPublished(ctx context.Context) ([]*models.WebsiteTemplate, error) {
	var templates []*models.WebsiteTemplate
	err := r.db.WithContext(ctx).Where("status = ?", models.TemplateStatusPublished).Find(&templates).Error
	return templates, err
}

// Search searches templates by name, description, tags
func (r *websiteTemplateRepository) Search(ctx context.Context, query string, limit int) ([]*models.WebsiteTemplate, error) {
	var templates []*models.WebsiteTemplate
	searchQuery := "%" + query + "%"

	dbQuery := r.db.WithContext(ctx).Where(
		"(name LIKE ? OR description LIKE ? OR industry LIKE ? OR JSON_SEARCH(tags, 'one', ?) IS NOT NULL) AND status = ?",
		searchQuery, searchQuery, searchQuery, query, models.TemplateStatusPublished,
	).Order("rating_average DESC, usage_count DESC")

	if limit > 0 {
		dbQuery = dbQuery.Limit(limit)
	}

	err := dbQuery.Preload("Category").Find(&templates).Error
	return templates, err
}

// UpdateUsage increments usage count
func (r *websiteTemplateRepository) UpdateUsage(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&models.WebsiteTemplate{}).Where("id = ?", id).UpdateColumn("usage_count", gorm.Expr("usage_count + 1")).Error
}

// UpdateDownloadCount increments download count
func (r *websiteTemplateRepository) UpdateDownloadCount(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&models.WebsiteTemplate{}).Where("id = ?", id).UpdateColumn("download_count", gorm.Expr("download_count + 1")).Error
}

// UpdateRating updates template rating
func (r *websiteTemplateRepository) UpdateRating(ctx context.Context, id uint, rating float64) error {
	var template models.WebsiteTemplate
	if err := r.db.WithContext(ctx).First(&template, id).Error; err != nil {
		return err
	}

	template.UpdateRating(rating)
	return r.db.WithContext(ctx).Save(&template).Error
}

// GetStats retrieves template statistics
func (r *websiteTemplateRepository) GetStats(ctx context.Context) (*models.TemplateStats, error) {
	stats := &models.TemplateStats{
		ByType:     make(map[string]int64),
		ByIndustry: make(map[string]int64),
		ByStatus:   make(map[string]int64),
	}

	// Total templates
	r.db.WithContext(ctx).Model(&models.WebsiteTemplate{}).Where("status != ?", models.TemplateStatusDeleted).Count(&stats.TotalTemplates)

	// Published templates
	r.db.WithContext(ctx).Model(&models.WebsiteTemplate{}).Where("status = ?", models.TemplateStatusPublished).Count(&stats.PublishedTemplates)

	// Premium templates
	r.db.WithContext(ctx).Model(&models.WebsiteTemplate{}).Where("is_premium = ? AND status != ?", true, models.TemplateStatusDeleted).Count(&stats.PremiumTemplates)

	// Featured templates
	r.db.WithContext(ctx).Model(&models.WebsiteTemplate{}).Where("is_featured = ? AND status != ?", true, models.TemplateStatusDeleted).Count(&stats.FeaturedTemplates)

	// By type
	var typeStats []struct {
		TemplateType string `json:"template_type"`
		Count        int64  `json:"count"`
	}
	r.db.WithContext(ctx).Model(&models.WebsiteTemplate{}).Select("template_type, count(*) as count").Where("status != ?", models.TemplateStatusDeleted).Group("template_type").Find(&typeStats)
	for _, stat := range typeStats {
		stats.ByType[stat.TemplateType] = stat.Count
	}

	// By industry
	var industryStats []struct {
		Industry string `json:"industry"`
		Count    int64  `json:"count"`
	}
	r.db.WithContext(ctx).Model(&models.WebsiteTemplate{}).Select("industry, count(*) as count").Where("industry != '' AND status != ?", models.TemplateStatusDeleted).Group("industry").Find(&industryStats)
	for _, stat := range industryStats {
		stats.ByIndustry[stat.Industry] = stat.Count
	}

	// By status
	var statusStats []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}
	r.db.WithContext(ctx).Model(&models.WebsiteTemplate{}).Select("status, count(*) as count").Where("status != ?", models.TemplateStatusDeleted).Group("status").Find(&statusStats)
	for _, stat := range statusStats {
		stats.ByStatus[stat.Status] = stat.Count
	}

	// Average rating
	r.db.WithContext(ctx).Model(&models.WebsiteTemplate{}).Where("rating_count > 0 AND status != ?", models.TemplateStatusDeleted).Select("AVG(rating_average)").Scan(&stats.AverageRating)

	// Total downloads and usage
	r.db.WithContext(ctx).Model(&models.WebsiteTemplate{}).Where("status != ?", models.TemplateStatusDeleted).Select("SUM(download_count)").Scan(&stats.TotalDownloads)
	r.db.WithContext(ctx).Model(&models.WebsiteTemplate{}).Where("status != ?", models.TemplateStatusDeleted).Select("SUM(usage_count)").Scan(&stats.TotalUsage)

	return stats, nil
}

// CheckSlugExists checks if a slug already exists
func (r *websiteTemplateRepository) CheckSlugExists(ctx context.Context, slug string, excludeID uint) (bool, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&models.WebsiteTemplate{}).Where("slug = ?", slug)
	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}
	err := query.Count(&count).Error
	return count > 0, err
}

// UpdateStatus updates template status
func (r *websiteTemplateRepository) UpdateStatus(ctx context.Context, id uint, status models.TemplateStatus) error {
	updates := map[string]interface{}{"status": status}
	if status == models.TemplateStatusPublished {
		updates["is_published"] = true
	}
	return r.db.WithContext(ctx).Model(&models.WebsiteTemplate{}).Where("id = ?", id).Updates(updates).Error
}

// GetWithCategory retrieves a template with its category
func (r *websiteTemplateRepository) GetWithCategory(ctx context.Context, id uint) (*models.WebsiteTemplate, error) {
	var template models.WebsiteTemplate
	err := r.db.WithContext(ctx).Preload("Category").First(&template, id).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

// CountByCategory counts templates by category
func (r *websiteTemplateRepository) CountByCategory(ctx context.Context, categoryID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&models.WebsiteTemplate{}).Where("category_id = ? AND status != ?", categoryID, models.TemplateStatusDeleted).Count(&count).Error
	return count, err
}

// GetTemplatesByIndustry retrieves templates by industry
func (r *websiteTemplateRepository) GetTemplatesByIndustry(ctx context.Context, industry string) ([]*models.WebsiteTemplate, error) {
	var templates []*models.WebsiteTemplate
	err := r.db.WithContext(ctx).Where("industry = ? AND status = ?", industry, models.TemplateStatusPublished).Preload("Category").Find(&templates).Error
	return templates, err
}

// applyFilters applies filters to the GORM query
func (r *websiteTemplateRepository) applyFilters(query *gorm.DB, filter models.TemplateFilter) *gorm.DB {
	if filter.CategoryID != nil {
		query = query.Where("category_id = ?", *filter.CategoryID)
	}

	if filter.TemplateType != nil {
		query = query.Where("template_type = ?", *filter.TemplateType)
	}

	if filter.Industry != nil {
		query = query.Where("industry = ?", *filter.Industry)
	}

	if filter.IsPremium != nil {
		query = query.Where("is_premium = ?", *filter.IsPremium)
	}

	if filter.IsFeatured != nil {
		query = query.Where("is_featured = ?", *filter.IsFeatured)
	}

	if filter.IsPublished != nil {
		query = query.Where("is_published = ?", *filter.IsPublished)
	}

	if filter.Status != nil {
		query = query.Where("status = ?", *filter.Status)
	} else {
		// Default: exclude deleted templates
		query = query.Where("status != ?", models.TemplateStatusDeleted)
	}

	if filter.Search != nil && *filter.Search != "" {
		searchTerm := "%" + *filter.Search + "%"
		query = query.Where("name LIKE ? OR description LIKE ? OR industry LIKE ?", searchTerm, searchTerm, searchTerm)
	}

	if len(filter.Tags) > 0 {
		for _, tag := range filter.Tags {
			query = query.Where("JSON_SEARCH(tags, 'one', ?) IS NOT NULL", tag)
		}
	}

	// Handle cursor-based pagination
	if filter.Cursor != "" {
		cursorData, err := pagination.ParseCursor(filter.Cursor)
		if err == nil {
			// Assume default sort is by created_at DESC, id DESC
			sortBy := filter.SortBy
			if sortBy == "" {
				sortBy = "created_at"
			}
			
			sortOrder := filter.SortOrder
			if sortOrder == "" || sortOrder != "asc" {
				sortOrder = "desc"
			}

			// Build cursor WHERE clause based on sort order
			if sortOrder == "desc" || sortOrder == "DESC" {
				if sortBy == "created_at" {
					query = query.Where("(created_at < ? OR (created_at = ? AND id < ?))", 
						cursorData.CreatedAt, cursorData.CreatedAt, cursorData.ID)
				} else {
					// For other sort fields, fall back to ID comparison
					query = query.Where("id < ?", cursorData.ID)
				}
			} else {
				if sortBy == "created_at" {
					query = query.Where("(created_at > ? OR (created_at = ? AND id > ?))", 
						cursorData.CreatedAt, cursorData.CreatedAt, cursorData.ID)
				} else {
					query = query.Where("id > ?", cursorData.ID)
				}
			}
		}
	}

	return query
}

package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"
)

// TemplateType represents the type of template
type TemplateType string

const (
	TemplateTypeBusiness   TemplateType = "business"
	TemplateTypePortfolio  TemplateType = "portfolio"
	TemplateTypeBlog       TemplateType = "blog"
	TemplateTypeEcommerce  TemplateType = "ecommerce"
	TemplateTypeLanding    TemplateType = "landing"
	TemplateTypePersonal   TemplateType = "personal"
	TemplateTypeNonprofit  TemplateType = "nonprofit"
	TemplateTypeEducation  TemplateType = "education"
	TemplateTypeEvent      TemplateType = "event"
	TemplateTypeRestaurant TemplateType = "restaurant"
)

// TemplateStatus represents the status of template
type TemplateStatus string

const (
	TemplateStatusDraft     TemplateStatus = "draft"
	TemplateStatusReview    TemplateStatus = "review"
	TemplateStatusPublished TemplateStatus = "published"
	TemplateStatusArchived  TemplateStatus = "archived"
	TemplateStatusDeleted   TemplateStatus = "deleted"
)

// TemplateData represents template configuration data
type TemplateData map[string]interface{}

// Scan implements sql.Scanner interface
func (td *TemplateData) Scan(value interface{}) error {
	if value == nil {
		*td = make(TemplateData)
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		*td = make(TemplateData)
		return nil
	}

	return json.Unmarshal(bytes, td)
}

// Value implements driver.Valuer interface
func (td TemplateData) Value() (driver.Value, error) {
	if td == nil {
		return "{}", nil
	}
	return json.Marshal(td)
}

// TemplateSections represents template sections data
type TemplateSections []map[string]interface{}

// Scan implements sql.Scanner interface
func (ts *TemplateSections) Scan(value interface{}) error {
	if value == nil {
		*ts = make(TemplateSections, 0)
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		*ts = make(TemplateSections, 0)
		return nil
	}

	return json.Unmarshal(bytes, ts)
}

// Value implements driver.Valuer interface
func (ts TemplateSections) Value() (driver.Value, error) {
	if ts == nil {
		return "[]", nil
	}
	return json.Marshal(ts)
}

// TemplateStyles represents template styles data
type TemplateStyles map[string]interface{}

// Scan implements sql.Scanner interface
func (ts *TemplateStyles) Scan(value interface{}) error {
	if value == nil {
		*ts = make(TemplateStyles)
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		*ts = make(TemplateStyles)
		return nil
	}

	return json.Unmarshal(bytes, ts)
}

// Value implements driver.Valuer interface
func (ts TemplateStyles) Value() (driver.Value, error) {
	if ts == nil {
		return "{}", nil
	}
	return json.Marshal(ts)
}

// TemplateSettings represents template settings data
type TemplateSettings map[string]interface{}

// Scan implements sql.Scanner interface
func (ts *TemplateSettings) Scan(value interface{}) error {
	if value == nil {
		*ts = make(TemplateSettings)
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		*ts = make(TemplateSettings)
		return nil
	}

	return json.Unmarshal(bytes, ts)
}

// Value implements driver.Valuer interface
func (ts TemplateSettings) Value() (driver.Value, error) {
	if ts == nil {
		return "{}", nil
	}
	return json.Marshal(ts)
}

// TemplateTags represents template tags
type TemplateTags []string

// Scan implements sql.Scanner interface
func (tt *TemplateTags) Scan(value interface{}) error {
	if value == nil {
		*tt = make(TemplateTags, 0)
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		*tt = make(TemplateTags, 0)
		return nil
	}

	return json.Unmarshal(bytes, tt)
}

// Value implements driver.Valuer interface
func (tt TemplateTags) Value() (driver.Value, error) {
	if tt == nil {
		return "[]", nil
	}
	return json.Marshal(tt)
}

// WebsiteTemplate represents a website template
type WebsiteTemplate struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	Name        string `json:"name" gorm:"not null" validate:"required,min=1,max=255"`
	Slug        string `json:"slug" gorm:"uniqueIndex;not null" validate:"required,min=1,max=255"`
	Description string `json:"description,omitempty"`

	// Template Content
	PreviewImageURL string           `json:"preview_image_url,omitempty"`
	TemplateData    TemplateData     `json:"template_data,omitempty" gorm:"type:json"`
	Sections        TemplateSections `json:"sections,omitempty" gorm:"type:json"`
	Styles          TemplateStyles   `json:"styles,omitempty" gorm:"type:json"`
	Settings        TemplateSettings `json:"settings,omitempty" gorm:"type:json"`

	// Categorization
	CategoryID *uint             `json:"category_id,omitempty"`
	Category   *TemplateCategory `json:"category,omitempty" gorm:"foreignKey:CategoryID"`
	Tags       TemplateTags      `json:"tags,omitempty" gorm:"type:json"`

	// Template Properties
	IsPremium   bool   `json:"is_premium" gorm:"default:false"`
	IsPublished bool   `json:"is_published" gorm:"default:false"`
	IsFeatured  bool   `json:"is_featured" gorm:"default:false"`
	Version     string `json:"version" gorm:"default:'1.0.0'"`

	// Usage Statistics
	DownloadCount uint    `json:"download_count" gorm:"default:0"`
	UsageCount    uint    `json:"usage_count" gorm:"default:0"`
	RatingAverage float64 `json:"rating_average" gorm:"type:decimal(3,2);default:0.00"`
	RatingCount   uint    `json:"rating_count" gorm:"default:0"`

	// Template Type and Industry
	TemplateType TemplateType `json:"template_type" gorm:"type:enum('business','portfolio','blog','ecommerce','landing','personal','nonprofit','education','event','restaurant');default:'business'"`
	Industry     string       `json:"industry,omitempty"`

	// Status and Lifecycle
	Status      TemplateStatus `json:"status" gorm:"type:enum('draft','review','published','archived','deleted');default:'draft'"`
	PublishedAt *time.Time     `json:"published_at,omitempty"`
	ArchivedAt  *time.Time     `json:"archived_at,omitempty"`

	// Timestamps
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName sets the table name for WebsiteTemplate
func (WebsiteTemplate) TableName() string {
	return "website_templates"
}

// IsActive returns true if the template is active/published
func (wt *WebsiteTemplate) IsActive() bool {
	return wt.Status == TemplateStatusPublished
}

// IsAvailable returns true if the template is available for use
func (wt *WebsiteTemplate) IsAvailable() bool {
	return wt.Status == TemplateStatusPublished && wt.IsPublished
}

// GetTemplateType returns the template type as string
func (wt *WebsiteTemplate) GetTemplateType() string {
	return string(wt.TemplateType)
}

// GetStatus returns the template status as string
func (wt *WebsiteTemplate) GetStatus() string {
	return string(wt.Status)
}

// MarkAsPublished marks the template as published
func (wt *WebsiteTemplate) MarkAsPublished() {
	wt.Status = TemplateStatusPublished
	wt.IsPublished = true
	if wt.PublishedAt == nil {
		now := time.Now()
		wt.PublishedAt = &now
	}
}

// MarkAsArchived marks the template as archived
func (wt *WebsiteTemplate) MarkAsArchived() {
	wt.Status = TemplateStatusArchived
	if wt.ArchivedAt == nil {
		now := time.Now()
		wt.ArchivedAt = &now
	}
}

// IncrementDownloadCount increments the download count
func (wt *WebsiteTemplate) IncrementDownloadCount() {
	wt.DownloadCount++
}

// IncrementUsageCount increments the usage count
func (wt *WebsiteTemplate) IncrementUsageCount() {
	wt.UsageCount++
}

// UpdateRating updates the template rating
func (wt *WebsiteTemplate) UpdateRating(newRating float64) {
	if wt.RatingCount == 0 {
		wt.RatingAverage = newRating
		wt.RatingCount = 1
	} else {
		totalRating := wt.RatingAverage * float64(wt.RatingCount)
		wt.RatingCount++
		wt.RatingAverage = (totalRating + newRating) / float64(wt.RatingCount)
	}
}

package models

// TemplateFilter represents filters for template queries
type TemplateFilter struct {
	CategoryID   *uint
	TemplateType *TemplateType
	Industry     *string
	IsPremium    *bool
	IsFeatured   *bool
	IsPublished  *bool
	Status       *TemplateStatus
	Search       *string
	Tags         []string

	// Sorting
	SortBy    string
	SortOrder string

	// Cursor-based pagination
	Cursor string
	Limit  int
	
	// Offset-based pagination (for backward compatibility)
	Offset int
}

// CategoryFilter represents filters for category queries
type CategoryFilter struct {
	ParentID   *uint
	Level      *uint
	IsActive   *bool
	IsFeatured *bool
	Status     *CategoryStatus
	Search     *string

	// Sorting
	SortBy    string
	SortOrder string

	// Pagination
	Offset int
	Limit  int
}

// TemplateStats represents template statistics
type TemplateStats struct {
	TotalTemplates     int64            `json:"total_templates"`
	PublishedTemplates int64            `json:"published_templates"`
	PremiumTemplates   int64            `json:"premium_templates"`
	FeaturedTemplates  int64            `json:"featured_templates"`
	ByType             map[string]int64 `json:"by_type"`
	ByIndustry         map[string]int64 `json:"by_industry"`
	ByStatus           map[string]int64 `json:"by_status"`
	AverageRating      float64          `json:"average_rating"`
	TotalDownloads     int64            `json:"total_downloads"`
	TotalUsage         int64            `json:"total_usage"`
}

// CategoryStats represents category statistics
type CategoryStats struct {
	TotalCategories    int64            `json:"total_categories"`
	ActiveCategories   int64            `json:"active_categories"`
	FeaturedCategories int64            `json:"featured_categories"`
	MainCategories     int64            `json:"main_categories"`
	Subcategories      int64            `json:"subcategories"`
	ByLevel            map[uint]int64   `json:"by_level"`
	ByStatus           map[string]int64 `json:"by_status"`
}

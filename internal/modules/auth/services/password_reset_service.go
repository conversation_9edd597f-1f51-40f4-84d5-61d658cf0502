package services

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"

	authModels "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	authRepositories "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories"
	notificationModels "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	notificationRepositories "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/repositories"
	notificationServices "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	userModels "github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	userRepositories "github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
	websiteModels "github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
	websiteServices "github.com/tranthanhloi/wn-api-v3/internal/modules/website/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

// PasswordResetConfig holds configuration for password reset
type PasswordResetConfig struct {
	TokenExpiryHours     int    `json:"token_expiry_hours"`     // Default: 1
	MaxRequestsPerHour   int    `json:"max_requests_per_hour"`  // Default: 3
	MaxRequestsPerIP     int    `json:"max_requests_per_ip"`    // Default: 10
	BaseResetURL         string `json:"base_reset_url"`         // Frontend URL for reset
	CleanupIntervalHours int    `json:"cleanup_interval_hours"` // Default: 24
}

// DefaultPasswordResetConfig returns default configuration
func DefaultPasswordResetConfig() *PasswordResetConfig {
	return &PasswordResetConfig{
		TokenExpiryHours:     1,  // 1 hour expiry for security
		MaxRequestsPerHour:   3,  // Max 3 requests per hour per user
		MaxRequestsPerIP:     10, // Max 10 requests per hour per IP
		BaseResetURL:         "http://localhost:9078/auth/reset-password",
		CleanupIntervalHours: 24, // Cleanup expired tokens every 24 hours
	}
}

// PasswordResetService interface defines methods for password reset operations
type PasswordResetService interface {
	// Password reset flow
	CreatePasswordResetToken(ctx context.Context, tenantID uint, websiteID uint, email string, userAgent *string, ipAddress *string) error
	ValidateAndResetPassword(ctx context.Context, token string, newPassword string) (*authModels.PasswordReset, error)

	// Token management
	GetTokenByID(ctx context.Context, id uint) (*authModels.PasswordReset, error)
	GetActiveTokenByUser(ctx context.Context, userID uint) (*authModels.PasswordReset, error)
	InvalidateUserTokens(ctx context.Context, userID uint) error

	// Rate limiting and security
	CanRequestReset(ctx context.Context, email string) (bool, string, error)
	CanRequestResetFromIP(ctx context.Context, ipAddress string) (bool, string, error)

	// Cleanup and maintenance
	CleanupExpiredTokens(ctx context.Context) (int64, error)

	// Configuration
	UpdateConfig(config *PasswordResetConfig)
	GetConfig() *PasswordResetConfig
}

// passwordResetService implements PasswordResetService
type passwordResetService struct {
	passwordResetRepo   authRepositories.PasswordResetRepository
	userRepo            userRepositories.UserRepository
	passwordService     PasswordService
	notificationService notificationServices.NotificationService
	templateRepo        notificationRepositories.TemplateRepository
	websiteService      websiteServices.WebsiteService
	config              *PasswordResetConfig
	logger              utils.Logger
}

// NewPasswordResetService creates a new password reset service
func NewPasswordResetService(
	passwordResetRepo authRepositories.PasswordResetRepository,
	userRepo userRepositories.UserRepository,
	passwordService PasswordService,
	notificationService notificationServices.NotificationService,
	templateRepo notificationRepositories.TemplateRepository,
	websiteService websiteServices.WebsiteService,
	logger utils.Logger,
) PasswordResetService {
	return &passwordResetService{
		passwordResetRepo:   passwordResetRepo,
		userRepo:            userRepo,
		passwordService:     passwordService,
		notificationService: notificationService,
		templateRepo:        templateRepo,
		websiteService:      websiteService,
		config:              DefaultPasswordResetConfig(),
		logger:              logger,
	}
}

// CreatePasswordResetToken creates a new password reset token and sends email
func (s *passwordResetService) CreatePasswordResetToken(ctx context.Context, tenantID uint, websiteID uint, email string, userAgent *string, ipAddress *string) error {
	// Check rate limiting for email
	if canRequest, reason, err := s.CanRequestReset(ctx, email); err != nil {
		s.logger.WithError(err).WithField("email", email).Error("Failed to check rate limiting for email")
		return fmt.Errorf("failed to check rate limiting")
	} else if !canRequest {
		s.logger.WithFields(map[string]interface{}{
			"email":  email,
			"reason": reason,
		}).Warn("Password reset rate limited for email")
		return fmt.Errorf(reason)
	}

	// Check rate limiting for IP address
	if ipAddress != nil {
		if canRequest, reason, err := s.CanRequestResetFromIP(ctx, *ipAddress); err != nil {
			s.logger.WithError(err).WithField("ip_address", *ipAddress).Error("Failed to check rate limiting for IP")
			return fmt.Errorf("failed to check rate limiting")
		} else if !canRequest {
			s.logger.WithFields(map[string]interface{}{
				"ip_address": *ipAddress,
				"reason":     reason,
			}).Warn("Password reset rate limited for IP")
			return fmt.Errorf(reason)
		}
	}

	// Get user by email - this is where we might reveal if user exists
	// For security, we should always return success even if user doesn't exist
	user, err := s.userRepo.GetByEmail(ctx, email)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// User doesn't exist - log it but return success to prevent user enumeration
			s.logger.WithField("email", email).Info("Password reset requested for non-existent user")
			return nil // Return success to prevent user enumeration
		}
		s.logger.WithError(err).WithField("email", email).Error("Failed to get user by email")
		return fmt.Errorf("failed to process password reset request")
	}

	// Check if user account is active
	if user.Status != userModels.UserStatusActive {
		s.logger.WithFields(map[string]interface{}{
			"user_id": user.ID,
			"email":   email,
			"status":  user.Status,
		}).Warn("Password reset requested for non-active user")
		// Return success to prevent user enumeration, but don't send email
		return nil
	}

	// Invalidate any existing tokens for this user
	if err := s.passwordResetRepo.InvalidateAllUserTokens(ctx, user.ID); err != nil {
		s.logger.WithError(err).WithField("user_id", user.ID).Warn("Failed to invalidate existing tokens")
	}

	// Generate secure token
	tokenBytes := make([]byte, 32)
	if _, err := rand.Read(tokenBytes); err != nil {
		s.logger.WithError(err).Error("Failed to generate secure token")
		return fmt.Errorf("failed to generate reset token")
	}
	token := hex.EncodeToString(tokenBytes)

	// Create password reset record
	passwordReset := &authModels.PasswordReset{
		TenantID:  tenantID,
		WebsiteID: websiteID,
		UserID:    user.ID,
		Token:     token,
		Email:     email,
		UserAgent: userAgent,
		IPAddress: ipAddress,
		ExpiresAt: time.Now().Add(time.Duration(s.config.TokenExpiryHours) * time.Hour),
	}

	if err := s.passwordResetRepo.Create(ctx, passwordReset); err != nil {
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": user.ID,
			"email":   email,
		}).Error("Failed to create password reset record")
		return fmt.Errorf("failed to create password reset token")
	}

	// Send password reset email
	if err := s.sendPasswordResetEmail(ctx, tenantID, passwordReset, user); err != nil {
		s.logger.WithError(err).WithField("reset_id", passwordReset.ID).Error("Failed to send password reset email")
		// Don't return error - token was created successfully
	}

	s.logger.WithFields(map[string]interface{}{
		"reset_id": passwordReset.ID,
		"user_id":  user.ID,
		"email":    email,
	}).Info("Created password reset token")

	return nil
}

// ValidateAndResetPassword validates token and resets password
func (s *passwordResetService) ValidateAndResetPassword(ctx context.Context, token string, newPassword string) (*authModels.PasswordReset, error) {
	// Get password reset by token
	passwordReset, err := s.passwordResetRepo.GetByToken(ctx, token)
	if err != nil {
		s.logger.WithError(err).WithField("token", token[:10]+"...").Error("Failed to get password reset by token")
		return nil, fmt.Errorf("failed to validate reset token")
	}

	if passwordReset == nil {
		s.logger.WithField("token", token[:10]+"...").Warn("Invalid or expired password reset token")
		return nil, fmt.Errorf("invalid or expired reset token")
	}

	// Check if token is valid
	if !passwordReset.IsValid() {
		s.logger.WithFields(map[string]interface{}{
			"reset_id":   passwordReset.ID,
			"is_used":    passwordReset.IsUsed,
			"expires_at": passwordReset.ExpiresAt,
		}).Warn("Password reset token is not valid")
		return nil, fmt.Errorf("invalid or expired reset token")
	}

	// Get user
	user, err := s.userRepo.GetByID(ctx, passwordReset.UserID)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", passwordReset.UserID).Error("Failed to get user for password reset")
		return nil, fmt.Errorf("user not found")
	}

	// Validate new password
	if err := s.passwordService.ValidatePassword(newPassword); err != nil {
		s.logger.WithError(err).WithField("user_id", user.ID).Warn("Invalid new password")
		return nil, fmt.Errorf("password validation failed: %w", err)
	}

	// Hash new password
	hashedPassword, err := s.passwordService.HashPassword(newPassword)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", user.ID).Error("Failed to hash new password")
		return nil, fmt.Errorf("failed to process new password")
	}

	// Update user password
	user.PasswordHash = hashedPassword
	if err := s.userRepo.Update(ctx, user); err != nil {
		s.logger.WithError(err).WithField("user_id", user.ID).Error("Failed to update user password")
		return nil, fmt.Errorf("failed to update password")
	}

	// Mark token as used
	passwordReset.MarkAsUsed()
	if err := s.passwordResetRepo.Update(ctx, passwordReset); err != nil {
		s.logger.WithError(err).WithField("reset_id", passwordReset.ID).Error("Failed to mark token as used")
		// Don't return error - password was updated successfully
	}

	// Invalidate all other tokens for this user
	if err := s.passwordResetRepo.InvalidateAllUserTokens(ctx, user.ID); err != nil {
		s.logger.WithError(err).WithField("user_id", user.ID).Warn("Failed to invalidate other tokens")
	}

	s.logger.WithFields(map[string]interface{}{
		"user_id":  user.ID,
		"reset_id": passwordReset.ID,
	}).Info("Password reset completed successfully")

	return passwordReset, nil
}

// sendPasswordResetEmail sends the password reset email using notification service
func (s *passwordResetService) sendPasswordResetEmail(ctx context.Context, tenantID uint, passwordReset *authModels.PasswordReset, user *userModels.User) error {
	// Get domain from tenant ID
	baseURL, err := s.getDomainFromTenantID(ctx, tenantID)
	if err != nil {
		s.logger.WithError(err).WithField("tenant_id", tenantID).Warn("Failed to get domain from tenant ID, using default")
		baseURL = s.config.BaseResetURL
	}

	// Generate reset URL
	resetURL := fmt.Sprintf("%s?token=%s", baseURL, passwordReset.Token)

	// Calculate expiry time in human-readable format
	expiryTime := fmt.Sprintf("%d hour", s.config.TokenExpiryHours)
	if s.config.TokenExpiryHours != 1 {
		expiryTime = fmt.Sprintf("%d hours", s.config.TokenExpiryHours)
	}
	if timeRemaining := time.Until(passwordReset.ExpiresAt); timeRemaining > 0 {
		if timeRemaining < time.Hour {
			expiryTime = fmt.Sprintf("%.0f minutes", timeRemaining.Minutes())
		}
	}

	// Create notification request
	notificationReq := notificationModels.CreateNotificationRequest{
		Type:    "password_reset",
		Channel: notificationModels.ChannelEmail,
		Subject: "Password Reset Request",
		Recipients: []notificationModels.CreateRecipientRequest{
			{
				RecipientType:    notificationModels.RecipientTypeUser,
				RecipientAddress: user.Email,
				UserID:           &user.ID,
			},
		},
		TemplateID: s.getTemplateIDByCode(tenantID, "password_reset_email"),
		TemplateData: map[string]interface{}{
			"user": map[string]interface{}{
				"name":  user.GetDisplayName(),
				"email": user.Email,
			},
			"reset_url":   resetURL,
			"expiry_time": expiryTime,
			"brand": map[string]interface{}{
				"name":          "BlogAPI",
				"support_email": "<EMAIL>",
				"logo_url":      "https://blogapi.com/logo.png",
			},
			"current_year": time.Now().Year(),
		},
		Priority: notificationModels.PriorityHigh,
	}

	// Create notification
	notification, err := s.notificationService.CreateNotification(tenantID, notificationReq)
	if err != nil {
		return fmt.Errorf("failed to create password reset email: %w", err)
	}

	// Send the email immediately
	if err := s.notificationService.SendNotification(tenantID, notification.ID); err != nil {
		return fmt.Errorf("failed to send password reset email: %w", err)
	}

	return nil
}

// GetTokenByID retrieves a password reset token by ID
func (s *passwordResetService) GetTokenByID(ctx context.Context, id uint) (*authModels.PasswordReset, error) {
	return s.passwordResetRepo.GetByID(ctx, id)
}

// GetActiveTokenByUser retrieves active password reset token for user
func (s *passwordResetService) GetActiveTokenByUser(ctx context.Context, userID uint) (*authModels.PasswordReset, error) {
	return s.passwordResetRepo.GetActiveTokenByUser(ctx, userID)
}

// InvalidateUserTokens invalidates all tokens for a user
func (s *passwordResetService) InvalidateUserTokens(ctx context.Context, userID uint) error {
	return s.passwordResetRepo.InvalidateAllUserTokens(ctx, userID)
}

// CanRequestReset checks if a user can request password reset (rate limiting)
func (s *passwordResetService) CanRequestReset(ctx context.Context, email string) (bool, string, error) {
	// Check recent requests for this email
	hasRecent, err := s.passwordResetRepo.HasRecentRequest(ctx, email, time.Hour)
	if err != nil {
		return false, "", fmt.Errorf("failed to check recent requests: %w", err)
	}

	if hasRecent {
		// Get the actual count to provide better error message
		// Note: We could get user by email first, but that would reveal if user exists
		// For now, just return generic rate limit message
		return false, fmt.Sprintf("please wait at least 1 hour before requesting another password reset"), nil
	}

	return true, "", nil
}

// CanRequestResetFromIP checks if an IP can request password reset (rate limiting)
func (s *passwordResetService) CanRequestResetFromIP(ctx context.Context, ipAddress string) (bool, string, error) {
	// Check recent requests from this IP
	requests, err := s.passwordResetRepo.GetRequestsByIP(ctx, ipAddress, time.Hour)
	if err != nil {
		return false, "", fmt.Errorf("failed to check IP requests: %w", err)
	}

	if len(requests) >= s.config.MaxRequestsPerIP {
		return false, fmt.Sprintf("too many password reset requests from this IP address, please try again later"), nil
	}

	return true, "", nil
}

// CleanupExpiredTokens removes expired tokens
func (s *passwordResetService) CleanupExpiredTokens(ctx context.Context) (int64, error) {
	return s.passwordResetRepo.CleanupExpiredTokens(ctx)
}

// UpdateConfig updates the service configuration
func (s *passwordResetService) UpdateConfig(config *PasswordResetConfig) {
	s.config = config
	s.logger.WithFields(map[string]interface{}{
		"token_expiry_hours":    config.TokenExpiryHours,
		"max_requests_per_hour": config.MaxRequestsPerHour,
		"max_requests_per_ip":   config.MaxRequestsPerIP,
	}).Info("Updated password reset configuration")
}

// GetConfig returns the current configuration
func (s *passwordResetService) GetConfig() *PasswordResetConfig {
	return s.config
}

// getTemplateIDByCode looks up template ID by code from database
func (s *passwordResetService) getTemplateIDByCode(tenantID uint, templateCode string) *uint {
	// Query the template repository to get template by code
	template, err := s.templateRepo.GetByCode(tenantID, templateCode)
	if err != nil {
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"tenant_id":     tenantID,
			"template_code": templateCode,
		}).Error("Failed to get template by code")
		return nil
	}

	if template == nil {
		s.logger.WithFields(map[string]interface{}{
			"tenant_id":     tenantID,
			"template_code": templateCode,
		}).Warn("Template not found")
		return nil
	}

	return &template.ID
}

// getDomainFromTenantID gets the domain/URL from tenant ID
func (s *passwordResetService) getDomainFromTenantID(ctx context.Context, tenantID uint) (string, error) {
	// Get the active website for the tenant
	filter := websiteModels.WebsiteFilter{
		Status: "active",
	}
	websiteListResponse, err := s.websiteService.ListWebsites(ctx, tenantID, filter)
	if err != nil {
		return "", fmt.Errorf("failed to get websites for tenant %d: %w", tenantID, err)
	}

	if len(websiteListResponse.Websites) == 0 {
		return "", fmt.Errorf("no websites found for tenant %d", tenantID)
	}

	// Get the first active website
	var activeWebsite *websiteModels.WebsiteResponse
	for i, website := range websiteListResponse.Websites {
		if website.Status == "active" {
			activeWebsite = &websiteListResponse.Websites[i]
			break
		}
	}

	if activeWebsite == nil {
		return "", fmt.Errorf("no active website found for tenant %d", tenantID)
	}

	// Build the reset URL
	var baseURL string
	if activeWebsite.Domain != "" {
		// Use custom domain
		baseURL = fmt.Sprintf("https://%s/reset-password", activeWebsite.Domain)
	} else if activeWebsite.Subdomain != "" {
		// Use subdomain with base domain
		baseURL = fmt.Sprintf("https://%s.example.com/reset-password", activeWebsite.Subdomain)
	} else {
		return "", fmt.Errorf("no domain or subdomain configured for tenant %d", tenantID)
	}

	// Check if we're in development mode
	if utils.GetEnv("APP_ENV", "production") == "development" {
		// In development, use localhost with appropriate port
		port := utils.GetEnv("FRONTEND_PORT", "9078")
		baseURL = fmt.Sprintf("http://localhost:%s/reset-password", port)
	}

	return baseURL, nil
}

package services

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/dto"
)

// Test simple OAuth routing logic without dependencies
func TestOAuthService_UnsupportedProvider(t *testing.T) {
	service := &OAuthService{}
	ctx := context.Background()

	t.Run("GetOAuthAuthURL with unsupported provider", func(t *testing.T) {
		req := &dto.OAuthAuthURLRequest{
			Provider: "twitter",
		}

		_, err := service.GetOAuthAuthURL(ctx, 1, req, "localhost:3000")
		assert.Error(t, err)
		assert.Contains(t, err.<PERSON>rror(), "unsupported OAuth provider: twitter")
	})

	t.Run("LoginWithOAuth with unsupported provider", func(t *testing.T) {
		req := &dto.OAuthLoginRequest{
			Provider: "twitter",
			Code:     "test-code",
		}

		_, err := service.LoginWithOAuth(ctx, 1, req, "localhost:3000")
		assert.Error(t, err)
		assert.Contains(t, err.<PERSON>(), "unsupported OAuth provider: twitter")
	})
}

// Test utility functions that don't require dependencies
func TestGenerateRandomString(t *testing.T) {
	tests := []struct {
		name   string
		length int
	}{
		{
			name:   "short string",
			length: 16,
		},
		{
			name:   "medium string",
			length: 32,
		},
		{
			name:   "long string",
			length: 64,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := generateRandomString(tt.length)

			assert.NotEmpty(t, result)
			assert.Equal(t, tt.length, len(result))

			// Test uniqueness by generating another
			result2 := generateRandomString(tt.length)
			assert.NotEqual(t, result, result2)

			// Test format (should be hex)
			for _, char := range result {
				assert.True(t,
					(char >= '0' && char <= '9') ||
						(char >= 'a' && char <= 'f'),
					"Character %c is not a valid hex character", char)
			}
		})
	}
}

// Test OAuth DTO structures
func TestOAuthDTOStructures(t *testing.T) {
	t.Run("OAuthAuthURLRequest", func(t *testing.T) {
		req := &dto.OAuthAuthURLRequest{
			Provider: "google",
			State:    "test-state",
		}

		assert.Equal(t, "google", req.Provider)
		assert.Equal(t, "test-state", req.State)
	})

	t.Run("OAuthAuthURLResponse", func(t *testing.T) {
		resp := &dto.OAuthAuthURLResponse{
			URL:   "https://accounts.google.com/oauth/authorize?client_id=123",
			State: "test-state",
		}

		assert.Equal(t, "https://accounts.google.com/oauth/authorize?client_id=123", resp.URL)
		assert.Equal(t, "test-state", resp.State)
	})

	t.Run("OAuthLoginRequest", func(t *testing.T) {
		req := &dto.OAuthLoginRequest{
			Provider: "google",
			Code:     "auth-code-123",
			State:    "test-state",
		}

		assert.Equal(t, "google", req.Provider)
		assert.Equal(t, "auth-code-123", req.Code)
		assert.Equal(t, "test-state", req.State)
	})

	t.Run("GoogleUserInfo", func(t *testing.T) {
		info := &dto.GoogleUserInfo{
			ID:            "*********",
			Email:         "<EMAIL>",
			VerifiedEmail: true,
			Name:          "Test User",
			GivenName:     "Test",
			FamilyName:    "User",
			Picture:       "https://example.com/photo.jpg",
			Locale:        "en",
		}

		assert.Equal(t, "*********", info.ID)
		assert.Equal(t, "<EMAIL>", info.Email)
		assert.True(t, info.VerifiedEmail)
		assert.Equal(t, "Test User", info.Name)
		assert.Equal(t, "Test", info.GivenName)
		assert.Equal(t, "User", info.FamilyName)
		assert.Equal(t, "https://example.com/photo.jpg", info.Picture)
		assert.Equal(t, "en", info.Locale)
	})

	t.Run("FacebookUserInfo", func(t *testing.T) {
		info := &dto.FacebookUserInfo{
			ID:        "987654321",
			Email:     "<EMAIL>",
			Name:      "Test User",
			FirstName: "Test",
			LastName:  "User",
		}
		info.Picture.Data.URL = "https://example.com/photo.jpg"

		assert.Equal(t, "987654321", info.ID)
		assert.Equal(t, "<EMAIL>", info.Email)
		assert.Equal(t, "Test User", info.Name)
		assert.Equal(t, "Test", info.FirstName)
		assert.Equal(t, "User", info.LastName)
		assert.Equal(t, "https://example.com/photo.jpg", info.Picture.Data.URL)
	})

	t.Run("OAuthLoginResponse", func(t *testing.T) {
		resp := &dto.OAuthLoginResponse{
			AccessToken:               "access-token-123",
			RefreshToken:              "refresh-token-456",
			ExpiresIn:                 3600,
			TokenType:                 "Bearer",
			SessionID:                 789,
			RequiresTwoFactor:         false,
			RequiresEmailVerification: false,
			RequiresOnboarding:        true,
			IsNewUser:                 true,
		}

		assert.Equal(t, "access-token-123", resp.AccessToken)
		assert.Equal(t, "refresh-token-456", resp.RefreshToken)
		assert.Equal(t, 3600, resp.ExpiresIn)
		assert.Equal(t, "Bearer", resp.TokenType)
		assert.Equal(t, uint(789), resp.SessionID)
		assert.False(t, resp.RequiresTwoFactor)
		assert.False(t, resp.RequiresEmailVerification)
		assert.True(t, resp.RequiresOnboarding)
		assert.True(t, resp.IsNewUser)
	})
}

// Test validation tags on DTO structures (basic validation)
func TestOAuthDTOValidation(t *testing.T) {
	t.Run("OAuthLoginRequest validation tags", func(t *testing.T) {
		// Test that the struct has proper validation tags
		req := &dto.OAuthLoginRequest{}

		// These should be empty initially
		assert.Empty(t, req.Provider)
		assert.Empty(t, req.Code)
		assert.Empty(t, req.State)

		// Set valid values
		req.Provider = "google"
		req.Code = "valid-code"
		req.State = "valid-state"

		assert.Equal(t, "google", req.Provider)
		assert.Equal(t, "valid-code", req.Code)
		assert.Equal(t, "valid-state", req.State)
	})
}

// Test that we can instantiate the OAuth service struct
func TestOAuthService_Instantiation(t *testing.T) {
	t.Run("can create service struct", func(t *testing.T) {
		service := &OAuthService{}
		assert.NotNil(t, service)

		// Test that all fields are initially nil/zero
		assert.Nil(t, service.oauthProviderRepo)
		assert.Nil(t, service.oauthConnectionRepo)
		assert.Nil(t, service.userRepo)
		assert.Nil(t, service.cache)
		assert.Nil(t, service.logger)
		assert.Nil(t, service.config)
	})
}

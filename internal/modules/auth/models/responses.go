package models

import (
	"time"

	tenantModels "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	userModels "github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
)

// AuthResponse represents the standard authentication response
type AuthResponse struct {
	User                      *userModels.User             `json:"user,omitempty"`
	AccessToken               string                       `json:"access_token,omitempty"`
	RefreshToken              string                       `json:"refresh_token,omitempty"`
	ExpiresIn                 int                          `json:"expires_in,omitempty"`
	TokenType                 string                       `json:"token_type,omitempty"`
	Tenant                    *tenantModels.Tenant         `json:"tenant,omitempty"`
	Membership                *userModels.TenantMembership `json:"membership,omitempty"`
	SessionID                 uint                         `json:"session_id,omitempty"`
	RequiresTwoFactor         bool                         `json:"requires_two_factor,omitempty"`
	RequiresEmailVerification bool                         `json:"requires_email_verification,omitempty"`
	RequiresOnboarding        bool                         `json:"requires_onboarding,omitempty"`
}

// RegisterResponse represents the response for user registration
type RegisterResponse struct {
	AuthResponse
	EmailVerificationSent bool `json:"email_verification_sent,omitempty"`
}

// TwoFactorChallenge represents a two-factor authentication challenge
type TwoFactorChallenge struct {
	ChallengeID string    `json:"challenge_id"`
	Methods     []string  `json:"methods"`
	ExpiresAt   time.Time `json:"expires_at"`
}

// LogoutResponse represents the response for user logout
type LogoutResponse struct {
	SessionsTerminated int `json:"sessions_terminated,omitempty"`
}

// RefreshTokenResponse represents the response for token refresh
type RefreshTokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token,omitempty"`
	ExpiresIn    int    `json:"expires_in"`
	TokenType    string `json:"token_type"`
}

// TwoFactorSetupResponse represents the response for 2FA setup
type TwoFactorSetupResponse struct {
	Secret      string   `json:"secret,omitempty"`
	QRCode      string   `json:"qr_code,omitempty"`
	BackupCodes []string `json:"backup_codes,omitempty"`
}

// TwoFactorVerifyResponse represents the response for 2FA verification
type TwoFactorVerifyResponse struct {
	Verified     bool     `json:"verified"`
	BackupCodes  []string `json:"backup_codes,omitempty"`
	RecoveryUsed bool     `json:"recovery_used,omitempty"`
}

// PasswordChangeResponse represents the response for password change
type PasswordChangeResponse struct {
	SessionsInvalidated bool `json:"sessions_invalidated,omitempty"`
}

// EmailVerificationResponse represents the response for email verification operations
type EmailVerificationResponse struct {
	EmailVerified bool       `json:"email_verified,omitempty"`
	UserID        uint       `json:"user_id,omitempty"`
	Email         string     `json:"email,omitempty"`
	TokenUsed     bool       `json:"token_used,omitempty"`
	TokenSent     bool       `json:"token_sent,omitempty"`
	ExpiresAt     *time.Time `json:"expires_at,omitempty"`
	ResendCount   uint       `json:"resend_count,omitempty"`
	MaxResends    uint       `json:"max_resends,omitempty"`
	NextResendAt  *time.Time `json:"next_resend_at,omitempty"`
}

// SessionResponse represents the response for session operations
type SessionResponse struct {
	Session      *Session   `json:"session,omitempty"`
	Sessions     []*Session `json:"sessions,omitempty"`
	Revoked      bool       `json:"revoked,omitempty"`
	RevokedCount int        `json:"revoked_count,omitempty"`
}

// UserProfileResponse represents the response for user profile operations
type UserProfileResponse struct {
	User         *userModels.User               `json:"user,omitempty"`
	Memberships  []*userModels.TenantMembership `json:"memberships,omitempty"`
	ActiveTenant *tenantModels.Tenant           `json:"active_tenant,omitempty"`
}

// InvitationResponse represents the response for invitation operations
type InvitationResponse struct {
	Invitation    *userModels.UserInvitation   `json:"invitation,omitempty"`
	Invitations   []*userModels.UserInvitation `json:"invitations,omitempty"`
	InvitationURL string                       `json:"invitation_url,omitempty"`
	EmailSent     bool                         `json:"email_sent,omitempty"`
	ExpiresAt     *time.Time                   `json:"expires_at,omitempty"`
}

// SecurityResponse represents the response for security operations
type SecurityResponse struct {
	TwoFactorEnabled     bool             `json:"two_factor_enabled,omitempty"`
	BackupCodesGenerated bool             `json:"backup_codes_generated,omitempty"`
	BackupCodes          []string         `json:"backup_codes,omitempty"`
	Sessions             []*Session       `json:"sessions,omitempty"`
	LoginAttempts        []*LoginAttempt  `json:"login_attempts,omitempty"`
	SecurityEvents       []*SecurityEvent `json:"security_events,omitempty"`
}

// HealthResponse represents the response for auth service health checks
type HealthResponse struct {
	Status   string                 `json:"status"`
	Version  string                 `json:"version,omitempty"`
	Uptime   string                 `json:"uptime,omitempty"`
	Database string                 `json:"database,omitempty"`
	Checks   map[string]interface{} `json:"checks,omitempty"`
}

// ValidationResponse represents the response for token/session validation
type ValidationResponse struct {
	Valid      bool                         `json:"valid"`
	UserID     uint                         `json:"user_id,omitempty"`
	TenantID   uint                         `json:"tenant_id,omitempty"`
	SessionID  uint                         `json:"session_id,omitempty"`
	ExpiresAt  *time.Time                   `json:"expires_at,omitempty"`
	Scopes     []string                     `json:"scopes,omitempty"`
	User       *userModels.User             `json:"user,omitempty"`
	Tenant     *tenantModels.Tenant         `json:"tenant,omitempty"`
	Membership *userModels.TenantMembership `json:"membership,omitempty"`
}

// StatsResponse represents the response for auth statistics
type StatsResponse struct {
	TotalUsers     uint                   `json:"total_users,omitempty"`
	ActiveUsers    uint                   `json:"active_users,omitempty"`
	ActiveSessions uint                   `json:"active_sessions,omitempty"`
	LoginAttempts  uint                   `json:"login_attempts,omitempty"`
	FailedLogins   uint                   `json:"failed_logins,omitempty"`
	SecurityEvents uint                   `json:"security_events,omitempty"`
	TimeRange      string                 `json:"time_range,omitempty"`
	Details        map[string]interface{} `json:"details,omitempty"`
}

// ErrorResponse represents the response for auth errors
type ErrorResponse struct {
	ErrorCode    string                 `json:"error_code,omitempty"`
	ErrorDetails map[string]interface{} `json:"error_details,omitempty"`
	RetryAfter   *time.Time             `json:"retry_after,omitempty"`
	HelpURL      string                 `json:"help_url,omitempty"`
}

package mysql

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

type userRepository struct {
	db     *gorm.DB
	logger utils.Logger
}

// NewUserRepository creates a new MySQL user repository
func NewUserRepository(db *gorm.DB, logger utils.Logger) repositories.UserRepository {
	return &userRepository{
		db:     db,
		logger: logger,
	}
}

// Create creates a new user
func (r *userRepository) Create(ctx context.Context, user *models.User) error {
	if err := r.db.WithContext(ctx).Create(user).Error; err != nil {
		if strings.Contains(err.<PERSON>rror(), "Duplicate entry") {
			if strings.Contains(err.Error(), "email") {
				r.logger.WithError(err).Error("Email already exists")
				return fmt.Errorf("email already exists")
			}
			if strings.Contains(err.Error(), "username") {
				r.logger.WithError(err).Error("Username already exists")
				return fmt.Errorf("username already exists")
			}
		}
		r.logger.WithError(err).Error("Failed to create user")
		return fmt.Errorf("failed to create user: %w", err)
	}
	return nil
}

// GetByID retrieves a user by ID
func (r *userRepository) GetByID(ctx context.Context, id uint) (*models.User, error) {
	var user models.User
	err := r.db.WithContext(ctx).
		Where("id = ? AND status != ?", id, models.UserStatusDeleted).
		Preload("TenantMemberships").
		First(&user).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		r.logger.WithError(err).Error("Failed to get user by ID")
		return nil, fmt.Errorf("failed to get user by id: %w", err)
	}
	return &user, nil
}

// GetByEmail retrieves a user by email
func (r *userRepository) GetByEmail(ctx context.Context, email string) (*models.User, error) {
	var user models.User
	err := r.db.WithContext(ctx).
		Where("email = ? AND status != ?", email, models.UserStatusDeleted).
		Preload("TenantMemberships").
		First(&user).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		r.logger.WithError(err).Error("Failed to get user by email")
		return nil, fmt.Errorf("failed to get user by email: %w", err)
	}
	return &user, nil
}

// GetByUsername retrieves a user by username
func (r *userRepository) GetByUsername(ctx context.Context, username string) (*models.User, error) {
	var user models.User
	err := r.db.WithContext(ctx).
		Where("username = ? AND status != ?", username, models.UserStatusDeleted).
		Preload("TenantMemberships").
		First(&user).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		r.logger.WithError(err).Error("Failed to get user by username")
		return nil, fmt.Errorf("failed to get user by username: %w", err)
	}
	return &user, nil
}

// Update updates a user
func (r *userRepository) Update(ctx context.Context, user *models.User) error {
	if err := r.db.WithContext(ctx).Save(user).Error; err != nil {
		if strings.Contains(err.Error(), "Duplicate entry") {
			if strings.Contains(err.Error(), "email") {
				r.logger.WithError(err).Error("Email already exists")
				return fmt.Errorf("email already exists")
			}
			if strings.Contains(err.Error(), "username") {
				r.logger.WithError(err).Error("Username already exists")
				return fmt.Errorf("username already exists")
			}
		}
		r.logger.WithError(err).Error("Failed to update user")
		return fmt.Errorf("failed to update user: %w", err)
	}
	return nil
}

// Delete soft deletes a user
func (r *userRepository) Delete(ctx context.Context, id uint) error {
	result := r.db.WithContext(ctx).Model(&models.User{}).
		Where("id = ?", id).
		Update("status", models.UserStatusDeleted)

	if result.Error != nil {
		return fmt.Errorf("failed to delete user: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("user not found")
	}
	return nil
}

// GetByEmailOrUsername retrieves a user by email or username
func (r *userRepository) GetByEmailOrUsername(ctx context.Context, identifier string) (*models.User, error) {
	var user models.User
	err := r.db.WithContext(ctx).
		Where("(email = ? OR username = ?) AND status != ?", identifier, identifier, models.UserStatusDeleted).
		Preload("TenantMemberships").
		First(&user).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		r.logger.WithError(err).Error("Failed to get user by identifier")
		return nil, fmt.Errorf("failed to get user by identifier: %w", err)
	}
	return &user, nil
}

// UpdateLastLogin updates user's last login time and IP
func (r *userRepository) UpdateLastLogin(ctx context.Context, userID uint, ip string) error {
	now := time.Now()

	updates := map[string]interface{}{
		"last_login_at": now,
		"last_login_ip": ip,
		"login_count":   gorm.Expr("login_count + 1"),
	}

	if err := r.db.WithContext(ctx).Model(&models.User{}).
		Where("id = ?", userID).
		Updates(updates).Error; err != nil {
		r.logger.WithError(err).Error("Failed to update last login")
		return fmt.Errorf("failed to update last login: %w", err)
	}
	return nil
}

// UpdatePassword updates user's password
func (r *userRepository) UpdatePassword(ctx context.Context, userID uint, hashedPassword string) error {
	if err := r.db.WithContext(ctx).Model(&models.User{}).
		Where("id = ?", userID).
		Update("password_hash", hashedPassword).Error; err != nil {
		r.logger.WithError(err).Error("Failed to update password")
		return fmt.Errorf("failed to update password: %w", err)
	}
	return nil
}

// VerifyEmail marks user's email as verified
func (r *userRepository) VerifyEmail(ctx context.Context, userID uint) error {
	now := time.Now()
	updates := map[string]interface{}{
		"email_verified":    true,
		"email_verified_at": &now,
	}

	if err := r.db.WithContext(ctx).Model(&models.User{}).
		Where("id = ?", userID).
		Updates(updates).Error; err != nil {
		r.logger.WithError(err).Error("Failed to verify email")
		return fmt.Errorf("failed to verify email: %w", err)
	}
	return nil
}

// UpdateEmailVerificationToken updates email verification token
func (r *userRepository) UpdateEmailVerificationToken(ctx context.Context, userID uint, token string) error {
	// Note: This field may not exist in the new user model
	// For now, we'll just return nil since verification is handled differently
	return nil
}

// GetByEmailVerificationToken retrieves user by email verification token
func (r *userRepository) GetByEmailVerificationToken(ctx context.Context, token string) (*models.User, error) {
	// Note: This field may not exist in the new user model
	// For now, we'll return nil since verification is handled differently
	return nil, nil
}

// EnableTwoFactor enables two-factor authentication
func (r *userRepository) EnableTwoFactor(ctx context.Context, userID uint, secret string) error {
	updates := map[string]interface{}{
		"two_factor_enabled": true,
		"two_factor_secret":  secret,
	}

	if err := r.db.WithContext(ctx).Model(&models.User{}).
		Where("id = ?", userID).
		Updates(updates).Error; err != nil {
		r.logger.WithError(err).Error("Failed to enable two-factor")
		return fmt.Errorf("failed to enable two-factor: %w", err)
	}
	return nil
}

// DisableTwoFactor disables two-factor authentication
func (r *userRepository) DisableTwoFactor(ctx context.Context, userID uint) error {
	updates := map[string]interface{}{
		"two_factor_enabled": false,
		"two_factor_secret":  nil,
		"recovery_codes":     "[]",
	}

	if err := r.db.WithContext(ctx).Model(&models.User{}).
		Where("id = ?", userID).
		Updates(updates).Error; err != nil {
		r.logger.WithError(err).Error("Failed to disable two-factor")
		return fmt.Errorf("failed to disable two-factor: %w", err)
	}
	return nil
}

// GetTwoFactorSecret retrieves user's two-factor secret
func (r *userRepository) GetTwoFactorSecret(ctx context.Context, userID uint) (string, error) {
	var secret string
	if err := r.db.WithContext(ctx).Model(&models.User{}).
		Where("id = ? AND two_factor_enabled = true", userID).
		Pluck("two_factor_secret", &secret).Error; err != nil {
		r.logger.WithError(err).Error("Failed to get two-factor secret")
		return "", fmt.Errorf("failed to get two-factor secret: %w", err)
	}
	return secret, nil
}

// UpdateTwoFactorBackupCodes updates two-factor backup codes
func (r *userRepository) UpdateTwoFactorBackupCodes(ctx context.Context, userID uint, codes []string) error {
	if err := r.db.WithContext(ctx).Model(&models.User{}).
		Where("id = ?", userID).
		Update("recovery_codes", codes).Error; err != nil {
		r.logger.WithError(err).Error("Failed to update backup codes")
		return fmt.Errorf("failed to update backup codes: %w", err)
	}
	return nil
}

// UpdateTwoFactorSecret updates user's two-factor secret (without enabling 2FA)
func (r *userRepository) UpdateTwoFactorSecret(ctx context.Context, userID uint, secret string) error {
	if err := r.db.WithContext(ctx).Model(&models.User{}).
		Where("id = ?", userID).
		Update("two_factor_secret", secret).Error; err != nil {
		r.logger.WithError(err).Error("Failed to update two-factor secret")
		return fmt.Errorf("failed to update two-factor secret: %w", err)
	}
	return nil
}

// UpdateStatus updates user status
func (r *userRepository) UpdateStatus(ctx context.Context, userID uint, status models.UserStatus) error {
	if err := r.db.WithContext(ctx).Model(&models.User{}).
		Where("id = ?", userID).
		Update("status", status).Error; err != nil {
		r.logger.WithError(err).Error("Failed to update status")
		return fmt.Errorf("failed to update status: %w", err)
	}
	return nil
}

// GetByIDs retrieves multiple users by their IDs
func (r *userRepository) GetByIDs(ctx context.Context, ids []uint) ([]*models.User, error) {
	var users []*models.User
	if err := r.db.WithContext(ctx).
		Where("id IN ? AND status != ?", ids, models.UserStatusDeleted).
		Preload("TenantMemberships").
		Find(&users).Error; err != nil {
		r.logger.WithError(err).Error("Failed to get users by IDs")
		return nil, fmt.Errorf("failed to get users by ids: %w", err)
	}
	return users, nil
}

// Search searches users based on query and filters (global search)
func (r *userRepository) Search(ctx context.Context, query string, filters *repositories.UserFilters) ([]*models.User, error) {
	var users []*models.User

	db := r.db.WithContext(ctx).Model(&models.User{})

	// Base condition - exclude deleted users
	db = db.Where("status != ?", models.UserStatusDeleted)

	// Apply search query
	if query != "" {
		searchQuery := "%" + query + "%"
		db = db.Where("(email LIKE ? OR username LIKE ? OR first_name LIKE ? OR last_name LIKE ?)",
			searchQuery, searchQuery, searchQuery, searchQuery)
	}

	// Apply filters
	if filters != nil {
		if filters.Status != nil {
			db = db.Where("status = ?", *filters.Status)
		}
		if filters.EmailVerified != nil {
			db = db.Where("email_verified = ?", *filters.EmailVerified)
		}
		if filters.TwoFactorEnabled != nil {
			db = db.Where("two_factor_enabled = ?", *filters.TwoFactorEnabled)
		}
		if filters.CreatedAfter != nil {
			db = db.Where("created_at > ?", *filters.CreatedAfter)
		}
		if filters.CreatedBefore != nil {
			db = db.Where("created_at < ?", *filters.CreatedBefore)
		}

		// Apply sorting
		orderBy := "created_at"
		if filters.SortBy != "" {
			orderBy = filters.SortBy
		}

		orderDir := "DESC"
		if filters.SortOrder == "asc" {
			orderDir = "ASC"
		}

		db = db.Order(fmt.Sprintf("%s %s", orderBy, orderDir))

		// Apply pagination
		if filters.Limit > 0 {
			db = db.Limit(filters.Limit)
		}
		if filters.Offset > 0 {
			db = db.Offset(filters.Offset)
		}
	}

	// Preload tenant memberships for global users
	db = db.Preload("TenantMemberships")

	if err := db.Find(&users).Error; err != nil {
		r.logger.WithError(err).Error("Failed to search users")
		return nil, fmt.Errorf("failed to search users: %w", err)
	}

	return users, nil
}

// Exists checks if a user with the given email exists
func (r *userRepository) Exists(ctx context.Context, email string) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.User{}).
		Where("email = ? AND status != ?", email, models.UserStatusDeleted).
		Count(&count).Error; err != nil {
		r.logger.WithError(err).Error("Failed to check user existence")
		return false, fmt.Errorf("failed to check user existence: %w", err)
	}
	return count > 0, nil
}

// ExistsByUsername checks if a user with the given username exists
func (r *userRepository) ExistsByUsername(ctx context.Context, username string) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.User{}).
		Where("username = ? AND status != ?", username, models.UserStatusDeleted).
		Count(&count).Error; err != nil {
		r.logger.WithError(err).Error("Failed to check username existence")
		return false, fmt.Errorf("failed to check username existence: %w", err)
	}
	return count > 0, nil
}

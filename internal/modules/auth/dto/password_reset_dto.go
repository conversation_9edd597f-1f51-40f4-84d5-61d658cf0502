package dto

// ForgotPasswordRequest represents the request payload for forgot password
type ForgotPasswordRequest struct {
	Email string `json:"email" validate:"required,email" example:"<EMAIL>"`
}

// ForgotPasswordResponse represents the response payload for forgot password
type ForgotPasswordResponse struct {
	Status string `json:"status,omitempty"`
}

// ResetPasswordRequest represents the request payload for password reset
type ResetPasswordRequest struct {
	Token           string `json:"token" validate:"required" example:"abc123def456"`
	NewPassword     string `json:"new_password" validate:"required,min=8" example:"NewSecurePass123!"`
	ConfirmPassword string `json:"confirm_password" validate:"required,min=8" example:"NewSecurePass123!"`
}

// ResetPasswordResponse represents the response payload for password reset
type ResetPasswordResponse struct {
	Status string `json:"status,omitempty"`
}

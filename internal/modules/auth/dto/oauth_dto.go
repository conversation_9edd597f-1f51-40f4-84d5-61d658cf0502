package dto

import "time"

// GoogleUserInfo represents user information returned by Google OAuth2
type GoogleUserInfo struct {
	ID            string `json:"id"`
	Email         string `json:"email"`
	VerifiedEmail bool   `json:"verified_email"`
	Name          string `json:"name"`
	GivenName     string `json:"given_name"`
	FamilyName    string `json:"family_name"`
	Picture       string `json:"picture"`
	Locale        string `json:"locale"`
}

// FacebookUserInfo represents user information returned by Facebook OAuth2
type FacebookUserInfo struct {
	ID      string `json:"id"`
	Email   string `json:"email"`
	Name    string `json:"name"`
	Picture struct {
		Data struct {
			URL string `json:"url"`
		} `json:"data"`
	} `json:"picture"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
}

// OAuthLoginRequest represents a request to login with OAuth
type OAuthLoginRequest struct {
	Provider string `json:"provider" validate:"required,oneof=google facebook"`
	Code     string `json:"code" validate:"required"`
	State    string `json:"state,omitempty"`
}

// OAuthLoginResponse represents the response from OAuth login
type OAuthLoginResponse struct {
	AccessToken               string    `json:"access_token"`
	RefreshToken              string    `json:"refresh_token"`
	ExpiresIn                 int       `json:"expires_in"`
	TokenType                 string    `json:"token_type"`
	SessionID                 uint      `json:"session_id"`
	RequiresTwoFactor         bool      `json:"requires_two_factor"`
	RequiresEmailVerification bool      `json:"requires_email_verification"`
	RequiresOnboarding        bool      `json:"requires_onboarding"`
	IsNewUser                 bool      `json:"is_new_user"`
	ConnectedAt               time.Time `json:"connected_at"`
}

// OAuthAuthURLRequest represents a request to get OAuth authorization URL
type OAuthAuthURLRequest struct {
	Provider    string `json:"provider" validate:"required,oneof=google facebook"`
	RedirectURL string `json:"redirect_url,omitempty" validate:"omitempty,url"`
	State       string `json:"state,omitempty"`
}

// OAuthAuthURLResponse represents the response containing OAuth authorization URL
type OAuthAuthURLResponse struct {
	URL   string `json:"url"`
	State string `json:"state"`
}

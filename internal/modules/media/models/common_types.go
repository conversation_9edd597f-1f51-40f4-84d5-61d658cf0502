package models

// Common reference types used across media models
// These would normally be defined in their respective modules

// User represents a user entity (reference type)
type User struct {
	ID    uint   `json:"id"`
	Name  string `json:"name"`
	Email string `json:"email"`
}

// Tenant represents a tenant entity (reference type)
type Tenant struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
	Slug string `json:"slug"`
}

// Website represents a website entity (reference type)
type Website struct {
	ID       uint   `json:"id"`
	Name     string `json:"name"`
	Domain   string `json:"domain"`
	TenantID uint   `json:"tenant_id"`
}

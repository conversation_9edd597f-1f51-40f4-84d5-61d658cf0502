package media

import (
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/storage"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
	"gorm.io/gorm"
)

// RegisterRoutes registers all media routes
func RegisterRoutes(
	router *gin.RouterGroup,
	db *gorm.DB,
	storageProviders map[string]storage.Storage,
	defaultStorageProvider string,
	validator validator.Validator,
	logger *logrus.Logger,
) {
	// Initialize repositories
	fileRepo := repositories.NewMediaFileRepository(db)
	folderRepo := repositories.NewMediaFolderRepository(db)
	thumbnailRepo := repositories.NewMediaThumbnailRepository(db)

	// Initialize services
	storageService := services.NewMediaStorageService(
		storageProviders,
		defaultStorageProvider,
		fileRepo,
		logger,
	)

	fileService := services.NewMediaFileService(
		fileRepo,
		folderRepo,
		storageService,
		logger,
	)

	folderService := services.NewMediaFolderService(
		folderRepo,
		fileRepo,
		logger,
	)

	thumbnailService := services.NewMediaThumbnailService(
		thumbnailRepo,
		storageService,
		logger,
	)

	// Initialize handlers
	mediaHandler := handlers.NewMediaHandler(
		fileService,
		storageService,
		folderService,
		thumbnailService,
		validator,
	)

	folderHandler := handlers.NewMediaFolderHandler(
		folderService,
		validator,
	)

	// Create media group with authentication middleware
	mediaGroup := router.Group("/media")
	mediaGroup.Use(middleware.AuthRequired())
	mediaGroup.Use(middleware.TenantRequired())

	// File routes
	mediaGroup.POST("/upload", mediaHandler.UploadFile)
	mediaGroup.GET("/files", mediaHandler.ListFiles)
	mediaGroup.GET("/files/:id", mediaHandler.GetFile)
	mediaGroup.PUT("/files/:id", mediaHandler.UpdateFile)
	mediaGroup.DELETE("/files/:id", mediaHandler.DeleteFile)
	mediaGroup.GET("/files/:id/download", mediaHandler.DownloadFile)
	mediaGroup.GET("/stats", mediaHandler.GetStats)

	// Folder routes
	mediaGroup.POST("/folders", folderHandler.CreateFolder)
	mediaGroup.GET("/folders", folderHandler.ListFolders)
	mediaGroup.GET("/folders/:id", folderHandler.GetFolder)
	mediaGroup.PUT("/folders/:id", folderHandler.UpdateFolder)
	mediaGroup.DELETE("/folders/:id", folderHandler.DeleteFolder)
	mediaGroup.POST("/folders/:id/move", folderHandler.MoveFolder)
	mediaGroup.GET("/folders/:id/breadcrumb", folderHandler.GetFolderBreadcrumb)
}

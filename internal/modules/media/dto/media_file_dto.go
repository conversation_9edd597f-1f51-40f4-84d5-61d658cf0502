package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// CreateMediaFileRequest represents a request to create a new media file
type CreateMediaFileRequest struct {
	WebsiteID   uint                   `json:"website_id" validate:"required" example:"1"`
	FolderID    *uint                  `json:"folder_id,omitempty" example:"1"`
	Filename    string                 `json:"filename" validate:"required,min=1,max=255" example:"image.jpg"`
	MimeType    string                 `json:"mime_type" validate:"required" example:"image/jpeg"`
	FileSize    uint64                 `json:"file_size" validate:"required" example:"1024000"`
	StorageType models.StorageType     `json:"storage_type,omitempty" example:"local"`
	StoragePath string                 `json:"storage_path" validate:"required" example:"/uploads/2024/01/image.jpg"`
	PublicURL   string                 `json:"public_url" validate:"required" example:"https://example.com/uploads/2024/01/image.jpg"`
	Width       *uint                  `json:"width,omitempty" example:"1920"`
	Height      *uint                  `json:"height,omitempty" example:"1080"`
	Duration    *uint                  `json:"duration,omitempty" example:"120"`
	AltText     *string                `json:"alt_text,omitempty" example:"Beautiful landscape image"`
	Title       *string                `json:"title,omitempty" example:"Sunset over mountains"`
	Description *string                `json:"description,omitempty" example:"A stunning sunset view over the mountain range"`
	Category    string                 `json:"category,omitempty" example:"landscape"`
	Visibility  models.FileVisibility  `json:"visibility,omitempty" example:"public"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// UpdateMediaFileRequest represents a request to update a media file
type UpdateMediaFileRequest struct {
	FolderID    *uint                  `json:"folder_id,omitempty" example:"2"`
	Filename    *string                `json:"filename,omitempty" validate:"omitempty,min=1,max=255" example:"new-image.jpg"`
	AltText     *string                `json:"alt_text,omitempty" example:"Updated alt text"`
	Title       *string                `json:"title,omitempty" example:"Updated title"`
	Description *string                `json:"description,omitempty" example:"Updated description"`
	Category    *string                `json:"category,omitempty" example:"nature"`
	Visibility  *models.FileVisibility `json:"visibility,omitempty" example:"private"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// MediaFileResponse represents a media file response
type MediaFileResponse struct {
	ID               uint                     `json:"id" example:"1"`
	TenantID         uint                     `json:"tenant_id" example:"1"`
	WebsiteID        uint                     `json:"website_id" example:"1"`
	FolderID         *uint                    `json:"folder_id,omitempty" example:"1"`
	UserID           uint                     `json:"user_id" example:"1"`
	Filename         string                   `json:"filename" example:"image.jpg"`
	OriginalFilename string                   `json:"original_filename" example:"IMG_1234.jpg"`
	Slug             string                   `json:"slug" example:"image-jpg"`
	MimeType         string                   `json:"mime_type" example:"image/jpeg"`
	FileSize         uint64                   `json:"file_size" example:"1024000"`
	FileHash         string                   `json:"file_hash" example:"sha256:abcd1234..."`
	StorageType      models.StorageType       `json:"storage_type" example:"local"`
	StoragePath      string                   `json:"storage_path" example:"/uploads/2024/01/image.jpg"`
	PublicURL        string                   `json:"public_url" example:"https://example.com/uploads/2024/01/image.jpg"`
	StorageProvider  *string                  `json:"storage_provider,omitempty" example:"minio"`
	CDNUrl           *string                  `json:"cdn_url,omitempty" example:"https://cdn.example.com/uploads/2024/01/image.jpg"`
	CDNProvider      *string                  `json:"cdn_provider,omitempty" example:"cloudflare"`
	OptimizedPath    *string                  `json:"optimized_path,omitempty" example:"/uploads/optimized/2024/01/image.webp"`
	OptimizedSize    *int64                   `json:"optimized_size,omitempty" example:"512000"`
	OptimizedURL     *string                  `json:"optimized_url,omitempty" example:"https://example.com/uploads/optimized/2024/01/image.webp"`
	OptimizedAt      *time.Time               `json:"optimized_at,omitempty"`
	Width            *uint                    `json:"width,omitempty" example:"1920"`
	Height           *uint                    `json:"height,omitempty" example:"1080"`
	Duration         *uint                    `json:"duration,omitempty" example:"120"`
	FileType         models.FileType          `json:"file_type" example:"image"`
	Category         string                   `json:"category" example:"landscape"`
	AltText          *string                  `json:"alt_text,omitempty" example:"Beautiful landscape image"`
	Title            *string                  `json:"title,omitempty" example:"Sunset over mountains"`
	Description      *string                  `json:"description,omitempty" example:"A stunning sunset view over the mountain range"`
	Visibility       models.FileVisibility    `json:"visibility" example:"public"`
	ViewCount        uint                     `json:"view_count" example:"150"`
	DownloadCount    uint                     `json:"download_count" example:"25"`
	LastAccessedAt   *time.Time               `json:"last_accessed_at,omitempty"`
	Status           models.FileStatus        `json:"status" example:"ready"`
	CreatedAt        time.Time                `json:"created_at"`
	UpdatedAt        time.Time                `json:"updated_at"`
	Folder           *MediaFolderResponse     `json:"folder,omitempty"`
	Thumbnails       []MediaThumbnailResponse `json:"thumbnails,omitempty"`
	Tags             []MediaTagResponse       `json:"tags,omitempty"`
}

// MediaFileListResponse represents a simplified media file response for listings
type MediaFileListResponse struct {
	ID               uint                     `json:"id" example:"1"`
	Filename         string                   `json:"filename" example:"image.jpg"`
	OriginalFilename string                   `json:"original_filename" example:"IMG_1234.jpg"`
	MimeType         string                   `json:"mime_type" example:"image/jpeg"`
	FileSize         uint64                   `json:"file_size" example:"1024000"`
	FileType         models.FileType          `json:"file_type" example:"image"`
	PublicURL        string                   `json:"public_url" example:"https://example.com/uploads/2024/01/image.jpg"`
	Width            *uint                    `json:"width,omitempty" example:"1920"`
	Height           *uint                    `json:"height,omitempty" example:"1080"`
	Duration         *uint                    `json:"duration,omitempty" example:"120"`
	AltText          *string                  `json:"alt_text,omitempty" example:"Beautiful landscape image"`
	Title            *string                  `json:"title,omitempty" example:"Sunset over mountains"`
	Status           models.FileStatus        `json:"status" example:"ready"`
	CreatedAt        time.Time                `json:"created_at"`
	UpdatedAt        time.Time                `json:"updated_at"`
	Thumbnails       []MediaThumbnailResponse `json:"thumbnails,omitempty"`
}

// MediaFileUploadResponse represents a response for file upload operations
type MediaFileUploadResponse struct {
	ID        uint              `json:"id" example:"1"`
	Filename  string            `json:"filename" example:"image.jpg"`
	PublicURL string            `json:"public_url" example:"https://example.com/uploads/2024/01/image.jpg"`
	Status    models.FileStatus `json:"status" example:"ready"`
	CreatedAt time.Time         `json:"created_at"`
}

// MediaFileSearchRequest represents a request to search media files
type MediaFileSearchRequest struct {
	// Embedded pagination - now supports both cursor and page-based
	pagination.FlexiblePaginationRequest

	// Media-specific filters
	WebsiteID     uint             `json:"website_id" form:"website_id" validate:"required" example:"1"`
	FolderID      *uint            `json:"folder_id,omitempty" form:"folder_id" example:"1"`
	Query         *string          `json:"query,omitempty" form:"query" example:"landscape"`
	FileType      *models.FileType `json:"file_type,omitempty" form:"file_type" example:"image"`
	Category      *string          `json:"category,omitempty" form:"category" example:"nature"`
	MimeType      *string          `json:"mime_type,omitempty" form:"mime_type" example:"image/jpeg"`
	Tags          []string         `json:"tags,omitempty" form:"tags" example:"['nature', 'outdoor']"`
	MinSize       *uint64          `json:"min_size,omitempty" form:"min_size" example:"100000"`
	MaxSize       *uint64          `json:"max_size,omitempty" form:"max_size" example:"5000000"`
	CreatedAfter  *time.Time       `json:"created_after,omitempty" form:"created_after"`
	CreatedBefore *time.Time       `json:"created_before,omitempty" form:"created_before"`

	// Deprecated fields (for backward compatibility)
	Page     int `json:"page,omitempty" form:"page" validate:"omitempty,min=1" example:"1"`
	PageSize int `json:"page_size,omitempty" form:"page_size" validate:"omitempty,min=1,max=100" example:"20"`
}

// MediaFileSearchResponse represents a response for media file search with cursor pagination
type MediaFileSearchResponse struct {
	Files      []MediaFileListResponse    `json:"files"`
	Pagination *pagination.CursorResponse `json:"pagination,omitempty"`
	// For backward compatibility
	Meta *MediaFileSearchMeta `json:"meta,omitempty"`
}

// MediaFileSearchMeta represents pagination metadata for backward compatibility
type MediaFileSearchMeta struct {
	Total      int64  `json:"total,omitempty"`
	Page       int    `json:"page,omitempty"`
	PageSize   int    `json:"page_size,omitempty"`
	TotalPages int    `json:"total_pages,omitempty"`
	HasNext    bool   `json:"has_next,omitempty"`
	HasPrev    bool   `json:"has_prev,omitempty"`
	NextCursor string `json:"next_cursor,omitempty"`
	HasMore    bool   `json:"has_more,omitempty"`
}

// MediaFileStatsResponse represents media file statistics
type MediaFileStatsResponse struct {
	TotalFiles     uint    `json:"total_files" example:"1250"`
	TotalSize      uint64  `json:"total_size" example:"2500000000"`
	TotalFolders   uint    `json:"total_folders" example:"45"`
	ImageFiles     uint    `json:"image_files" example:"800"`
	VideoFiles     uint    `json:"video_files" example:"150"`
	AudioFiles     uint    `json:"audio_files" example:"75"`
	DocumentFiles  uint    `json:"document_files" example:"200"`
	ArchiveFiles   uint    `json:"archive_files" example:"15"`
	OtherFiles     uint    `json:"other_files" example:"10"`
	StorageUsed    uint64  `json:"storage_used" example:"2500000000"`
	StorageLimit   uint64  `json:"storage_limit" example:"10000000000"`
	StoragePercent float64 `json:"storage_percent" example:"25.0"`
}

// BulkMediaFileOperationRequest represents a request for bulk operations on media files
type BulkMediaFileOperationRequest struct {
	FileIDs   []uint `json:"file_ids" validate:"required,min=1" example:"[1,2,3,4,5]"`
	Operation string `json:"operation" validate:"required,oneof=delete move copy tag untag" example:"move"`
	FolderID  *uint  `json:"folder_id,omitempty" example:"2"`
	TagIDs    []uint `json:"tag_ids,omitempty" example:"[1,2]"`
}

// BulkMediaFileOperationResponse represents a response for bulk operations
type BulkMediaFileOperationResponse struct {
	TotalFiles     uint   `json:"total_files" example:"5"`
	ProcessedFiles uint   `json:"processed_files" example:"4"`
	FailedFiles    uint   `json:"failed_files" example:"1"`
	Status         string `json:"status" example:"partial_success"`
	Message        string `json:"message" example:"4 files processed successfully, 1 file failed"`
}

// MediaFileCopyRequest represents a request to copy a media file
type MediaFileCopyRequest struct {
	WebsiteID uint  `json:"website_id" validate:"required" example:"1"`
	FolderID  *uint `json:"folder_id,omitempty" example:"2"`
}

// MediaFileMoveRequest represents a request to move a media file
type MediaFileMoveRequest struct {
	FolderID *uint `json:"folder_id" validate:"required" example:"2"`
}

// MediaFileTagRequest represents a request to tag/untag media files
type MediaFileTagRequest struct {
	TagIDs []uint `json:"tag_ids" validate:"required,min=1" example:"[1,2,3]"`
}

// MediaFileAnalysisRequest represents a request for media file analysis
type MediaFileAnalysisRequest struct {
	AnalysisType string `json:"analysis_type" validate:"required,oneof=metadata exif content ai" example:"metadata"`
	Force        bool   `json:"force,omitempty" example:"false"`
}

// MediaFileAnalysisResponse represents a response for media file analysis
type MediaFileAnalysisResponse struct {
	FileID       uint                   `json:"file_id" example:"1"`
	AnalysisType string                 `json:"analysis_type" example:"metadata"`
	Results      map[string]interface{} `json:"results"`
	Status       string                 `json:"status" example:"completed"`
	ProcessedAt  time.Time              `json:"processed_at"`
}

package dto

import (
	"io"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
)

// UploadRequest represents a file upload request
type UploadRequest struct {
	WebsiteID       uint                   `json:"website_id" validate:"required" example:"1"`
	FolderID        *uint                  `json:"folder_id,omitempty" example:"1"`
	Reader          io.Reader              `json:"-"`
	Filename        string                 `json:"filename" validate:"required" example:"vacation-photo.jpg"`
	OriginalName    string                 `json:"original_name" validate:"required" example:"IMG_1234.JPG"`
	MimeType        string                 `json:"mime_type" validate:"required" example:"image/jpeg"`
	Size            int64                  `json:"size" validate:"required,min=1" example:"2048000"`
	Path            string                 `json:"path" validate:"required" example:"/uploads/2024/01/vacation-photo.jpg"`
	StorageProvider string                 `json:"storage_provider,omitempty" example:"minio"`
	AltText         *string                `json:"alt_text,omitempty" example:"Beautiful vacation photo"`
	Title           *string                `json:"title,omitempty" example:"Summer Vacation 2024"`
	Description     *string                `json:"description,omitempty" example:"A memorable photo from our summer vacation"`
	Category        string                 `json:"category,omitempty" example:"personal"`
	Visibility      models.FileVisibility  `json:"visibility,omitempty" example:"public"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// MultiUploadRequest represents a multi-file upload request
type MultiUploadRequest struct {
	WebsiteID       uint                   `json:"website_id" validate:"required" example:"1"`
	FolderID        *uint                  `json:"folder_id,omitempty" example:"1"`
	Files           []UploadFileInfo       `json:"files" validate:"required,dive"`
	StorageProvider string                 `json:"storage_provider,omitempty" example:"minio"`
	Category        string                 `json:"category,omitempty" example:"batch_upload"`
	Visibility      models.FileVisibility  `json:"visibility,omitempty" example:"public"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// UploadFileInfo represents information about a file to upload
type UploadFileInfo struct {
	Reader       io.Reader `json:"-"`
	Filename     string    `json:"filename" validate:"required" example:"document.pdf"`
	OriginalName string    `json:"original_name" validate:"required" example:"Important_Document.pdf"`
	MimeType     string    `json:"mime_type" validate:"required" example:"application/pdf"`
	Size         int64     `json:"size" validate:"required,min=1" example:"1024000"`
	AltText      *string   `json:"alt_text,omitempty" example:"Important document"`
	Title        *string   `json:"title,omitempty" example:"Legal Document"`
	Description  *string   `json:"description,omitempty" example:"Important legal documentation"`
}

// StorageResult represents the result of a storage operation
type StorageResult struct {
	Path            string                 `json:"path" example:"/uploads/2024/01/vacation-photo.jpg"`
	URL             string                 `json:"url" example:"https://example.com/uploads/2024/01/vacation-photo.jpg"`
	CDNURL          *string                `json:"cdn_url,omitempty" example:"https://cdn.example.com/uploads/2024/01/vacation-photo.jpg"`
	Size            int64                  `json:"size" example:"2048000"`
	StorageProvider string                 `json:"storage_provider" example:"minio"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
	UploadedAt      time.Time              `json:"uploaded_at"`
	Hash            string                 `json:"hash,omitempty" example:"sha256:abcd1234..."`
	ETag            string                 `json:"etag,omitempty" example:"d41d8cd98f00b204e9800998ecf8427e"`
}

// UploadResult represents the result of an upload operation
type UploadResult struct {
	File           *MediaFileResponse       `json:"file"`
	Storage        *StorageResult           `json:"storage"`
	Thumbnails     []MediaThumbnailResponse `json:"thumbnails,omitempty"`
	ProcessingJobs []ProcessingJob          `json:"processing_jobs,omitempty"`
}

// MultiUploadResult represents the result of a multi-file upload
type MultiUploadResult struct {
	SuccessfulUploads []UploadResult `json:"successful_uploads"`
	FailedUploads     []UploadError  `json:"failed_uploads"`
	TotalFiles        int            `json:"total_files" example:"5"`
	SuccessCount      int            `json:"success_count" example:"4"`
	FailureCount      int            `json:"failure_count" example:"1"`
}

// UploadError represents an upload error
type UploadError struct {
	Filename string `json:"filename" example:"invalid-file.exe"`
	Error    string `json:"error" example:"File type not allowed"`
	Code     string `json:"code,omitempty" example:"INVALID_FILE_TYPE"`
}

// ProcessingJob represents a background processing job
type ProcessingJob struct {
	ID     string `json:"id" example:"job_12345"`
	Type   string `json:"type" example:"thumbnail"`
	Status string `json:"status" example:"queued"`
	FileID uint   `json:"file_id" example:"1"`
}

// StorageInfoResponse represents storage information and statistics
type StorageInfoResponse struct {
	TotalFiles      uint                   `json:"total_files" example:"1250"`
	TotalSize       uint64                 `json:"total_size" example:"**********"`
	ProviderUsage   []StorageProviderUsage `json:"provider_usage"`
	Providers       []string               `json:"providers" example:"['local','minio','s3']"`
	DefaultProvider string                 `json:"default_provider" example:"minio"`
	Quotas          StorageQuotas          `json:"quotas"`
}

// StorageProviderUsage represents usage statistics for a storage provider
type StorageProviderUsage struct {
	Provider   string  `json:"provider" example:"minio"`
	FileCount  uint    `json:"file_count" example:"800"`
	TotalSize  uint64  `json:"total_size" example:"**********"`
	Percentage float64 `json:"percentage" example:"60.0"`
}

// StorageQuotas represents storage quotas and limits
type StorageQuotas struct {
	TotalQuota     uint64  `json:"total_quota" example:"10737418240"`
	UsedSpace      uint64  `json:"used_space" example:"**********"`
	AvailableSpace uint64  `json:"available_space" example:"**********"`
	UsagePercent   float64 `json:"usage_percent" example:"50.0"`
	FileLimit      uint    `json:"file_limit" example:"10000"`
	MaxFileSize    uint64  `json:"max_file_size" example:"*********"`
}

// SyncResult represents the result of a storage sync operation
type SyncResult struct {
	TotalFiles    int       `json:"total_files" example:"1250"`
	ValidFiles    int       `json:"valid_files" example:"1200"`
	InvalidFiles  int       `json:"invalid_files" example:"25"`
	MissingFiles  int       `json:"missing_files" example:"15"`
	OrphanedFiles int       `json:"orphaned_files" example:"10"`
	Provider      string    `json:"provider" example:"minio"`
	SyncedAt      time.Time `json:"synced_at"`
	Errors        []string  `json:"errors,omitempty"`
}

// MediaFileFilter represents filter options for file listing
type MediaFileFilter struct {
	WebsiteID       *uint                  `json:"website_id,omitempty" example:"1"`
	FolderID        *uint                  `json:"folder_id,omitempty" example:"1"`
	UserID          *uint                  `json:"user_id,omitempty" example:"1"`
	FileType        *models.FileType       `json:"file_type,omitempty" example:"image"`
	MimeType        *string                `json:"mime_type,omitempty" example:"image/jpeg"`
	Category        *string                `json:"category,omitempty" example:"personal"`
	Visibility      *models.FileVisibility `json:"visibility,omitempty" example:"public"`
	Status          *string                `json:"status,omitempty" example:"ready"`
	StorageProvider *string                `json:"storage_provider,omitempty" example:"minio"`
	Tags            []string               `json:"tags,omitempty" example:"['vacation','2024']"`
	Query           *string                `json:"query,omitempty" example:"vacation"`
	MinSize         *uint64                `json:"min_size,omitempty" example:"100000"`
	MaxSize         *uint64                `json:"max_size,omitempty" example:"10000000"`
	CreatedAfter    *time.Time             `json:"created_after,omitempty"`
	CreatedBefore   *time.Time             `json:"created_before,omitempty"`
	SortBy          *string                `json:"sort_by,omitempty" validate:"omitempty,oneof=name size created_at updated_at" example:"created_at"`
	SortOrder       *string                `json:"sort_order,omitempty" validate:"omitempty,oneof=asc desc" example:"desc"`
}

// UploadConfigResponse represents upload configuration
type UploadConfigResponse struct {
	MaxFileSize            uint64                `json:"max_file_size" example:"*********"`
	MaxFilesPerUpload      int                   `json:"max_files_per_upload" example:"10"`
	AllowedMimeTypes       []string              `json:"allowed_mime_types"`
	AllowedExtensions      []string              `json:"allowed_extensions"`
	RequireAuth            bool                  `json:"require_auth" example:"true"`
	DefaultVisibility      models.FileVisibility `json:"default_visibility" example:"public"`
	DefaultStorageProvider string                `json:"default_storage_provider" example:"minio"`
	EnableThumbnails       bool                  `json:"enable_thumbnails" example:"true"`
	EnableOptimization     bool                  `json:"enable_optimization" example:"true"`
	EnableVirusScanning    bool                  `json:"enable_virus_scanning" example:"false"`
	EnableWatermark        bool                  `json:"enable_watermark" example:"false"`
	WatermarkConfig        *WatermarkConfig      `json:"watermark_config,omitempty"`
}

// WatermarkConfig represents watermark configuration
type WatermarkConfig struct {
	Enabled   bool    `json:"enabled" example:"true"`
	Text      string  `json:"text,omitempty" example:"© Company Name"`
	ImagePath string  `json:"image_path,omitempty" example:"/assets/watermark.png"`
	Position  string  `json:"position" example:"bottom-right"`
	Opacity   float64 `json:"opacity" example:"0.5"`
	Scale     float64 `json:"scale" example:"0.2"`
	Margin    int     `json:"margin" example:"10"`
}

// UploadPolicyResponse represents upload policies and restrictions
type UploadPolicyResponse struct {
	MaxFileSize       uint64     `json:"max_file_size" example:"*********"`
	MaxTotalSize      uint64     `json:"max_total_size" example:"1073741824"`
	AllowedMimeTypes  []string   `json:"allowed_mime_types"`
	BlockedMimeTypes  []string   `json:"blocked_mime_types"`
	AllowedExtensions []string   `json:"allowed_extensions"`
	BlockedExtensions []string   `json:"blocked_extensions"`
	RequireAuth       bool       `json:"require_auth" example:"true"`
	AllowAnonymous    bool       `json:"allow_anonymous" example:"false"`
	RateLimits        RateLimits `json:"rate_limits"`
}

// RateLimits represents rate limiting configuration
type RateLimits struct {
	UploadsPerMinute int    `json:"uploads_per_minute" example:"10"`
	UploadsPerHour   int    `json:"uploads_per_hour" example:"100"`
	UploadsPerDay    int    `json:"uploads_per_day" example:"1000"`
	BytesPerMinute   uint64 `json:"bytes_per_minute" example:"*********"`
	BytesPerHour     uint64 `json:"bytes_per_hour" example:"1073741824"`
	BytesPerDay      uint64 `json:"bytes_per_day" example:"10737418240"`
}

// ChunkUploadRequest represents a chunked upload request
type ChunkUploadRequest struct {
	WebsiteID   uint      `json:"website_id" validate:"required" example:"1"`
	UploadID    string    `json:"upload_id" validate:"required" example:"upload_12345"`
	ChunkNumber int       `json:"chunk_number" validate:"required,min=1" example:"1"`
	TotalChunks int       `json:"total_chunks" validate:"required,min=1" example:"5"`
	ChunkSize   int64     `json:"chunk_size" validate:"required,min=1" example:"5242880"`
	TotalSize   int64     `json:"total_size" validate:"required,min=1" example:"26214400"`
	Filename    string    `json:"filename" validate:"required" example:"large-video.mp4"`
	MimeType    string    `json:"mime_type" validate:"required" example:"video/mp4"`
	ChunkData   io.Reader `json:"-"`
	Hash        string    `json:"hash,omitempty" example:"sha256:chunk_hash"`
}

// ChunkUploadResult represents the result of a chunk upload
type ChunkUploadResult struct {
	UploadID        string             `json:"upload_id" example:"upload_12345"`
	ChunkNumber     int                `json:"chunk_number" example:"1"`
	TotalChunks     int                `json:"total_chunks" example:"5"`
	IsComplete      bool               `json:"is_complete" example:"false"`
	PercentComplete float64            `json:"percent_complete" example:"20.0"`
	NextChunk       *int               `json:"next_chunk,omitempty" example:"2"`
	File            *MediaFileResponse `json:"file,omitempty"`
}

// ResumableUploadResponse represents a resumable upload session
type ResumableUploadResponse struct {
	ID             string                 `json:"id" example:"upload_12345"`
	TenantID       uint                   `json:"tenant_id" example:"1"`
	WebsiteID      uint                   `json:"website_id" example:"1"`
	UserID         uint                   `json:"user_id" example:"1"`
	Filename       string                 `json:"filename" example:"large-video.mp4"`
	MimeType       string                 `json:"mime_type" example:"video/mp4"`
	TotalSize      int64                  `json:"total_size" example:"26214400"`
	ChunkSize      int64                  `json:"chunk_size" example:"5242880"`
	TotalChunks    int                    `json:"total_chunks" example:"5"`
	UploadedChunks []int                  `json:"uploaded_chunks" example:"[1,2,3]"`
	Status         string                 `json:"status" example:"active"`
	CreatedAt      time.Time              `json:"created_at"`
	UpdatedAt      time.Time              `json:"updated_at"`
	ExpiresAt      time.Time              `json:"expires_at"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
}

// InitializeUploadRequest represents a request to initialize a resumable upload
type InitializeUploadRequest struct {
	WebsiteID   uint                   `json:"website_id" validate:"required" example:"1"`
	FolderID    *uint                  `json:"folder_id,omitempty" example:"1"`
	Filename    string                 `json:"filename" validate:"required" example:"large-video.mp4"`
	MimeType    string                 `json:"mime_type" validate:"required" example:"video/mp4"`
	TotalSize   int64                  `json:"total_size" validate:"required,min=1" example:"26214400"`
	ChunkSize   int64                  `json:"chunk_size,omitempty" example:"5242880"`
	AltText     *string                `json:"alt_text,omitempty" example:"Training video"`
	Title       *string                `json:"title,omitempty" example:"Company Training Video"`
	Description *string                `json:"description,omitempty" example:"New employee training material"`
	Category    string                 `json:"category,omitempty" example:"training"`
	Visibility  models.FileVisibility  `json:"visibility,omitempty" example:"private"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// InitializeUploadResponse represents a response for upload initialization
type InitializeUploadResponse struct {
	UploadID     string    `json:"upload_id" example:"upload_12345"`
	ChunkSize    int64     `json:"chunk_size" example:"5242880"`
	TotalChunks  int       `json:"total_chunks" example:"5"`
	ExpiresAt    time.Time `json:"expires_at"`
	NextChunkURL string    `json:"next_chunk_url" example:"/api/media/upload/upload_12345/chunk/1"`
	Status       string    `json:"status" example:"initialized"`
}

// CompleteUploadRequest represents a request to complete a resumable upload
type CompleteUploadRequest struct {
	UploadID string `json:"upload_id" validate:"required" example:"upload_12345"`
}

// UploadProgressResponse represents upload progress information
type UploadProgressResponse struct {
	UploadID        string    `json:"upload_id" example:"upload_12345"`
	TotalChunks     int       `json:"total_chunks" example:"5"`
	UploadedChunks  int       `json:"uploaded_chunks" example:"3"`
	PercentComplete float64   `json:"percent_complete" example:"60.0"`
	Status          string    `json:"status" example:"active"`
	NextChunk       *int      `json:"next_chunk,omitempty" example:"4"`
	EstimatedTime   *int      `json:"estimated_time,omitempty" example:"120"`
	UploadSpeed     *float64  `json:"upload_speed,omitempty" example:"1048576.0"`
	UpdatedAt       time.Time `json:"updated_at"`
}

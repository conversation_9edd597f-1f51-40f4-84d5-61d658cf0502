package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
)

// CreateMediaTagRequest represents a request to create a new media tag
type CreateMediaTagRequest struct {
	WebsiteID   uint                   `json:"website_id" validate:"required" example:"1"`
	Name        string                 `json:"name" validate:"required,min=1,max=100" example:"landscape"`
	Description *string                `json:"description,omitempty" example:"Tags for landscape and nature photography"`
	Color       string                 `json:"color,omitempty" example:"#22c55e"`
	Icon        *string                `json:"icon,omitempty" example:"mountain"`
	Category    string                 `json:"category,omitempty" example:"photography"`
	Type        models.TagType         `json:"type,omitempty" example:"user"`
	IsFeatured  bool                   `json:"is_featured,omitempty" example:"false"`
	IsPrivate   bool                   `json:"is_private,omitempty" example:"false"`
	Settings    map[string]interface{} `json:"settings,omitempty"`
}

// UpdateMediaTagRequest represents a request to update a media tag
type UpdateMediaTagRequest struct {
	Name        *string                `json:"name,omitempty" validate:"omitempty,min=1,max=100" example:"nature"`
	Description *string                `json:"description,omitempty" example:"Updated description for nature photography"`
	Color       *string                `json:"color,omitempty" example:"#16a34a"`
	Icon        *string                `json:"icon,omitempty" example:"tree"`
	Category    *string                `json:"category,omitempty" example:"nature"`
	IsFeatured  *bool                  `json:"is_featured,omitempty" example:"true"`
	IsPrivate   *bool                  `json:"is_private,omitempty" example:"false"`
	Settings    map[string]interface{} `json:"settings,omitempty"`
}

// MediaTagResponse represents a media tag response
type MediaTagResponse struct {
	ID              uint             `json:"id" example:"1"`
	TenantID        uint             `json:"tenant_id" example:"1"`
	WebsiteID       uint             `json:"website_id" example:"1"`
	UserID          uint             `json:"user_id" example:"1"`
	Name            string           `json:"name" example:"landscape"`
	Slug            string           `json:"slug" example:"landscape"`
	Description     *string          `json:"description,omitempty" example:"Tags for landscape and nature photography"`
	Color           string           `json:"color" example:"#22c55e"`
	Icon            *string          `json:"icon,omitempty" example:"mountain"`
	Category        string           `json:"category" example:"photography"`
	Type            models.TagType   `json:"type" example:"user"`
	UsageCount      uint             `json:"usage_count" example:"45"`
	PopularityScore float64          `json:"popularity_score" example:"8.5"`
	IsFeatured      bool             `json:"is_featured" example:"false"`
	IsPrivate       bool             `json:"is_private" example:"false"`
	Status          models.TagStatus `json:"status" example:"active"`
	CreatedAt       time.Time        `json:"created_at"`
	UpdatedAt       time.Time        `json:"updated_at"`
}

// MediaTagListResponse represents a simplified media tag response for listings
type MediaTagListResponse struct {
	ID              uint             `json:"id" example:"1"`
	Name            string           `json:"name" example:"landscape"`
	Slug            string           `json:"slug" example:"landscape"`
	Description     *string          `json:"description,omitempty" example:"Tags for landscape and nature photography"`
	Color           string           `json:"color" example:"#22c55e"`
	Icon            *string          `json:"icon,omitempty" example:"mountain"`
	Category        string           `json:"category" example:"photography"`
	Type            models.TagType   `json:"type" example:"user"`
	UsageCount      uint             `json:"usage_count" example:"45"`
	PopularityScore float64          `json:"popularity_score" example:"8.5"`
	IsFeatured      bool             `json:"is_featured" example:"false"`
	IsPrivate       bool             `json:"is_private" example:"false"`
	Status          models.TagStatus `json:"status" example:"active"`
	CreatedAt       time.Time        `json:"created_at"`
}

// MediaTagSearchRequest represents a request to search media tags
type MediaTagSearchRequest struct {
	WebsiteID  uint            `json:"website_id" validate:"required" example:"1"`
	Query      *string         `json:"query,omitempty" example:"nature"`
	Category   *string         `json:"category,omitempty" example:"photography"`
	Type       *models.TagType `json:"type,omitempty" example:"user"`
	IsFeatured *bool           `json:"is_featured,omitempty" example:"true"`
	IsPrivate  *bool           `json:"is_private,omitempty" example:"false"`
	SortBy     *string         `json:"sort_by,omitempty" validate:"omitempty,oneof=name usage_count popularity_score created_at" example:"usage_count"`
	SortOrder  *string         `json:"sort_order,omitempty" validate:"omitempty,oneof=asc desc" example:"desc"`
	Page       int             `json:"page,omitempty" validate:"omitempty,min=1" example:"1"`
	PageSize   int             `json:"page_size,omitempty" validate:"omitempty,min=1,max=100" example:"20"`
}

// MediaTagStatsResponse represents media tag statistics
type MediaTagStatsResponse struct {
	TotalTags      uint               `json:"total_tags" example:"125"`
	ActiveTags     uint               `json:"active_tags" example:"120"`
	ArchivedTags   uint               `json:"archived_tags" example:"5"`
	UserTags       uint               `json:"user_tags" example:"100"`
	SystemTags     uint               `json:"system_tags" example:"20"`
	AutoTags       uint               `json:"auto_tags" example:"5"`
	FeaturedTags   uint               `json:"featured_tags" example:"15"`
	PrivateTags    uint               `json:"private_tags" example:"10"`
	TagsByCategory map[string]uint    `json:"tags_by_category"`
	MostUsedTags   []MediaTagResponse `json:"most_used_tags"`
	RecentTags     []MediaTagResponse `json:"recent_tags"`
	TotalUsage     uint               `json:"total_usage" example:"2500"`
	AverageUsage   float64            `json:"average_usage" example:"20.0"`
}

// BulkTagOperationRequest represents a request for bulk operations on tags
type BulkTagOperationRequest struct {
	TagIDs    []uint `json:"tag_ids" validate:"required,min=1" example:"[1,2,3,4,5]"`
	Operation string `json:"operation" validate:"required,oneof=delete archive activate feature unfeature" example:"feature"`
}

// BulkTagOperationResponse represents a response for bulk tag operations
type BulkTagOperationResponse struct {
	TotalTags     uint   `json:"total_tags" example:"5"`
	ProcessedTags uint   `json:"processed_tags" example:"5"`
	FailedTags    uint   `json:"failed_tags" example:"0"`
	Status        string `json:"status" example:"success"`
	Message       string `json:"message" example:"All tags processed successfully"`
}

// MediaTagSuggestionsRequest represents a request for tag suggestions
type MediaTagSuggestionsRequest struct {
	WebsiteID uint   `json:"website_id" validate:"required" example:"1"`
	Query     string `json:"query" validate:"required,min=1" example:"nat"`
	Limit     int    `json:"limit,omitempty" validate:"omitempty,min=1,max=50" example:"10"`
}

// MediaTagSuggestionsResponse represents a response for tag suggestions
type MediaTagSuggestionsResponse struct {
	Query       string             `json:"query" example:"nat"`
	Suggestions []MediaTagResponse `json:"suggestions"`
	Total       uint               `json:"total" example:"3"`
}

// MediaTagCloudRequest represents a request for tag cloud data
type MediaTagCloudRequest struct {
	WebsiteID uint    `json:"website_id" validate:"required" example:"1"`
	MinUsage  *uint   `json:"min_usage,omitempty" example:"5"`
	MaxTags   *uint   `json:"max_tags,omitempty" example:"50"`
	Category  *string `json:"category,omitempty" example:"photography"`
}

// MediaTagCloudResponse represents a response for tag cloud data
type MediaTagCloudResponse struct {
	Tags  []MediaTagCloudItem `json:"tags"`
	Total uint                `json:"total" example:"25"`
}

// MediaTagCloudItem represents a single item in tag cloud
type MediaTagCloudItem struct {
	ID         uint    `json:"id" example:"1"`
	Name       string  `json:"name" example:"landscape"`
	Slug       string  `json:"slug" example:"landscape"`
	Color      string  `json:"color" example:"#22c55e"`
	UsageCount uint    `json:"usage_count" example:"45"`
	Weight     float64 `json:"weight" example:"0.85"`
	FontSize   uint    `json:"font_size" example:"18"`
}

// MediaTagUsageRequest represents a request for tag usage analytics
type MediaTagUsageRequest struct {
	WebsiteID   uint       `json:"website_id" validate:"required" example:"1"`
	StartDate   *time.Time `json:"start_date,omitempty"`
	EndDate     *time.Time `json:"end_date,omitempty"`
	Granularity string     `json:"granularity,omitempty" validate:"omitempty,oneof=day week month" example:"week"`
}

// MediaTagUsageResponse represents tag usage analytics
type MediaTagUsageResponse struct {
	TagID      uint                `json:"tag_id" example:"1"`
	TagName    string              `json:"tag_name" example:"landscape"`
	TotalUsage uint                `json:"total_usage" example:"45"`
	UsageData  []TagUsageDataPoint `json:"usage_data"`
	Trend      string              `json:"trend" example:"increasing"`
	ChangeRate float64             `json:"change_rate" example:"15.5"`
}

// TagUsageDataPoint represents a single data point in usage analytics
type TagUsageDataPoint struct {
	Date  time.Time `json:"date"`
	Count uint      `json:"count" example:"5"`
}

// MediaTagMergeRequest represents a request to merge tags
type MediaTagMergeRequest struct {
	SourceTagIDs []uint `json:"source_tag_ids" validate:"required,min=1" example:"[2,3,4]"`
	TargetTagID  uint   `json:"target_tag_id" validate:"required" example:"1"`
}

// MediaTagMergeResponse represents a response for tag merge operation
type MediaTagMergeResponse struct {
	TargetTagID    uint   `json:"target_tag_id" example:"1"`
	MergedTagCount uint   `json:"merged_tag_count" example:"3"`
	TotalFiles     uint   `json:"total_files" example:"75"`
	Status         string `json:"status" example:"completed"`
	Message        string `json:"message" example:"Successfully merged 3 tags into target tag"`
}

// MediaTagExportRequest represents a request to export tags
type MediaTagExportRequest struct {
	WebsiteID    uint   `json:"website_id" validate:"required" example:"1"`
	TagIDs       []uint `json:"tag_ids,omitempty" example:"[1,2,3]"`
	Format       string `json:"format" validate:"required,oneof=csv json xlsx" example:"csv"`
	IncludeFiles bool   `json:"include_files,omitempty" example:"true"`
}

// MediaTagImportRequest represents a request to import tags
type MediaTagImportRequest struct {
	WebsiteID     uint   `json:"website_id" validate:"required" example:"1"`
	Format        string `json:"format" validate:"required,oneof=csv json xlsx" example:"csv"`
	OverwriteMode string `json:"overwrite_mode" validate:"required,oneof=skip merge overwrite" example:"merge"`
}

// MediaTagImportResponse represents a response for tag import operation
type MediaTagImportResponse struct {
	TotalTags    uint   `json:"total_tags" example:"50"`
	ImportedTags uint   `json:"imported_tags" example:"45"`
	SkippedTags  uint   `json:"skipped_tags" example:"3"`
	FailedTags   uint   `json:"failed_tags" example:"2"`
	Status       string `json:"status" example:"partial_success"`
	Message      string `json:"message" example:"45 tags imported successfully, 5 tags had issues"`
}

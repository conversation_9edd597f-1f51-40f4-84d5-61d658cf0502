package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
)

// CreateFileTagRequest represents a request to tag a file
type CreateFileTagRequest struct {
	FileID          uint                   `json:"file_id" validate:"required" example:"1"`
	TagID           uint                   `json:"tag_id" validate:"required" example:"5"`
	TagSource       models.TagSource       `json:"tag_source,omitempty" example:"manual"`
	ConfidenceScore float64                `json:"confidence_score,omitempty" example:"1.0"`
	TagContext      map[string]interface{} `json:"tag_context,omitempty"`
}

// UpdateFileTagRequest represents a request to update a file tag
type UpdateFileTagRequest struct {
	ConfidenceScore *float64               `json:"confidence_score,omitempty" example:"0.95"`
	TagContext      map[string]interface{} `json:"tag_context,omitempty"`
}

// MediaFileTagResponse represents a file tag response
type MediaFileTagResponse struct {
	ID              uint                   `json:"id" example:"1"`
	FileID          uint                   `json:"file_id" example:"1"`
	TagID           uint                   `json:"tag_id" example:"5"`
	UserID          uint                   `json:"user_id" example:"1"`
	TaggedAt        time.Time              `json:"tagged_at"`
	TagSource       models.TagSource       `json:"tag_source" example:"manual"`
	ConfidenceScore float64                `json:"confidence_score" example:"1.0"`
	TagContext      map[string]interface{} `json:"tag_context,omitempty"`
	Tag             *MediaTagResponse      `json:"tag,omitempty"`
}

// BulkTagFilesRequest represents a request to tag multiple files
type BulkTagFilesRequest struct {
	FileIDs         []uint                 `json:"file_ids" validate:"required,min=1" example:"[1,2,3,4,5]"`
	TagIDs          []uint                 `json:"tag_ids" validate:"required,min=1" example:"[1,2]"`
	TagSource       models.TagSource       `json:"tag_source,omitempty" example:"manual"`
	ConfidenceScore float64                `json:"confidence_score,omitempty" example:"1.0"`
	TagContext      map[string]interface{} `json:"tag_context,omitempty"`
}

// BulkTagFilesResponse represents a response for bulk file tagging
type BulkTagFilesResponse struct {
	TotalFiles     uint   `json:"total_files" example:"5"`
	ProcessedFiles uint   `json:"processed_files" example:"5"`
	FailedFiles    uint   `json:"failed_files" example:"0"`
	TotalTags      uint   `json:"total_tags" example:"10"`
	ProcessedTags  uint   `json:"processed_tags" example:"10"`
	Status         string `json:"status" example:"success"`
	Message        string `json:"message" example:"All files tagged successfully"`
}

// BulkUntagFilesRequest represents a request to untag multiple files
type BulkUntagFilesRequest struct {
	FileIDs []uint `json:"file_ids" validate:"required,min=1" example:"[1,2,3]"`
	TagIDs  []uint `json:"tag_ids" validate:"required,min=1" example:"[1,2]"`
}

// BulkUntagFilesResponse represents a response for bulk file untagging
type BulkUntagFilesResponse struct {
	TotalFiles     uint   `json:"total_files" example:"3"`
	ProcessedFiles uint   `json:"processed_files" example:"3"`
	FailedFiles    uint   `json:"failed_files" example:"0"`
	TotalTags      uint   `json:"total_tags" example:"6"`
	RemovedTags    uint   `json:"removed_tags" example:"6"`
	Status         string `json:"status" example:"success"`
	Message        string `json:"message" example:"All tags removed successfully"`
}

// FileTagsRequest represents a request to get file tags
type FileTagsRequest struct {
	FileID uint `json:"file_id" validate:"required" example:"1"`
}

// FileTagsResponse represents a response for file tags
type FileTagsResponse struct {
	FileID uint                   `json:"file_id" example:"1"`
	Tags   []MediaFileTagResponse `json:"tags"`
	Total  uint                   `json:"total" example:"5"`
}

// TagFilesRequest represents a request to get files by tag
type TagFilesRequest struct {
	TagID    uint `json:"tag_id" validate:"required" example:"1"`
	Page     int  `json:"page,omitempty" validate:"omitempty,min=1" example:"1"`
	PageSize int  `json:"page_size,omitempty" validate:"omitempty,min=1,max=100" example:"20"`
}

// TagFilesResponse represents a response for tag files
type TagFilesResponse struct {
	TagID    uint                    `json:"tag_id" example:"1"`
	Files    []MediaFileListResponse `json:"files"`
	Total    uint                    `json:"total" example:"25"`
	Page     int                     `json:"page" example:"1"`
	PageSize int                     `json:"page_size" example:"20"`
}

// AutoTagRequest represents a request for automatic tagging
type AutoTagRequest struct {
	FileIDs        []uint   `json:"file_ids" validate:"required,min=1" example:"[1,2,3,4,5]"`
	MinConfidence  float64  `json:"min_confidence,omitempty" validate:"omitempty,min=0,max=1" example:"0.7"`
	MaxTagsPerFile uint     `json:"max_tags_per_file,omitempty" validate:"omitempty,min=1,max=50" example:"10"`
	EnableAI       bool     `json:"enable_ai,omitempty" example:"true"`
	TagCategories  []string `json:"tag_categories,omitempty" example:"['photography','nature']"`
}

// AutoTagResponse represents a response for automatic tagging
type AutoTagResponse struct {
	TotalFiles     uint                `json:"total_files" example:"5"`
	ProcessedFiles uint                `json:"processed_files" example:"4"`
	FailedFiles    uint                `json:"failed_files" example:"1"`
	TotalTags      uint                `json:"total_tags" example:"25"`
	CreatedTags    uint                `json:"created_tags" example:"20"`
	Results        []AutoTagFileResult `json:"results"`
	Status         string              `json:"status" example:"partial_success"`
	Message        string              `json:"message" example:"4 files processed successfully, 1 file failed"`
}

// AutoTagFileResult represents the result of auto-tagging a single file
type AutoTagFileResult struct {
	FileID   uint                   `json:"file_id" example:"1"`
	Filename string                 `json:"filename" example:"landscape.jpg"`
	Tags     []MediaFileTagResponse `json:"tags"`
	Status   string                 `json:"status" example:"success"`
	Error    *string                `json:"error,omitempty"`
}

// FileTagAnalyticsRequest represents a request for file tag analytics
type FileTagAnalyticsRequest struct {
	WebsiteID  uint               `json:"website_id" validate:"required" example:"1"`
	FileIDs    []uint             `json:"file_ids,omitempty" example:"[1,2,3]"`
	TagIDs     []uint             `json:"tag_ids,omitempty" example:"[1,2]"`
	TagSources []models.TagSource `json:"tag_sources,omitempty" example:"['manual','ai']"`
	DateFrom   *time.Time         `json:"date_from,omitempty"`
	DateTo     *time.Time         `json:"date_to,omitempty"`
}

// FileTagAnalyticsResponse represents file tag analytics
type FileTagAnalyticsResponse struct {
	TotalFileTags       uint                   `json:"total_file_tags" example:"250"`
	ManualTags          uint                   `json:"manual_tags" example:"150"`
	AutoTags            uint                   `json:"auto_tags" example:"50"`
	AITags              uint                   `json:"ai_tags" example:"40"`
	ImportedTags        uint                   `json:"imported_tags" example:"10"`
	AverageConfidence   float64                `json:"average_confidence" example:"0.85"`
	TagsBySource        map[string]uint        `json:"tags_by_source"`
	TagsByConfidence    map[string]uint        `json:"tags_by_confidence"`
	MostUsedTags        []MediaTagResponse     `json:"most_used_tags"`
	RecentlyTaggedFiles []MediaFileTagResponse `json:"recently_tagged_files"`
	TaggingActivity     []TaggingActivityPoint `json:"tagging_activity"`
}

// TaggingActivityPoint represents a point in tagging activity analytics
type TaggingActivityPoint struct {
	Date  time.Time `json:"date"`
	Count uint      `json:"count" example:"15"`
}

// SimilarFilesRequest represents a request to find similar files
type SimilarFilesRequest struct {
	FileID        uint    `json:"file_id" validate:"required" example:"1"`
	MinSimilarity float64 `json:"min_similarity,omitempty" validate:"omitempty,min=0,max=1" example:"0.5"`
	Limit         int     `json:"limit,omitempty" validate:"omitempty,min=1,max=100" example:"10"`
}

// SimilarFilesResponse represents a response for similar files
type SimilarFilesResponse struct {
	FileID       uint                `json:"file_id" example:"1"`
	SimilarFiles []SimilarFileResult `json:"similar_files"`
	Total        uint                `json:"total" example:"8"`
}

// SimilarFileResult represents a single similar file result
type SimilarFileResult struct {
	File            MediaFileListResponse `json:"file"`
	SimilarityScore float64               `json:"similarity_score" example:"0.85"`
	CommonTags      []MediaTagResponse    `json:"common_tags"`
	CommonTagCount  uint                  `json:"common_tag_count" example:"5"`
}

// FileTagSuggestionsRequest represents a request for tag suggestions
type FileTagSuggestionsRequest struct {
	FileID   uint `json:"file_id" validate:"required" example:"1"`
	Limit    int  `json:"limit,omitempty" validate:"omitempty,min=1,max=50" example:"10"`
	EnableAI bool `json:"enable_ai,omitempty" example:"true"`
}

// FileTagSuggestionsResponse represents tag suggestions for a file
type FileTagSuggestionsResponse struct {
	FileID      uint            `json:"file_id" example:"1"`
	Suggestions []TagSuggestion `json:"suggestions"`
	Total       uint            `json:"total" example:"8"`
}

// TagSuggestion represents a single tag suggestion
type TagSuggestion struct {
	Tag             MediaTagResponse `json:"tag"`
	ConfidenceScore float64          `json:"confidence_score" example:"0.85"`
	Reason          string           `json:"reason" example:"Based on image content analysis"`
	Source          models.TagSource `json:"source" example:"ai"`
}

// TagConflictRequest represents a request to resolve tag conflicts
type TagConflictRequest struct {
	FileID     uint   `json:"file_id" validate:"required" example:"1"`
	TagIDs     []uint `json:"tag_ids" validate:"required,min=2" example:"[1,2,3]"`
	Resolution string `json:"resolution" validate:"required,oneof=merge replace_all keep_highest_confidence" example:"keep_highest_confidence"`
}

// TagConflictResponse represents a response for tag conflict resolution
type TagConflictResponse struct {
	FileID       uint                   `json:"file_id" example:"1"`
	ResolvedTags []MediaFileTagResponse `json:"resolved_tags"`
	RemovedTags  []MediaFileTagResponse `json:"removed_tags"`
	Status       string                 `json:"status" example:"resolved"`
	Message      string                 `json:"message" example:"Tag conflicts resolved successfully"`
}

// TagValidationRequest represents a request to validate tags
type TagValidationRequest struct {
	FileIDs []uint `json:"file_ids" validate:"required,min=1" example:"[1,2,3]"`
}

// TagValidationResponse represents a response for tag validation
type TagValidationResponse struct {
	TotalFiles       uint                 `json:"total_files" example:"3"`
	ValidatedFiles   uint                 `json:"validated_files" example:"3"`
	TotalTags        uint                 `json:"total_tags" example:"15"`
	ValidTags        uint                 `json:"valid_tags" example:"12"`
	InvalidTags      uint                 `json:"invalid_tags" example:"3"`
	ValidationIssues []TagValidationIssue `json:"validation_issues"`
	Status           string               `json:"status" example:"completed"`
}

// TagValidationIssue represents a tag validation issue
type TagValidationIssue struct {
	FileID      uint   `json:"file_id" example:"1"`
	TagID       uint   `json:"tag_id" example:"5"`
	IssueType   string `json:"issue_type" example:"low_confidence"`
	Description string `json:"description" example:"Tag confidence score is below threshold"`
	Severity    string `json:"severity" example:"warning"`
}

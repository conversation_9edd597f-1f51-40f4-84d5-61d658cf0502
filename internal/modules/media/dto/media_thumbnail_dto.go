package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
)

// CreateThumbnailRequest represents a request to create a new thumbnail
type CreateThumbnailRequest struct {
	FileID            uint                    `json:"file_id" validate:"required" example:"1"`
	SizeName          string                  `json:"size_name" validate:"required" example:"medium"`
	Width             uint                    `json:"width" validate:"required" example:"300"`
	Height            uint                    `json:"height" validate:"required" example:"300"`
	Quality           uint                    `json:"quality,omitempty" validate:"omitempty,min=1,max=100" example:"85"`
	ProcessingMethod  models.ProcessingMethod `json:"processing_method,omitempty" example:"resize"`
	ProcessingOptions map[string]interface{}  `json:"processing_options,omitempty"`
	Format            string                  `json:"format,omitempty" example:"webp"`
	StorageType       models.StorageType      `json:"storage_type,omitempty" example:"local"`
}

// UpdateThumbnailRequest represents a request to update a thumbnail
type UpdateThumbnailRequest struct {
	Quality           *uint                    `json:"quality,omitempty" validate:"omitempty,min=1,max=100" example:"90"`
	ProcessingMethod  *models.ProcessingMethod `json:"processing_method,omitempty" example:"crop"`
	ProcessingOptions map[string]interface{}   `json:"processing_options,omitempty"`
	Format            *string                  `json:"format,omitempty" example:"jpg"`
	IsOptimized       *bool                    `json:"is_optimized,omitempty" example:"true"`
}

// MediaThumbnailResponse represents a media thumbnail response
type MediaThumbnailResponse struct {
	ID                  uint                    `json:"id" example:"1"`
	FileID              uint                    `json:"file_id" example:"1"`
	SizeName            string                  `json:"size_name" example:"medium"`
	Width               uint                    `json:"width" example:"300"`
	Height              uint                    `json:"height" example:"300"`
	Quality             uint                    `json:"quality" example:"85"`
	StorageType         models.StorageType      `json:"storage_type" example:"local"`
	StoragePath         string                  `json:"storage_path" example:"/uploads/thumbnails/2024/01/image_medium.webp"`
	PublicURL           string                  `json:"public_url" example:"https://example.com/uploads/thumbnails/2024/01/image_medium.webp"`
	FileSize            uint64                  `json:"file_size" example:"25600"`
	FileHash            string                  `json:"file_hash" example:"sha256:efgh5678..."`
	MimeType            string                  `json:"mime_type" example:"image/webp"`
	ProcessingMethod    models.ProcessingMethod `json:"processing_method" example:"resize"`
	Format              string                  `json:"format" example:"webp"`
	IsOptimized         bool                    `json:"is_optimized" example:"true"`
	OptimizationSavings float64                 `json:"optimization_savings" example:"35.5"`
	Status              models.ThumbnailStatus  `json:"status" example:"ready"`
	CreatedAt           time.Time               `json:"created_at"`
	UpdatedAt           time.Time               `json:"updated_at"`
}

// ThumbnailGenerationRequest represents a request to generate thumbnails
type ThumbnailGenerationRequest struct {
	FileID   uint                   `json:"file_id" validate:"required" example:"1"`
	Sizes    []ThumbnailSizeRequest `json:"sizes" validate:"required,min=1"`
	Format   string                 `json:"format,omitempty" example:"webp"`
	Quality  uint                   `json:"quality,omitempty" validate:"omitempty,min=1,max=100" example:"85"`
	Optimize bool                   `json:"optimize,omitempty" example:"true"`
}

// ThumbnailSizeRequest represents a thumbnail size specification
type ThumbnailSizeRequest struct {
	Name             string                  `json:"name" validate:"required" example:"medium"`
	Width            uint                    `json:"width" validate:"required" example:"300"`
	Height           uint                    `json:"height" validate:"required" example:"300"`
	ProcessingMethod models.ProcessingMethod `json:"processing_method,omitempty" example:"resize"`
}

// ThumbnailGenerationResponse represents a response for thumbnail generation
type ThumbnailGenerationResponse struct {
	FileID     uint                     `json:"file_id" example:"1"`
	Thumbnails []MediaThumbnailResponse `json:"thumbnails"`
	Status     string                   `json:"status" example:"completed"`
	Message    string                   `json:"message" example:"Generated 4 thumbnails successfully"`
}

// ThumbnailBatchRequest represents a request for batch thumbnail generation
type ThumbnailBatchRequest struct {
	FileIDs  []uint                 `json:"file_ids" validate:"required,min=1" example:"[1,2,3,4,5]"`
	Sizes    []ThumbnailSizeRequest `json:"sizes" validate:"required,min=1"`
	Format   string                 `json:"format,omitempty" example:"webp"`
	Quality  uint                   `json:"quality,omitempty" validate:"omitempty,min=1,max=100" example:"85"`
	Optimize bool                   `json:"optimize,omitempty" example:"true"`
}

// ThumbnailBatchResponse represents a response for batch thumbnail generation
type ThumbnailBatchResponse struct {
	TotalFiles     uint                          `json:"total_files" example:"5"`
	ProcessedFiles uint                          `json:"processed_files" example:"4"`
	FailedFiles    uint                          `json:"failed_files" example:"1"`
	Results        []ThumbnailGenerationResponse `json:"results"`
	Status         string                        `json:"status" example:"partial_success"`
	Message        string                        `json:"message" example:"4 files processed successfully, 1 file failed"`
}

// ThumbnailStatsResponse represents thumbnail statistics
type ThumbnailStatsResponse struct {
	TotalThumbnails     uint            `json:"total_thumbnails" example:"1250"`
	TotalSize           uint64          `json:"total_size" example:"125000000"`
	OptimizedThumbnails uint            `json:"optimized_thumbnails" example:"1100"`
	AverageOptimization float64         `json:"average_optimization" example:"32.5"`
	TotalSavings        uint64          `json:"total_savings" example:"40625000"`
	ThumbnailsBySize    map[string]uint `json:"thumbnails_by_size"`
	ThumbnailsByFormat  map[string]uint `json:"thumbnails_by_format"`
	ThumbnailsByStatus  map[string]uint `json:"thumbnails_by_status"`
}

// ThumbnailSearchRequest represents a request to search thumbnails
type ThumbnailSearchRequest struct {
	FileIDs          []uint                   `json:"file_ids,omitempty" example:"[1,2,3]"`
	SizeNames        []string                 `json:"size_names,omitempty" example:"['small','medium','large']"`
	Format           *string                  `json:"format,omitempty" example:"webp"`
	Status           *models.ThumbnailStatus  `json:"status,omitempty" example:"ready"`
	ProcessingMethod *models.ProcessingMethod `json:"processing_method,omitempty" example:"resize"`
	IsOptimized      *bool                    `json:"is_optimized,omitempty" example:"true"`
	MinWidth         *uint                    `json:"min_width,omitempty" example:"100"`
	MaxWidth         *uint                    `json:"max_width,omitempty" example:"1000"`
	MinHeight        *uint                    `json:"min_height,omitempty" example:"100"`
	MaxHeight        *uint                    `json:"max_height,omitempty" example:"1000"`
	MinFileSize      *uint64                  `json:"min_file_size,omitempty" example:"1000"`
	MaxFileSize      *uint64                  `json:"max_file_size,omitempty" example:"1000000"`
	CreatedAfter     *time.Time               `json:"created_after,omitempty"`
	CreatedBefore    *time.Time               `json:"created_before,omitempty"`
	SortBy           *string                  `json:"sort_by,omitempty" validate:"omitempty,oneof=size_name width height file_size created_at" example:"created_at"`
	SortOrder        *string                  `json:"sort_order,omitempty" validate:"omitempty,oneof=asc desc" example:"desc"`
	Page             int                      `json:"page,omitempty" validate:"omitempty,min=1" example:"1"`
	PageSize         int                      `json:"page_size,omitempty" validate:"omitempty,min=1,max=100" example:"20"`
}

// ThumbnailRegenerateRequest represents a request to regenerate thumbnails
type ThumbnailRegenerateRequest struct {
	FileIDs      []uint   `json:"file_ids,omitempty" example:"[1,2,3]"`
	SizeNames    []string `json:"size_names,omitempty" example:"['medium','large']"`
	Force        bool     `json:"force,omitempty" example:"false"`
	UpdateFormat bool     `json:"update_format,omitempty" example:"true"`
	NewFormat    string   `json:"new_format,omitempty" example:"webp"`
	NewQuality   *uint    `json:"new_quality,omitempty" validate:"omitempty,min=1,max=100" example:"90"`
}

// ThumbnailRegenerateResponse represents a response for thumbnail regeneration
type ThumbnailRegenerateResponse struct {
	TotalThumbnails       uint   `json:"total_thumbnails" example:"15"`
	RegeneratedThumbnails uint   `json:"regenerated_thumbnails" example:"12"`
	SkippedThumbnails     uint   `json:"skipped_thumbnails" example:"2"`
	FailedThumbnails      uint   `json:"failed_thumbnails" example:"1"`
	Status                string `json:"status" example:"partial_success"`
	Message               string `json:"message" example:"12 thumbnails regenerated successfully"`
}

// ThumbnailCleanupRequest represents a request to cleanup orphaned thumbnails
type ThumbnailCleanupRequest struct {
	DryRun         bool       `json:"dry_run,omitempty" example:"true"`
	OlderThan      *time.Time `json:"older_than,omitempty"`
	StatusFilter   []string   `json:"status_filter,omitempty" example:"['error','deleted']"`
	SizeNameFilter []string   `json:"size_name_filter,omitempty" example:"['small']"`
}

// ThumbnailCleanupResponse represents a response for thumbnail cleanup
type ThumbnailCleanupResponse struct {
	TotalThumbnails   uint   `json:"total_thumbnails" example:"50"`
	CleanedThumbnails uint   `json:"cleaned_thumbnails" example:"8"`
	FreedSpace        uint64 `json:"freed_space" example:"25600000"`
	Status            string `json:"status" example:"completed"`
	Message           string `json:"message" example:"Cleaned up 8 orphaned thumbnails, freed 25.6MB"`
}

// ThumbnailConfigRequest represents a request to configure thumbnail settings
type ThumbnailConfigRequest struct {
	DefaultFormat   string                 `json:"default_format" validate:"required,oneof=jpg png webp avif" example:"webp"`
	DefaultQuality  uint                   `json:"default_quality" validate:"required,min=1,max=100" example:"85"`
	AutoGenerate    bool                   `json:"auto_generate" example:"true"`
	DefaultSizes    []ThumbnailSizeRequest `json:"default_sizes" validate:"required,min=1"`
	OptimizeDefault bool                   `json:"optimize_default" example:"true"`
	StorageProvider string                 `json:"storage_provider,omitempty" example:"local"`
}

// ThumbnailConfigResponse represents thumbnail configuration
type ThumbnailConfigResponse struct {
	DefaultFormat   string                 `json:"default_format" example:"webp"`
	DefaultQuality  uint                   `json:"default_quality" example:"85"`
	AutoGenerate    bool                   `json:"auto_generate" example:"true"`
	DefaultSizes    []ThumbnailSizeRequest `json:"default_sizes"`
	OptimizeDefault bool                   `json:"optimize_default" example:"true"`
	StorageProvider string                 `json:"storage_provider" example:"local"`
	UpdatedAt       time.Time              `json:"updated_at"`
}

// ThumbnailOptimizationRequest represents a request to optimize thumbnails
type ThumbnailOptimizationRequest struct {
	ThumbnailIDs []uint `json:"thumbnail_ids,omitempty" example:"[1,2,3]"`
	FileIDs      []uint `json:"file_ids,omitempty" example:"[1,2]"`
	TargetFormat string `json:"target_format,omitempty" example:"webp"`
	Quality      *uint  `json:"quality,omitempty" validate:"omitempty,min=1,max=100" example:"80"`
	Force        bool   `json:"force,omitempty" example:"false"`
}

// ThumbnailOptimizationResponse represents a response for thumbnail optimization
type ThumbnailOptimizationResponse struct {
	TotalThumbnails     uint    `json:"total_thumbnails" example:"25"`
	OptimizedThumbnails uint    `json:"optimized_thumbnails" example:"22"`
	SkippedThumbnails   uint    `json:"skipped_thumbnails" example:"2"`
	FailedThumbnails    uint    `json:"failed_thumbnails" example:"1"`
	TotalSizeBefore     uint64  `json:"total_size_before" example:"12800000"`
	TotalSizeAfter      uint64  `json:"total_size_after" example:"8960000"`
	SpaceSaved          uint64  `json:"space_saved" example:"3840000"`
	AverageSavings      float64 `json:"average_savings" example:"30.0"`
	Status              string  `json:"status" example:"partial_success"`
	Message             string  `json:"message" example:"22 thumbnails optimized, saved 3.8MB"`
}

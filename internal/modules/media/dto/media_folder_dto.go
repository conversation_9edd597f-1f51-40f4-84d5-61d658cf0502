package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
)

// CreateMediaFolderRequest represents a request to create a new media folder
type CreateMediaFolderRequest struct {
	WebsiteID   uint                    `json:"website_id" validate:"required" example:"1"`
	ParentID    *uint                   `json:"parent_id,omitempty" example:"1"`
	Name        string                  `json:"name" validate:"required,min=1,max=255" example:"Nature Photos"`
	Description *string                 `json:"description,omitempty" example:"Collection of nature and landscape photographs"`
	Type        models.FolderType       `json:"type,omitempty" example:"user"`
	Color       string                  `json:"color,omitempty" example:"#22c55e"`
	Icon        string                  `json:"icon,omitempty" example:"folder"`
	Visibility  models.FolderVisibility `json:"visibility,omitempty" example:"public"`
	Settings    map[string]interface{}  `json:"settings,omitempty"`
}

// UpdateMediaFolderRequest represents a request to update a media folder
type UpdateMediaFolderRequest struct {
	Name        *string                  `json:"name,omitempty" validate:"omitempty,min=1,max=255" example:"Updated Nature Photos"`
	Description *string                  `json:"description,omitempty" example:"Updated collection description"`
	ParentID    *uint                    `json:"parent_id,omitempty" example:"2"`
	Color       *string                  `json:"color,omitempty" example:"#3b82f6"`
	Icon        *string                  `json:"icon,omitempty" example:"image"`
	Visibility  *models.FolderVisibility `json:"visibility,omitempty" example:"private"`
	Settings    map[string]interface{}   `json:"settings,omitempty"`
}

// MediaFolderResponse represents a media folder response
type MediaFolderResponse struct {
	ID          uint                    `json:"id" example:"1"`
	TenantID    uint                    `json:"tenant_id" example:"1"`
	WebsiteID   uint                    `json:"website_id" example:"1"`
	ParentID    *uint                   `json:"parent_id,omitempty" example:"1"`
	UserID      uint                    `json:"user_id" example:"1"`
	Name        string                  `json:"name" example:"Nature Photos"`
	Slug        string                  `json:"slug" example:"nature-photos"`
	Path        string                  `json:"path" example:"/nature-photos"`
	Description *string                 `json:"description,omitempty" example:"Collection of nature and landscape photographs"`
	Type        models.FolderType       `json:"type" example:"user"`
	Color       string                  `json:"color" example:"#22c55e"`
	Icon        string                  `json:"icon" example:"folder"`
	Visibility  models.FolderVisibility `json:"visibility" example:"public"`
	Level       uint                    `json:"level" example:"1"`
	SortOrder   uint                    `json:"sort_order" example:"0"`
	FileCount   uint                    `json:"file_count" example:"25"`
	TotalSize   uint64                  `json:"total_size" example:"128000000"`
	Status      models.FolderStatus     `json:"status" example:"active"`
	CreatedAt   time.Time               `json:"created_at"`
	UpdatedAt   time.Time               `json:"updated_at"`
	Children    []MediaFolderResponse   `json:"children,omitempty"`
	Files       []MediaFileListResponse `json:"files,omitempty"`
}

// MediaFolderTreeResponse represents a folder tree structure response
type MediaFolderTreeResponse struct {
	ID        uint                      `json:"id" example:"1"`
	Name      string                    `json:"name" example:"Nature Photos"`
	Path      string                    `json:"path" example:"/nature-photos"`
	Icon      string                    `json:"icon" example:"folder"`
	Color     string                    `json:"color" example:"#22c55e"`
	FileCount uint                      `json:"file_count" example:"25"`
	TotalSize uint64                    `json:"total_size" example:"128000000"`
	Level     uint                      `json:"level" example:"1"`
	Children  []MediaFolderTreeResponse `json:"children,omitempty"`
}

// MediaFolderListResponse represents a simplified folder response for listings
type MediaFolderListResponse struct {
	ID          uint                    `json:"id" example:"1"`
	Name        string                  `json:"name" example:"Nature Photos"`
	Slug        string                  `json:"slug" example:"nature-photos"`
	Path        string                  `json:"path" example:"/nature-photos"`
	Description *string                 `json:"description,omitempty" example:"Collection of nature and landscape photographs"`
	Type        models.FolderType       `json:"type" example:"user"`
	Color       string                  `json:"color" example:"#22c55e"`
	Icon        string                  `json:"icon" example:"folder"`
	Visibility  models.FolderVisibility `json:"visibility" example:"public"`
	Level       uint                    `json:"level" example:"1"`
	FileCount   uint                    `json:"file_count" example:"25"`
	TotalSize   uint64                  `json:"total_size" example:"128000000"`
	Status      models.FolderStatus     `json:"status" example:"active"`
	CreatedAt   time.Time               `json:"created_at"`
	UpdatedAt   time.Time               `json:"updated_at"`
}

// MediaFolderSearchRequest represents a request to search media folders
type MediaFolderSearchRequest struct {
	WebsiteID  uint                     `json:"website_id" validate:"required" example:"1"`
	ParentID   *uint                    `json:"parent_id,omitempty" example:"1"`
	Type       *models.FolderType       `json:"type,omitempty" example:"user"`
	Visibility *models.FolderVisibility `json:"visibility,omitempty" example:"public"`
	Query      *string                  `json:"query,omitempty" example:"nature"`
	SortBy     *string                  `json:"sort_by,omitempty" validate:"omitempty,oneof=name file_count total_size created_at updated_at" example:"name"`
	SortOrder  *string                  `json:"sort_order,omitempty" validate:"omitempty,oneof=asc desc" example:"asc"`
	Page       int                      `json:"page,omitempty" validate:"omitempty,min=1" example:"1"`
	PageSize   int                      `json:"page_size,omitempty" validate:"omitempty,min=1,max=100" example:"20"`
}

// MediaFolderStatsResponse represents media folder statistics
type MediaFolderStatsResponse struct {
	TotalFolders    uint   `json:"total_folders" example:"25"`
	TotalFiles      uint   `json:"total_files" example:"150"`
	TotalSize       uint64 `json:"total_size" example:"750000000"`
	ActiveFolders   uint   `json:"active_folders" example:"23"`
	ArchivedFolders uint   `json:"archived_folders" example:"2"`
	UserFolders     uint   `json:"user_folders" example:"20"`
	SystemFolders   uint   `json:"system_folders" example:"3"`
	PublicFolders   uint   `json:"public_folders" example:"18"`
	PrivateFolders  uint   `json:"private_folders" example:"5"`
	SharedFolders   uint   `json:"shared_folders" example:"2"`
}

// MediaFolderMoveRequest represents a request to move a folder
type MediaFolderMoveRequest struct {
	ParentID *uint `json:"parent_id" validate:"required" example:"2"`
}

// MediaFolderCopyRequest represents a request to copy a folder
type MediaFolderCopyRequest struct {
	WebsiteID uint   `json:"website_id" validate:"required" example:"1"`
	ParentID  *uint  `json:"parent_id,omitempty" example:"2"`
	Name      string `json:"name" validate:"required,min=1,max=255" example:"Copy of Nature Photos"`
	CopyFiles bool   `json:"copy_files,omitempty" example:"true"`
}

// MediaFolderBreadcrumbItem represents a breadcrumb item
type MediaFolderBreadcrumbItem struct {
	ID   uint   `json:"id" example:"1"`
	Name string `json:"name" example:"Nature Photos"`
	Path string `json:"path" example:"/nature-photos"`
}

// MediaFolderBreadcrumbResponse represents folder breadcrumb navigation
type MediaFolderBreadcrumbResponse struct {
	Items []MediaFolderBreadcrumbItem `json:"items"`
}

// BulkFolderOperationRequest represents a request for bulk operations on folders
type BulkFolderOperationRequest struct {
	FolderIDs []uint `json:"folder_ids" validate:"required,min=1" example:"[1,2,3]"`
	Operation string `json:"operation" validate:"required,oneof=delete archive activate move" example:"archive"`
	ParentID  *uint  `json:"parent_id,omitempty" example:"2"`
}

// BulkFolderOperationResponse represents a response for bulk folder operations
type BulkFolderOperationResponse struct {
	TotalFolders     uint   `json:"total_folders" example:"3"`
	ProcessedFolders uint   `json:"processed_folders" example:"3"`
	FailedFolders    uint   `json:"failed_folders" example:"0"`
	Status           string `json:"status" example:"success"`
	Message          string `json:"message" example:"All folders processed successfully"`
}

// MediaFolderPermissionRequest represents a request to manage folder permissions
type MediaFolderPermissionRequest struct {
	UserID     uint   `json:"user_id" validate:"required" example:"2"`
	Permission string `json:"permission" validate:"required,oneof=read write delete" example:"read"`
}

// MediaFolderPermissionResponse represents folder permission information
type MediaFolderPermissionResponse struct {
	UserID     uint      `json:"user_id" example:"2"`
	Permission string    `json:"permission" example:"read"`
	GrantedAt  time.Time `json:"granted_at"`
}

// MediaFolderSyncRequest represents a request to sync folder content
type MediaFolderSyncRequest struct {
	Force bool `json:"force,omitempty" example:"false"`
}

// MediaFolderSyncResponse represents a response for folder sync operation
type MediaFolderSyncResponse struct {
	FolderID     uint      `json:"folder_id" example:"1"`
	SyncedFiles  uint      `json:"synced_files" example:"25"`
	AddedFiles   uint      `json:"added_files" example:"3"`
	UpdatedFiles uint      `json:"updated_files" example:"2"`
	RemovedFiles uint      `json:"removed_files" example:"1"`
	Status       string    `json:"status" example:"completed"`
	Message      string    `json:"message" example:"Folder sync completed successfully"`
	SyncedAt     time.Time `json:"synced_at"`
}

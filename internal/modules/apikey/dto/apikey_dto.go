package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/apikey/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// CreateAPIKeyRequest represents the request to create a new API key
type CreateAPIKeyRequest struct {
	Name        string                 `json:"name" validate:"required,min=1,max=100" example:"Production API Key"`
	Description string                 `json:"description" validate:"max=1000" example:"API key for production environment"`
	Permissions map[string]interface{} `json:"permissions,omitempty"`
	Scopes      []string               `json:"scopes,omitempty" example:"read,write"`
	IPWhitelist []string               `json:"ip_whitelist,omitempty" example:"***********,***********"`
	RateLimit   int                    `json:"rate_limit" validate:"min=1,max=100000" example:"1000"`
	RateWindow  int                    `json:"rate_window" validate:"min=1,max=86400" example:"3600"`
	ExpiresIn   int                    `json:"expires_in" validate:"min=1" example:"7776000"`
}

// UpdateAPIKeyRequest represents the request to update an API key
type UpdateAPIKeyRequest struct {
	Name        *string                 `json:"name,omitempty" validate:"omitempty,min=1,max=100" example:"Updated API Key"`
	Description *string                 `json:"description,omitempty" validate:"omitempty,max=1000" example:"Updated description"`
	Permissions *map[string]interface{} `json:"permissions,omitempty"`
	Scopes      *[]string               `json:"scopes,omitempty"`
	IPWhitelist *[]string               `json:"ip_whitelist,omitempty"`
	RateLimit   *int                    `json:"rate_limit,omitempty" validate:"omitempty,min=1,max=100000" example:"2000"`
	RateWindow  *int                    `json:"rate_window,omitempty" validate:"omitempty,min=1,max=86400" example:"7200"`
	Status      *models.APIKeyStatus    `json:"status,omitempty" validate:"omitempty,oneof=active inactive" example:"active"`
}

// APIKeyResponse represents the response when returning an API key
type APIKeyResponse struct {
	ID          uint                `json:"id" example:"1"`
	TenantID    uint                `json:"tenant_id" example:"1"`
	WebsiteID   uint                `json:"website_id" example:"1"`
	Key         string              `json:"key,omitempty" example:"ak_1234567890abcdef"` // KEEP_OMITEMPTY: Sensitive data, only shown on creation
	KeyPrefix   string              `json:"key_prefix" example:"ak_1234"`
	Name        string              `json:"name" example:"Production API Key"`
	Description string              `json:"description" example:"API key for production environment"`
	Status      models.APIKeyStatus `json:"status" example:"active"`
	RateLimit   int                 `json:"rate_limit" example:"1000"`
	RateWindow  int                 `json:"rate_window" example:"3600"`
	ExpiresAt   *time.Time          `json:"expires_at,omitempty"`   // KEEP_OMITEMPTY: Optional expiration
	LastUsedAt  *time.Time          `json:"last_used_at,omitempty"` // KEEP_OMITEMPTY: Optional usage timestamp
	CreatedAt   time.Time           `json:"created_at"`
	UpdatedAt   time.Time           `json:"updated_at"`
}

// APIKeyDetailResponse represents detailed API key information
type APIKeyDetailResponse struct {
	APIKeyResponse
	Permissions     map[string]interface{} `json:"permissions"`
	Scopes          []string               `json:"scopes"`
	IPWhitelist     []string               `json:"ip_whitelist"`
	UsageStats      *APIKeyUsageStats      `json:"usage_stats,omitempty"`      // KEEP_OMITEMPTY: Optional analytics data
	PermissionList  []APIKeyPermission     `json:"permission_list,omitempty"`  // KEEP_OMITEMPTY: Optional nested data
	ScopeList       []APIKeyScope          `json:"scope_list,omitempty"`       // KEEP_OMITEMPTY: Optional nested data
	RecentUsage     []APIKeyUsage          `json:"recent_usage,omitempty"`     // KEEP_OMITEMPTY: Optional analytics data
	RotationHistory []APIKeyRotation       `json:"rotation_history,omitempty"` // KEEP_OMITEMPTY: Optional history data
}

// APIKeyListResponse represents the response for listing API keys
type APIKeyListResponse struct {
	APIKeys    []APIKeyResponse           `json:"api_keys"`
	Pagination *pagination.CursorResponse `json:"pagination"`
}

// APIKeyFilter represents filters for querying API keys
type APIKeyFilter struct {
	pagination.CursorRequest
	Status    *models.APIKeyStatus `form:"status" validate:"omitempty,oneof=active inactive expired revoked"`
	Search    string               `form:"search" validate:"max=100"`
	CreatedBy *uint                `form:"created_by" validate:"omitempty,min=1"`
	ExpiresIn *int                 `form:"expires_in" validate:"omitempty,min=1"`
	SortBy    string               `form:"sort_by" validate:"omitempty,oneof=name created_at updated_at last_used_at" example:"created_at"`
	SortOrder string               `form:"sort_order" validate:"omitempty,oneof=asc desc" example:"desc"`
}

package dto

// APIKeyBulkActionRequest represents the request for bulk actions on API keys
type APIKeyBulkActionRequest struct {
	APIKeyIDs []uint `json:"api_key_ids" validate:"required,min=1" example:"[1,2,3]"`
	Action    string `json:"action" validate:"required,oneof=activate deactivate revoke delete" example:"activate"`
	Reason    string `json:"reason" validate:"max=255" example:"Bulk activation for new deployment"`
}

// APIKeyBulkActionResponse represents the response for bulk actions
type APIKeyBulkActionResponse struct {
	Success     []uint              `json:"success" example:"[1,2]"`
	Failed      []BulkActionFailure `json:"failed"`
	Total       int                 `json:"total" example:"3"`
	Successful  int                 `json:"successful" example:"2"`
	FailedCount int                 `json:"failed_count" example:"1"`
}

// BulkActionFailure represents a failed bulk action
type BulkActionFailure struct {
	APIKeyID uint   `json:"api_key_id" example:"3"`
	Error    string `json:"error" example:"API key not found"`
}

package dto

import "time"

// CreateAPIKeyPermissionRequest represents the request to create API key permissions
type CreateAPIKeyPermissionRequest struct {
	Resource   string                 `json:"resource" validate:"required,min=1,max=100" example:"users"`
	Action     string                 `json:"action" validate:"required,min=1,max=50" example:"read"`
	Conditions map[string]interface{} `json:"conditions,omitempty"`
}

// APIKeyPermissionResponse represents the response for API key permissions
type APIKeyPermissionResponse struct {
	ID         uint                   `json:"id" example:"1"`
	Resource   string                 `json:"resource" example:"users"`
	Action     string                 `json:"action" example:"read"`
	Conditions map[string]interface{} `json:"conditions"`
	CreatedAt  time.Time              `json:"created_at"`
}

// CreateAPIKeyScopeRequest represents the request to create API key scopes
type CreateAPIKeyScopeRequest struct {
	Scope      string                 `json:"scope" validate:"required,min=1,max=100" example:"user:read"`
	Resource   string                 `json:"resource" validate:"required,min=1,max=100" example:"users"`
	Actions    []string               `json:"actions" validate:"required,min=1" example:"read,write"`
	Conditions map[string]interface{} `json:"conditions,omitempty"`
	ExpiresIn  int                    `json:"expires_in" validate:"min=1" example:"3600"`
}

// APIKeyScopeResponse represents the response for API key scopes
type APIKeyScopeResponse struct {
	ID         uint                   `json:"id" example:"1"`
	Scope      string                 `json:"scope" example:"user:read"`
	Resource   string                 `json:"resource" example:"users"`
	Actions    []string               `json:"actions" example:"read,write"`
	Conditions map[string]interface{} `json:"conditions"`
	IsActive   bool                   `json:"is_active" example:"true"`
	ExpiresAt  *time.Time             `json:"expires_at,omitempty"`
	CreatedAt  time.Time              `json:"created_at"`
	UpdatedAt  time.Time              `json:"updated_at"`
}

// APIKeyPermission represents permission entry
type APIKeyPermission struct {
	ID         uint                   `json:"id"`
	APIKeyID   uint                   `json:"api_key_id"`
	Resource   string                 `json:"resource"`
	Action     string                 `json:"action"`
	Conditions map[string]interface{} `json:"conditions"`
	IsActive   bool                   `json:"is_active"`
	CreatedAt  time.Time              `json:"created_at"`
	UpdatedAt  time.Time              `json:"updated_at"`
}

// APIKeyScope represents scope entry
type APIKeyScope struct {
	ID         uint                   `json:"id"`
	APIKeyID   uint                   `json:"api_key_id"`
	Scope      string                 `json:"scope"`
	Resource   string                 `json:"resource"`
	Actions    []string               `json:"actions"`
	Conditions map[string]interface{} `json:"conditions"`
	IsActive   bool                   `json:"is_active"`
	ExpiresAt  *time.Time             `json:"expires_at,omitempty"`
	CreatedAt  time.Time              `json:"created_at"`
	UpdatedAt  time.Time              `json:"updated_at"`
}

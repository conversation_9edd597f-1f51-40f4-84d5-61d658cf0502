package dto

import "time"

// APIKeyUsageRequest represents the request for API key usage analytics
type APIKeyUsageRequest struct {
	Period      string `form:"period" validate:"omitempty,oneof=1h 24h 7d 30d 90d" example:"7d"`
	StartDate   string `form:"start_date" example:"2023-01-01"`
	EndDate     string `form:"end_date" example:"2023-01-31"`
	Granularity string `form:"granularity" validate:"omitempty,oneof=hour day week month" example:"day"`
}

// APIKeyUsageResponse represents the response for API key usage analytics
type APIKeyUsageResponse struct {
	Summary    APIKeyUsageStats `json:"summary"`
	Timeline   []UsageDataPoint `json:"timeline"`
	ByEndpoint []EndpointUsage  `json:"by_endpoint"`
	ByStatus   map[string]int64 `json:"by_status"`
	ByMethod   map[string]int64 `json:"by_method"`
	TopIPs     []IPUsage        `json:"top_ips"`
}

// APIKeyUsageStats represents usage statistics for an API key
type APIKeyUsageStats struct {
	TotalRequests       int64      `json:"total_requests" example:"10000"`
	SuccessfulRequests  int64      `json:"successful_requests" example:"9500"`
	FailedRequests      int64      `json:"failed_requests" example:"500"`
	AverageResponseTime int64      `json:"average_response_time" example:"150"`
	LastUsedAt          *time.Time `json:"last_used_at"`
	MostUsedEndpoint    string     `json:"most_used_endpoint" example:"/api/v1/users"`
	RequestsToday       int64      `json:"requests_today" example:"250"`
	RequestsThisWeek    int64      `json:"requests_this_week" example:"1500"`
	RequestsThisMonth   int64      `json:"requests_this_month" example:"6000"`
	ErrorRate           float64    `json:"error_rate" example:"0.05"`
}

// UsageDataPoint represents a single point in usage timeline
type UsageDataPoint struct {
	Timestamp time.Time `json:"timestamp"`
	Requests  int64     `json:"requests" example:"120"`
	Errors    int64     `json:"errors" example:"5"`
	AvgTime   int64     `json:"avg_response_time" example:"145"`
}

// EndpointUsage represents usage statistics for a specific endpoint
type EndpointUsage struct {
	Endpoint        string    `json:"endpoint" example:"/api/v1/users"`
	Method          string    `json:"method" example:"GET"`
	Count           int64     `json:"count" example:"3500"`
	AvgResponseTime int64     `json:"avg_response_time" example:"120"`
	ErrorRate       float64   `json:"error_rate" example:"0.02"`
	LastUsed        time.Time `json:"last_used"`
}

// IPUsage represents usage statistics for a specific IP address
type IPUsage struct {
	IPAddress string    `json:"ip_address" example:"*************"`
	Count     int64     `json:"count" example:"500"`
	LastUsed  time.Time `json:"last_used"`
	ErrorRate float64   `json:"error_rate" example:"0.01"`
}

// APIKeyUsage represents a single usage record
type APIKeyUsage struct {
	ID           uint      `json:"id"`
	APIKeyID     uint      `json:"api_key_id"`
	IPAddress    string    `json:"ip_address"`
	UserAgent    string    `json:"user_agent"`
	Endpoint     string    `json:"endpoint"`
	Method       string    `json:"method"`
	StatusCode   int       `json:"status_code"`
	ResponseTime int64     `json:"response_time"`
	RequestSize  int64     `json:"request_size"`
	ResponseSize int64     `json:"response_size"`
	RequestedAt  time.Time `json:"requested_at"`
}

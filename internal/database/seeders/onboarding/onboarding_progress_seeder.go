package onboarding

import (
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

// OnboardingProgress represents the onboarding_progress table structure for seeding
type OnboardingProgress struct {
	ID          uint   `gorm:"primaryKey"`
	UserID      uint   `gorm:"not null;uniqueIndex"`
	Status      string `gorm:"not null;default:'pending'"`
	Step        string `gorm:"not null;default:'create_tenant'"`
	StartedAt   *time.Time
	CompletedAt *time.Time
	Metadata    string `gorm:"type:json"`
	CreatedAt   time.Time
	UpdatedAt   time.Time
}

// TableName sets the table name for OnboardingProgress
func (OnboardingProgress) TableName() string {
	return "onboarding_progress"
}

// OnboardingProgressSeeder handles seeding of onboarding progress data
type OnboardingProgressSeeder struct{}

// Seed creates sample onboarding progress records
func (s *OnboardingProgressSeeder) Seed(db *gorm.DB) error {
	// Helper function to convert map to JSON string
	toJSON := func(m map[string]interface{}) string {
		b, _ := json.Marshal(m)
		return string(b)
	}

	// Get existing users to create onboarding progress for
	var userIDs []uint
	if err := db.Table("users").Pluck("id", &userIDs).Error; err != nil {
		return err
	}

	// If no users exist, skip seeding
	if len(userIDs) == 0 {
		return nil
	}

	// Create sample onboarding progress records
	now := time.Now()
	progressRecords := []OnboardingProgress{}

	// Create different scenarios for onboarding progress
	for i, userID := range userIDs {
		var progress OnboardingProgress

		switch i % 4 {
		case 0: // Completed onboarding
			startedAt := now.AddDate(0, 0, -7)   // Started 7 days ago
			completedAt := now.AddDate(0, 0, -5) // Completed 5 days ago
			progress = OnboardingProgress{
				UserID:      userID,
				Status:      "completed",
				Step:        "completed",
				StartedAt:   &startedAt,
				CompletedAt: &completedAt,
				Metadata: toJSON(map[string]interface{}{
					"tenant_created":  true,
					"website_created": true,
					"completion_time": "2d",
					"steps_completed": []string{"create_tenant", "create_website"},
				}),
			}
		case 1: // In progress - creating website
			startedAt := now.AddDate(0, 0, -2) // Started 2 days ago
			progress = OnboardingProgress{
				UserID:    userID,
				Status:    "processing",
				Step:      "create_website",
				StartedAt: &startedAt,
				Metadata: toJSON(map[string]interface{}{
					"tenant_created":  true,
					"website_created": false,
					"current_step":    "create_website",
					"steps_completed": []string{"create_tenant"},
				}),
			}
		case 2: // Just started - creating tenant
			startedAt := now.AddDate(0, 0, -1) // Started 1 day ago
			progress = OnboardingProgress{
				UserID:    userID,
				Status:    "processing",
				Step:      "create_tenant",
				StartedAt: &startedAt,
				Metadata: toJSON(map[string]interface{}{
					"tenant_created":  false,
					"website_created": false,
					"current_step":    "create_tenant",
					"steps_completed": []string{},
				}),
			}
		case 3: // Not started yet
			progress = OnboardingProgress{
				UserID: userID,
				Status: "pending",
				Step:   "create_tenant",
				Metadata: toJSON(map[string]interface{}{
					"tenant_created":  false,
					"website_created": false,
					"current_step":    "create_tenant",
					"steps_completed": []string{},
				}),
			}
		}

		progressRecords = append(progressRecords, progress)
	}

	// Insert or update onboarding progress records
	for _, progress := range progressRecords {
		var existing OnboardingProgress
		if err := db.Where("user_id = ?", progress.UserID).First(&existing).Error; err == nil {
			// Update existing record
			db.Model(&existing).Updates(progress)
		} else {
			// Create new record
			if err := db.Create(&progress).Error; err != nil {
				return err
			}
		}
	}

	return nil
}

// Rollback removes seeded onboarding progress records
func (s *OnboardingProgressSeeder) Rollback(db *gorm.DB) error {
	// Delete all onboarding progress records (be careful in production)
	return db.Where("1 = 1").Delete(&OnboardingProgress{}).Error
}

// Name returns the name of this seeder
func (s *OnboardingProgressSeeder) Name() string {
	return "onboarding_progress"
}

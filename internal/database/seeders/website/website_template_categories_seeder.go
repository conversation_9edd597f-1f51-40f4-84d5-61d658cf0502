package website

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"time"
)

// WebsiteTemplateCategoriesSeeder implements the website template categories data seeding
type WebsiteTemplateCategoriesSeeder struct {
	db *sql.DB
}

// NewWebsiteTemplateCategoriesSeeder creates a new website template categories seeder
func NewWebsiteTemplateCategoriesSeeder(db *sql.DB) *WebsiteTemplateCategoriesSeeder {
	return &WebsiteTemplateCategoriesSeeder{db: db}
}

// Name returns the seeder name
func (s *WebsiteTemplateCategoriesSeeder) Name() string {
	return "website_template_categories_seeder"
}

// Dependencies returns the seeders that must run before this one
func (s *WebsiteTemplateCategoriesSeeder) Dependencies() []string {
	return []string{} // No dependencies since this is global data
}

// Seed executes the website template categories seeding process
func (s *WebsiteTemplateCategoriesSeeder) Seed(ctx context.Context) error {
	log.Println("Starting website template categories seeding...")

	// Check if categories already exist
	count, err := s.getCategoryCount(ctx)
	if err != nil {
		return fmt.Errorf("failed to check existing categories: %w", err)
	}

	if count > 0 {
		log.Printf("Website template categories already exist (%d), skipping seeding", count)
		return nil
	}

	// Create main categories
	mainCategories, err := s.createMainCategories(ctx)
	if err != nil {
		return fmt.Errorf("failed to create main categories: %w", err)
	}

	// Create subcategories
	if err := s.createSubcategories(ctx, mainCategories); err != nil {
		return fmt.Errorf("failed to create subcategories: %w", err)
	}

	log.Println("✅ Website template categories seeding completed successfully")
	return nil
}

// getCategoryCount returns the number of existing categories
func (s *WebsiteTemplateCategoriesSeeder) getCategoryCount(ctx context.Context) (int, error) {
	query := `SELECT COUNT(*) FROM website_template_categories WHERE status = 'active'`
	var count int
	err := s.db.QueryRowContext(ctx, query).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count categories: %w", err)
	}
	return count, nil
}

// createMainCategories creates the main template categories
func (s *WebsiteTemplateCategoriesSeeder) createMainCategories(ctx context.Context) (map[string]int, error) {
	log.Println("Creating main template categories...")

	categories := []struct {
		Name        string
		Slug        string
		Description string
		Icon        string
		Color       string
		SortOrder   int
		IsFeatured  bool
	}{
		{
			Name:        "Business",
			Slug:        "business",
			Description: "Professional business websites, corporate pages, and company portfolios",
			Icon:        "briefcase",
			Color:       "#2563eb",
			SortOrder:   1,
			IsFeatured:  true,
		},
		{
			Name:        "Portfolio",
			Slug:        "portfolio",
			Description: "Creative portfolios, personal showcases, and professional galleries",
			Icon:        "palette",
			Color:       "#7c3aed",
			SortOrder:   2,
			IsFeatured:  true,
		},
		{
			Name:        "Blog",
			Slug:        "blog",
			Description: "Blog templates, news sites, and content-focused websites",
			Icon:        "edit",
			Color:       "#059669",
			SortOrder:   3,
			IsFeatured:  true,
		},
		{
			Name:        "E-commerce",
			Slug:        "ecommerce",
			Description: "Online stores, product catalogs, and shopping websites",
			Icon:        "shopping-cart",
			Color:       "#dc2626",
			SortOrder:   4,
			IsFeatured:  true,
		},
		{
			Name:        "Landing Page",
			Slug:        "landing",
			Description: "Single page websites, product launches, and marketing pages",
			Icon:        "target",
			Color:       "#ea580c",
			SortOrder:   5,
			IsFeatured:  false,
		},
		{
			Name:        "Personal",
			Slug:        "personal",
			Description: "Personal websites, resumes, and individual profiles",
			Icon:        "user",
			Color:       "#0891b2",
			SortOrder:   6,
			IsFeatured:  false,
		},
		{
			Name:        "Non-profit",
			Slug:        "nonprofit",
			Description: "Non-profit organizations, charities, and community websites",
			Icon:        "heart",
			Color:       "#be185d",
			SortOrder:   7,
			IsFeatured:  false,
		},
		{
			Name:        "Education",
			Slug:        "education",
			Description: "Educational institutions, online courses, and learning platforms",
			Icon:        "academic-cap",
			Color:       "#0d9488",
			SortOrder:   8,
			IsFeatured:  false,
		},
		{
			Name:        "Event",
			Slug:        "event",
			Description: "Event websites, conferences, workshops, and celebrations",
			Icon:        "calendar",
			Color:       "#7e22ce",
			SortOrder:   9,
			IsFeatured:  false,
		},
		{
			Name:        "Restaurant",
			Slug:        "restaurant",
			Description: "Restaurants, cafes, food services, and culinary businesses",
			Icon:        "utensils",
			Color:       "#b91c1c",
			SortOrder:   10,
			IsFeatured:  false,
		},
	}

	categoryIDs := make(map[string]int)
	now := time.Now()

	for _, category := range categories {
		query := `
			INSERT INTO website_template_categories (
				name, slug, description, icon, color, sort_order, is_active, 
				is_featured, parent_id, level, path, template_count,
				meta_title, meta_description, status, created_at, updated_at
			) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		`

		result, err := s.db.ExecContext(ctx, query,
			category.Name, category.Slug, category.Description, category.Icon, category.Color,
			category.SortOrder, true, category.IsFeatured, nil, 0, "/", 0,
			category.Name+" Templates", category.Description, "active", now, now,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to insert category %s: %w", category.Name, err)
		}

		categoryID, err := result.LastInsertId()
		if err != nil {
			return nil, fmt.Errorf("failed to get category ID for %s: %w", category.Name, err)
		}

		categoryIDs[category.Slug] = int(categoryID)
		log.Printf("Created category: %s (ID: %d)", category.Name, categoryID)
	}

	return categoryIDs, nil
}

// createSubcategories creates subcategories under main categories
func (s *WebsiteTemplateCategoriesSeeder) createSubcategories(ctx context.Context, mainCategories map[string]int) error {
	log.Println("Creating template subcategories...")

	subcategories := []struct {
		Name        string
		Slug        string
		Description string
		Icon        string
		Color       string
		ParentSlug  string
		SortOrder   int
	}{
		// Business subcategories
		{
			Name:        "Corporate",
			Slug:        "corporate",
			Description: "Large corporation and enterprise websites",
			Icon:        "building",
			Color:       "#1e40af",
			ParentSlug:  "business",
			SortOrder:   1,
		},
		{
			Name:        "Startup",
			Slug:        "startup",
			Description: "Startup companies and innovative business websites",
			Icon:        "rocket",
			Color:       "#3730a3",
			ParentSlug:  "business",
			SortOrder:   2,
		},
		{
			Name:        "Consulting",
			Slug:        "consulting",
			Description: "Consulting firms and professional services",
			Icon:        "chat",
			Color:       "#4338ca",
			ParentSlug:  "business",
			SortOrder:   3,
		},
		// Portfolio subcategories
		{
			Name:        "Design",
			Slug:        "design",
			Description: "Design portfolios and creative showcases",
			Icon:        "brush",
			Color:       "#6d28d9",
			ParentSlug:  "portfolio",
			SortOrder:   1,
		},
		{
			Name:        "Photography",
			Slug:        "photography",
			Description: "Photography portfolios and image galleries",
			Icon:        "camera",
			Color:       "#7c2d12",
			ParentSlug:  "portfolio",
			SortOrder:   2,
		},
		{
			Name:        "Developer",
			Slug:        "developer",
			Description: "Developer portfolios and technical showcases",
			Icon:        "code",
			Color:       "#065f46",
			ParentSlug:  "portfolio",
			SortOrder:   3,
		},
		// Blog subcategories
		{
			Name:        "Personal Blog",
			Slug:        "personal-blog",
			Description: "Personal blogs and lifestyle content",
			Icon:        "pencil",
			Color:       "#047857",
			ParentSlug:  "blog",
			SortOrder:   1,
		},
		{
			Name:        "News",
			Slug:        "news",
			Description: "News websites and journalism platforms",
			Icon:        "newspaper",
			Color:       "#065f46",
			ParentSlug:  "blog",
			SortOrder:   2,
		},
		{
			Name:        "Magazine",
			Slug:        "magazine",
			Description: "Digital magazines and publication websites",
			Icon:        "book-open",
			Color:       "#059669",
			ParentSlug:  "blog",
			SortOrder:   3,
		},
	}

	now := time.Now()

	for _, subcategory := range subcategories {
		parentID, exists := mainCategories[subcategory.ParentSlug]
		if !exists {
			log.Printf("Warning: parent category %s not found for subcategory %s", subcategory.ParentSlug, subcategory.Name)
			continue
		}

		path := fmt.Sprintf("/%s/", subcategory.ParentSlug)

		query := `
			INSERT INTO website_template_categories (
				name, slug, description, icon, color, sort_order, is_active, 
				is_featured, parent_id, level, path, template_count,
				meta_title, meta_description, status, created_at, updated_at
			) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		`

		result, err := s.db.ExecContext(ctx, query,
			subcategory.Name, subcategory.Slug, subcategory.Description, subcategory.Icon, subcategory.Color,
			subcategory.SortOrder, true, false, parentID, 1, path, 0,
			subcategory.Name+" Templates", subcategory.Description, "active", now, now,
		)
		if err != nil {
			return fmt.Errorf("failed to insert subcategory %s: %w", subcategory.Name, err)
		}

		subcategoryID, err := result.LastInsertId()
		if err != nil {
			return fmt.Errorf("failed to get subcategory ID for %s: %w", subcategory.Name, err)
		}

		log.Printf("Created subcategory: %s (ID: %d, Parent: %s)", subcategory.Name, subcategoryID, subcategory.ParentSlug)
	}

	return nil
}

// Rollback removes the seeded website template categories data
func (s *WebsiteTemplateCategoriesSeeder) Rollback(db *sql.DB) error {
	log.Println("Rolling back website template categories seeder...")

	// Delete subcategories first (level 1), then main categories (level 0)
	queries := []string{
		"DELETE FROM website_template_categories WHERE level = 1 AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)",
		"DELETE FROM website_template_categories WHERE level = 0 AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)",
	}

	for _, query := range queries {
		_, err := db.Exec(query)
		if err != nil {
			return fmt.Errorf("failed to delete template categories: %w", err)
		}
	}

	log.Println("✅ Website template categories seeder rollback completed")
	return nil
}

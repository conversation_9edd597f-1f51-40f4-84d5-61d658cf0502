package website

import (
	"context"
	"database/sql"
	"fmt"
	"log"
)

// WebsiteModuleSeeder handles all website module seeding operations
type WebsiteModuleSeeder struct {
	db *sql.DB
}

// NewWebsiteModuleSeeder creates a new website module seeder
func NewWebsiteModuleSeeder(db *sql.DB) *WebsiteModuleSeeder {
	return &WebsiteModuleSeeder{db: db}
}

// Name returns the seeder name
func (s *WebsiteModuleSeeder) Name() string {
	return "website_module_seeder"
}

// Dependencies returns the seeders that must run before this one
func (s *WebsiteModuleSeeder) Dependencies() []string {
	return []string{"tenant_module_seeder"}
}

// Seed executes the website module seeding process
func (s *WebsiteModuleSeeder) Seed(ctx context.Context) error {
	log.Println("Starting website module seeding...")

	// 1. First seed template categories (templates depend on categories)
	categoriesSeeder := NewWebsiteTemplateCategoriesSeeder(s.db)
	log.Printf("Running seeder: %s", categoriesSeeder.Name())
	if err := categoriesSeeder.Seed(ctx); err != nil {
		return fmt.Errorf("failed to run template categories seeder: %w", err)
	}
	log.Printf("✅ Seeder %s completed successfully", categoriesSeeder.Name())

	// 2. Then seed templates (depends on categories)
	templatesSeeder := NewWebsiteTemplatesSeeder(s.db)
	log.Printf("Running seeder: %s", templatesSeeder.Name())
	if err := templatesSeeder.Seed(ctx); err != nil {
		return fmt.Errorf("failed to run templates seeder: %w", err)
	}
	log.Printf("✅ Seeder %s completed successfully", templatesSeeder.Name())

	// 3. Finally seed websites (depends on tenants)
	websiteSeeder := NewWebsiteSeeder(s.db)
	log.Printf("Running seeder: %s", websiteSeeder.Name())
	if err := websiteSeeder.Seed(ctx); err != nil {
		return fmt.Errorf("failed to run website seeder: %w", err)
	}
	log.Printf("✅ Seeder %s completed successfully", websiteSeeder.Name())

	log.Println("✅ Website module seeding completed successfully")
	return nil
}

// Rollback removes all website module seeded data
func (s *WebsiteModuleSeeder) Rollback(db *sql.DB) error {
	log.Println("Rolling back website module seeder...")

	// Roll back in reverse order
	// 1. Roll back websites first
	websiteSeeder := NewWebsiteSeeder(db)
	if err := websiteSeeder.Rollback(db); err != nil {
		log.Printf("Warning: Failed to rollback website seeder: %v", err)
	}

	// 2. Roll back templates
	templatesSeeder := NewWebsiteTemplatesSeeder(db)
	if err := templatesSeeder.Rollback(db); err != nil {
		log.Printf("Warning: Failed to rollback templates seeder: %v", err)
	}

	// 3. Roll back categories last
	categoriesSeeder := NewWebsiteTemplateCategoriesSeeder(db)
	if err := categoriesSeeder.Rollback(db); err != nil {
		log.Printf("Warning: Failed to rollback template categories seeder: %v", err)
	}

	log.Println("✅ Website module seeder rollback completed")
	return nil
}

package seeders

import (
	"encoding/json"
	"fmt"
	"log"

	"gorm.io/gorm"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
)

// SeedBlogPostTemplates seeds the default blog post templates
func SeedBlogPostTemplates(db *gorm.DB) error {
	log.Println("Seeding blog post templates...")
	
	templates := []models.BlogPostTemplate{
		// Standard Blog Post Template
		{
			Name:        "Standard Blog Post",
			Slug:        "standard-blog-post",
			Description: "A simple template for regular blog posts with title, content, and featured image",
			Type:        models.BlogPostTemplateTypeStandard,
			Scope:       models.BlogPostTemplateScopeSystem,
			Icon:        "📝",
			Color:       "#4A90E2",
			IsActive:    true,
			IsFeatured:  true,
			TitleTemplate: "{{title}}",
			ContentTemplate: `{{introduction}}

## Main Content

{{main_content}}

## Conclusion

{{conclusion}}`,
			ExcerptTemplate: "{{excerpt}}",
			Structure: models.TemplateStructure{
				Sections: []models.TemplateSection{
					{
						ID:          "basic-info",
						Title:       "Basic Information",
						Description: "Essential post details",
						Order:       1,
						Collapsible: false,
						Fields: []models.TemplateField{
							{
								Name:        "title",
								Label:       "Post Title",
								Type:        "text",
								Required:    true,
								Placeholder: "Enter your post title",
								HelpText:    "Choose a compelling title for your post",
							},
							{
								Name:        "excerpt",
								Label:       "Excerpt",
								Type:        "textarea",
								Required:    false,
								Placeholder: "Brief summary of your post",
								HelpText:    "This will appear in post listings",
							},
						},
					},
					{
						ID:          "content",
						Title:       "Content",
						Description: "Main post content",
						Order:       2,
						Collapsible: false,
						Fields: []models.TemplateField{
							{
								Name:        "introduction",
								Label:       "Introduction",
								Type:        "textarea",
								Required:    true,
								Placeholder: "Hook your readers with a compelling introduction",
							},
							{
								Name:        "main_content",
								Label:       "Main Content",
								Type:        "textarea",
								Required:    true,
								Placeholder: "The main body of your post",
							},
							{
								Name:        "conclusion",
								Label:       "Conclusion",
								Type:        "textarea",
								Required:    true,
								Placeholder: "Wrap up your post with a strong conclusion",
							},
						},
					},
				},
			},
			DefaultType:   models.BlogPostTypePost,
			DefaultStatus: models.BlogPostStatusDraft,
			DefaultAllowComments: true,
			SEOTitleTemplate: "{{title}} | Blog",
			SEODescriptionTemplate: "{{excerpt}}",
		},
		
		// Tutorial Template
		{
			Name:        "Tutorial / How-To Guide",
			Slug:        "tutorial-how-to",
			Description: "Step-by-step guide template with prerequisites, steps, and conclusion",
			Type:        models.BlogPostTemplateTypeTutorial,
			Scope:       models.BlogPostTemplateScopeSystem,
			Icon:        "🎓",
			Color:       "#27AE60",
			IsActive:    true,
			IsFeatured:  true,
			TitleTemplate: "How to {{task}}",
			ContentTemplate: `# How to {{task}}

## Introduction

{{introduction}}

## Prerequisites

{{prerequisites}}

## Step-by-Step Guide

{{#each steps}}
### Step {{@index}}: {{this.title}}

{{this.content}}

{{/each}}

## Common Issues and Troubleshooting

{{troubleshooting}}

## Conclusion

{{conclusion}}

## Additional Resources

{{resources}}`,
			Structure: models.TemplateStructure{
				Sections: []models.TemplateSection{
					{
						ID:          "tutorial-basics",
						Title:       "Tutorial Basics",
						Order:       1,
						Fields: []models.TemplateField{
							{
								Name:        "task",
								Label:       "What are you teaching?",
								Type:        "text",
								Required:    true,
								Placeholder: "e.g., 'Build a REST API with Go'",
							},
							{
								Name:        "introduction",
								Label:       "Introduction",
								Type:        "textarea",
								Required:    true,
								Placeholder: "Explain what readers will learn",
							},
							{
								Name:        "prerequisites",
								Label:       "Prerequisites",
								Type:        "textarea",
								Required:    true,
								Placeholder: "What readers need to know/have before starting",
							},
						},
					},
					{
						ID:          "tutorial-content",
						Title:       "Tutorial Content",
						Order:       2,
						Fields: []models.TemplateField{
							{
								Name:        "steps",
								Label:       "Steps",
								Type:        "textarea",
								Required:    true,
								Placeholder: "Enter each step (use numbered list)",
								HelpText:    "Break down the process into clear, actionable steps",
							},
							{
								Name:        "troubleshooting",
								Label:       "Troubleshooting",
								Type:        "textarea",
								Required:    false,
								Placeholder: "Common issues and their solutions",
							},
							{
								Name:        "conclusion",
								Label:       "Conclusion",
								Type:        "textarea",
								Required:    true,
								Placeholder: "Summarize what was learned",
							},
							{
								Name:        "resources",
								Label:       "Additional Resources",
								Type:        "textarea",
								Required:    false,
								Placeholder: "Links to documentation, related tutorials, etc.",
							},
						},
					},
				},
			},
			DefaultType:   models.BlogPostTypePost,
			DefaultStatus: models.BlogPostStatusDraft,
			DefaultAllowComments: true,
			SEOTitleTemplate: "How to {{task}} - Complete Guide",
			SEODescriptionTemplate: "Learn how to {{task}} with this comprehensive step-by-step tutorial. {{introduction}}",
		},
		
		// Product Review Template
		{
			Name:        "Product Review",
			Slug:        "product-review",
			Description: "Comprehensive product review template with pros, cons, and ratings",
			Type:        models.BlogPostTemplateTypeReview,
			Scope:       models.BlogPostTemplateScopeSystem,
			Icon:        "⭐",
			Color:       "#F39C12",
			IsActive:    true,
			IsFeatured:  true,
			TitleTemplate: "{{product_name}} Review: {{tagline}}",
			ContentTemplate: `# {{product_name}} Review

## Quick Summary

{{quick_summary}}

**Rating: {{rating}}/5**

## Introduction

{{introduction}}

## Features

{{features}}

## Pros

{{pros}}

## Cons

{{cons}}

## Performance & Testing

{{performance}}

## Pricing

{{pricing}}

## Who Is This For?

{{target_audience}}

## Final Verdict

{{verdict}}

## Alternatives

{{alternatives}}`,
			Structure: models.TemplateStructure{
				Sections: []models.TemplateSection{
					{
						ID:          "product-info",
						Title:       "Product Information",
						Order:       1,
						Fields: []models.TemplateField{
							{
								Name:        "product_name",
								Label:       "Product Name",
								Type:        "text",
								Required:    true,
								Placeholder: "Enter the product name",
							},
							{
								Name:        "tagline",
								Label:       "Review Tagline",
								Type:        "text",
								Required:    true,
								Placeholder: "e.g., 'Worth the Hype?'",
							},
							{
								Name:        "rating",
								Label:       "Overall Rating",
								Type:        "select",
								Required:    true,
								Options:     []string{"1", "2", "3", "4", "5"},
							},
							{
								Name:        "quick_summary",
								Label:       "Quick Summary",
								Type:        "textarea",
								Required:    true,
								Placeholder: "Brief overview of your experience",
							},
						},
					},
					{
						ID:          "review-content",
						Title:       "Review Content",
						Order:       2,
						Fields: []models.TemplateField{
							{
								Name:        "introduction",
								Label:       "Introduction",
								Type:        "textarea",
								Required:    true,
								Placeholder: "Set the context for your review",
							},
							{
								Name:        "features",
								Label:       "Key Features",
								Type:        "textarea",
								Required:    true,
								Placeholder: "List and describe main features",
							},
							{
								Name:        "pros",
								Label:       "Pros",
								Type:        "textarea",
								Required:    true,
								Placeholder: "What you liked (use bullet points)",
							},
							{
								Name:        "cons",
								Label:       "Cons",
								Type:        "textarea",
								Required:    true,
								Placeholder: "What could be improved (use bullet points)",
							},
							{
								Name:        "performance",
								Label:       "Performance & Testing",
								Type:        "textarea",
								Required:    false,
								Placeholder: "Your experience using the product",
							},
							{
								Name:        "pricing",
								Label:       "Pricing Information",
								Type:        "textarea",
								Required:    true,
								Placeholder: "Cost and value proposition",
							},
							{
								Name:        "target_audience",
								Label:       "Target Audience",
								Type:        "textarea",
								Required:    true,
								Placeholder: "Who would benefit from this product",
							},
							{
								Name:        "verdict",
								Label:       "Final Verdict",
								Type:        "textarea",
								Required:    true,
								Placeholder: "Your recommendation",
							},
							{
								Name:        "alternatives",
								Label:       "Alternatives",
								Type:        "textarea",
								Required:    false,
								Placeholder: "Similar products to consider",
							},
						},
					},
				},
			},
			DefaultType:   models.BlogPostTypePost,
			DefaultStatus: models.BlogPostStatusDraft,
			DefaultAllowComments: true,
			SEOTitleTemplate: "{{product_name}} Review {{current_year}} - {{tagline}}",
			SEODescriptionTemplate: "Honest review of {{product_name}}. {{quick_summary}} Rating: {{rating}}/5.",
		},
		
		// News Article Template
		{
			Name:        "News Article",
			Slug:        "news-article",
			Description: "Breaking news and article template with headline, lead, and supporting details",
			Type:        models.BlogPostTemplateTypeNewsArticle,
			Scope:       models.BlogPostTemplateScopeSystem,
			Icon:        "📰",
			Color:       "#E74C3C",
			IsActive:    true,
			IsFeatured:  false,
			TitleTemplate: "{{headline}}",
			ContentTemplate: `# {{headline}}

**{{lead}}**

*{{location}} - {{date}}*

{{opening_paragraph}}

## Background

{{background}}

## Key Details

{{key_details}}

## Impact

{{impact}}

## What's Next

{{whats_next}}

## Related Stories

{{related_stories}}`,
			Structure: models.TemplateStructure{
				Sections: []models.TemplateSection{
					{
						ID:          "news-basics",
						Title:       "Article Basics",
						Order:       1,
						Fields: []models.TemplateField{
							{
								Name:        "headline",
								Label:       "Headline",
								Type:        "text",
								Required:    true,
								Placeholder: "Attention-grabbing headline",
							},
							{
								Name:        "lead",
								Label:       "Lead/Subheading",
								Type:        "textarea",
								Required:    true,
								Placeholder: "Summary in 1-2 sentences",
								HelpText:    "Answer who, what, when, where, why",
							},
							{
								Name:        "location",
								Label:       "Location",
								Type:        "text",
								Required:    true,
								Placeholder: "Where this happened",
							},
							{
								Name:        "date",
								Label:       "Date",
								Type:        "date",
								Required:    true,
							},
						},
					},
					{
						ID:          "news-content",
						Title:       "Article Content",
						Order:       2,
						Fields: []models.TemplateField{
							{
								Name:        "opening_paragraph",
								Label:       "Opening Paragraph",
								Type:        "textarea",
								Required:    true,
								Placeholder: "Expand on the lead with key facts",
							},
							{
								Name:        "background",
								Label:       "Background/Context",
								Type:        "textarea",
								Required:    true,
								Placeholder: "Provide context and history",
							},
							{
								Name:        "key_details",
								Label:       "Key Details",
								Type:        "textarea",
								Required:    true,
								Placeholder: "Important facts and quotes",
							},
							{
								Name:        "impact",
								Label:       "Impact/Analysis",
								Type:        "textarea",
								Required:    true,
								Placeholder: "Why this matters",
							},
							{
								Name:        "whats_next",
								Label:       "What's Next",
								Type:        "textarea",
								Required:    false,
								Placeholder: "Future implications or developments",
							},
							{
								Name:        "related_stories",
								Label:       "Related Stories",
								Type:        "textarea",
								Required:    false,
								Placeholder: "Links to related articles",
							},
						},
					},
				},
			},
			DefaultType:   models.BlogPostTypePost,
			DefaultStatus: models.BlogPostStatusDraft,
			DefaultAllowComments: true,
			SEOTitleTemplate: "{{headline}} - Breaking News",
			SEODescriptionTemplate: "{{lead}} Read the full story.",
		},
		
		// Case Study Template
		{
			Name:        "Case Study",
			Slug:        "case-study",
			Description: "Business case study template with challenge, solution, and results",
			Type:        models.BlogPostTemplateTypeCaseStudy,
			Scope:       models.BlogPostTemplateScopeSystem,
			Icon:        "📊",
			Color:       "#9B59B6",
			IsActive:    true,
			IsFeatured:  false,
			TitleTemplate: "{{client_name}} Case Study: {{achievement}}",
			ContentTemplate: `# {{client_name}} Case Study

## Executive Summary

{{executive_summary}}

## The Challenge

{{challenge}}

## Our Solution

{{solution}}

## Implementation Process

{{implementation}}

## Results & Impact

{{results}}

### Key Metrics:
{{metrics}}

## Client Testimonial

> {{testimonial}}
> 
> — {{testimonial_author}}, {{testimonial_title}}

## Lessons Learned

{{lessons_learned}}

## Conclusion

{{conclusion}}`,
			Structure: models.TemplateStructure{
				Sections: []models.TemplateSection{
					{
						ID:          "case-study-overview",
						Title:       "Case Study Overview",
						Order:       1,
						Fields: []models.TemplateField{
							{
								Name:        "client_name",
								Label:       "Client/Company Name",
								Type:        "text",
								Required:    true,
								Placeholder: "Name of the client or company",
							},
							{
								Name:        "achievement",
								Label:       "Key Achievement",
								Type:        "text",
								Required:    true,
								Placeholder: "e.g., 'Increased Sales by 300%'",
							},
							{
								Name:        "executive_summary",
								Label:       "Executive Summary",
								Type:        "textarea",
								Required:    true,
								Placeholder: "Brief overview of the case study",
							},
						},
					},
					{
						ID:          "case-study-details",
						Title:       "Case Study Details",
						Order:       2,
						Fields: []models.TemplateField{
							{
								Name:        "challenge",
								Label:       "The Challenge",
								Type:        "textarea",
								Required:    true,
								Placeholder: "What problem needed solving?",
							},
							{
								Name:        "solution",
								Label:       "The Solution",
								Type:        "textarea",
								Required:    true,
								Placeholder: "How you addressed the challenge",
							},
							{
								Name:        "implementation",
								Label:       "Implementation Process",
								Type:        "textarea",
								Required:    true,
								Placeholder: "Steps taken to implement the solution",
							},
							{
								Name:        "results",
								Label:       "Results & Impact",
								Type:        "textarea",
								Required:    true,
								Placeholder: "Outcomes and achievements",
							},
							{
								Name:        "metrics",
								Label:       "Key Metrics",
								Type:        "textarea",
								Required:    true,
								Placeholder: "Quantifiable results (use bullet points)",
							},
						},
					},
					{
						ID:          "case-study-testimonial",
						Title:       "Testimonial",
						Order:       3,
						Collapsible: true,
						Fields: []models.TemplateField{
							{
								Name:        "testimonial",
								Label:       "Client Testimonial",
								Type:        "textarea",
								Required:    false,
								Placeholder: "Quote from the client",
							},
							{
								Name:        "testimonial_author",
								Label:       "Testimonial Author",
								Type:        "text",
								Required:    false,
								Placeholder: "Name of the person",
							},
							{
								Name:        "testimonial_title",
								Label:       "Author Title",
								Type:        "text",
								Required:    false,
								Placeholder: "Job title at company",
							},
						},
					},
					{
						ID:          "case-study-conclusion",
						Title:       "Conclusion",
						Order:       4,
						Fields: []models.TemplateField{
							{
								Name:        "lessons_learned",
								Label:       "Lessons Learned",
								Type:        "textarea",
								Required:    true,
								Placeholder: "Key takeaways from this project",
							},
							{
								Name:        "conclusion",
								Label:       "Conclusion",
								Type:        "textarea",
								Required:    true,
								Placeholder: "Final thoughts and next steps",
							},
						},
					},
				},
			},
			DefaultType:   models.BlogPostTypePost,
			DefaultStatus: models.BlogPostStatusDraft,
			DefaultAllowComments: true,
			SEOTitleTemplate: "{{client_name}} Case Study: {{achievement}}",
			SEODescriptionTemplate: "Learn how we helped {{client_name}} achieve {{achievement}}. {{executive_summary}}",
		},
		
		// Event Announcement Template
		{
			Name:        "Event Announcement",
			Slug:        "event-announcement",
			Description: "Template for announcing events, conferences, webinars, or meetups",
			Type:        models.BlogPostTemplateTypeEvent,
			Scope:       models.BlogPostTemplateScopeSystem,
			Icon:        "📅",
			Color:       "#3498DB",
			IsActive:    true,
			IsFeatured:  false,
			TitleTemplate: "{{event_name}} - {{event_date}}",
			ContentTemplate: `# {{event_name}}

## Event Overview

{{event_description}}

## Event Details

- **Date:** {{event_date}}
- **Time:** {{event_time}}
- **Location:** {{event_location}}
- **Duration:** {{event_duration}}
- **Format:** {{event_format}}

## What to Expect

{{what_to_expect}}

## Agenda

{{agenda}}

## Speakers/Presenters

{{speakers}}

## Who Should Attend

{{target_audience}}

## Registration

{{registration_info}}

**[Register Now]({{registration_link}})**

## Additional Information

{{additional_info}}

## Contact

{{contact_info}}`,
			Structure: models.TemplateStructure{
				Sections: []models.TemplateSection{
					{
						ID:          "event-basics",
						Title:       "Event Basics",
						Order:       1,
						Fields: []models.TemplateField{
							{
								Name:        "event_name",
								Label:       "Event Name",
								Type:        "text",
								Required:    true,
								Placeholder: "Name of the event",
							},
							{
								Name:        "event_description",
								Label:       "Event Description",
								Type:        "textarea",
								Required:    true,
								Placeholder: "Brief overview of the event",
							},
							{
								Name:        "event_date",
								Label:       "Event Date",
								Type:        "date",
								Required:    true,
							},
							{
								Name:        "event_time",
								Label:       "Event Time",
								Type:        "text",
								Required:    true,
								Placeholder: "e.g., '2:00 PM - 5:00 PM EST'",
							},
							{
								Name:        "event_location",
								Label:       "Location",
								Type:        "text",
								Required:    true,
								Placeholder: "Physical address or 'Online'",
							},
							{
								Name:        "event_duration",
								Label:       "Duration",
								Type:        "text",
								Required:    true,
								Placeholder: "e.g., '3 hours'",
							},
							{
								Name:        "event_format",
								Label:       "Format",
								Type:        "select",
								Required:    true,
								Options:     []string{"In-Person", "Virtual", "Hybrid"},
							},
						},
					},
					{
						ID:          "event-details",
						Title:       "Event Details",
						Order:       2,
						Fields: []models.TemplateField{
							{
								Name:        "what_to_expect",
								Label:       "What to Expect",
								Type:        "textarea",
								Required:    true,
								Placeholder: "What attendees will learn/experience",
							},
							{
								Name:        "agenda",
								Label:       "Agenda",
								Type:        "textarea",
								Required:    true,
								Placeholder: "Event schedule and activities",
							},
							{
								Name:        "speakers",
								Label:       "Speakers/Presenters",
								Type:        "textarea",
								Required:    false,
								Placeholder: "List of speakers with brief bios",
							},
							{
								Name:        "target_audience",
								Label:       "Target Audience",
								Type:        "textarea",
								Required:    true,
								Placeholder: "Who should attend this event",
							},
						},
					},
					{
						ID:          "event-registration",
						Title:       "Registration",
						Order:       3,
						Fields: []models.TemplateField{
							{
								Name:        "registration_info",
								Label:       "Registration Information",
								Type:        "textarea",
								Required:    true,
								Placeholder: "How to register, cost, deadlines",
							},
							{
								Name:        "registration_link",
								Label:       "Registration Link",
								Type:        "text",
								Required:    true,
								Placeholder: "URL to registration page",
								Validation:  "^https?://.*",
							},
							{
								Name:        "additional_info",
								Label:       "Additional Information",
								Type:        "textarea",
								Required:    false,
								Placeholder: "Parking, dress code, requirements, etc.",
							},
							{
								Name:        "contact_info",
								Label:       "Contact Information",
								Type:        "textarea",
								Required:    true,
								Placeholder: "Email and phone for questions",
							},
						},
					},
				},
			},
			DefaultType:   models.BlogPostTypeAnnouncement,
			DefaultStatus: models.BlogPostStatusDraft,
			DefaultAllowComments: true,
			SEOTitleTemplate: "{{event_name}} - {{event_date}} | Event Announcement",
			SEODescriptionTemplate: "Join us for {{event_name}} on {{event_date}}. {{event_description}}",
		},
	}
	
	// Insert templates
	for _, template := range templates {
		// Set CreatedBy to system user (ID: 1)
		template.CreatedBy = 1
		
		// Convert default tags if any
		if len(template.DefaultTags) > 0 {
			// Already in JSON format
		} else {
			template.DefaultTags = json.RawMessage("[]")
		}
		
		// Check if template already exists
		var existingTemplate models.BlogPostTemplate
		result := db.Where("slug = ? AND scope = ?", template.Slug, template.Scope).First(&existingTemplate)
		
		if result.Error == nil {
			log.Printf("Template '%s' already exists, skipping...", template.Name)
			continue
		}
		
		// Create template
		if err := db.Create(&template).Error; err != nil {
			return fmt.Errorf("failed to create template '%s': %w", template.Name, err)
		}
		
		log.Printf("Created template: %s", template.Name)
	}
	
	log.Println("Blog post templates seeding completed!")
	return nil
}
-- Create website_template_categories table for organizing templates
CREATE TABLE IF NOT EXISTS website_template_categories (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    
    -- Basic Information
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Category Properties
    icon VARCHAR(100),
    color VARCHAR(7), -- Hex color code
    sort_order INT UNSIGNED DEFAULT 0,
    
    -- Category Status
    is_active BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    
    -- Hierarchy Support
    parent_id INT UNSIGNED NULL,
    level INT UNSIGNED DEFAULT 0,
    path VARCHAR(500) NOT NULL DEFAULT '/',
    
    -- Statistics
    template_count INT UNSIGNED DEFAULT 0,
    
    -- SEO
    meta_title VARCHAR(255),
    meta_description TEXT,
    
    -- Status
    status ENUM('active', 'inactive', 'deleted') DEFAULT 'active',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_website_template_categories_parent_id FOREIGN KEY (parent_id) REFERENCES website_template_categories(id) ON DELETE SET NULL,
    
    -- Indexes
    UNIQUE KEY uk_website_template_categories_slug (slug),
    INDEX idx_website_template_categories_parent_id (parent_id),
    INDEX idx_website_template_categories_status (status),
    INDEX idx_website_template_categories_active (is_active),
    INDEX idx_website_template_categories_featured (is_featured),
    INDEX idx_website_template_categories_sort_order (sort_order),
    INDEX idx_website_template_categories_level (level),
    INDEX idx_website_template_categories_path (path)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


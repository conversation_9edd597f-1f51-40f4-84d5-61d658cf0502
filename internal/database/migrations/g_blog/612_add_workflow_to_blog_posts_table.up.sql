-- Add workflow columns to blog_posts table
-- This migration adds workflow state tracking to the existing blog_posts table

-- Step 1: Add workflow columns with defaults
ALTER TABLE blog_posts 
ADD COLUMN workflow_state VARCHAR(50) DEFAULT 'creation' AFTER status,
ADD COLUMN workflow_assigned_to INT UNSIGNED DEFAULT NULL AFTER workflow_state,
ADD COLUMN workflow_assigned_at TIMESTAMP NULL AFTER workflow_assigned_to,
ADD COLUMN workflow_due_at TIMESTAMP NULL AFTER workflow_assigned_at,
ADD COLUMN workflow_notes TEXT DEFAULT NULL AFTER workflow_due_at;
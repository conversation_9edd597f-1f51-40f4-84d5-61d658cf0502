-- Rollback workflow columns from blog_posts table
-- This migration removes workflow state tracking from blog_posts table

-- Step 1: Convert workflow states back to single status before removing columns
-- This attempts to restore the original status logic
UPDATE blog_posts 
SET status = CASE
    WHEN workflow_state = 'creation' AND status = 'draft' THEN 'draft'
    WHEN workflow_state = 'pending_review' THEN 'review'
    WHEN workflow_state = 'in_review' THEN 'review'
    WHEN workflow_state = 'pending_approval' THEN 'review'
    WHEN workflow_state = 'pending_eic' THEN 'review'
    WHEN workflow_state = 'approved' AND status = 'scheduled' THEN 'scheduled'
    WHEN workflow_state = 'returned' THEN 'draft'
    WHEN workflow_state = 'rejected' THEN 'rejected'
    WHEN workflow_state = 'completed' AND status = 'published' THEN 'published'
    WHEN workflow_state = 'completed' AND status = 'scheduled' THEN 'scheduled'
    WHEN workflow_state = 'completed' AND status = 'archived' THEN 'archived'
    WHEN workflow_state = 'completed' AND status = 'deleted' THEN 'deleted'
    ELSE status
END;

-- Step 2: Drop indexes
ALTER TABLE blog_posts 
DROP INDEX idx_blog_posts_workflow_due,
DROP INDEX idx_blog_posts_status_workflow,
DROP INDEX idx_blog_posts_assigned,
DROP INDEX idx_blog_posts_workflow_state;

-- Step 3: Drop foreign key constraint
ALTER TABLE blog_posts 
DROP FOREIGN KEY fk_blog_posts_workflow_assigned_to;

-- Step 4: Drop workflow columns
ALTER TABLE blog_posts 
DROP COLUMN workflow_notes,
DROP COLUMN workflow_due_at,
DROP COLUMN workflow_assigned_at,
DROP COLUMN workflow_assigned_to,
DROP COLUMN workflow_state;

-- Step 5: Restore original status enum with review and rejected
-- ALTER TABLE blog_posts 
-- MODIFY COLUMN status ENUM('draft', 'review', 'published', 'scheduled', 'archived', 'rejected', 'deleted') 
-- NOT NULL DEFAULT 'draft';
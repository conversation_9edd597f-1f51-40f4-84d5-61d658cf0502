-- Migrate existing data to new workflow system

-- Step 1: Migrate existing data based on current status
-- Map current single status to new dual status+workflow system
UPDATE blog_posts 
SET workflow_state = CASE
    WHEN status = 'draft' THEN 'creation'
    WHEN status = 'review' THEN 'in_review'
    WHEN status = 'published' THEN 'completed'
    WHEN status = 'scheduled' THEN 'approved'
    WHEN status = 'archived' THEN 'completed'
    WHEN status = 'rejected' THEN 'rejected'
    WHEN status = 'deleted' THEN 'completed'
    ELSE 'creation'
END
WHERE workflow_state = 'creation';
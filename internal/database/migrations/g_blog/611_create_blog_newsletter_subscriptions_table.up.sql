-- Create blog_newsletter_subscriptions table
CREATE TABLE IF NOT EXISTS blog_newsletter_subscriptions (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    email VARCHAR(255) NOT NULL,
    name VARCHA<PERSON>(255) NULL,
    categories JSON DEFAULT (JSON_ARRAY()) COMMENT 'Array of category IDs to subscribe to',
    tags JSON DEFAULT (JSON_ARRAY()) COMMENT 'Array of tag IDs to subscribe to',
    frequency ENUM('instant', 'daily', 'weekly', 'monthly') NOT NULL DEFAULT 'weekly',
    token VARCHAR(64) NOT NULL COMMENT 'Unsubscribe token',
    confirmed_at TIMESTAMP NULL,
    last_sent_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('pending', 'confirmed', 'unsubscribed', 'bounced', 'deleted') NOT NULL DEFAULT 'pending',
    
    CONSTRAINT fk_blog_newsletter_subscriptions_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_newsletter_subscriptions_website FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    UNIQUE KEY uk_blog_newsletter_subscriptions_tenant_website_email (tenant_id, website_id, email),
    UNIQUE KEY uk_blog_newsletter_subscriptions_token (token),
    INDEX idx_blog_newsletter_subscriptions_tenant_id (tenant_id),
    INDEX idx_blog_newsletter_subscriptions_tenant_website (tenant_id, website_id),
    INDEX idx_blog_newsletter_subscriptions_tenant_status (tenant_id, status),
    INDEX idx_blog_newsletter_subscriptions_website_status (website_id, status),
    INDEX idx_blog_newsletter_subscriptions_frequency_status (frequency, status),
    INDEX idx_blog_newsletter_subscriptions_confirmed_at (confirmed_at),
    INDEX idx_blog_newsletter_subscriptions_last_sent_at (last_sent_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
-- Rollback workflow data migration

-- Convert workflow states back to single status before removing columns
-- This attempts to restore the original status logic
UPDATE blog_posts 
SET status = CASE
    WHEN workflow_state = 'creation' AND status = 'draft' THEN 'draft'
    WHEN workflow_state = 'pending_review' THEN 'review'  
    WHEN workflow_state = 'in_review' THEN 'review'
    WHEN workflow_state = 'pending_approval' THEN 'review'
    WHEN workflow_state = 'pending_eic' THEN 'review'
    WHEN workflow_state = 'approved' AND status = 'scheduled' THEN 'scheduled'
    WHEN workflow_state = 'returned' THEN 'draft'
    WHEN workflow_state = 'rejected' THEN 'rejected'
    WHEN workflow_state = 'completed' AND status = 'published' THEN 'published'
    WHEN workflow_state = 'completed' AND status = 'scheduled' THEN 'scheduled'
    WHEN workflow_state = 'completed' AND status = 'archived' THEN 'archived'
    WHEN workflow_state = 'completed' AND status = 'deleted' THEN 'deleted'
    ELSE status
END;
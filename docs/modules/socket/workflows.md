# Luồng hoạt động chính

## 1. <PERSON><PERSON><PERSON> n<PERSON>i WebSocket

```mermaid
sequenceDiagram
    participant Client as Client Browser
    participant Gateway as WebSocket Gateway
    participant Auth as Auth Service
    participant Hub as Connection Hub
    participant DB as Database
    
    Client->>Gateway: WebSocket connection
    Gateway->>Auth: Validate JWT token
    Auth->>Gateway: User authenticated
    Gateway->>Hub: Register connection
    Hub->>DB: Store connection info
    Hub->>Gateway: Connection registered
    Gateway->>Client: Connection established
    
    Note over Client,DB: User now online and ready for real-time communication
```

## 2. Tham gia Room

```mermaid
flowchart TD
    A[User request join room] --> B{Room exists?}
    B -->|Không| C[Create room]
    B -->|Có| D{User has permission?}
    C --> E[Add user as member]
    D -->|Không| F[Reject request]
    D -->|Có| G{Already member?}
    G -->|Có| H[Return room info]
    G -->|Không| E
    E --> I[Update member list]
    I --> J[Broadcast user joined]
    J --> K[Send room history]
    F --> L[Send error message]
    H --> M[Send current status]
    K --> N[User in room]
    M --> N
```

## 3. <PERSON><PERSON><PERSON> tin nhắn Chat

```mermaid
sequenceDiagram
    participant User as User A
    participant Gateway as WebSocket Gateway
    participant MessageService as Message Service
    participant RoomHub as Room Hub
    participant DB as Database
    participant Users as Other Users
    
    User->>Gateway: Send message
    Gateway->>MessageService: Process message
    MessageService->>DB: Save message
    MessageService->>RoomHub: Broadcast to room
    RoomHub->>Users: Deliver message
    MessageService->>Gateway: Confirm sent
    Gateway->>User: Message sent confirmation
    
    Note over Users: Real-time message delivery
```

## 4. Real-time Notifications

```mermaid
flowchart LR
    A[Event Trigger] --> B[Notification Service]
    B --> C[User Preferences]
    C --> D{WebSocket Connected?}
    D -->|Có| E[Send Real-time]
    D -->|Không| F[Queue for later]
    E --> G[Display Notification]
    F --> H[Send when online]
    H --> G
```
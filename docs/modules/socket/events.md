# Các loại Event

## Chat Events

- **message.send**: <PERSON><PERSON><PERSON> tin nhắn
- **message.edit**: Chỉnh sửa tin nhắn
- **message.delete**: <PERSON><PERSON><PERSON> tin nhắn
- **message.react**: Reaction tin nhắn
- **typing.start**: <PERSON><PERSON><PERSON> đầu gõ
- **typing.stop**: Dừng gõ

## Room Events

- **room.join**: Tham gia room
- **room.leave**: Rời room
- **room.create**: Tạo room mới
- **room.update**: Cập nhật room
- **member.add**: Thêm thành viên
- **member.remove**: <PERSON><PERSON><PERSON> thành viên

## Presence Events

- **user.online**: User online
- **user.offline**: User offline
- **user.away**: User away
- **user.busy**: User busy

## Notification Events

- **notification.new**: Thông báo mới
- **notification.read**: <PERSON><PERSON> đọc thông báo
- **notification.clear**: <PERSON><PERSON><PERSON> thông báo

# WebSocket Protocol

## Connection Authentication

```json
{
  "type": "auth",
  "data": {
    "token": "jwt_token_here",
    "user_id": 123
  }
}
```

## Message Format

```json
{
  "id": "msg_uuid",
  "type": "message.send",
  "room_id": "room_123",
  "data": {
    "content": "Hello everyone!",
    "message_type": "text",
    "reply_to": null
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Event Broadcasting

```json
{
  "type": "message.received",
  "room_id": "room_123", 
  "data": {
    "id": "msg_456",
    "user": {
      "id": 123,
      "name": "John Doe",
      "avatar": "https://example.com/avatar.jpg"
    },
    "content": "Hello everyone!",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```
# Chat System

## Private Chat (1-1)

```mermaid
sequenceDiagram
    participant User<PERSON> as User A
    participant System as Chat System
    participant UserB as User B
    
    UserA->>System: Start chat with User B
    System->>System: Create private room
    System->>UserA: Room created
    System->>UserB: Chat invitation
    UserB->>System: Accept invitation
    System->>UserA: User B joined
    
    UserA->>System: Send message
    System->>UserB: Deliver message
    UserB->>System: Send reply
    System->>UserA: Deliver reply
```

## Group Chat

```mermaid
flowchart TD
    A[Create Group Chat] --> B[Invite Members]
    B --> C[Members Join]
    C --> D[Chat Active]
    
    D --> E[Send Message]
    E --> F[Broadcast to All]
    F --> G[Message History]
    
    D --> H[Add Member]
    H --> I[Update Member List]
    I --> F
    
    D --> J[Remove Member]
    J --> K[Update Permissions]
    K --> F
```

## Typing Indicators

```mermaid
sequenceDiagram
    participant User<PERSON> as User A (Typing)
    participant System as System
    participant UserB as User B
    participant UserC as User C
    
    UserA->>System: typing.start
    System->>UserB: User A is typing...
    System->>UserC: User A is typing...
    
    Note over UserA: User continues typing
    
    UserA->>System: typing.stop
    System->>UserB: User A stopped typing
    System->>UserC: User A stopped typing
    
    UserA->>System: Send message
    System->>UserB: Message from User A
    System->>UserC: Message from User A
```
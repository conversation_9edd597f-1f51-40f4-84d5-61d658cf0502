# Monitoring và Analytics

## Connection Metrics

```mermaid
graph LR
    A[Socket Metrics] --> B[Active Connections]
    A --> C[Message Volume]
    A --> D[Room Activity]
    A --> E[User Engagement]
    A --> F[Error Rates]
    
    B --> B1[Current Online]
    B --> B2[Peak Concurrent]
    
    C --> C1[Messages/sec]
    C --> C2[Daily Volume]
    
    D --> D1[Active Rooms]
    D --> D2[Room Participation]
    
    E --> E1[Session Duration]
    E --> E2[Return Rate]
    
    F --> F1[Connection Errors]
    F --> F2[Message Failures]
```

## Performance Monitoring

- **Connection Latency**: <PERSON><PERSON> trễ kết nối
- **Message Delivery Time**: Thời gian gửi tin nhắn
- **Room Join/Leave Rate**: Tỷ lệ vào/ra room
- **Memory Usage**: Sử dụng bộ nhớ
- **CPU Usage**: Sử dụng CPU
- **Website Isolation**: Monitor cross-website access attempts
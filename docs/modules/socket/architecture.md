# Kiến trúc Module Socket

## Cấu trúc thư mục

```
internal/modules/socket/
├── models/                   # Các model socket
│   ├── connection.go        # Model kết nối
│   ├── room.go              # Model room/channel
│   ├── message.go           # Model tin nhắn
│   └── presence.go          # Model trạng thái online
├── services/                # Logic nghiệp vụ
│   ├── socket_service.go    # Dịch vụ WebSocket
│   ├── room_service.go      # Dịch vụ room management
│   ├── message_service.go   # Dịch vụ tin nhắn
│   ├── presence_service.go  # Dịch vụ presence
│   └── broadcast_service.go # Dịch vụ broadcast
├── handlers/                # WebSocket handlers
│   ├── socket_handler.go    # Handler chính
│   ├── chat_handler.go      # Handler chat
│   └── notification_handler.go # Handler thông báo
├── hub/                     # Connection hub
│   ├── connection_hub.go    # Quản lý connections
│   ├── room_hub.go          # Quản lý rooms
│   └── broadcast_hub.go     # Broadcast messages
├── events/                  # Event types
│   ├── chat_events.go       # Event chat
│   ├── notification_events.go # Event thông báo
│   └── presence_events.go   # Event presence
└── middleware/              # WebSocket middleware
    ├── auth_middleware.go   # <PERSON><PERSON><PERSON> thực WebSocket
    └── rate_limit_middleware.go # Rate limiting
```

## Tổng quan kiến trúc

Socket module được thiết kế theo kiến trúc layered với các thành phần chính:

- **Models**: Định nghĩa cấu trúc dữ liệu
- **Services**: Business logic và xử lý nghiệp vụ
- **Handlers**: WebSocket request handlers
- **Hub**: Quản lý kết nối và broadcasting
- **Events**: Định nghĩa các event types
- **Middleware**: Xử lý authentication và rate limiting

## Thành phần chính

### Models Layer
- **Connection**: Quản lý thông tin kết nối WebSocket
- **Room**: Quản lý rooms và channels
- **Message**: Cấu trúc tin nhắn
- **Presence**: Trạng thái online/offline của users

### Services Layer
- **SocketService**: Quản lý WebSocket connections
- **RoomService**: Quản lý rooms và members
- **MessageService**: Xử lý tin nhắn và messaging
- **PresenceService**: Quản lý trạng thái presence
- **BroadcastService**: Broadcasting messages

### Handlers Layer
- **SocketHandler**: Handler chính cho WebSocket connections
- **ChatHandler**: Xử lý chat messages
- **NotificationHandler**: Xử lý real-time notifications

### Hub Layer
- **ConnectionHub**: Quản lý tất cả WebSocket connections
- **RoomHub**: Quản lý rooms và room membership
- **BroadcastHub**: Broadcast messages đến multiple clients

### Events Layer
- **ChatEvents**: Định nghĩa events cho chat system
- **NotificationEvents**: Định nghĩa events cho notifications
- **PresenceEvents**: Định nghĩa events cho presence system

### Middleware Layer
- **AuthMiddleware**: Xác thực WebSocket connections
- **RateLimitMiddleware**: Rate limiting cho messages
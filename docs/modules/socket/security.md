# Security Features

## Authentication & Authorization

```mermaid
flowchart TD
    A[WebSocket Connect] --> B[Validate JWT]
    B --> C{Token Valid?}
    C -->|No| D[Reject Connection]
    C -->|Yes| E[Check Permissions]
    E --> F{Has Permission?}
    F -->|No| G[Limited Access]
    F -->|Yes| H[Full Access]
    
    H --> I[Join Authorized Rooms]
    G --> J[Join Public Rooms Only]
```

## Rate Limiting

```mermaid
flowchart LR
    A[Message Sent] --> B[Rate Limiter]
    B --> C{Within Limit?}
    C -->|Yes| D[Process Message]
    C -->|No| E[Drop Message]
    E --> F[Send Rate Limit Warning]
    D --> G[Deliver Message]
```
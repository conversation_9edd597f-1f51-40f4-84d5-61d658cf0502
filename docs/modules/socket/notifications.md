# Notification System

## Real-time Notification Delivery

```mermaid
flowchart TD
    A[Event Occurs] --> B[Notification Service]
    B --> C[Get User Preferences]
    C --> D{User Online?}
    
    D -->|Online| E[Check WebSocket]
    D -->|Offline| F[Store for Later]
    
    E --> G{Socket Connected?}
    G -->|Yes| H[Send Real-time]
    G -->|No| I[Send Push/Email]
    
    H --> J[<PERSON> as Delivered]
    I --> K[Queue for Retry]
    F --> L[Send when Online]
```

## Notification Types

### Comment Notifications

```json
{
  "type": "notification.comment",
  "data": {
    "post_id": 123,
    "post_title": "My Blog Post",
    "commenter": {
      "id": 456,
      "name": "<PERSON>"
    },
    "comment": "Great article!",
    "url": "/posts/my-blog-post#comment-789"
  }
}
```

### Follow Notifications

```json
{
  "type": "notification.follow",
  "data": {
    "follower": {
      "id": 789,
      "name": "New Follower",
      "avatar": "https://example.com/avatar.jpg"
    },
    "message": "started following you"
  }
}
```
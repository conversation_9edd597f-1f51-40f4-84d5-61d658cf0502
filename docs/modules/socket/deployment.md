# Deployment Architecture

## Single Server Setup

```mermaid
flowchart TD
    A[Client Browsers] --> B[Nginx Load Balancer]
    B --> C[Go WebSocket Server]
    C --> D[Redis Pub/Sub]
    C --> E[MySQL Database]
    C --> F[File Storage]
```

## Multi-Server Setup

```mermaid
flowchart TD
    A[Client Browsers] --> B[Load Balancer]
    B --> C[WebSocket Server 1]
    B --> D[WebSocket Server 2]
    B --> E[WebSocket Server 3]
    
    C --> F[Redis Cluster]
    D --> F
    E --> F
    
    F --> G[Message Synchronization]
    
    C --> H[Database Cluster]
    D --> H
    E --> H
```
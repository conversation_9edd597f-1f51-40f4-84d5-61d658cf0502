# API Endpoints

## HTTP Endpoints (REST fallback)

- `GET /api/cms/v1/socket/rooms` - <PERSON>h sách rooms
- `POST /api/cms/v1/socket/rooms` - Tạo room mới
- `GET /api/cms/v1/socket/rooms/{id}/messages` - <PERSON><PERSON><PERSON> sử tin nhắn
- `POST /api/cms/v1/socket/rooms/{id}/join` - Tham gia room
- `DELETE /api/cms/v1/socket/rooms/{id}/leave` - Rời room

## WebSocket Events

- `message.send` - <PERSON><PERSON><PERSON> tin nhắn
- `room.join` - Tham gia room
- `room.leave` - Rời room
- `typing.start` - Bắt đầu gõ
- `typing.stop` - <PERSON>ừng gõ
- `presence.update` - Cập nhật trạng thái
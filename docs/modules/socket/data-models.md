# Mô hình dữ liệu Socket

## Connection

- **ID**: Định danh kết nối
- **UserID**: ID người dùng
- **TenantID**: ID tenant
- **WebsiteID**: ID website
- **SocketID**: ID WebSocket connection
- **Status**: Tr<PERSON>ng thái (connected, disconnected)
- **LastSeen**: Lần cuối online
- **UserAgent**: Thông tin browser/device
- **IPAddress**: Địa chỉ IP

## Room

- **ID**: Định danh room
- **TenantID**: ID tenant
- **WebsiteID**: ID website
- **Name**: Tên room
- **Type**: Loại room (chat, notification, post_comments)
- **IsPrivate**: Room riêng tư hay công khai
- **MaxMembers**: Số thành viên tối đa
- **CreatedBy**: ID người tạo
- **Settings**: Cài đặt room (JSON)

## ChatMessage

- **ID**: Định danh tin nhắn
- **RoomID**: ID room
- **WebsiteID**: ID website
- **UserID**: ID người gửi
- **Content**: Nội dung tin nhắn
- **Type**: Loại tin nhắn (text, image, file, system)
- **ReplyToID**: ID tin nhắn được reply
- **EditedAt**: Thời gian chỉnh sửa
- **Reactions**: Reactions (JSON)
- **Status**: Trạng thái (sent, delivered, read)

## RoomMember

- **RoomID**: ID room
- **UserID**: ID thành viên
- **Role**: Vai trò (member, moderator, admin)
- **JoinedAt**: Thời gian tham gia
- **LastReadAt**: Lần cuối đọc tin nhắn
- **Notifications**: Cài đặt thông báo
# Multi-Tenancy & Website Isolation

## Website-based Socket Management

```mermaid
flowchart TD
    A[Socket Connection] --> B[Extract Website Context]
    B --> C[Validate Website Access]
    C --> D[Join Website-specific Rooms]
    D --> E[Set Website Permissions]
    E --> F[Enable Website Communication]
    
    G[Message Broadcast] --> H[Check Website Scope]
    H --> I[Filter Recipients by Website]
    I --> J[Send to Website Users Only]
```

## Socket Service với Website Isolation

```go
type WebsiteSocketService struct {
    hub       *SocketHub
    websiteID uint
    userPerms UserPermissionService
}

func (s *WebsiteSocketService) JoinRoom(userID uint, roomName string) error {
    // Validate user has access to website
    if !s.userPerms.HasWebsiteAccess(userID, s.websiteID) {
        return errors.New("access denied to website")
    }
    
    // Create website-scoped room name
    scopedRoomName := fmt.Sprintf("website:%d:%s", s.websiteID, roomName)
    
    // Join the room
    return s.hub.JoinRoom(userID, scopedRoomName)
}

func (s *WebsiteSocketService) BroadcastToWebsite(message *Message) error {
    // Ensure message is scoped to website
    message.WebsiteID = s.websiteID
    
    // Broadcast to all website users
    roomName := fmt.Sprintf("website:%d:broadcast", s.websiteID)
    return s.hub.BroadcastToRoom(roomName, message)
}
```

## Website-specific Namespaces

```javascript
// Client-side connection with website context
const socket = io('https://api.yourblog.com', {
  query: {
    website_id: websiteId,
    user_id: userId,
    token: authToken
  },
  transports: ['websocket']
});

// Auto-join website-specific rooms
socket.on('connect', () => {
  socket.emit('join_website_room', {
    website_id: websiteId,
    room_type: 'general'
  });
});
```
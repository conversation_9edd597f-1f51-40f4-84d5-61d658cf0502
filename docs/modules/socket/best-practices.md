# Best Practices

## Connection Management

- **Graceful Disconnection**: <PERSON><PERSON> lý ngắt kết nối mượt mà
- **Reconnection Logic**: Tự động kết nối lại với website context
- **Heartbeat Mechanism**: Ki<PERSON>m tra kết nối định kỳ
- **Connection Pooling**: Quản lý pool kết nối per website

## Message Handling

- **Message Queuing**: Xếp hàng tin nhắn với website context
- **Delivery Guarantee**: Đ<PERSON>m bảo gửi tin nhắn trong website scope
- **Duplicate Prevention**: Tránh tin nhắn trùng per website
- **Message Ordering**: <PERSON><PERSON><PERSON> b<PERSON>o thứ tự tin nhắn per website

## Scalability

- **Horizontal Scaling**: Mở rộng theo chiều ngang với website isolation
- **Load Distribution**: Phân tải đều theo website
- **State Management**: Quản lý state phân tán per website
- **Cache Strategy**: Chiến lược cache hiệu quả với website keys

## Website Isolation

- **Namespace Separation**: Tách biệt namespaces per website
- **Room Scoping**: Rooms luôn có website prefix
- **Message Filtering**: Lọc tin nhắn theo website
- **Permission Validation**: Kiểm tra quyền website trong real-time

## Example Implementation

```go
type WebsiteSocketHandler struct {
    hub       *SocketHub
    websiteID uint
}

func (h *WebsiteSocketHandler) HandleConnection(conn *websocket.Conn, userID uint) {
    // Validate website access
    if !h.validateWebsiteAccess(userID, h.websiteID) {
        conn.Close()
        return
    }
    
    // Create website-scoped connection
    client := &SocketClient{
        ID:        generateID(),
        UserID:    userID,
        WebsiteID: h.websiteID,
        Conn:      conn,
        Send:      make(chan []byte, 256),
    }
    
    // Register client
    h.hub.Register <- client
    
    // Auto-join website room
    websiteRoom := fmt.Sprintf("website:%d", h.websiteID)
    h.hub.JoinRoom(client.ID, websiteRoom)
    
    // Start goroutines
    go client.writePump()
    go client.readPump(h.hub)
}
```
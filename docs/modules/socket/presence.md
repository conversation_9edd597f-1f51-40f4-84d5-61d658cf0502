# Presence System

## User Presence States

```mermaid
stateDiagram-v2
    [*] --> Offline
    Offline --> Online: Connect
    Online --> Away: Idle timeout
    Online --> Busy: Manual set
    Online --> Offline: Disconnect
    Away --> Online: Activity detected
    Away --> Offline: Disconnect
    Busy --> Online: Manual unset
    Busy --> Offline: Disconnect
```

## Presence Broadcasting

```mermaid
flowchart LR
    A[User Status Change] --> B[Presence Service]
    B --> C[Update Database]
    C --> D[Get Friends List]
    D --> E[Broadcast to Friends]
    E --> F[Update UI Status]
```
# Tracking System

## Tracking Service

```go
type TrackingService struct {
    db    *gorm.DB
    cache *redis.Client
}

// TrackEvent tracks email events
func (s *TrackingService) TrackEvent(websiteID, emailID uint, trackingID, eventType string, metadata map[string]interface{}) error {
    tracking := &EmailTracking{
        WebsiteID:  websiteID,
        EmailID:    emailID,
        TrackingID: trackingID,
        EventType:  eventType,
        Metadata:   metadata,
        CreatedAt:  time.Now(),
    }
    
    // Save to database
    if err := s.db.Create(tracking).Error; err != nil {
        return err
    }
    
    // Update cache counters
    s.updateCacheCounters(websiteID, eventType)
    
    return nil
}

// TrackOpen tracks email opens
func (s *TrackingService) TrackOpen(trackingID string, userAgent, ipAddress string) error {
    // Get email info from tracking ID
    var email EmailQueue
    if err := s.db.Where("tracking_id = ?", trackingID).First(&email).Error; err != nil {
        return err
    }
    
    metadata := map[string]interface{}{
        "user_agent":  userAgent,
        "ip_address":  ipAddress,
        "opened_at":   time.Now(),
    }
    
    return s.TrackEvent(email.WebsiteID, email.ID, trackingID, "opened", metadata)
}

// TrackClick tracks email clicks
func (s *TrackingService) TrackClick(trackingID, url string, userAgent, ipAddress string) error {
    // Get email info from tracking ID
    var email EmailQueue
    if err := s.db.Where("tracking_id = ?", trackingID).First(&email).Error; err != nil {
        return err
    }
    
    metadata := map[string]interface{}{
        "url":         url,
        "user_agent":  userAgent,
        "ip_address":  ipAddress,
        "clicked_at":  time.Now(),
    }
    
    return s.TrackEvent(email.WebsiteID, email.ID, trackingID, "clicked", metadata)
}

// GetEmailStats gets email statistics
func (s *TrackingService) GetEmailStats(websiteID uint, period string) (*EmailStats, error) {
    var stats EmailStats
    
    // Calculate date range
    var startDate time.Time
    switch period {
    case "7d":
        startDate = time.Now().AddDate(0, 0, -7)
    case "30d":
        startDate = time.Now().AddDate(0, 0, -30)
    case "90d":
        startDate = time.Now().AddDate(0, 0, -90)
    default:
        startDate = time.Now().AddDate(0, 0, -30)
    }
    
    // Get total emails sent
    var totalSent int64
    s.db.Model(&EmailQueue{}).Where("website_id = ? AND created_at >= ?", websiteID, startDate).Count(&totalSent)
    
    // Get tracking stats
    var trackingStats []struct {
        EventType string
        Count     int64
    }
    
    s.db.Model(&EmailTracking{}).
        Select("event_type, COUNT(*) as count").
        Where("website_id = ? AND created_at >= ?", websiteID, startDate).
        Group("event_type").
        Scan(&trackingStats)
    
    // Build stats
    stats.TotalSent = totalSent
    
    for _, stat := range trackingStats {
        switch stat.EventType {
        case "delivered":
            stats.TotalDelivered = stat.Count
        case "opened":
            stats.TotalOpened = stat.Count
        case "clicked":
            stats.TotalClicked = stat.Count
        case "bounced":
            stats.TotalBounced = stat.Count
        case "unsubscribed":
            stats.TotalUnsubscribed = stat.Count
        }
    }
    
    // Calculate rates
    if totalSent > 0 {
        stats.DeliveryRate = float64(stats.TotalDelivered) / float64(totalSent) * 100
        stats.OpenRate = float64(stats.TotalOpened) / float64(totalSent) * 100
        stats.ClickRate = float64(stats.TotalClicked) / float64(totalSent) * 100
        stats.BounceRate = float64(stats.TotalBounced) / float64(totalSent) * 100
    }
    
    return &stats, nil
}

// updateCacheCounters updates cache counters for real-time stats
func (s *TrackingService) updateCacheCounters(websiteID uint, eventType string) {
    key := fmt.Sprintf("email_stats:%d:%s", websiteID, eventType)
    s.cache.Incr(context.Background(), key)
    s.cache.Expire(context.Background(), key, 24*time.Hour)
}
```

## Tracking Handlers

```go
// Track email open
func (h *TrackingHandler) TrackOpen(c *gin.Context) {
    trackingID := c.Param("tracking_id")
    userAgent := c.GetHeader("User-Agent")
    ipAddress := c.ClientIP()
    
    // Track the open event
    if err := h.tracker.TrackOpen(trackingID, userAgent, ipAddress); err != nil {
        log.Printf("Failed to track open: %v", err)
    }
    
    // Return 1x1 transparent pixel
    pixel := []byte{
        0x47, 0x49, 0x46, 0x38, 0x39, 0x61, 0x01, 0x00,
        0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x21, 0xF9, 0x04, 0x01, 0x00,
        0x00, 0x00, 0x00, 0x2C, 0x00, 0x00, 0x00, 0x00,
        0x01, 0x00, 0x01, 0x00, 0x00, 0x02, 0x02, 0x04,
        0x01, 0x00, 0x3B,
    }
    
    c.Header("Content-Type", "image/gif")
    c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
    c.Data(200, "image/gif", pixel)
}

// Track email click
func (h *TrackingHandler) TrackClick(c *gin.Context) {
    trackingID := c.Param("tracking_id")
    url := c.Query("url")
    userAgent := c.GetHeader("User-Agent")
    ipAddress := c.ClientIP()
    
    // Track the click event
    if err := h.tracker.TrackClick(trackingID, url, userAgent, ipAddress); err != nil {
        log.Printf("Failed to track click: %v", err)
    }
    
    // Redirect to original URL
    c.Redirect(302, url)
}

// Track unsubscribe
func (h *TrackingHandler) TrackUnsubscribe(c *gin.Context) {
    token := c.Query("token")
    
    // Unsubscribe user
    if err := h.subscriptionService.Unsubscribe(c, token); err != nil {
        c.JSON(400, gin.H{"error": "Invalid unsubscribe token"})
        return
    }
    
    c.JSON(200, gin.H{"message": "Successfully unsubscribed"})
}
```

## Analytics Service

```go
type AnalyticsService struct {
    db          *gorm.DB
    cache       *redis.Client
    tracker     *TrackingService
}

// GetCampaignAnalytics gets analytics for a specific campaign
func (s *AnalyticsService) GetCampaignAnalytics(websiteID, campaignID uint) (*CampaignAnalytics, error) {
    var analytics CampaignAnalytics
    
    // Get campaign info
    var campaign EmailCampaign
    if err := s.db.First(&campaign, campaignID).Error; err != nil {
        return nil, err
    }
    
    // Get emails sent for this campaign
    var emails []EmailQueue
    if err := s.db.Where("website_id = ? AND campaign_id = ?", websiteID, campaignID).Find(&emails).Error; err != nil {
        return nil, err
    }
    
    analytics.CampaignID = campaignID
    analytics.CampaignName = campaign.Name
    analytics.TotalRecipients = len(emails)
    
    // Get tracking events
    emailIDs := make([]uint, len(emails))
    for i, email := range emails {
        emailIDs[i] = email.ID
    }
    
    var trackingEvents []EmailTracking
    if err := s.db.Where("email_id IN ?", emailIDs).Find(&trackingEvents).Error; err != nil {
        return nil, err
    }
    
    // Calculate metrics
    eventCounts := make(map[string]int)
    for _, event := range trackingEvents {
        eventCounts[event.EventType]++
    }
    
    analytics.Delivered = eventCounts["delivered"]
    analytics.Opened = eventCounts["opened"]
    analytics.Clicked = eventCounts["clicked"]
    analytics.Bounced = eventCounts["bounced"]
    analytics.Unsubscribed = eventCounts["unsubscribed"]
    
    // Calculate rates
    if analytics.TotalRecipients > 0 {
        analytics.DeliveryRate = float64(analytics.Delivered) / float64(analytics.TotalRecipients) * 100
        analytics.OpenRate = float64(analytics.Opened) / float64(analytics.TotalRecipients) * 100
        analytics.ClickRate = float64(analytics.Clicked) / float64(analytics.TotalRecipients) * 100
        analytics.BounceRate = float64(analytics.Bounced) / float64(analytics.TotalRecipients) * 100
    }
    
    return &analytics, nil
}

// GetTemplateAnalytics gets analytics for a specific template
func (s *AnalyticsService) GetTemplateAnalytics(websiteID, templateID uint, period string) (*TemplateAnalytics, error) {
    var analytics TemplateAnalytics
    
    // Get template info
    var template EmailTemplate
    if err := s.db.First(&template, templateID).Error; err != nil {
        return nil, err
    }
    
    // Calculate date range
    var startDate time.Time
    switch period {
    case "7d":
        startDate = time.Now().AddDate(0, 0, -7)
    case "30d":
        startDate = time.Now().AddDate(0, 0, -30)
    case "90d":
        startDate = time.Now().AddDate(0, 0, -90)
    default:
        startDate = time.Now().AddDate(0, 0, -30)
    }
    
    // Get emails sent using this template
    var emails []EmailQueue
    if err := s.db.Where("website_id = ? AND template_id = ? AND created_at >= ?", 
        websiteID, templateID, startDate).Find(&emails).Error; err != nil {
        return nil, err
    }
    
    analytics.TemplateID = templateID
    analytics.TemplateName = template.Name
    analytics.TotalSent = len(emails)
    
    // Get tracking events
    emailIDs := make([]uint, len(emails))
    for i, email := range emails {
        emailIDs[i] = email.ID
    }
    
    if len(emailIDs) > 0 {
        var trackingEvents []EmailTracking
        if err := s.db.Where("email_id IN ?", emailIDs).Find(&trackingEvents).Error; err != nil {
            return nil, err
        }
        
        // Calculate metrics
        eventCounts := make(map[string]int)
        for _, event := range trackingEvents {
            eventCounts[event.EventType]++
        }
        
        analytics.Delivered = eventCounts["delivered"]
        analytics.Opened = eventCounts["opened"]
        analytics.Clicked = eventCounts["clicked"]
        analytics.Bounced = eventCounts["bounced"]
        
        // Calculate rates
        if analytics.TotalSent > 0 {
            analytics.DeliveryRate = float64(analytics.Delivered) / float64(analytics.TotalSent) * 100
            analytics.OpenRate = float64(analytics.Opened) / float64(analytics.TotalSent) * 100
            analytics.ClickRate = float64(analytics.Clicked) / float64(analytics.TotalSent) * 100
            analytics.BounceRate = float64(analytics.Bounced) / float64(analytics.TotalSent) * 100
        }
    }
    
    return &analytics, nil
}
```

## Analytics Data Structures

```go
// EmailStats represents email statistics
type EmailStats struct {
    TotalSent         int64   `json:"total_sent"`
    TotalDelivered    int64   `json:"total_delivered"`
    TotalOpened       int64   `json:"total_opened"`
    TotalClicked      int64   `json:"total_clicked"`
    TotalBounced      int64   `json:"total_bounced"`
    TotalUnsubscribed int64   `json:"total_unsubscribed"`
    DeliveryRate      float64 `json:"delivery_rate"`
    OpenRate          float64 `json:"open_rate"`
    ClickRate         float64 `json:"click_rate"`
    BounceRate        float64 `json:"bounce_rate"`
}

// CampaignAnalytics represents campaign analytics
type CampaignAnalytics struct {
    CampaignID       uint    `json:"campaign_id"`
    CampaignName     string  `json:"campaign_name"`
    TotalRecipients  int     `json:"total_recipients"`
    Delivered        int     `json:"delivered"`
    Opened           int     `json:"opened"`
    Clicked          int     `json:"clicked"`
    Bounced          int     `json:"bounced"`
    Unsubscribed     int     `json:"unsubscribed"`
    DeliveryRate     float64 `json:"delivery_rate"`
    OpenRate         float64 `json:"open_rate"`
    ClickRate        float64 `json:"click_rate"`
    BounceRate       float64 `json:"bounce_rate"`
    UnsubscribeRate  float64 `json:"unsubscribe_rate"`
}

// TemplateAnalytics represents template analytics
type TemplateAnalytics struct {
    TemplateID    uint    `json:"template_id"`
    TemplateName  string  `json:"template_name"`
    TotalSent     int     `json:"total_sent"`
    Delivered     int     `json:"delivered"`
    Opened        int     `json:"opened"`
    Clicked       int     `json:"clicked"`
    Bounced       int     `json:"bounced"`
    DeliveryRate  float64 `json:"delivery_rate"`
    OpenRate      float64 `json:"open_rate"`
    ClickRate     float64 `json:"click_rate"`
    BounceRate    float64 `json:"bounce_rate"`
}
```

## Tracking Utilities

```go
// TrackingUtils provides utility functions for tracking
type TrackingUtils struct {
    baseURL string
}

// GenerateTrackingPixel generates a tracking pixel URL
func (u *TrackingUtils) GenerateTrackingPixel(emailID uint) string {
    trackingID := GenerateTrackingID()
    return fmt.Sprintf("%s/track/%s/open", u.baseURL, trackingID)
}

// GenerateClickTrackingURL generates a click tracking URL
func (u *TrackingUtils) GenerateClickTrackingURL(emailID uint, originalURL string) string {
    trackingID := GenerateTrackingID()
    return fmt.Sprintf("%s/track/%s/click?url=%s", u.baseURL, trackingID, url.QueryEscape(originalURL))
}

// InjectTrackingLinks injects tracking links into HTML content
func (u *TrackingUtils) InjectTrackingLinks(htmlContent string, emailID uint) string {
    // Parse HTML
    doc, err := goquery.NewDocumentFromReader(strings.NewReader(htmlContent))
    if err != nil {
        return htmlContent
    }
    
    // Replace all links with tracking links
    doc.Find("a").Each(func(i int, s *goquery.Selection) {
        href, exists := s.Attr("href")
        if exists && !strings.Contains(href, u.baseURL) {
            trackingURL := u.GenerateClickTrackingURL(emailID, href)
            s.SetAttr("href", trackingURL)
        }
    })
    
    // Return modified HTML
    modifiedHTML, err := doc.Html()
    if err != nil {
        return htmlContent
    }
    
    return modifiedHTML
}

// ExtractUserAgent extracts browser and OS information from user agent
func (u *TrackingUtils) ExtractUserAgent(userAgent string) map[string]string {
    info := make(map[string]string)
    
    // Simple user agent parsing
    if strings.Contains(userAgent, "Chrome") {
        info["browser"] = "Chrome"
    } else if strings.Contains(userAgent, "Firefox") {
        info["browser"] = "Firefox"
    } else if strings.Contains(userAgent, "Safari") {
        info["browser"] = "Safari"
    } else if strings.Contains(userAgent, "Edge") {
        info["browser"] = "Edge"
    } else {
        info["browser"] = "Unknown"
    }
    
    if strings.Contains(userAgent, "Windows") {
        info["os"] = "Windows"
    } else if strings.Contains(userAgent, "Macintosh") {
        info["os"] = "macOS"
    } else if strings.Contains(userAgent, "Linux") {
        info["os"] = "Linux"
    } else if strings.Contains(userAgent, "iPhone") {
        info["os"] = "iOS"
    } else if strings.Contains(userAgent, "Android") {
        info["os"] = "Android"
    } else {
        info["os"] = "Unknown"
    }
    
    return info
}
```

## Real-time Tracking

```go
// RealTimeTracker provides real-time tracking capabilities
type RealTimeTracker struct {
    redis       *redis.Client
    subscribers map[string]chan *TrackingEvent
    mu          sync.RWMutex
}

type TrackingEvent struct {
    WebsiteID   uint                   `json:"website_id"`
    EmailID     uint                   `json:"email_id"`
    EventType   string                 `json:"event_type"`
    Timestamp   time.Time              `json:"timestamp"`
    Metadata    map[string]interface{} `json:"metadata"`
}

// Subscribe subscribes to real-time tracking events
func (t *RealTimeTracker) Subscribe(websiteID uint) chan *TrackingEvent {
    t.mu.Lock()
    defer t.mu.Unlock()
    
    channel := make(chan *TrackingEvent, 100)
    key := fmt.Sprintf("website_%d", websiteID)
    t.subscribers[key] = channel
    
    return channel
}

// Publish publishes a tracking event to subscribers
func (t *RealTimeTracker) Publish(event *TrackingEvent) {
    t.mu.RLock()
    defer t.mu.RUnlock()
    
    key := fmt.Sprintf("website_%d", event.WebsiteID)
    if channel, exists := t.subscribers[key]; exists {
        select {
        case channel <- event:
        default:
            // Channel is full, skip
        }
    }
    
    // Also publish to Redis for cross-server communication
    eventJSON, _ := json.Marshal(event)
    t.redis.Publish(context.Background(), fmt.Sprintf("tracking:%d", event.WebsiteID), eventJSON)
}
```
# Email Interfaces

## Core Interfaces

### Email Service Interface

Email Module định nghĩa abstract interfaces mà các Email Plugins phải implement:

```go
// Core email service interface
type EmailService interface {
    // Basic email operations
    SendEmail(ctx context.Context, email *EmailMessage) (*EmailResult, error)
    SendTemplate(ctx context.Context, template *TemplateMessage) (*EmailResult, error)
    SendBulk(ctx context.Context, emails []*EmailMessage) ([]*EmailResult, error)

    // Delivery tracking
    GetDeliveryStatus(ctx context.Context, messageID string) (*DeliveryStatus, error)
    GetBulkDeliveryStatus(ctx context.Context, messageIDs []string) ([]*DeliveryStatus, error)

    // Webhook handling
    HandleWebhook(ctx context.Context, payload []byte, headers map[string]string) (*WebhookResult, error)

    // Provider info
    GetProviderInfo() *ProviderInfo
    HealthCheck(ctx context.Context) error
}

// Template service interface
type TemplateService interface {
    CreateTemplate(ctx context.Context, template *EmailTemplate) error
    UpdateTemplate(ctx context.Context, id string, template *EmailTemplate) error
    DeleteTemplate(ctx context.Context, id string) error
    GetTemplate(ctx context.Context, id string) (*EmailTemplate, error)
    ListTemplates(ctx context.Context, filters *TemplateFilters) ([]*EmailTemplate, error)
}

// Analytics service interface
type AnalyticsService interface {
    TrackOpen(ctx context.Context, messageID string, metadata map[string]interface{}) error
    TrackClick(ctx context.Context, messageID string, url string, metadata map[string]interface{}) error
    TrackBounce(ctx context.Context, messageID string, bounceType string, reason string) error
    TrackUnsubscribe(ctx context.Context, messageID string, email string) error

    GetEmailMetrics(ctx context.Context, filters *MetricsFilters) (*EmailMetrics, error)
    GetCampaignMetrics(ctx context.Context, campaignID string) (*CampaignMetrics, error)
}
```

## Plugin Management Interface

```go
// Plugin registry interface
type EmailPluginRegistry interface {
    RegisterPlugin(name string, plugin EmailService) error
    GetPlugin(name string) (EmailService, error)
    GetActivePlugin() (EmailService, error)
    ListPlugins() []PluginInfo
    SetActivePlugin(name string) error
}

// Plugin lifecycle interface
type EmailPlugin interface {
    EmailService

    // Plugin lifecycle
    Initialize(config map[string]interface{}) error
    Shutdown() error
    GetInfo() *PluginInfo
    ValidateConfig(config map[string]interface{}) error
}
```

## Data Structures

### Core Email Message Structure

```go
// Core email message structure
type EmailMessage struct {
    To          []string               `json:"to"`
    CC          []string               `json:"cc,omitempty"`
    BCC         []string               `json:"bcc,omitempty"`
    From        string                 `json:"from"`
    ReplyTo     string                 `json:"reply_to,omitempty"`
    Subject     string                 `json:"subject"`
    HTMLContent string                 `json:"html_content,omitempty"`
    TextContent string                 `json:"text_content,omitempty"`
    Attachments []*EmailAttachment     `json:"attachments,omitempty"`
    Headers     map[string]string      `json:"headers,omitempty"`
    Metadata    map[string]interface{} `json:"metadata,omitempty"`
    Priority    EmailPriority          `json:"priority"`
    ScheduledAt *time.Time             `json:"scheduled_at,omitempty"`
}

// Template message structure
type TemplateMessage struct {
    TemplateID  string                 `json:"template_id"`
    To          []string               `json:"to"`
    Variables   map[string]interface{} `json:"variables"`
    Metadata    map[string]interface{} `json:"metadata,omitempty"`
    Priority    EmailPriority          `json:"priority"`
    ScheduledAt *time.Time             `json:"scheduled_at,omitempty"`
}

// Email result structure
type EmailResult struct {
    MessageID    string                 `json:"message_id"`
    Status       EmailStatus            `json:"status"`
    ProviderID   string                 `json:"provider_id,omitempty"`
    Error        string                 `json:"error,omitempty"`
    Metadata     map[string]interface{} `json:"metadata,omitempty"`
    SentAt       *time.Time             `json:"sent_at,omitempty"`
}
```

### Email Attachment Structure

```go
type EmailAttachment struct {
    Filename    string `json:"filename"`
    ContentType string `json:"content_type"`
    Content     []byte `json:"content"`
    ContentID   string `json:"content_id,omitempty"`
    Disposition string `json:"disposition,omitempty"` // attachment, inline
}
```

### Email Priority and Status

```go
type EmailPriority int

const (
    PriorityLow    EmailPriority = 1
    PriorityNormal EmailPriority = 2
    PriorityHigh   EmailPriority = 3
    PriorityUrgent EmailPriority = 4
)

type EmailStatus string

const (
    StatusPending   EmailStatus = "pending"
    StatusQueued    EmailStatus = "queued"
    StatusSent      EmailStatus = "sent"
    StatusDelivered EmailStatus = "delivered"
    StatusFailed    EmailStatus = "failed"
    StatusBounced   EmailStatus = "bounced"
)
```

### Provider Information

```go
type ProviderInfo struct {
    Name         string                 `json:"name"`
    Version      string                 `json:"version"`
    Description  string                 `json:"description"`
    Author       string                 `json:"author"`
    Capabilities []string               `json:"capabilities"`
    Limits       *ProviderLimits        `json:"limits,omitempty"`
    Config       map[string]interface{} `json:"config,omitempty"`
}

type ProviderLimits struct {
    MaxRecipientsPerEmail int `json:"max_recipients_per_email"`
    MaxEmailsPerHour      int `json:"max_emails_per_hour"`
    MaxEmailsPerDay       int `json:"max_emails_per_day"`
    MaxAttachmentSize     int `json:"max_attachment_size"`
    MaxEmailSize          int `json:"max_email_size"`
}
```

### Delivery Status

```go
type DeliveryStatus struct {
    MessageID     string                 `json:"message_id"`
    Status        EmailStatus            `json:"status"`
    DeliveredAt   *time.Time             `json:"delivered_at,omitempty"`
    BouncedAt     *time.Time             `json:"bounced_at,omitempty"`
    OpenedAt      *time.Time             `json:"opened_at,omitempty"`
    ClickedAt     *time.Time             `json:"clicked_at,omitempty"`
    UnsubscribedAt *time.Time            `json:"unsubscribed_at,omitempty"`
    BounceReason  string                 `json:"bounce_reason,omitempty"`
    Metadata      map[string]interface{} `json:"metadata,omitempty"`
}
```

### Webhook Result

```go
type WebhookResult struct {
    Processed bool                   `json:"processed"`
    Events    []WebhookEvent         `json:"events"`
    Error     string                 `json:"error,omitempty"`
    Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

type WebhookEvent struct {
    MessageID string                 `json:"message_id"`
    Event     string                 `json:"event"`
    Timestamp time.Time              `json:"timestamp"`
    Data      map[string]interface{} `json:"data"`
}
```

## Email Providers Interface

```go
// EmailProvider interface
type EmailProvider interface {
    SendEmail(msg *EmailMessage) error
    GetDeliveryStatus(messageID string) (*DeliveryStatus, error)
}
```

### Example Provider Implementations

#### SMTP Provider
```go
// SMTP Provider
type SMTPProvider struct {
    host     string
    port     int
    username string
    password string
}

func (p *SMTPProvider) SendEmail(msg *EmailMessage) error {
    auth := smtp.PlainAuth("", p.username, p.password, p.host)
    
    // Compose email
    to := []string{msg.To}
    body := p.composeEmail(msg)
    
    // Send email
    addr := fmt.Sprintf("%s:%d", p.host, p.port)
    return smtp.SendMail(addr, auth, msg.From, to, []byte(body))
}

func (p *SMTPProvider) composeEmail(msg *EmailMessage) string {
    var body strings.Builder
    
    body.WriteString(fmt.Sprintf("From: %s\r\n", msg.From))
    body.WriteString(fmt.Sprintf("To: %s\r\n", strings.Join(msg.To, ",")))
    body.WriteString(fmt.Sprintf("Subject: %s\r\n", msg.Subject))
    
    if msg.ReplyTo != "" {
        body.WriteString(fmt.Sprintf("Reply-To: %s\r\n", msg.ReplyTo))
    }
    
    // Add custom headers
    for key, value := range msg.Headers {
        body.WriteString(fmt.Sprintf("%s: %s\r\n", key, value))
    }
    
    body.WriteString("MIME-Version: 1.0\r\n")
    body.WriteString("Content-Type: multipart/alternative; boundary=\"boundary\"\r\n\r\n")
    
    // Text part
    if msg.TextContent != "" {
        body.WriteString("--boundary\r\n")
        body.WriteString("Content-Type: text/plain; charset=\"utf-8\"\r\n\r\n")
        body.WriteString(msg.TextContent)
        body.WriteString("\r\n\r\n")
    }
    
    // HTML part
    if msg.HTMLContent != "" {
        body.WriteString("--boundary\r\n")
        body.WriteString("Content-Type: text/html; charset=\"utf-8\"\r\n\r\n")
        body.WriteString(msg.HTMLContent)
        body.WriteString("\r\n\r\n")
    }
    
    body.WriteString("--boundary--")
    
    return body.String()
}
```

#### SendGrid Provider
```go
// SendGrid Provider
type SendGridProvider struct {
    apiKey string
    client *sendgrid.Client
}

func (p *SendGridProvider) SendEmail(msg *EmailMessage) error {
    from := mail.NewEmail("", msg.From)
    to := mail.NewEmail("", msg.To[0]) // SendGrid v3 API handles single recipient
    
    message := mail.NewSingleEmail(from, msg.Subject, to, msg.TextContent, msg.HTMLContent)
    
    // Add tracking
    message.SetTrackingSettings(&mail.TrackingSettings{
        ClickTracking: &mail.ClickTrackingSettings{
            Enable: mail.NewBool(true),
        },
        OpenTracking: &mail.OpenTrackingSettings{
            Enable: mail.NewBool(true),
        },
    })
    
    // Add attachments
    for _, attachment := range msg.Attachments {
        a := mail.NewAttachment()
        a.SetFilename(attachment.Filename)
        a.SetContent(base64.StdEncoding.EncodeToString(attachment.Content))
        a.SetType(attachment.ContentType)
        a.SetDisposition(attachment.Disposition)
        message.AddAttachment(a)
    }
    
    response, err := p.client.Send(message)
    if err != nil {
        return err
    }
    
    if response.StatusCode >= 400 {
        return fmt.Errorf("failed to send email: %s", response.Body)
    }
    
    return nil
}
```

## Plugin Registry Implementation

```go
type EmailPluginRegistry struct {
    plugins      map[string]EmailPlugin
    activePlugin string
    mu           sync.RWMutex
}

func NewEmailPluginRegistry() *EmailPluginRegistry {
    return &EmailPluginRegistry{
        plugins: make(map[string]EmailPlugin),
    }
}

func (r *EmailPluginRegistry) RegisterPlugin(name string, plugin EmailPlugin) error {
    r.mu.Lock()
    defer r.mu.Unlock()
    
    if _, exists := r.plugins[name]; exists {
        return fmt.Errorf("plugin %s already registered", name)
    }
    
    r.plugins[name] = plugin
    return nil
}

func (r *EmailPluginRegistry) GetPlugin(name string) (EmailPlugin, error) {
    r.mu.RLock()
    defer r.mu.RUnlock()
    
    plugin, exists := r.plugins[name]
    if !exists {
        return nil, fmt.Errorf("plugin %s not found", name)
    }
    
    return plugin, nil
}

func (r *EmailPluginRegistry) GetActivePlugin() (EmailPlugin, error) {
    r.mu.RLock()
    defer r.mu.RUnlock()
    
    if r.activePlugin == "" {
        return nil, fmt.Errorf("no active plugin set")
    }
    
    return r.GetPlugin(r.activePlugin)
}

func (r *EmailPluginRegistry) SetActivePlugin(name string) error {
    r.mu.Lock()
    defer r.mu.Unlock()
    
    if _, exists := r.plugins[name]; !exists {
        return fmt.Errorf("plugin %s not found", name)
    }
    
    r.activePlugin = name
    return nil
}

func (r *EmailPluginRegistry) ListPlugins() []PluginInfo {
    r.mu.RLock()
    defer r.mu.RUnlock()
    
    var plugins []PluginInfo
    for _, plugin := range r.plugins {
        plugins = append(plugins, *plugin.GetInfo())
    }
    
    return plugins
}
```

## Template Processing Interface

```go
type TemplateProcessor interface {
    ParseTemplate(content string) (*ParsedTemplate, error)
    RenderTemplate(template *ParsedTemplate, variables map[string]interface{}) (string, error)
    ValidateTemplate(content string) error
    ExtractVariables(content string) ([]string, error)
}

type ParsedTemplate struct {
    Content   string
    Variables []string
    Metadata  map[string]interface{}
}
```

## Queue Interface

```go
type EmailQueue interface {
    Enqueue(job *EmailJob) error
    Dequeue() (*EmailJob, error)
    Requeue(job *EmailJob, delay time.Duration) error
    Dead(job *EmailJob, reason string) error
    Stats() (*QueueStats, error)
}

type EmailJob struct {
    ID          string                 `json:"id"`
    EmailID     uint                   `json:"email_id"`
    WebsiteID   uint                   `json:"website_id"`
    Priority    EmailPriority          `json:"priority"`
    Attempts    int                    `json:"attempts"`
    MaxAttempts int                    `json:"max_attempts"`
    CreatedAt   time.Time              `json:"created_at"`
    ScheduledAt *time.Time             `json:"scheduled_at,omitempty"`
    Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

type QueueStats struct {
    Pending   int `json:"pending"`
    Processing int `json:"processing"`
    Completed int `json:"completed"`
    Failed    int `json:"failed"`
    Dead      int `json:"dead"`
}
```
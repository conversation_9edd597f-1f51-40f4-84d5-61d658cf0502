# Configuration

## Email Module Configuration

```yaml
email:
  enabled: true
  
  providers:
    default: "smtp"
    smtp:
      host: "smtp.gmail.com"
      port: 587
      username: "${SMTP_USERNAME}"
      password: "${SMTP_PASSWORD}"
      tls: true
    sendgrid:
      api_key: "${SENDGRID_API_KEY}"
    mailgun:
      api_key: "${MAILGUN_API_KEY}"
      domain: "${MAILGUN_DOMAIN}"
      
  queue:
    driver: "redis"
    max_retries: 3
    retry_delay: "5m"
    
  tracking:
    enabled: true
    base_url: "https://track.yourdomain.com"
    pixel_enabled: true
    click_tracking: true
    
  templates:
    cache_enabled: true
    cache_ttl: "1h"
    
  rate_limiting:
    enabled: true
    max_per_hour: 1000
    max_per_day: 10000
```

## Environment Variables

```bash
# Database
DATABASE_URL=mysql://user:password@localhost:3306/database

# Redis
REDIS_URL=redis://localhost:6379/0

# SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true

# SendGrid
SENDGRID_API_KEY=your-sendgrid-api-key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=Your App Name

# Mailgun
MAILGUN_API_KEY=your-mailgun-api-key
MAILGUN_DOMAIN=yourdomain.com
MAILGUN_BASE_URL=https://api.mailgun.net/v3

# Amazon SES
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_SES_SOURCE=<EMAIL>

# Tracking
TRACKING_BASE_URL=https://track.yourdomain.com
TRACKING_PIXEL_ENABLED=true
CLICK_TRACKING_ENABLED=true

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_PER_HOUR=1000
RATE_LIMIT_PER_DAY=10000

# Template Configuration
TEMPLATE_CACHE_ENABLED=true
TEMPLATE_CACHE_TTL=3600

# Queue Configuration
QUEUE_DRIVER=redis
QUEUE_MAX_RETRIES=3
QUEUE_RETRY_DELAY=300
QUEUE_WORKER_COUNT=5

# Security
JWT_SECRET=your-jwt-secret-key
ENCRYPTION_KEY=your-encryption-key

# Logging
LOG_LEVEL=info
LOG_FORMAT=json
```

## Docker Configuration

### Dockerfile
```dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN go build -o email-service ./cmd/email

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/email-service .
COPY --from=builder /app/config ./config
COPY --from=builder /app/templates ./templates

EXPOSE 8080
CMD ["./email-service"]
```

### Docker Compose
```yaml
version: '3.8'

services:
  email-service:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=mysql://user:password@mysql:3306/emails
      - REDIS_URL=redis://redis:6379/0
      - SMTP_HOST=smtp.gmail.com
      - SMTP_PORT=587
      - SMTP_USERNAME=${SMTP_USERNAME}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
    depends_on:
      - mysql
      - redis
    volumes:
      - ./config:/app/config
      - ./templates:/app/templates
      - ./logs:/app/logs

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: emails
      MYSQL_USER: user
      MYSQL_PASSWORD: password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  email-worker:
    build: .
    command: ["./email-service", "worker"]
    environment:
      - DATABASE_URL=mysql://user:password@mysql:3306/emails
      - REDIS_URL=redis://redis:6379/0
      - SMTP_HOST=smtp.gmail.com
      - SMTP_USERNAME=${SMTP_USERNAME}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
    depends_on:
      - mysql
      - redis
    volumes:
      - ./config:/app/config
    scale: 3

volumes:
  mysql_data:
  redis_data:
```

## Kubernetes Configuration

### Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: email-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: email-service
  template:
    metadata:
      labels:
        app: email-service
    spec:
      containers:
      - name: email-service
        image: email-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: email-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: email-config
              key: redis-url
        - name: SMTP_USERNAME
          valueFrom:
            secretKeyRef:
              name: email-secrets
              key: smtp-username
        - name: SMTP_PASSWORD
          valueFrom:
            secretKeyRef:
              name: email-secrets
              key: smtp-password
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: email-service
spec:
  selector:
    app: email-service
  ports:
  - port: 80
    targetPort: 8080
  type: ClusterIP

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: email-config
data:
  redis-url: "redis://redis-service:6379/0"
  smtp-host: "smtp.gmail.com"
  smtp-port: "587"
  tracking-base-url: "https://track.yourdomain.com"

---
apiVersion: v1
kind: Secret
metadata:
  name: email-secrets
type: Opaque
data:
  database-url: <base64-encoded-url>
  smtp-username: <base64-encoded-username>
  smtp-password: <base64-encoded-password>
  sendgrid-api-key: <base64-encoded-key>
```

### Worker Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: email-worker
spec:
  replicas: 5
  selector:
    matchLabels:
      app: email-worker
  template:
    metadata:
      labels:
        app: email-worker
    spec:
      containers:
      - name: email-worker
        image: email-service:latest
        command: ["./email-service", "worker"]
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: email-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: email-config
              key: redis-url
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
```

## Application Configuration

### Config Structure
```go
type Config struct {
    Server    ServerConfig    `yaml:"server"`
    Database  DatabaseConfig  `yaml:"database"`
    Redis     RedisConfig     `yaml:"redis"`
    Email     EmailConfig     `yaml:"email"`
    Queue     QueueConfig     `yaml:"queue"`
    Tracking  TrackingConfig  `yaml:"tracking"`
    Security  SecurityConfig  `yaml:"security"`
    Logging   LoggingConfig   `yaml:"logging"`
}

type ServerConfig struct {
    Port         int    `yaml:"port"`
    Host         string `yaml:"host"`
    ReadTimeout  int    `yaml:"read_timeout"`
    WriteTimeout int    `yaml:"write_timeout"`
}

type DatabaseConfig struct {
    URL             string `yaml:"url"`
    MaxOpenConns    int    `yaml:"max_open_conns"`
    MaxIdleConns    int    `yaml:"max_idle_conns"`
    ConnMaxLifetime int    `yaml:"conn_max_lifetime"`
}

type EmailConfig struct {
    DefaultProvider string                    `yaml:"default_provider"`
    Providers       map[string]ProviderConfig `yaml:"providers"`
    Templates       TemplateConfig            `yaml:"templates"`
    RateLimit       RateLimitConfig           `yaml:"rate_limit"`
}

type ProviderConfig struct {
    Enabled bool                   `yaml:"enabled"`
    Config  map[string]interface{} `yaml:"config"`
}
```

### Configuration Loading
```go
func LoadConfig(configPath string) (*Config, error) {
    data, err := ioutil.ReadFile(configPath)
    if err != nil {
        return nil, fmt.Errorf("failed to read config file: %w", err)
    }
    
    var config Config
    if err := yaml.Unmarshal(data, &config); err != nil {
        return nil, fmt.Errorf("failed to parse config: %w", err)
    }
    
    // Load environment variables
    config.loadFromEnv()
    
    // Validate configuration
    if err := config.Validate(); err != nil {
        return nil, fmt.Errorf("invalid configuration: %w", err)
    }
    
    return &config, nil
}

func (c *Config) loadFromEnv() {
    if dbURL := os.Getenv("DATABASE_URL"); dbURL != "" {
        c.Database.URL = dbURL
    }
    
    if redisURL := os.Getenv("REDIS_URL"); redisURL != "" {
        c.Redis.URL = redisURL
    }
    
    if smtpHost := os.Getenv("SMTP_HOST"); smtpHost != "" {
        c.Email.Providers["smtp"].Config["host"] = smtpHost
    }
    
    // Load other environment variables...
}
```

## Health Checks

### Health Check Configuration
```go
type HealthChecker struct {
    db       *sql.DB
    redis    *redis.Client
    providers map[string]EmailProvider
}

func (h *HealthChecker) CheckHealth() *HealthStatus {
    status := &HealthStatus{
        Status:    "healthy",
        Timestamp: time.Now(),
        Checks:    make(map[string]interface{}),
    }
    
    // Database check
    if err := h.db.Ping(); err != nil {
        status.Status = "unhealthy"
        status.Checks["database"] = map[string]interface{}{
            "status": "down",
            "error":  err.Error(),
        }
    } else {
        status.Checks["database"] = map[string]interface{}{
            "status": "up",
        }
    }
    
    // Redis check
    if err := h.redis.Ping(context.Background()).Err(); err != nil {
        status.Status = "unhealthy"
        status.Checks["redis"] = map[string]interface{}{
            "status": "down",
            "error":  err.Error(),
        }
    } else {
        status.Checks["redis"] = map[string]interface{}{
            "status": "up",
        }
    }
    
    // Email provider checks
    for name, provider := range h.providers {
        if err := provider.HealthCheck(context.Background()); err != nil {
            status.Status = "degraded"
            status.Checks[name] = map[string]interface{}{
                "status": "down",
                "error":  err.Error(),
            }
        } else {
            status.Checks[name] = map[string]interface{}{
                "status": "up",
            }
        }
    }
    
    return status
}
```

## Monitoring Configuration

### Prometheus Metrics
```go
var (
    emailsSentTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "emails_sent_total",
            Help: "Total number of emails sent",
        },
        []string{"website_id", "provider", "status"},
    )
    
    emailDeliveryDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "email_delivery_duration_seconds",
            Help: "Email delivery duration in seconds",
        },
        []string{"website_id", "provider"},
    )
    
    queueSize = prometheus.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "email_queue_size",
            Help: "Current email queue size",
        },
        []string{"website_id", "priority"},
    )
)

func init() {
    prometheus.MustRegister(emailsSentTotal)
    prometheus.MustRegister(emailDeliveryDuration)
    prometheus.MustRegister(queueSize)
}
```

### Logging Configuration
```yaml
logging:
  level: info
  format: json
  output: stdout
  file:
    enabled: true
    path: /var/log/email-service.log
    max_size: 100MB
    max_backups: 5
    max_age: 30
    compress: true
```

## Security Configuration

### JWT Configuration
```go
type JWTConfig struct {
    Secret     string        `yaml:"secret"`
    Expiration time.Duration `yaml:"expiration"`
    Issuer     string        `yaml:"issuer"`
}

func (j *JWTConfig) GenerateToken(userID uint, websiteID uint) (string, error) {
    claims := jwt.MapClaims{
        "user_id":    userID,
        "website_id": websiteID,
        "exp":        time.Now().Add(j.Expiration).Unix(),
        "iss":        j.Issuer,
    }
    
    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString([]byte(j.Secret))
}
```

### Rate Limiting Configuration
```go
type RateLimitConfig struct {
    Enabled     bool `yaml:"enabled"`
    PerHour     int  `yaml:"per_hour"`
    PerDay      int  `yaml:"per_day"`
    BurstSize   int  `yaml:"burst_size"`
    CleanupInterval time.Duration `yaml:"cleanup_interval"`
}

func (r *RateLimitConfig) CreateLimiter() *RateLimiter {
    return &RateLimiter{
        hourlyLimit: r.PerHour,
        dailyLimit:  r.PerDay,
        burstSize:   r.BurstSize,
        store:       make(map[string]*UserLimits),
    }
}
```
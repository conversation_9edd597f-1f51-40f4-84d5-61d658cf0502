# Template System

## Template Structure

```go
type TemplateVariables struct {
    User      UserData              `json:"user"`
    Website   WebsiteData           `json:"website"`
    Content   ContentData           `json:"content"`
    Custom    map[string]interface{} `json:"custom"`
}

type UserData struct {
    ID        uint   `json:"id"`
    Name      string `json:"name"`
    Email     string `json:"email"`
    FirstName string `json:"first_name"`
    LastName  string `json:"last_name"`
}

type WebsiteData struct {
    Name    string `json:"name"`
    URL     string `json:"url"`
    Logo    string `json:"logo"`
    Support string `json:"support_email"`
}
```

## Template Examples

### Welcome Email Template
```html
<!DOCTYPE html>
<html>
<head>
    <title>{{.Website.Name}} - Welcome!</title>
    <style>
        body { font-family: Arial, sans-serif; }
        .header { background-color: #3498db; color: white; padding: 20px; }
        .content { padding: 20px; }
        .footer { background-color: #f8f9fa; padding: 10px; text-align: center; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Welcome to {{.Website.Name}}!</h1>
    </div>
    
    <div class="content">
        <p>Hi {{.User.FirstName}},</p>
        
        <p>Welcome to {{.Website.Name}}! We're excited to have you join our community.</p>
        
        <p>Here's what you can do next:</p>
        <ul>
            <li><a href="{{.Website.URL}}/profile">Complete your profile</a></li>
            <li><a href="{{.Website.URL}}/posts">Explore our latest posts</a></li>
            <li><a href="{{.Website.URL}}/settings">Customize your preferences</a></li>
        </ul>
        
        <p>If you have any questions, don't hesitate to reach out to us at {{.Website.Support}}.</p>
        
        <p>Best regards,<br>The {{.Website.Name}} Team</p>
    </div>
    
    <div class="footer">
        <p>&copy; {{.Website.Name}} | <a href="{{.Website.URL}}/unsubscribe?token={{.UnsubscribeToken}}">Unsubscribe</a></p>
    </div>
    
    <!-- Tracking pixel -->
    <img src="{{.TrackingURL}}" width="1" height="1" style="display:none;" />
</body>
</html>
```

### Password Reset Template
```html
<!DOCTYPE html>
<html>
<head>
    <title>{{.Website.Name}} - Password Reset</title>
</head>
<body>
    <h2>Password Reset Request</h2>
    
    <p>Hi {{.User.FirstName}},</p>
    
    <p>We received a request to reset your password for your {{.Website.Name}} account.</p>
    
    <p>Click the button below to reset your password:</p>
    
    <a href="{{.Custom.ResetURL}}" style="background-color: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a>
    
    <p>This link will expire in {{.Custom.ExpiresIn}} minutes.</p>
    
    <p>If you didn't request this password reset, please ignore this email.</p>
    
    <p>Best regards,<br>The {{.Website.Name}} Team</p>
</body>
</html>
```

### Email Verification Template
```html
<!DOCTYPE html>
<html>
<head>
    <title>{{.Website.Name}} - Email Verification</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            line-height: 1.6;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #4CAF50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 30px;
            background-color: #f9f9f9;
        }
        .button {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Email Verification</h1>
        </div>
        
        <div class="content">
            <p>Hi {{.User.FirstName}},</p>
            
            <p>Thank you for signing up for {{.Website.Name}}! To complete your registration, please verify your email address.</p>
            
            <p>Click the button below to verify your email:</p>
            
            <p style="text-align: center;">
                <a href="{{.Custom.VerificationURL}}" class="button">Verify Email Address</a>
            </p>
            
            <p>Or copy and paste this link into your browser:</p>
            <p style="word-break: break-all; background-color: #f0f0f0; padding: 10px; border-radius: 3px;">{{.Custom.VerificationURL}}</p>
            
            <p>This link will expire in {{.Custom.ExpiresIn}} hours.</p>
            
            <p>If you didn't create an account on {{.Website.Name}}, you can safely ignore this email.</p>
        </div>
        
        <div class="footer">
            <p>Best regards,<br>The {{.Website.Name}} Team</p>
            <p>&copy; {{.Website.Name}} | <a href="{{.Website.URL}}">Visit Website</a></p>
        </div>
    </div>
    
    <!-- Tracking pixel -->
    <img src="{{.TrackingURL}}" width="1" height="1" style="display:none;" />
</body>
</html>
```

### Marketing Newsletter Template
```html
<!DOCTYPE html>
<html>
<head>
    <title>{{.Website.Name}} - Newsletter</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
        }
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .article {
            border-bottom: 1px solid #eee;
            padding: 20px 0;
        }
        .article:last-child {
            border-bottom: none;
        }
        .article h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .read-more {
            background-color: #3498db;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 3px;
            display: inline-block;
            margin-top: 10px;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <h1>{{.Website.Name}} Newsletter</h1>
            <p>Your weekly digest of amazing content</p>
        </div>
        
        <div class="content">
            <p>Hi {{.User.FirstName}},</p>
            
            <p>Here are the latest updates from {{.Website.Name}}:</p>
            
            {{range .Custom.Articles}}
            <div class="article">
                <h3>{{.Title}}</h3>
                <p>{{.Excerpt}}</p>
                <a href="{{.URL}}" class="read-more">Read More</a>
            </div>
            {{end}}
            
            <p>Don't forget to follow us on social media for more updates!</p>
        </div>
        
        <div class="footer">
            <p>Thanks for reading!</p>
            <p>The {{.Website.Name}} Team</p>
            <p>
                <a href="{{.Website.URL}}">Visit Website</a> | 
                <a href="{{.Website.URL}}/unsubscribe?token={{.UnsubscribeToken}}">Unsubscribe</a>
            </p>
        </div>
    </div>
    
    <!-- Tracking pixel -->
    <img src="{{.TrackingURL}}" width="1" height="1" style="display:none;" />
</body>
</html>
```

## Template Processing

### Template Engine
```go
type TemplateEngine struct {
    templates map[string]*template.Template
    helpers   template.FuncMap
    mu        sync.RWMutex
}

func NewTemplateEngine() *TemplateEngine {
    engine := &TemplateEngine{
        templates: make(map[string]*template.Template),
        helpers:   make(template.FuncMap),
    }
    
    // Register built-in helpers
    engine.registerHelpers()
    
    return engine
}

func (e *TemplateEngine) registerHelpers() {
    e.helpers["formatDate"] = func(date time.Time, format string) string {
        return date.Format(format)
    }
    
    e.helpers["now"] = func() time.Time {
        return time.Now()
    }
    
    e.helpers["currentYear"] = func() int {
        return time.Now().Year()
    }
    
    e.helpers["truncate"] = func(text string, length int) string {
        if len(text) > length {
            return text[:length] + "..."
        }
        return text
    }
    
    e.helpers["default"] = func(value interface{}, defaultValue string) string {
        if value == nil {
            return defaultValue
        }
        return fmt.Sprintf("%v", value)
    }
    
    e.helpers["upper"] = func(text string) string {
        return strings.ToUpper(text)
    }
    
    e.helpers["lower"] = func(text string) string {
        return strings.ToLower(text)
    }
    
    e.helpers["title"] = func(text string) string {
        return strings.Title(text)
    }
}

func (e *TemplateEngine) CompileTemplate(name, content string) error {
    e.mu.Lock()
    defer e.mu.Unlock()
    
    tmpl, err := template.New(name).Funcs(e.helpers).Parse(content)
    if err != nil {
        return err
    }
    
    e.templates[name] = tmpl
    return nil
}

func (e *TemplateEngine) RenderTemplate(name string, data interface{}) (string, error) {
    e.mu.RLock()
    tmpl, exists := e.templates[name]
    e.mu.RUnlock()
    
    if !exists {
        return "", fmt.Errorf("template %s not found", name)
    }
    
    var buf bytes.Buffer
    if err := tmpl.Execute(&buf, data); err != nil {
        return "", err
    }
    
    return buf.String(), nil
}
```

### Template Validation
```go
type TemplateValidator struct {
    engine *TemplateEngine
}

func NewTemplateValidator() *TemplateValidator {
    return &TemplateValidator{
        engine: NewTemplateEngine(),
    }
}

func (v *TemplateValidator) ValidateTemplate(content string) error {
    // Try to parse the template
    _, err := template.New("validation").Funcs(v.engine.helpers).Parse(content)
    if err != nil {
        return fmt.Errorf("template syntax error: %v", err)
    }
    
    // Check for required variables
    if err := v.checkRequiredVariables(content); err != nil {
        return err
    }
    
    // Check for XSS vulnerabilities
    if err := v.checkXSS(content); err != nil {
        return err
    }
    
    return nil
}

func (v *TemplateValidator) checkRequiredVariables(content string) error {
    required := []string{".Website.Name", ".Website.URL"}
    
    for _, req := range required {
        if !strings.Contains(content, req) {
            return fmt.Errorf("required variable %s not found in template", req)
        }
    }
    
    return nil
}

func (v *TemplateValidator) checkXSS(content string) error {
    // Check for dangerous patterns
    dangerous := []string{
        "<script",
        "javascript:",
        "onload=",
        "onerror=",
        "onclick=",
    }
    
    lowerContent := strings.ToLower(content)
    for _, pattern := range dangerous {
        if strings.Contains(lowerContent, pattern) {
            return fmt.Errorf("potentially dangerous content detected: %s", pattern)
        }
    }
    
    return nil
}
```

## Template Variables

### Common Variables
```go
type CommonVariables struct {
    User    UserVariables    `json:"user"`
    Website WebsiteVariables `json:"website"`
    System  SystemVariables  `json:"system"`
}

type UserVariables struct {
    ID        uint   `json:"id"`
    Name      string `json:"name"`
    Email     string `json:"email"`
    FirstName string `json:"first_name"`
    LastName  string `json:"last_name"`
    Avatar    string `json:"avatar"`
    JoinedAt  string `json:"joined_at"`
}

type WebsiteVariables struct {
    Name        string `json:"name"`
    URL         string `json:"url"`
    Logo        string `json:"logo"`
    Description string `json:"description"`
    Support     string `json:"support_email"`
    Privacy     string `json:"privacy_url"`
    Terms       string `json:"terms_url"`
}

type SystemVariables struct {
    CurrentYear     int    `json:"current_year"`
    CurrentDate     string `json:"current_date"`
    UnsubscribeURL  string `json:"unsubscribe_url"`
    TrackingURL     string `json:"tracking_url"`
    UnsubscribeToken string `json:"unsubscribe_token"`
}
```

### Variable Injection
```go
func (s *EmailService) buildTemplateVariables(email *EmailQueue, user *User, website *Website) (*CommonVariables, error) {
    variables := &CommonVariables{
        User: UserVariables{
            ID:        user.ID,
            Name:      user.Name,
            Email:     user.Email,
            FirstName: user.FirstName,
            LastName:  user.LastName,
            Avatar:    user.Avatar,
            JoinedAt:  user.CreatedAt.Format("2006-01-02"),
        },
        Website: WebsiteVariables{
            Name:        website.Name,
            URL:         website.URL,
            Logo:        website.Logo,
            Description: website.Description,
            Support:     website.SupportEmail,
            Privacy:     website.PrivacyURL,
            Terms:       website.TermsURL,
        },
        System: SystemVariables{
            CurrentYear:     time.Now().Year(),
            CurrentDate:     time.Now().Format("2006-01-02"),
            UnsubscribeURL:  s.buildUnsubscribeURL(email),
            TrackingURL:     s.buildTrackingURL(email),
            UnsubscribeToken: s.generateUnsubscribeToken(email),
        },
    }
    
    return variables, nil
}

func (s *EmailService) buildUnsubscribeURL(email *EmailQueue) string {
    token := s.generateUnsubscribeToken(email)
    return fmt.Sprintf("%s/unsubscribe?token=%s", s.baseURL, token)
}

func (s *EmailService) buildTrackingURL(email *EmailQueue) string {
    trackingID := s.generateTrackingID(email)
    return fmt.Sprintf("%s/track/%s/open", s.baseURL, trackingID)
}
```

## Template Best Practices

### Email Design Guidelines
1. **Mobile-First**: Design for mobile devices first
2. **Inline CSS**: Use inline styles for better email client compatibility
3. **Fallback Fonts**: Use web-safe fonts with fallbacks
4. **Alt Text**: Always include alt text for images
5. **Preheader Text**: Include preheader text for better preview
6. **Unsubscribe Link**: Always include an unsubscribe link
7. **Tracking Pixel**: Include tracking pixel for open tracking

### Template Security
1. **XSS Prevention**: Escape all user input
2. **Content Validation**: Validate template content before saving
3. **Variable Sanitization**: Sanitize all template variables
4. **Permission Checks**: Ensure users can only access their templates
5. **Audit Trail**: Log all template changes
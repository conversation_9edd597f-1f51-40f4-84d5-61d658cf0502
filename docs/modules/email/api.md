# API Endpoints

## Template Management

### Create Email Template
```http
POST /api/cms/v1/email-templates
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "name": "Welcome Email",
  "subject": "Welcome to {{.Website.Name}}!",
  "html_content": "<html>...</html>",
  "text_content": "Welcome to {{.Website.Name}}!...",
  "category": "user_onboarding",
  "variables": {
    "user": ["name", "email"],
    "website": ["name", "url"],
    "custom": ["action_url"]
  }
}
```

**Response:**
```json
{
  "status": {
    "code": 201,
    "message": "Template created successfully",
    "success": true
  },
  "data": {
    "id": 1,
    "name": "Welcome Email",
    "subject": "Welcome to {{.Website.Name}}!",
    "category": "user_onboarding",
    "status": "active",
    "created_at": "2024-07-15T10:00:00Z"
  }
}
```

### Get Email Template
```http
GET /api/cms/v1/email-templates/{template_id}
Authorization: Bearer {admin_token}
```

### Update Email Template
```http
PUT /api/cms/v1/email-templates/{template_id}
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "name": "Updated Welcome Email",
  "subject": "Welcome to {{.Website.Name}}, {{.User.Name}}!",
  "html_content": "<html>...</html>",
  "status": "active"
}
```

### List Email Templates
```http
GET /api/cms/v1/email-templates?category=user_onboarding&status=active&limit=20
Authorization: Bearer {admin_token}
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Templates retrieved successfully",
    "success": true
  },
  "data": [
    {
      "id": 1,
      "name": "Welcome Email",
      "subject": "Welcome to {{.Website.Name}}!",
      "category": "user_onboarding",
      "status": "active",
      "created_at": "2024-07-15T10:00:00Z"
    }
  ],
  "meta": {
    "total": 1,
    "page": 1,
    "per_page": 20
  }
}
```

### Delete Email Template
```http
DELETE /api/cms/v1/email-templates/{template_id}
Authorization: Bearer {admin_token}
```

## Email Sending

### Send Template Email
```http
POST /api/cms/v1/emails/send-template
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "template_id": 1,
  "recipient_email": "<EMAIL>",
  "recipient_name": "John Doe",
  "variables": {
    "user": {
      "name": "John Doe",
      "email": "<EMAIL>"
    },
    "custom": {
      "action_url": "https://example.com/verify?token=abc123"
    }
  },
  "priority": "high",
  "scheduled_at": "2024-07-15T10:00:00Z"
}
```

**Response:**
```json
{
  "status": {
    "code": 202,
    "message": "Email queued for delivery",
    "success": true
  },
  "data": {
    "email_id": 123,
    "status": "queued",
    "scheduled_at": "2024-07-15T10:00:00Z"
  }
}
```

### Send Direct Email
```http
POST /api/cms/v1/emails/send-direct
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "recipient_email": "<EMAIL>",
  "recipient_name": "John Doe",
  "subject": "Important Update",
  "html_content": "<html>...</html>",
  "text_content": "Important update...",
  "priority": "normal"
}
```

### Send Bulk Emails
```http
POST /api/cms/v1/emails/send-bulk
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "template_id": 1,
  "recipients": [
    {
      "email": "<EMAIL>",
      "name": "User 1",
      "variables": {
        "user": {"name": "User 1"}
      }
    },
    {
      "email": "<EMAIL>",
      "name": "User 2",
      "variables": {
        "user": {"name": "User 2"}
      }
    }
  ],
  "priority": "normal"
}
```

## Email Queue Management

### List Email Queue
```http
GET /api/cms/v1/emails/queue?status=pending&priority=high&limit=50
Authorization: Bearer {admin_token}
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Queue retrieved successfully",
    "success": true
  },
  "data": [
    {
      "id": 123,
      "recipient_email": "<EMAIL>",
      "recipient_name": "John Doe",
      "subject": "Welcome Email",
      "status": "pending",
      "priority": "high",
      "retry_count": 0,
      "scheduled_at": "2024-07-15T10:00:00Z",
      "created_at": "2024-07-15T09:30:00Z"
    }
  ],
  "meta": {
    "total": 1,
    "page": 1,
    "per_page": 50
  }
}
```

### Get Email Status
```http
GET /api/cms/v1/emails/{email_id}/status
Authorization: Bearer {admin_token}
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Email status retrieved",
    "success": true
  },
  "data": {
    "id": 123,
    "status": "delivered",
    "sent_at": "2024-07-15T10:00:00Z",
    "delivery_info": {
      "provider": "sendgrid",
      "message_id": "abc123",
      "delivered_at": "2024-07-15T10:00:15Z"
    },
    "tracking": [
      {
        "event_type": "delivered",
        "timestamp": "2024-07-15T10:00:15Z"
      },
      {
        "event_type": "opened",
        "timestamp": "2024-07-15T10:05:00Z"
      }
    ]
  }
}
```

### Retry Failed Email
```http
POST /api/cms/v1/emails/{email_id}/retry
Authorization: Bearer {admin_token}
```

### Cancel Scheduled Email
```http
POST /api/cms/v1/emails/{email_id}/cancel
Authorization: Bearer {admin_token}
```

## Email Analytics

### Get Email Statistics
```http
GET /api/cms/v1/emails/stats?period=7d
Authorization: Bearer {admin_token}
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Statistics retrieved successfully",
    "success": true
  },
  "data": {
    "summary": {
      "total_sent": 15420,
      "total_delivered": 15000,
      "total_opened": 8500,
      "total_clicked": 2100,
      "total_bounced": 420,
      "total_unsubscribed": 50,
      "delivery_rate": 97.3,
      "open_rate": 56.7,
      "click_rate": 14.0,
      "bounce_rate": 2.7
    },
    "by_template": [
      {
        "template_id": 1,
        "template_name": "Welcome Email",
        "sent": 5000,
        "opened": 2850,
        "clicked": 750,
        "open_rate": 57.0,
        "click_rate": 15.0
      }
    ],
    "trends": [
      {
        "date": "2024-07-01",
        "sent": 2100,
        "delivered": 2050,
        "opened": 1200,
        "clicked": 250
      }
    ]
  }
}
```

### Get Template Analytics
```http
GET /api/cms/v1/email-templates/{template_id}/analytics?period=30d
Authorization: Bearer {admin_token}
```

### Get Campaign Analytics
```http
GET /api/cms/v1/email-campaigns/{campaign_id}/analytics
Authorization: Bearer {admin_token}
```

## Campaign Management

### Create Email Campaign
```http
POST /api/cms/v1/email-campaigns
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "name": "Product Launch Campaign",
  "template_id": 1,
  "subject": "Introducing Our New Product!",
  "target_criteria": {
    "user_status": "active",
    "has_subscription": true,
    "last_login": "30d"
  },
  "scheduled_at": "2024-07-16T09:00:00Z"
}
```

### List Email Campaigns
```http
GET /api/cms/v1/email-campaigns?status=sent&limit=20
Authorization: Bearer {admin_token}
```

### Send Campaign
```http
POST /api/cms/v1/email-campaigns/{campaign_id}/send
Authorization: Bearer {admin_token}
```

### Pause Campaign
```http
POST /api/cms/v1/email-campaigns/{campaign_id}/pause
Authorization: Bearer {admin_token}
```

### Resume Campaign
```http
POST /api/cms/v1/email-campaigns/{campaign_id}/resume
Authorization: Bearer {admin_token}
```

## Subscription Management

### Subscribe User
```http
POST /api/cms/v1/email-subscriptions
Content-Type: application/json

{
  "email": "<EMAIL>",
  "preferences": {
    "newsletter": true,
    "product_updates": true,
    "marketing": false
  }
}
```

**Response:**
```json
{
  "status": {
    "code": 201,
    "message": "Subscription created successfully",
    "success": true
  },
  "data": {
    "id": 456,
    "email": "<EMAIL>",
    "status": "active",
    "preferences": {
      "newsletter": true,
      "product_updates": true,
      "marketing": false
    },
    "unsubscribe_token": "unsub_abc123",
    "subscribed_at": "2024-07-15T10:00:00Z"
  }
}
```

### Get Subscription
```http
GET /api/cms/v1/email-subscriptions/{subscription_id}
Authorization: Bearer {admin_token}
```

### Update Subscription Preferences
```http
PUT /api/cms/v1/email-subscriptions/{subscription_id}
Content-Type: application/json

{
  "preferences": {
    "newsletter": false,
    "product_updates": true,
    "marketing": true
  }
}
```

### List Subscriptions
```http
GET /api/cms/v1/email-subscriptions?status=active&limit=50
Authorization: Bearer {admin_token}
```

### Unsubscribe
```http
POST /api/cms/v1/email-subscriptions/unsubscribe
Content-Type: application/json

{
  "token": "unsub_abc123"
}
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Successfully unsubscribed",
    "success": true
  },
  "data": {
    "email": "<EMAIL>",
    "unsubscribed_at": "2024-07-15T10:30:00Z"
  }
}
```

## Tracking Endpoints

### Track Email Open
```http
GET /track/{tracking_id}/open
```

Returns a 1x1 transparent GIF image.

### Track Email Click
```http
GET /track/{tracking_id}/click?url={original_url}
```

Redirects to the original URL after tracking the click.

### Get Email Tracking
```http
GET /api/cms/v1/emails/{email_id}/tracking
Authorization: Bearer {admin_token}
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Tracking data retrieved",
    "success": true
  },
  "data": [
    {
      "id": 789,
      "event_type": "delivered",
      "timestamp": "2024-07-15T10:00:15Z",
      "metadata": {
        "provider_response": "250 OK"
      }
    },
    {
      "id": 790,
      "event_type": "opened",
      "timestamp": "2024-07-15T10:05:00Z",
      "metadata": {
        "user_agent": "Mozilla/5.0...",
        "ip_address": "***********"
      }
    }
  ]
}
```

## Error Handling

### Standard Error Response
```json
{
  "status": {
    "code": 400,
    "message": "Validation failed",
    "success": false
  },
  "errors": [
    {
      "field": "recipient_email",
      "message": "Invalid email format",
      "code": "INVALID_EMAIL"
    },
    {
      "field": "template_id",
      "message": "Template not found",
      "code": "TEMPLATE_NOT_FOUND"
    }
  ]
}
```

### Common Error Codes
- `INVALID_EMAIL` - Invalid email address format
- `TEMPLATE_NOT_FOUND` - Email template not found
- `TEMPLATE_INACTIVE` - Template is not active
- `SUBSCRIPTION_NOT_FOUND` - Email subscription not found
- `INVALID_TOKEN` - Invalid unsubscribe token
- `RATE_LIMIT_EXCEEDED` - API rate limit exceeded
- `INSUFFICIENT_PERMISSIONS` - User lacks required permissions

## Rate Limiting

All API endpoints are subject to rate limiting:

- **Admin endpoints**: 1000 requests per hour
- **Email sending**: 500 emails per hour (per website)
- **Public endpoints**: 100 requests per hour (per IP)

Rate limit headers are included in all responses:
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1626345600
```

## Authentication

All CMS endpoints require authentication using Bearer tokens:

```http
Authorization: Bearer {jwt_token}
```

Public endpoints (tracking, unsubscribe) do not require authentication.

## Pagination

List endpoints support cursor-based pagination:

```http
GET /api/cms/v1/emails/queue?cursor=abc123&limit=50
```

Response includes pagination metadata:
```json
{
  "data": [...],
  "meta": {
    "next_cursor": "xyz789",
    "has_more": true,
    "total": 1500
  }
}
```
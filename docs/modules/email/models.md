# Email Models

## Database Schema

```mermaid
erDiagram
    EMAIL_TEMPLATE {
        uint id PK
        uint website_id FK
        string name
        string subject
        text html_content
        text text_content
        string category
        json variables
        string status
        uint created_by
        datetime created_at
        datetime updated_at
        string status
    }
    
    EMAIL_QUEUE {
        uint id PK
        uint website_id FK
        uint template_id FK
        string recipient_email
        string recipient_name
        string subject
        text html_content
        text text_content
        json variables
        string priority
        string status
        int retry_count
        datetime scheduled_at
        datetime sent_at
        datetime created_at
        datetime updated_at
    }
    
    EMAIL_TRACKING {
        uint id PK
        uint website_id FK
        uint email_id FK
        string tracking_id
        string event_type
        string user_agent
        string ip_address
        json metadata
        datetime created_at
    }
    
    EMAIL_SUBSCRIPTION {
        uint id PK
        uint website_id FK
        uint user_id FK
        string email
        string status
        json preferences
        string unsubscribe_token
        datetime subscribed_at
        datetime unsubscribed_at
        datetime created_at
        datetime updated_at
    }
    
    EMAIL_CAMPAIGN {
        uint id PK
        uint website_id FK
        uint template_id FK
        string name
        string subject
        text content
        string status
        json target_criteria
        datetime scheduled_at
        datetime sent_at
        uint created_by
        datetime created_at
        datetime updated_at
    }
    
    WEBSITE ||--o{ EMAIL_TEMPLATE : "has"
    WEBSITE ||--o{ EMAIL_QUEUE : "has"
    EMAIL_TEMPLATE ||--o{ EMAIL_QUEUE : "uses"
    EMAIL_QUEUE ||--o{ EMAIL_TRACKING : "tracks"
    WEBSITE ||--o{ EMAIL_SUBSCRIPTION : "has"
    WEBSITE ||--o{ EMAIL_CAMPAIGN : "has"
```

## Models

### Email Template Model

```go
type EmailTemplate struct {
    ID           uint           `gorm:"primarykey" json:"id"`
    WebsiteID    uint           `gorm:"not null;index" json:"website_id"`
    Name         string         `gorm:"size:100;not null" json:"name"`
    Subject      string         `gorm:"size:255;not null" json:"subject"`
    HTMLContent  string         `gorm:"type:text" json:"html_content"`
    TextContent  string         `gorm:"type:text" json:"text_content"`
    Category     string         `gorm:"size:50" json:"category"`
    Variables    datatypes.JSON `gorm:"type:json" json:"variables"`
    Status       string         `gorm:"size:20;default:'active'" json:"status"`
    CreatedBy    uint           `gorm:"not null" json:"created_by"`
    CreatedAt    time.Time      `json:"created_at"`
    UpdatedAt    time.Time      `json:"updated_at"`
    // Uses status field for soft deletes instead of DeletedAt
    
    // Relations
    Website      Website        `json:"website,omitempty"`
    Emails       []EmailQueue   `json:"emails,omitempty"`
}

// Status constants
const (
    TemplateStatusActive   = "active"
    TemplateStatusInactive = "inactive"
    TemplateStatusDraft    = "draft"
    TemplateStatusDeleted  = "deleted"
)

// Category constants
const (
    CategoryWelcome         = "welcome"
    CategoryPasswordReset   = "password_reset"
    CategoryEmailVerification = "email_verification"
    CategoryNotification    = "notification"
    CategoryMarketing       = "marketing"
    CategoryTransactional   = "transactional"
    CategorySystem          = "system"
)

// Template validation
func (t *EmailTemplate) Validate() error {
    if t.WebsiteID == 0 {
        return errors.New("website_id is required")
    }
    
    if t.Name == "" {
        return errors.New("name is required")
    }
    
    if t.Subject == "" {
        return errors.New("subject is required")
    }
    
    if t.HTMLContent == "" && t.TextContent == "" {
        return errors.New("either html_content or text_content is required")
    }
    
    validStatuses := []string{
        TemplateStatusActive,
        TemplateStatusInactive,
        TemplateStatusDraft,
        TemplateStatusDeleted,
    }
    
    if !contains(validStatuses, t.Status) {
        return errors.New("invalid status")
    }
    
    return nil
}
```

### Email Queue Model

```go
type EmailQueue struct {
    ID            uint           `gorm:"primarykey" json:"id"`
    WebsiteID     uint           `gorm:"not null;index" json:"website_id"`
    TemplateID    *uint          `gorm:"index" json:"template_id"`
    RecipientEmail string        `gorm:"size:255;not null" json:"recipient_email"`
    RecipientName  string        `gorm:"size:255" json:"recipient_name"`
    Subject       string         `gorm:"size:255;not null" json:"subject"`
    HTMLContent   string         `gorm:"type:text" json:"html_content"`
    TextContent   string         `gorm:"type:text" json:"text_content"`
    Variables     datatypes.JSON `gorm:"type:json" json:"variables"`
    Priority      string         `gorm:"size:20;default:'normal'" json:"priority"`
    Status        string         `gorm:"size:20;default:'pending'" json:"status"`
    RetryCount    int            `gorm:"default:0" json:"retry_count"`
    ScheduledAt   *time.Time     `json:"scheduled_at"`
    SentAt        *time.Time     `json:"sent_at"`
    CreatedAt     time.Time      `json:"created_at"`
    UpdatedAt     time.Time      `json:"updated_at"`
    
    // Relations
    Website       Website        `json:"website,omitempty"`
    Template      *EmailTemplate `json:"template,omitempty"`
    Tracking      []EmailTracking `json:"tracking,omitempty"`
}

// Priority constants
const (
    PriorityLow    = "low"
    PriorityNormal = "normal"
    PriorityHigh   = "high"
    PriorityUrgent = "urgent"
)

// Status constants
const (
    StatusPending   = "pending"
    StatusQueued    = "queued"
    StatusSent      = "sent"
    StatusDelivered = "delivered"
    StatusFailed    = "failed"
    StatusBounced   = "bounced"
    StatusRetrying  = "retrying"
)

// Email validation
func (e *EmailQueue) Validate() error {
    if e.WebsiteID == 0 {
        return errors.New("website_id is required")
    }
    
    if e.RecipientEmail == "" {
        return errors.New("recipient_email is required")
    }
    
    if !regexp.MustCompile(`^[^\s@]+@[^\s@]+\.[^\s@]+$`).MatchString(e.RecipientEmail) {
        return errors.New("invalid recipient_email format")
    }
    
    if e.Subject == "" {
        return errors.New("subject is required")
    }
    
    if e.HTMLContent == "" && e.TextContent == "" {
        return errors.New("either html_content or text_content is required")
    }
    
    validPriorities := []string{PriorityLow, PriorityNormal, PriorityHigh, PriorityUrgent}
    if !contains(validPriorities, e.Priority) {
        return errors.New("invalid priority")
    }
    
    validStatuses := []string{
        StatusPending, StatusQueued, StatusSent, 
        StatusDelivered, StatusFailed, StatusBounced, StatusRetrying,
    }
    if !contains(validStatuses, e.Status) {
        return errors.New("invalid status")
    }
    
    return nil
}
```

### Email Tracking Model

```go
type EmailTracking struct {
    ID          uint           `gorm:"primarykey" json:"id"`
    WebsiteID   uint           `gorm:"not null;index" json:"website_id"`
    EmailID     uint           `gorm:"not null;index" json:"email_id"`
    TrackingID  string         `gorm:"size:255;uniqueIndex" json:"tracking_id"`
    EventType   string         `gorm:"size:50;not null" json:"event_type"`
    UserAgent   string         `gorm:"size:500" json:"user_agent"`
    IPAddress   string         `gorm:"size:45" json:"ip_address"`
    Metadata    datatypes.JSON `gorm:"type:json" json:"metadata"`
    CreatedAt   time.Time      `json:"created_at"`
    
    // Relations
    Website     Website        `json:"website,omitempty"`
    Email       EmailQueue     `json:"email,omitempty"`
}

// Event type constants
const (
    EventSent         = "sent"
    EventDelivered    = "delivered"
    EventBounced      = "bounced"
    EventOpened       = "opened"
    EventClicked      = "clicked"
    EventUnsubscribed = "unsubscribed"
    EventComplaint    = "complaint"
    EventDropped      = "dropped"
)

// Tracking validation
func (t *EmailTracking) Validate() error {
    if t.WebsiteID == 0 {
        return errors.New("website_id is required")
    }
    
    if t.EmailID == 0 {
        return errors.New("email_id is required")
    }
    
    if t.TrackingID == "" {
        return errors.New("tracking_id is required")
    }
    
    if t.EventType == "" {
        return errors.New("event_type is required")
    }
    
    validEvents := []string{
        EventSent, EventDelivered, EventBounced, EventOpened,
        EventClicked, EventUnsubscribed, EventComplaint, EventDropped,
    }
    
    if !contains(validEvents, t.EventType) {
        return errors.New("invalid event_type")
    }
    
    return nil
}
```

### Email Subscription Model

```go
type EmailSubscription struct {
    ID               uint           `gorm:"primarykey" json:"id"`
    WebsiteID        uint           `gorm:"not null;index" json:"website_id"`
    UserID           *uint          `gorm:"index" json:"user_id"`
    Email            string         `gorm:"size:255;not null" json:"email"`
    Status           string         `gorm:"size:20;default:'active'" json:"status"`
    Preferences      datatypes.JSON `gorm:"type:json" json:"preferences"`
    UnsubscribeToken string         `gorm:"size:255;uniqueIndex" json:"unsubscribe_token"`
    SubscribedAt     *time.Time     `json:"subscribed_at"`
    UnsubscribedAt   *time.Time     `json:"unsubscribed_at"`
    CreatedAt        time.Time      `json:"created_at"`
    UpdatedAt        time.Time      `json:"updated_at"`
    
    // Relations
    Website          Website        `json:"website,omitempty"`
    User             *User          `json:"user,omitempty"`
}

// Subscription status constants
const (
    SubscriptionStatusActive       = "active"
    SubscriptionStatusUnsubscribed = "unsubscribed"
    SubscriptionStatusBounced      = "bounced"
    SubscriptionStatusComplaint    = "complaint"
    SubscriptionStatusPending      = "pending"
)

// Subscription validation
func (s *EmailSubscription) Validate() error {
    if s.WebsiteID == 0 {
        return errors.New("website_id is required")
    }
    
    if s.Email == "" {
        return errors.New("email is required")
    }
    
    if !regexp.MustCompile(`^[^\s@]+@[^\s@]+\.[^\s@]+$`).MatchString(s.Email) {
        return errors.New("invalid email format")
    }
    
    validStatuses := []string{
        SubscriptionStatusActive, SubscriptionStatusUnsubscribed,
        SubscriptionStatusBounced, SubscriptionStatusComplaint,
        SubscriptionStatusPending,
    }
    
    if !contains(validStatuses, s.Status) {
        return errors.New("invalid status")
    }
    
    return nil
}
```

### Email Campaign Model

```go
type EmailCampaign struct {
    ID              uint           `gorm:"primarykey" json:"id"`
    WebsiteID       uint           `gorm:"not null;index" json:"website_id"`
    TemplateID      *uint          `gorm:"index" json:"template_id"`
    Name            string         `gorm:"size:100;not null" json:"name"`
    Subject         string         `gorm:"size:255;not null" json:"subject"`
    Content         string         `gorm:"type:text" json:"content"`
    Status          string         `gorm:"size:20;default:'draft'" json:"status"`
    TargetCriteria  datatypes.JSON `gorm:"type:json" json:"target_criteria"`
    ScheduledAt     *time.Time     `json:"scheduled_at"`
    SentAt          *time.Time     `json:"sent_at"`
    CreatedBy       uint           `gorm:"not null" json:"created_by"`
    CreatedAt       time.Time      `json:"created_at"`
    UpdatedAt       time.Time      `json:"updated_at"`
    
    // Relations
    Website         Website        `json:"website,omitempty"`
    Template        *EmailTemplate `json:"template,omitempty"`
    CreatedByUser   User           `json:"created_by_user,omitempty"`
}

// Campaign status constants
const (
    CampaignStatusDraft     = "draft"
    CampaignStatusScheduled = "scheduled"
    CampaignStatusSending   = "sending"
    CampaignStatusSent      = "sent"
    CampaignStatusPaused    = "paused"
    CampaignStatusCancelled = "cancelled"
)

// Campaign validation
func (c *EmailCampaign) Validate() error {
    if c.WebsiteID == 0 {
        return errors.New("website_id is required")
    }
    
    if c.Name == "" {
        return errors.New("name is required")
    }
    
    if c.Subject == "" {
        return errors.New("subject is required")
    }
    
    if c.CreatedBy == 0 {
        return errors.New("created_by is required")
    }
    
    validStatuses := []string{
        CampaignStatusDraft, CampaignStatusScheduled, CampaignStatusSending,
        CampaignStatusSent, CampaignStatusPaused, CampaignStatusCancelled,
    }
    
    if !contains(validStatuses, c.Status) {
        return errors.New("invalid status")
    }
    
    return nil
}
```

## Repository Interfaces

### Email Template Repository

```go
type EmailTemplateRepository interface {
    Create(template *EmailTemplate) error
    GetByID(websiteID, id uint) (*EmailTemplate, error)
    GetByName(websiteID uint, name string) (*EmailTemplate, error)
    List(websiteID uint, filters *TemplateFilters) ([]EmailTemplate, error)
    Update(template *EmailTemplate) error
    Delete(websiteID, id uint) error
    GetActiveTemplates(websiteID uint) ([]EmailTemplate, error)
}

type TemplateFilters struct {
    Category string
    Status   string
    Search   string
    Limit    int
    Offset   int
}
```

### Email Queue Repository

```go
type EmailQueueRepository interface {
    Create(email *EmailQueue) error
    GetByID(websiteID, id uint) (*EmailQueue, error)
    List(websiteID uint, filters *EmailFilters) ([]EmailQueue, error)
    Update(email *EmailQueue) error
    Delete(websiteID, id uint) error
    GetPendingEmails(websiteID uint, limit int) ([]EmailQueue, error)
    GetByStatus(websiteID uint, status string) ([]EmailQueue, error)
    UpdateStatus(websiteID, id uint, status string) error
}

type EmailFilters struct {
    Status      string
    Priority    string
    TemplateID  *uint
    DateFrom    *time.Time
    DateTo      *time.Time
    Search      string
    Limit       int
    Offset      int
}
```

### Email Tracking Repository

```go
type EmailTrackingRepository interface {
    Create(tracking *EmailTracking) error
    GetByTrackingID(trackingID string) (*EmailTracking, error)
    GetByEmailID(websiteID, emailID uint) ([]EmailTracking, error)
    List(websiteID uint, filters *TrackingFilters) ([]EmailTracking, error)
    GetStats(websiteID uint, filters *StatsFilters) (*EmailStats, error)
}

type TrackingFilters struct {
    EmailID   *uint
    EventType string
    DateFrom  *time.Time
    DateTo    *time.Time
    Limit     int
    Offset    int
}

type StatsFilters struct {
    DateFrom *time.Time
    DateTo   *time.Time
    GroupBy  string // day, week, month
}
```

## Database Migration

### Migration SQL
```sql
-- Create email_templates table
CREATE TABLE email_templates (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL,
    name VARCHAR(100) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    html_content TEXT,
    text_content TEXT,
    category VARCHAR(50),
    variables JSON DEFAULT (JSON_OBJECT()),
    status ENUM('active', 'inactive', 'draft', 'deleted') NOT NULL DEFAULT 'active',
    created_by INT UNSIGNED NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_website_status (website_id, status),
    INDEX idx_website_category (website_id, category),
    INDEX idx_created_by (created_by),
    
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
);

-- Create email_queue table
CREATE TABLE email_queue (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL,
    template_id INT UNSIGNED,
    recipient_email VARCHAR(255) NOT NULL,
    recipient_name VARCHAR(255),
    subject VARCHAR(255) NOT NULL,
    html_content TEXT,
    text_content TEXT,
    variables JSON DEFAULT (JSON_OBJECT()),
    priority ENUM('low', 'normal', 'high', 'urgent') NOT NULL DEFAULT 'normal',
    status ENUM('pending', 'queued', 'sent', 'delivered', 'failed', 'bounced', 'retrying') NOT NULL DEFAULT 'pending',
    retry_count INT NOT NULL DEFAULT 0,
    scheduled_at TIMESTAMP NULL,
    sent_at TIMESTAMP NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_website_status (website_id, status),
    INDEX idx_website_priority (website_id, priority),
    INDEX idx_recipient_email (recipient_email),
    INDEX idx_scheduled_at (scheduled_at),
    INDEX idx_template_id (template_id),
    
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES email_templates(id) ON DELETE SET NULL
);

-- Create email_tracking table
CREATE TABLE email_tracking (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL,
    email_id INT UNSIGNED NOT NULL,
    tracking_id VARCHAR(255) NOT NULL,
    event_type ENUM('sent', 'delivered', 'bounced', 'opened', 'clicked', 'unsubscribed', 'complaint', 'dropped') NOT NULL,
    user_agent VARCHAR(500),
    ip_address VARCHAR(45),
    metadata JSON DEFAULT (JSON_OBJECT()),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY idx_tracking_id (tracking_id),
    INDEX idx_website_email (website_id, email_id),
    INDEX idx_event_type (event_type),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    FOREIGN KEY (email_id) REFERENCES email_queue(id) ON DELETE CASCADE
);

-- Create email_subscriptions table
CREATE TABLE email_subscriptions (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED,
    email VARCHAR(255) NOT NULL,
    status ENUM('active', 'unsubscribed', 'bounced', 'complaint', 'pending') NOT NULL DEFAULT 'active',
    preferences JSON DEFAULT (JSON_OBJECT()),
    unsubscribe_token VARCHAR(255) NOT NULL,
    subscribed_at TIMESTAMP NULL,
    unsubscribed_at TIMESTAMP NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY idx_unsubscribe_token (unsubscribe_token),
    INDEX idx_website_email (website_id, email),
    INDEX idx_website_status (website_id, status),
    INDEX idx_user_id (user_id),
    
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Create email_campaigns table
CREATE TABLE email_campaigns (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL,
    template_id INT UNSIGNED,
    name VARCHAR(100) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    content TEXT,
    status ENUM('draft', 'scheduled', 'sending', 'sent', 'paused', 'cancelled') NOT NULL DEFAULT 'draft',
    target_criteria JSON DEFAULT (JSON_OBJECT()),
    scheduled_at TIMESTAMP NULL,
    sent_at TIMESTAMP NULL,
    created_by INT UNSIGNED NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_website_status (website_id, status),
    INDEX idx_created_by (created_by),
    INDEX idx_scheduled_at (scheduled_at),
    INDEX idx_template_id (template_id),
    
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES email_templates(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
);
```

### Helper Functions

```go
// Helper function to check if a string is in a slice
func contains(slice []string, item string) bool {
    for _, s := range slice {
        if s == item {
            return true
        }
    }
    return false
}

// Generate unique tracking ID
func GenerateTrackingID() string {
    return fmt.Sprintf("track_%d_%s", time.Now().UnixNano(), randomString(8))
}

// Generate unique unsubscribe token
func GenerateUnsubscribeToken() string {
    return fmt.Sprintf("unsub_%d_%s", time.Now().UnixNano(), randomString(16))
}

// Generate random string
func randomString(length int) string {
    const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    b := make([]byte, length)
    for i := range b {
        b[i] = charset[rand.Intn(len(charset))]
    }
    return string(b)
}
```
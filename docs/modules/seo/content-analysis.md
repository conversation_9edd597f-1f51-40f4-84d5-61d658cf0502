# SEO Content Analysis

## Overview

SEO Content Analysis module cung cấp các tính năng phân tích nội dung để tối ưu hóa SEO, bao gồm keyword analysis, readability scoring, content quality assessment, và competitor analysis.

## Content Analysis Engine

### SEO Content Scoring

```mermaid
flowchart TD
    A[Content Input] --> B[Text Analysis]
    A --> C[Structure Analysis]
    A --> D[Meta Analysis]
    
    B --> E[Keyword Density]
    B --> F[Readability Score]
    B --> G[Word Count]
    
    C --> H[Heading Structure]
    C --> I[Internal Links]
    C --> J[Image Alt Tags]
    
    D --> K[Title Optimization]
    D --> L[Description Quality]
    
    E --> M[SEO Score]
    F --> M
    G --> M
    H --> M
    I --> M
    J --> M
    K --> M
    L --> M
```

### Analysis Metrics

#### Keyword Optimization
- **Keyword Density**: Optimal density (1-3%) for target keywords
- **Keyword Placement**: Strategic placement in title, headings, content
- **Keyword Variations**: Use of synonyms and related terms
- **Long-tail Keywords**: Inclusion of specific long-tail phrases

#### Content Quality
- **Word Count**: Optimal length based on content type
- **Readability**: Flesch-Kincaid readability score
- **Sentence Structure**: Average sentence length and complexity
- **Paragraph Structure**: Optimal paragraph length and organization

#### Technical SEO
- **Title Tag**: Length, keyword inclusion, uniqueness
- **Meta Description**: Length, call-to-action, keyword inclusion
- **Heading Structure**: Proper H1-H6 hierarchy
- **Internal Linking**: Relevant internal links
- **Image Optimization**: Alt text, file names, compression

## Readability Analysis

### Flesch-Kincaid Score

```go
type ReadabilityAnalysis struct {
    FleschScore          float64 `json:"flesch_score"`
    FleschKincaidGrade   float64 `json:"flesch_kincaid_grade"`
    ReadingLevel         string  `json:"reading_level"`
    AverageSentenceLength float64 `json:"average_sentence_length"`
    AverageWordsPerSentence float64 `json:"average_words_per_sentence"`
    AverageSyllablesPerWord float64 `json:"average_syllables_per_word"`
    
    // Recommendations
    ComplexSentences     int     `json:"complex_sentences"`
    LongWords           int     `json:"long_words"`
    PassiveSentences    int     `json:"passive_sentences"`
    
    // Improvements
    Suggestions         []string `json:"suggestions"`
}
```

### Reading Level Classification

```go
func ClassifyReadingLevel(fleschScore float64) string {
    switch {
    case fleschScore >= 90:
        return "Very Easy"
    case fleschScore >= 80:
        return "Easy"
    case fleschScore >= 70:
        return "Fairly Easy"
    case fleschScore >= 60:
        return "Standard"
    case fleschScore >= 50:
        return "Fairly Difficult"
    case fleschScore >= 30:
        return "Difficult"
    default:
        return "Very Difficult"
    }
}
```

## Keyword Analysis

### Keyword Density Calculation

```go
type KeywordAnalysis struct {
    PrimaryKeyword      KeywordMetrics   `json:"primary_keyword"`
    SecondaryKeywords   []KeywordMetrics `json:"secondary_keywords"`
    LongTailKeywords    []KeywordMetrics `json:"long_tail_keywords"`
    TotalWords          int              `json:"total_words"`
    KeywordVariations   []string         `json:"keyword_variations"`
    
    // Recommendations
    OverOptimizedKeywords []string `json:"over_optimized_keywords"`
    UnderOptimizedKeywords []string `json:"under_optimized_keywords"`
    MissingKeywords       []string `json:"missing_keywords"`
}

type KeywordMetrics struct {
    Keyword             string    `json:"keyword"`
    Density             float64   `json:"density"`
    Count               int       `json:"count"`
    Positions           []int     `json:"positions"`
    InTitle             bool      `json:"in_title"`
    InDescription       bool      `json:"in_description"`
    InHeadings          []string  `json:"in_headings"`
    InImageAlt          bool      `json:"in_image_alt"`
    ProminenceScore     float64   `json:"prominence_score"`
}
```

### Keyword Density Rules

```yaml
keyword_density_rules:
  primary_keyword:
    min_density: 0.5
    max_density: 3.0
    optimal_density: 1.5
    
  secondary_keywords:
    min_density: 0.3
    max_density: 2.0
    optimal_density: 1.0
    
  long_tail_keywords:
    min_density: 0.1
    max_density: 1.0
    optimal_density: 0.5
    
  placement_bonus:
    title: 2.0
    first_paragraph: 1.5
    headings: 1.3
    last_paragraph: 1.2
    image_alt: 1.1
```

## Content Structure Analysis

### Heading Structure Analysis

```go
type HeadingStructure struct {
    H1Count             int       `json:"h1_count"`
    H2Count             int       `json:"h2_count"`
    H3Count             int       `json:"h3_count"`
    H4Count             int       `json:"h4_count"`
    H5Count             int       `json:"h5_count"`
    H6Count             int       `json:"h6_count"`
    
    // Structure Quality
    HasH1               bool      `json:"has_h1"`
    MultipleH1          bool      `json:"multiple_h1"`
    ProperHierarchy     bool      `json:"proper_hierarchy"`
    KeywordInHeadings   []string  `json:"keyword_in_headings"`
    
    // Issues
    MissingHeadings     []string  `json:"missing_headings"`
    EmptyHeadings       []string  `json:"empty_headings"`
    TooLongHeadings     []string  `json:"too_long_headings"`
    
    // Recommendations
    Suggestions         []string  `json:"suggestions"`
}
```

### Internal Link Analysis

```go
type InternalLinkAnalysis struct {
    TotalInternalLinks  int       `json:"total_internal_links"`
    UniqueInternalLinks int       `json:"unique_internal_links"`
    ExternalLinks       int       `json:"external_links"`
    
    // Link Quality
    AnchorTextAnalysis  []AnchorText `json:"anchor_text_analysis"`
    BrokenLinks         []string     `json:"broken_links"`
    NoFollowLinks       []string     `json:"no_follow_links"`
    
    // Recommendations
    RecommendedLinks    []LinkRecommendation `json:"recommended_links"`
    LinkOpportunities   []string            `json:"link_opportunities"`
}

type AnchorText struct {
    Text                string  `json:"text"`
    URL                 string  `json:"url"`
    Type                string  `json:"type"` // exact, partial, branded, generic
    KeywordRelevance    float64 `json:"keyword_relevance"`
}

type LinkRecommendation struct {
    TargetURL           string  `json:"target_url"`
    SuggestedAnchorText string  `json:"suggested_anchor_text"`
    Relevance           float64 `json:"relevance"`
    Reason              string  `json:"reason"`
}
```

## Image SEO Analysis

### Image Optimization Analysis

```go
type ImageSEOAnalysis struct {
    TotalImages         int       `json:"total_images"`
    ImagesWithAlt       int       `json:"images_with_alt"`
    ImagesWithoutAlt    int       `json:"images_without_alt"`
    
    // Alt Text Quality
    AltTextAnalysis     []ImageAltText `json:"alt_text_analysis"`
    
    // File Optimization
    LargeImages         []ImageFile    `json:"large_images"`
    UnoptimizedImages   []ImageFile    `json:"unoptimized_images"`
    
    // Recommendations
    Suggestions         []string       `json:"suggestions"`
}

type ImageAltText struct {
    ImageURL            string  `json:"image_url"`
    AltText             string  `json:"alt_text"`
    HasKeywords         bool    `json:"has_keywords"`
    Length              int     `json:"length"`
    Quality             string  `json:"quality"` // excellent, good, fair, poor
    Suggestions         []string `json:"suggestions"`
}

type ImageFile struct {
    URL                 string  `json:"url"`
    Size                int64   `json:"size"`
    Format              string  `json:"format"`
    Dimensions          string  `json:"dimensions"`
    OptimizationSuggestion string `json:"optimization_suggestion"`
}
```

## Content Quality Scoring

### Quality Assessment Framework

```go
type ContentQualityScore struct {
    OverallScore        float64 `json:"overall_score"`
    
    // Component Scores
    ContentScore        float64 `json:"content_score"`
    TechnicalScore      float64 `json:"technical_score"`
    ReadabilityScore    float64 `json:"readability_score"`
    KeywordScore        float64 `json:"keyword_score"`
    StructureScore      float64 `json:"structure_score"`
    
    // Quality Metrics
    WordCount           int     `json:"word_count"`
    UniqueWords         int     `json:"unique_words"`
    SentenceCount       int     `json:"sentence_count"`
    ParagraphCount      int     `json:"paragraph_count"`
    ReadingTime         int     `json:"reading_time"` // minutes
    
    // Issues by Severity
    CriticalIssues      []QualityIssue `json:"critical_issues"`
    WarningIssues       []QualityIssue `json:"warning_issues"`
    InfoIssues          []QualityIssue `json:"info_issues"`
    
    // Recommendations
    Recommendations     []string `json:"recommendations"`
}

type QualityIssue struct {
    Code                string  `json:"code"`
    Message             string  `json:"message"`
    Severity            string  `json:"severity"`
    Element             string  `json:"element"`
    Position            int     `json:"position"`
    Impact              string  `json:"impact"`
    Solution            string  `json:"solution"`
}
```

### Scoring Algorithm

```go
func CalculateContentQualityScore(analysis *ContentAnalysis) float64 {
    scores := map[string]float64{
        "content":     calculateContentScore(analysis),
        "technical":   calculateTechnicalScore(analysis),
        "readability": calculateReadabilityScore(analysis),
        "keywords":    calculateKeywordScore(analysis),
        "structure":   calculateStructureScore(analysis),
    }
    
    // Weighted average
    weights := map[string]float64{
        "content":     0.25,
        "technical":   0.20,
        "readability": 0.20,
        "keywords":    0.20,
        "structure":   0.15,
    }
    
    var totalScore float64
    for component, score := range scores {
        totalScore += score * weights[component]
    }
    
    return math.Round(totalScore*100) / 100
}
```

## Competitor Analysis

### Competitor Content Analysis

```go
type CompetitorAnalysis struct {
    CompetitorURL       string              `json:"competitor_url"`
    CompetitorTitle     string              `json:"competitor_title"`
    CompetitorKeywords  []string            `json:"competitor_keywords"`
    
    // Content Comparison
    WordCountComparison ContentComparison   `json:"word_count_comparison"`
    ReadabilityComparison ContentComparison `json:"readability_comparison"`
    KeywordComparison   KeywordComparison   `json:"keyword_comparison"`
    
    // Technical Comparison
    TechnicalComparison TechnicalComparison `json:"technical_comparison"`
    
    // Recommendations
    ContentGaps         []string            `json:"content_gaps"`
    Opportunities       []string            `json:"opportunities"`
    Improvements        []string            `json:"improvements"`
}

type ContentComparison struct {
    YourContent         float64 `json:"your_content"`
    CompetitorContent   float64 `json:"competitor_content"`
    Difference          float64 `json:"difference"`
    Status              string  `json:"status"` // better, worse, similar
}

type KeywordComparison struct {
    SharedKeywords      []string `json:"shared_keywords"`
    YourUniqueKeywords  []string `json:"your_unique_keywords"`
    CompetitorUniqueKeywords []string `json:"competitor_unique_keywords"`
    MissingKeywords     []string `json:"missing_keywords"`
}
```

## SEO Analysis Rules Engine

### Content Quality Rules

```yaml
content_quality_rules:
  word_count:
    min_words: 300
    optimal_words: 1500
    max_words: 5000
    
  readability:
    target_flesch_score: 60
    max_sentence_length: 25
    max_paragraph_length: 150
    
  keyword_optimization:
    primary_keyword_density: 1.5
    secondary_keyword_density: 1.0
    keyword_stuffing_threshold: 4.0
    
  structure:
    required_h1: 1
    max_h1: 1
    min_h2: 2
    max_heading_length: 70
    
  links:
    min_internal_links: 2
    max_external_links: 10
    anchor_text_variation: 0.8
    
  images:
    alt_text_required: true
    max_image_size: 1048576 # 1MB
    recommended_formats: [jpg, png, webp]
```

### Technical SEO Rules

```yaml
technical_seo_rules:
  title_tag:
    min_length: 30
    max_length: 60
    required_keywords: true
    avoid_duplicates: true
    
  meta_description:
    min_length: 120
    max_length: 160
    unique_per_page: true
    call_to_action: recommended
    
  url_structure:
    max_length: 75
    lowercase: true
    no_special_chars: true
    include_keywords: true
    
  schema_markup:
    article_schema: required
    organization_schema: recommended
    breadcrumb_schema: recommended
```

## Analysis Automation

### Auto-Analysis Triggers

```mermaid
flowchart TD
    A[Content Created/Updated] --> B{Auto-Analysis Enabled?}
    B -->|Yes| C[Queue Analysis Job]
    B -->|No| D[Manual Analysis Only]
    
    C --> E[Perform Analysis]
    E --> F[Generate Report]
    F --> G[Send Notifications]
    
    G --> H{Score Below Threshold?}
    H -->|Yes| I[Send Improvement Suggestions]
    H -->|No| J[Store Results]
    
    I --> J
    J --> K[Update Cache]
```

### Batch Analysis

```go
type BatchAnalysisJob struct {
    ID              string    `json:"id"`
    WebsiteID       uint      `json:"website_id"`
    ContentType     string    `json:"content_type"`
    Filters         map[string]interface{} `json:"filters"`
    Status          string    `json:"status"`
    Progress        int       `json:"progress"`
    TotalItems      int       `json:"total_items"`
    ProcessedItems  int       `json:"processed_items"`
    CreatedAt       time.Time `json:"created_at"`
    StartedAt       *time.Time `json:"started_at"`
    CompletedAt     *time.Time `json:"completed_at"`
    Results         []AnalysisResult `json:"results"`
}

type AnalysisResult struct {
    ContentID       uint    `json:"content_id"`
    Score           float64 `json:"score"`
    Issues          int     `json:"issues"`
    Status          string  `json:"status"`
    ProcessedAt     time.Time `json:"processed_at"`
}
```

## Performance Optimization

### Caching Strategy

```go
type AnalysisCache struct {
    ContentHash     string    `json:"content_hash"`
    AnalysisResult  []byte    `json:"analysis_result"`
    TTL             int       `json:"ttl"`
    CreatedAt       time.Time `json:"created_at"`
}

func (c *ContentAnalysisService) GetCachedAnalysis(contentID uint, contentHash string) (*ContentQualityScore, bool) {
    cacheKey := fmt.Sprintf("analysis:%d:%s", contentID, contentHash)
    
    if cached, found := c.cache.Get(cacheKey); found {
        var result ContentQualityScore
        if err := json.Unmarshal(cached.([]byte), &result); err == nil {
            return &result, true
        }
    }
    
    return nil, false
}
```

### Async Processing

```go
type AnalysisQueue struct {
    queue chan AnalysisJob
    workers int
}

func (q *AnalysisQueue) ProcessAnalysis(job AnalysisJob) {
    go func() {
        defer func() {
            if r := recover(); r != nil {
                log.Printf("Analysis job failed: %v", r)
            }
        }()
        
        result := q.analyzer.AnalyzeContent(job.ContentID, job.Options)
        q.storeResult(job.ContentID, result)
        
        if job.NotifyOnComplete {
            q.notificationService.SendAnalysisComplete(job.UserID, result)
        }
    }()
}
```
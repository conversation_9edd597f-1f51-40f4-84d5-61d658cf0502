# SEO API Endpoints

## Meta Management

### Get Content Meta
```http
GET /api/cms/v1/seo/meta/{content_type}/{content_id}
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 123,
    "website_id": 1,
    "content_type": "post",
    "content_id": 456,
    "meta_title": "Complete Guide to Go Programming",
    "meta_description": "Learn Go programming from basics to advanced concepts with practical examples and best practices.",
    "meta_keywords": "go programming, golang, web development",
    "canonical_url": "https://blog.example.com/go-programming-guide",
    "og_title": "Complete Guide to Go Programming",
    "og_description": "Learn Go programming from basics to advanced concepts with practical examples and best practices.",
    "og_image": "https://cdn.example.com/og-image.jpg",
    "og_type": "article",
    "twitter_card": "summary_large_image",
    "no_index": false,
    "no_follow": false,
    "structured_data": {
      "@context": "https://schema.org",
      "@type": "Article",
      "headline": "Complete Guide to Go Programming"
    },
    "status": "active",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### Update Content Meta
```http
PUT /api/cms/v1/seo/meta/{content_type}/{content_id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "meta_title": "Updated Title for SEO",
  "meta_description": "Updated description under 160 characters",
  "meta_keywords": "updated, keywords, seo",
  "canonical_url": "https://blog.example.com/updated-url",
  "og_title": "Updated Social Media Title",
  "og_description": "Updated social media description",
  "og_image": "https://cdn.example.com/new-og-image.jpg",
  "twitter_card": "summary_large_image",
  "no_index": false,
  "no_follow": false,
  "structured_data": {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": "Updated Article Title"
  }
}
```

### Create Content Meta
```http
POST /api/cms/v1/seo/meta
Authorization: Bearer {token}
Content-Type: application/json

{
  "content_type": "post",
  "content_id": 789,
  "meta_title": "New Content Title",
  "meta_description": "New content description",
  "meta_keywords": "new, content, seo"
}
```

### Delete Content Meta
```http
DELETE /api/cms/v1/seo/meta/{content_type}/{content_id}
Authorization: Bearer {token}
```

## URL Redirects Management

### List Redirects
```http
GET /api/cms/v1/seo/redirects?cursor=abc123&limit=20
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "website_id": 1,
      "from_url": "/old-url",
      "to_url": "/new-url",
      "status_code": 301,
      "type": "manual",
      "hit_count": 150,
      "last_hit": "2024-01-01T12:00:00Z",
      "status": "active",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "cursor": "def456",
    "has_more": true,
    "limit": 20
  }
}
```

### Create Redirect
```http
POST /api/cms/v1/seo/redirects
Authorization: Bearer {token}
Content-Type: application/json

{
  "from_url": "/old-blog-post",
  "to_url": "/new-blog-post",
  "status_code": 301,
  "type": "manual"
}
```

### Update Redirect
```http
PUT /api/cms/v1/seo/redirects/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "from_url": "/updated-old-url",
  "to_url": "/updated-new-url",
  "status_code": 302,
  "type": "temporary"
}
```

### Delete Redirect
```http
DELETE /api/cms/v1/seo/redirects/{id}
Authorization: Bearer {token}
```

## Sitemap Management

### Generate Sitemap
```http
POST /api/cms/v1/seo/sitemap/generate
Authorization: Bearer {token}
Content-Type: application/json

{
  "type": "main", // main, posts, pages, images, news
  "force_regenerate": false
}
```

### Get Sitemap
```http
GET /api/cms/v1/seo/sitemap/{type}
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "website_id": 1,
    "type": "main",
    "url": "https://blog.example.com/sitemap.xml",
    "last_modified": "2024-01-01T00:00:00Z",
    "change_frequency": "weekly",
    "priority": 0.8,
    "file_path": "/sitemaps/website-1/sitemap.xml",
    "file_size": 15360,
    "url_count": 150,
    "status": "active"
  }
}
```

### List Sitemaps
```http
GET /api/cms/v1/seo/sitemaps
Authorization: Bearer {token}
```

### Sitemap Status
```http
GET /api/cms/v1/seo/sitemap/status
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "main_sitemap": {
      "exists": true,
      "last_generated": "2024-01-01T00:00:00Z",
      "url_count": 150,
      "file_size": 15360
    },
    "posts_sitemap": {
      "exists": true,
      "last_generated": "2024-01-01T00:00:00Z",
      "url_count": 100,
      "file_size": 12800
    },
    "generation_in_progress": false,
    "last_submission": "2024-01-01T00:00:00Z"
  }
}
```

## SEO Analysis

### Analyze Content
```http
POST /api/cms/v1/seo/analyze
Authorization: Bearer {token}
Content-Type: application/json

{
  "content_type": "post",
  "content_id": 123,
  "target_keywords": ["go programming", "golang tutorial"],
  "competitor_urls": ["https://competitor.com/go-tutorial"]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "overall_score": 85.5,
    "title_score": 90,
    "description_score": 85,
    "content_score": 88,
    "technical_score": 78,
    "issues": {
      "critical": [],
      "warning": [
        {
          "code": "TITLE_TOO_LONG",
          "message": "Title exceeds 60 characters",
          "severity": "warning",
          "element": "title"
        }
      ],
      "info": []
    },
    "suggestions": [
      "Consider shortening the title to under 60 characters",
      "Add more internal links to related content",
      "Optimize images with descriptive alt text"
    ],
    "keyword_analysis": {
      "primary_keyword": {
        "keyword": "go programming",
        "density": 2.5,
        "count": 15,
        "placement": "good"
      },
      "secondary_keywords": [
        {
          "keyword": "golang tutorial",
          "density": 1.2,
          "count": 7,
          "placement": "fair"
        }
      ]
    }
  }
}
```

### Get SEO Report
```http
GET /api/cms/v1/seo/reports/{content_type}/{content_id}
Authorization: Bearer {token}
```

### Bulk SEO Audit
```http
POST /api/cms/v1/seo/audit/bulk
Authorization: Bearer {token}
Content-Type: application/json

{
  "content_type": "post",
  "filters": {
    "status": "published",
    "date_from": "2024-01-01",
    "date_to": "2024-12-31",
    "category_id": 5
  },
  "options": {
    "include_keywords": true,
    "include_competitors": false,
    "force_reanalysis": false
  }
}
```

## Keywords Management

### Get Keywords
```http
GET /api/cms/v1/seo/keywords/{content_type}/{content_id}
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "website_id": 1,
    "content_type": "post",
    "content_id": 123,
    "primary_keyword": "go programming",
    "secondary_keywords": ["golang tutorial", "web development"],
    "long_tail_keywords": ["go programming for beginners", "golang web development tutorial"],
    "search_volume": 5400,
    "difficulty": 65,
    "competition": 0.7,
    "cpc": 2.50,
    "current_ranking": 15,
    "best_ranking": 8,
    "click_through": 2.5,
    "impressions": 1200,
    "status": "active"
  }
}
```

### Update Keywords
```http
PUT /api/cms/v1/seo/keywords/{content_type}/{content_id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "primary_keyword": "golang programming",
  "secondary_keywords": ["go tutorial", "golang basics"],
  "long_tail_keywords": ["golang programming tutorial for beginners"]
}
```

### Keyword Research
```http
POST /api/cms/v1/seo/keywords/research
Authorization: Bearer {token}
Content-Type: application/json

{
  "seed_keywords": ["go programming", "golang"],
  "content_type": "post",
  "content_id": 123,
  "include_metrics": true
}
```

## SEO Tools

### Generate Meta Tags
```http
POST /api/cms/v1/seo/tools/generate-meta
Authorization: Bearer {token}
Content-Type: application/json

{
  "content_type": "post",
  "content_id": 123,
  "target_keywords": ["go programming"],
  "meta_types": ["title", "description", "keywords"]
}
```

### URL Slug Generator
```http
POST /api/cms/v1/seo/tools/generate-slug
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "Complete Guide to Go Programming",
  "max_length": 50,
  "include_keywords": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "slug": "complete-guide-go-programming",
    "alternatives": [
      "go-programming-complete-guide",
      "guide-to-go-programming",
      "complete-go-programming-guide"
    ]
  }
}
```

### Content Readability
```http
POST /api/cms/v1/seo/tools/readability
Authorization: Bearer {token}
Content-Type: application/json

{
  "content": "Your article content here...",
  "target_audience": "general" // general, technical, beginner
}
```

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "error": {
    "code": "INVALID_REQUEST",
    "message": "Invalid request parameters",
    "details": [
      {
        "field": "meta_title",
        "message": "Title exceeds maximum length of 70 characters"
      }
    ]
  }
}
```

### 404 Not Found
```json
{
  "success": false,
  "error": {
    "code": "NOT_FOUND",
    "message": "SEO meta not found for the specified content"
  }
}
```

### 422 Validation Error
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Validation failed",
    "details": [
      {
        "field": "from_url",
        "message": "URL redirect already exists"
      }
    ]
  }
}
```

## Rate Limiting

All SEO API endpoints are subject to rate limiting:

- **Meta Management**: 100 requests per minute
- **Redirects**: 50 requests per minute  
- **Sitemap Generation**: 5 requests per minute
- **SEO Analysis**: 10 requests per minute
- **Bulk Operations**: 2 requests per minute

## Authentication

All endpoints require authentication via Bearer token:

```http
Authorization: Bearer {your-api-token}
```

## Permissions

SEO operations require the following permissions:

- `seo.read`: View SEO data
- `seo.write`: Create/Update SEO data
- `seo.delete`: Delete SEO data
- `seo.analyze`: Run SEO analysis
- `seo.sitemap`: Generate sitemaps
- `seo.redirects`: Manage URL redirects
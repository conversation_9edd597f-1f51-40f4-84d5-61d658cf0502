# SEO Module - Tổng quan

## Mục tiêu

SEO Module cung cấp các tính năng tối ưu hóa công cụ tìm kiếm (Search Engine Optimization) cho Blog API v3, bao gồm quản lý meta tags, sitemap generation, structured data, và các công cụ phân tích SEO.

> **📌 Important Note**: Blog module đã remove tất cả meta fields (meta_title, meta_description, meta_keywords) khỏi database tables. Tất cả SEO metadata được quản lý centralized bởi SEO Module này để tránh trùng lặp và tối ưu performance.

## Các tính năng chính

- **Search Engine Optimization**: Tối ưu hóa content cho search engines
- **Meta Tag Management**: Quản lý meta tags cho tất cả content
- **Sitemap Generation**: Tự động tạo XML sitemaps
- **Structured Data**: Schema.org markup cho rich snippets
- **SEO Analytics**: <PERSON>ân tích hiệu suất SEO
- **URL Optimization**: Friendly URLs và redirects management

## Kiến trúc hệ thống

### SEO Architecture Overview

```mermaid
flowchart TD
    A[SEO Module] --> B[Meta Management]
    A --> C[Sitemap Generator]
    A --> D[Structured Data]
    A --> E[URL Management]
    A --> F[SEO Analytics]
    A --> G[Content Analysis]
    
    B --> B1[Page Meta]
    B --> B2[Open Graph]
    B --> B3[Twitter Cards]
    B --> B4[Custom Meta]
    
    C --> C1[XML Sitemap]
    C --> C2[Image Sitemap]
    C --> C3[News Sitemap]
    C --> C4[Video Sitemap]
    
    D --> D1[Article Schema]
    D --> D2[Organization Schema]
    D --> D3[Breadcrumb Schema]
    D --> D4[FAQ Schema]
    
    E --> E1[URL Rewrites]
    E --> E2[Redirects]
    E --> E3[Canonical URLs]
    E --> E4[Pretty URLs]
```

### Components

#### Meta Management
- **Page Meta Tags**: Title, description, keywords
- **Open Graph**: Facebook sharing optimization
- **Twitter Cards**: Twitter sharing optimization
- **Custom Meta**: Module-specific meta tags

#### Sitemap Generation
- **XML Sitemaps**: Standard website structure
- **Image Sitemaps**: Image content indexing
- **News Sitemaps**: News content optimization
- **Video Sitemaps**: Video content indexing

#### Structured Data
- **Schema.org Markup**: Rich snippets support
- **JSON-LD**: JavaScript-based structured data
- **Microdata**: HTML-embedded structured data
- **RDFa**: Resource Description Framework

## Multi-Tenancy & Website Isolation

### Website-scoped SEO Management

```mermaid
flowchart TD
    A[SEO Request] --> B[Website Detection]
    B --> C[Load Website SEO Config]
    C --> D[Process SEO Operation]
    D --> E[Update Website-specific Data]
    E --> F[Update Website Cache]
    
    G[Sitemap Generation] --> H[Website Context]
    H --> I[Generate Website Sitemap]
    I --> J[Upload to Website Domain]
    
    K[Meta Management] --> L[Website Context]
    L --> M[Load Website Meta Rules]
    M --> N[Generate Website-specific Meta]
```

### SEO Best Practices cho Multi-Website

#### Website Isolation
- **Separate Sitemaps**: Mỗi website có sitemap riêng
- **Domain-specific Meta**: Meta tags theo domain của website
- **Isolated Analytics**: SEO analytics riêng per website
- **Website-specific Redirects**: Redirects chỉ áp dụng cho website

#### Performance Optimization
- **Website-scoped Caching**: Cache keys có website prefix
- **Lazy Loading**: Load SEO data khi cần cho website
- **Batch Processing**: Process SEO operations theo website
- **Website-aware Indexing**: Database indexes có website_id

## Tài liệu liên quan

- [Models & Schema](./models-schema.md)
- [API Endpoints](./api-endpoints.md)
- [Content Analysis](./content-analysis.md)
- [Sitemap Management](./sitemap-management.md)
- [URL Management](./url-management.md)
- [SEO Analytics](./seo-analytics.md)
- [Configuration](./configuration.md)
- [Best Practices](./best-practices.md)
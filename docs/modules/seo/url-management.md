# URL Management

## Overview

URL Management module quản lý URL structure, redirects, canonical URLs, và URL optimization để cải thiện SEO performance và user experience.

## URL Structure Management

### Pretty URL Generation

```mermaid
flowchart LR
    A[Original URL] --> B[URL Processor]
    B --> C[Slug Generation]
    B --> D[Conflict Resolution]
    B --> E[SEO Optimization]
    
    C --> F[Clean URL]
    D --> F
    E --> F
    
    F --> G[Database Storage]
    F --> H[Redirect Setup]
```

### URL Slug Generator

```go
type URLSlugGenerator struct {
    maxLength       int
    reservedWords   []string
    replacements    map[string]string
}

func (g *URLSlugGenerator) GenerateSlug(title string, options SlugOptions) string {
    // Clean and normalize
    slug := g.cleanText(title)
    
    // Apply replacements
    slug = g.applyReplacements(slug)
    
    // Remove special characters
    slug = g.removeSpecialChars(slug)
    
    // Convert to lowercase
    slug = strings.ToLower(slug)
    
    // Limit length
    if len(slug) > g.maxLength {
        slug = g.truncateSlug(slug, g.maxLength)
    }
    
    // Check for conflicts
    if g.isConflict(slug, options.WebsiteID, options.ContentType) {
        slug = g.resolveConflict(slug, options)
    }
    
    return slug
}

func (g *URLSlugGenerator) cleanText(text string) string {
    // Remove HTML tags
    text = stripHtml(text)
    
    // Normalize Unicode
    text = norm.NFC.String(text)
    
    // Replace spaces with hyphens
    text = strings.ReplaceAll(text, " ", "-")
    
    // Remove multiple hyphens
    text = regexp.MustCompile(`-+`).ReplaceAllString(text, "-")
    
    // Trim hyphens from start and end
    text = strings.Trim(text, "-")
    
    return text
}
```

### URL Conflict Resolution

```go
type URLConflictResolver struct {
    repo URLRepository
}

func (r *URLConflictResolver) ResolveConflict(baseSlug string, options SlugOptions) string {
    counter := 1
    currentSlug := baseSlug
    
    for r.slugExists(currentSlug, options.WebsiteID, options.ContentType) {
        currentSlug = fmt.Sprintf("%s-%d", baseSlug, counter)
        counter++
        
        // Prevent infinite loop
        if counter > 100 {
            currentSlug = fmt.Sprintf("%s-%d", baseSlug, time.Now().Unix())
            break
        }
    }
    
    return currentSlug
}

func (r *URLConflictResolver) slugExists(slug string, websiteID uint, contentType string) bool {
    count, err := r.repo.CountBySlug(websiteID, contentType, slug)
    if err != nil {
        return false
    }
    return count > 0
}
```

## URL Redirects Management

### Redirect Types

```go
type RedirectType string

const (
    RedirectManual    RedirectType = "manual"    // Manually created
    RedirectAuto      RedirectType = "auto"      // Auto-generated (slug change)
    RedirectTemporary RedirectType = "temporary" // Temporary redirect
    RedirectBulk      RedirectType = "bulk"      // Bulk import
)

type RedirectStatusCode int

const (
    StatusMovedPermanently RedirectStatusCode = 301
    StatusFound            RedirectStatusCode = 302
    StatusTemporaryRedirect RedirectStatusCode = 307
    StatusPermanentRedirect RedirectStatusCode = 308
)
```

### Redirect Service

```go
type RedirectService struct {
    repo      URLRedirectRepository
    cache     CacheService
    websiteID uint
}

func (s *RedirectService) CreateRedirect(redirect *URLRedirect) error {
    // Validate redirect
    if err := s.validateRedirect(redirect); err != nil {
        return err
    }
    
    // Check for redirect loops
    if err := s.checkRedirectLoop(redirect); err != nil {
        return err
    }
    
    // Check for existing redirect
    if existing, err := s.repo.GetByFromURL(s.websiteID, redirect.FromURL); err == nil {
        return s.updateExistingRedirect(existing, redirect)
    }
    
    // Create new redirect
    redirect.WebsiteID = s.websiteID
    redirect.CreatedAt = time.Now()
    redirect.Status = "active"
    
    if err := s.repo.Create(redirect); err != nil {
        return err
    }
    
    // Update cache
    s.updateRedirectCache(redirect)
    
    return nil
}

func (s *RedirectService) validateRedirect(redirect *URLRedirect) error {
    // Check required fields
    if redirect.FromURL == "" {
        return errors.New("from_url is required")
    }
    
    if redirect.ToURL == "" {
        return errors.New("to_url is required")
    }
    
    // Check for self-redirect
    if redirect.FromURL == redirect.ToURL {
        return errors.New("from_url and to_url cannot be the same")
    }
    
    // Validate URLs
    if !s.isValidURL(redirect.FromURL) {
        return errors.New("invalid from_url format")
    }
    
    if !s.isValidURL(redirect.ToURL) {
        return errors.New("invalid to_url format")
    }
    
    // Validate status code
    if !s.isValidStatusCode(redirect.StatusCode) {
        return errors.New("invalid status code")
    }
    
    return nil
}
```

### Redirect Loop Detection

```go
func (s *RedirectService) checkRedirectLoop(redirect *URLRedirect) error {
    visited := make(map[string]bool)
    current := redirect.ToURL
    maxDepth := 10
    
    for i := 0; i < maxDepth; i++ {
        if visited[current] {
            return errors.New("redirect loop detected")
        }
        
        visited[current] = true
        
        // Check if current URL has a redirect
        if next, err := s.repo.GetByFromURL(s.websiteID, current); err == nil {
            if next.ToURL == redirect.FromURL {
                return errors.New("redirect loop detected")
            }
            current = next.ToURL
        } else {
            // No more redirects, safe
            break
        }
    }
    
    if len(visited) >= maxDepth {
        return errors.New("redirect chain too long")
    }
    
    return nil
}
```

### Redirect Middleware

```go
type RedirectMiddleware struct {
    service   RedirectService
    cache     CacheService
    websiteID uint
}

func (m *RedirectMiddleware) HandleRedirect(w http.ResponseWriter, r *http.Request) bool {
    requestPath := r.URL.Path
    
    // Check cache first
    if redirect, found := m.getCachedRedirect(requestPath); found {
        m.performRedirect(w, r, redirect)
        return true
    }
    
    // Check database
    redirect, err := m.service.GetByFromURL(m.websiteID, requestPath)
    if err != nil {
        return false // No redirect found
    }
    
    // Cache the redirect
    m.cacheRedirect(requestPath, redirect)
    
    // Perform redirect
    m.performRedirect(w, r, redirect)
    
    // Update hit count
    go m.updateHitCount(redirect.ID)
    
    return true
}

func (m *RedirectMiddleware) performRedirect(w http.ResponseWriter, r *http.Request, redirect *URLRedirect) {
    // Log redirect
    log.Printf("Redirecting %s to %s (status: %d)", redirect.FromURL, redirect.ToURL, redirect.StatusCode)
    
    // Set location header
    w.Header().Set("Location", redirect.ToURL)
    
    // Set cache headers
    if redirect.StatusCode == 301 || redirect.StatusCode == 308 {
        w.Header().Set("Cache-Control", "max-age=31536000") // 1 year
    } else {
        w.Header().Set("Cache-Control", "no-cache")
    }
    
    // Write status code
    w.WriteHeader(redirect.StatusCode)
}
```

## Canonical URLs

### Canonical URL Management

```go
type CanonicalURLService struct {
    repo      SEORepository
    websiteID uint
    domain    string
}

func (s *CanonicalURLService) SetCanonicalURL(contentType string, contentID uint, canonicalURL string) error {
    // Validate canonical URL
    if err := s.validateCanonicalURL(canonicalURL); err != nil {
        return err
    }
    
    // Get existing SEO meta
    meta, err := s.repo.GetSEOMeta(s.websiteID, contentType, contentID)
    if err != nil {
        // Create new meta
        meta = &SEOMeta{
            WebsiteID:   s.websiteID,
            ContentType: contentType,
            ContentID:   contentID,
            CanonicalURL: canonicalURL,
            Status:      "active",
        }
        return s.repo.CreateSEOMeta(meta)
    }
    
    // Update existing meta
    meta.CanonicalURL = canonicalURL
    meta.UpdatedAt = time.Now()
    
    return s.repo.UpdateSEOMeta(meta)
}

func (s *CanonicalURLService) GenerateCanonicalURL(contentType string, contentID uint) (string, error) {
    switch contentType {
    case "post":
        return s.generatePostCanonicalURL(contentID)
    case "page":
        return s.generatePageCanonicalURL(contentID)
    case "category":
        return s.generateCategoryCanonicalURL(contentID)
    case "tag":
        return s.generateTagCanonicalURL(contentID)
    default:
        return "", fmt.Errorf("unsupported content type: %s", contentType)
    }
}
```

### Canonical URL Validation

```go
func (s *CanonicalURLService) validateCanonicalURL(url string) error {
    // Check URL format
    parsedURL, err := neturl.Parse(url)
    if err != nil {
        return fmt.Errorf("invalid URL format: %v", err)
    }
    
    // Check scheme
    if parsedURL.Scheme != "https" && parsedURL.Scheme != "http" {
        return errors.New("canonical URL must use http or https scheme")
    }
    
    // Check if URL belongs to this website
    if !s.isWebsiteURL(parsedURL.Host) {
        return errors.New("canonical URL must belong to this website")
    }
    
    // Check for fragments
    if parsedURL.Fragment != "" {
        return errors.New("canonical URL should not contain fragments")
    }
    
    // Check for query parameters (optional warning)
    if parsedURL.RawQuery != "" {
        log.Printf("Warning: Canonical URL contains query parameters: %s", url)
    }
    
    return nil
}
```

## URL Optimization

### URL Structure Rules

```yaml
url_optimization_rules:
  structure:
    max_length: 75
    max_segments: 5
    preferred_separators: ["-"]
    avoid_separators: ["_", " ", "%20"]
    
  content:
    include_keywords: true
    max_keywords: 3
    remove_stop_words: true
    avoid_special_chars: true
    
  seo:
    lowercase_only: true
    no_trailing_slash: true
    avoid_duplicate_urls: true
    use_canonical: true
    
  performance:
    cache_friendly: true
    cdn_compatible: true
    mobile_friendly: true
```

### URL Optimization Service

```go
type URLOptimizationService struct {
    rules     URLOptimizationRules
    analyzer  ContentAnalyzer
    websiteID uint
}

func (s *URLOptimizationService) OptimizeURL(content Content, currentURL string) (*URLOptimization, error) {
    optimization := &URLOptimization{
        CurrentURL: currentURL,
        ContentID:  content.ID,
    }
    
    // Analyze current URL
    analysis := s.analyzeURL(currentURL)
    optimization.CurrentScore = analysis.Score
    optimization.Issues = analysis.Issues
    
    // Generate optimized URL
    optimizedURL, err := s.generateOptimizedURL(content)
    if err != nil {
        return nil, err
    }
    
    optimization.OptimizedURL = optimizedURL
    optimization.OptimizedScore = s.scoreURL(optimizedURL)
    optimization.Improvements = s.calculateImprovements(analysis)
    
    return optimization, nil
}

func (s *URLOptimizationService) analyzeURL(url string) *URLAnalysis {
    analysis := &URLAnalysis{
        URL: url,
    }
    
    // Check length
    if len(url) > s.rules.MaxLength {
        analysis.Issues = append(analysis.Issues, URLIssue{
            Type:     "length",
            Severity: "warning",
            Message:  fmt.Sprintf("URL is too long (%d chars, max: %d)", len(url), s.rules.MaxLength),
        })
    }
    
    // Check for keywords
    if !s.containsKeywords(url) {
        analysis.Issues = append(analysis.Issues, URLIssue{
            Type:     "keywords",
            Severity: "info",
            Message:  "URL doesn't contain target keywords",
        })
    }
    
    // Check for stop words
    if stopWords := s.findStopWords(url); len(stopWords) > 0 {
        analysis.Issues = append(analysis.Issues, URLIssue{
            Type:     "stop_words",
            Severity: "info",
            Message:  fmt.Sprintf("URL contains stop words: %s", strings.Join(stopWords, ", ")),
        })
    }
    
    // Calculate score
    analysis.Score = s.calculateURLScore(analysis)
    
    return analysis
}
```

### URL Performance Monitoring

```go
type URLPerformanceMonitor struct {
    analytics AnalyticsService
    websiteID uint
}

func (m *URLPerformanceMonitor) TrackURLPerformance(url string) (*URLPerformance, error) {
    performance := &URLPerformance{
        URL: url,
    }
    
    // Get search engine rankings
    rankings, err := m.analytics.GetSearchRankings(url)
    if err == nil {
        performance.SearchRankings = rankings
    }
    
    // Get click-through rates
    ctr, err := m.analytics.GetClickThroughRate(url)
    if err == nil {
        performance.ClickThroughRate = ctr
    }
    
    // Get organic traffic
    traffic, err := m.analytics.GetOrganicTraffic(url)
    if err == nil {
        performance.OrganicTraffic = traffic
    }
    
    // Get bounce rate
    bounceRate, err := m.analytics.GetBounceRate(url)
    if err == nil {
        performance.BounceRate = bounceRate
    }
    
    return performance, nil
}
```

## Bulk URL Operations

### Bulk Redirect Import

```go
type BulkRedirectService struct {
    redirectService RedirectService
    validator       RedirectValidator
    websiteID       uint
}

func (s *BulkRedirectService) ImportRedirects(redirects []URLRedirect) (*BulkImportResult, error) {
    result := &BulkImportResult{
        Total:     len(redirects),
        Processed: 0,
        Succeeded: 0,
        Failed:    0,
        Errors:    []BulkError{},
    }
    
    for i, redirect := range redirects {
        result.Processed++
        
        // Validate redirect
        if err := s.validator.Validate(&redirect); err != nil {
            result.Failed++
            result.Errors = append(result.Errors, BulkError{
                Row:     i + 1,
                Message: err.Error(),
            })
            continue
        }
        
        // Create redirect
        redirect.WebsiteID = s.websiteID
        redirect.Type = RedirectBulk
        
        if err := s.redirectService.CreateRedirect(&redirect); err != nil {
            result.Failed++
            result.Errors = append(result.Errors, BulkError{
                Row:     i + 1,
                Message: err.Error(),
            })
            continue
        }
        
        result.Succeeded++
    }
    
    return result, nil
}
```

### URL Structure Migration

```go
type URLStructureMigrator struct {
    redirectService RedirectService
    seoService      SEOService
    websiteID       uint
}

func (m *URLStructureMigrator) MigrateURLStructure(migration *URLMigration) error {
    // Get all content URLs
    urls, err := m.getAllContentURLs()
    if err != nil {
        return err
    }
    
    // Create redirects for old URLs
    for _, url := range urls {
        newURL := m.applyMigrationRules(url, migration.Rules)
        
        if newURL != url.Current {
            redirect := &URLRedirect{
                FromURL:    url.Current,
                ToURL:      newURL,
                StatusCode: 301,
                Type:       RedirectAuto,
            }
            
            if err := m.redirectService.CreateRedirect(redirect); err != nil {
                return err
            }
        }
    }
    
    return nil
}
```

## URL Security

### URL Sanitization

```go
type URLSanitizer struct {
    allowedSchemes   []string
    blockedPatterns  []string
    maxLength        int
}

func (s *URLSanitizer) SanitizeURL(url string) (string, error) {
    // Parse URL
    parsedURL, err := neturl.Parse(url)
    if err != nil {
        return "", fmt.Errorf("invalid URL: %v", err)
    }
    
    // Check scheme
    if !s.isAllowedScheme(parsedURL.Scheme) {
        return "", fmt.Errorf("scheme not allowed: %s", parsedURL.Scheme)
    }
    
    // Check for blocked patterns
    if s.containsBlockedPattern(url) {
        return "", errors.New("URL contains blocked pattern")
    }
    
    // Check length
    if len(url) > s.maxLength {
        return "", fmt.Errorf("URL too long: %d chars (max: %d)", len(url), s.maxLength)
    }
    
    // Normalize URL
    normalizedURL := s.normalizeURL(parsedURL)
    
    return normalizedURL, nil
}

func (s *URLSanitizer) normalizeURL(url *neturl.URL) string {
    // Remove default ports
    if url.Port() == "80" && url.Scheme == "http" {
        url.Host = url.Hostname()
    } else if url.Port() == "443" && url.Scheme == "https" {
        url.Host = url.Hostname()
    }
    
    // Remove trailing slash
    url.Path = strings.TrimSuffix(url.Path, "/")
    
    // Sort query parameters
    if url.RawQuery != "" {
        values := url.Query()
        url.RawQuery = values.Encode()
    }
    
    return url.String()
}
```

### URL Access Control

```go
type URLAccessController struct {
    rbac      RBACService
    websiteID uint
}

func (c *URLAccessController) CheckURLAccess(userID uint, url string, action string) error {
    // Parse URL to get content type and ID
    contentType, contentID, err := c.parseContentURL(url)
    if err != nil {
        return err
    }
    
    // Check RBAC permissions
    permission := fmt.Sprintf("%s.%s", contentType, action)
    
    if !c.rbac.HasPermission(userID, permission, c.websiteID) {
        return errors.New("access denied")
    }
    
    return nil
}
```
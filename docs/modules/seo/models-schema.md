# SEO Models & Schema

## Database Schema

### SEO Meta Model

```go
type SEOMeta struct {
    ID               uint      `gorm:"primarykey" json:"id"`
    WebsiteID        uint      `gorm:"not null;index" json:"website_id"`
    ContentType      string    `gorm:"size:50;not null" json:"content_type"` // post, page, category
    ContentID        uint      `gorm:"not null;index" json:"content_id"`
    
    // Basic Meta
    MetaTitle        string    `gorm:"size:70" json:"meta_title"`
    MetaDescription  string    `gorm:"size:160" json:"meta_description"`
    MetaKeywords     string    `gorm:"size:255" json:"meta_keywords"`
    CanonicalURL     string    `gorm:"size:255" json:"canonical_url"`
    
    // Open Graph
    OGTitle          string    `gorm:"size:95" json:"og_title"`
    OGDescription    string    `gorm:"size:297" json:"og_description"`
    OGImage          string    `gorm:"size:255" json:"og_image"`
    OGType           string    `gorm:"size:50" json:"og_type"`
    OGLocale         string    `gorm:"size:10" json:"og_locale"`
    
    // Twitter Cards
    TwitterCard      string    `gorm:"size:50" json:"twitter_card"`
    TwitterTitle     string    `gorm:"size:70" json:"twitter_title"`
    TwitterDescription string  `gorm:"size:200" json:"twitter_description"`
    TwitterImage     string    `gorm:"size:255" json:"twitter_image"`
    TwitterCreator   string    `gorm:"size:50" json:"twitter_creator"`
    
    // SEO Settings
    NoIndex          bool      `gorm:"default:false" json:"no_index"`
    NoFollow         bool      `gorm:"default:false" json:"no_follow"`
    NoSnippet        bool      `gorm:"default:false" json:"no_snippet"`
    NoArchive        bool      `gorm:"default:false" json:"no_archive"`
    
    // Advanced
    CustomMeta       JSON      `gorm:"type:json" json:"custom_meta"`
    StructuredData   JSON      `gorm:"type:json" json:"structured_data"`
    
    // Status
    Status           string    `gorm:"size:20;default:'active'" json:"status"`
    
    CreatedAt        time.Time `json:"created_at"`
    UpdatedAt        time.Time `json:"updated_at"`
}
```

### URL Redirect Model

```go
type URLRedirect struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    WebsiteID   uint      `gorm:"not null;index" json:"website_id"`
    FromURL     string    `gorm:"size:255;not null" json:"from_url"`
    ToURL       string    `gorm:"size:255;not null" json:"to_url"`
    StatusCode  int       `gorm:"default:301" json:"status_code"` // 301, 302, 307, 308
    Type        string    `gorm:"size:20;default:'manual'" json:"type"` // manual, auto, temporary
    HitCount    uint      `gorm:"default:0" json:"hit_count"`
    LastHit     *time.Time `json:"last_hit"`
    Status      string    `gorm:"size:20;default:'active'" json:"status"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}
```

### SEO Audit Model

```go
type SEOAudit struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    WebsiteID   uint      `gorm:"not null;index" json:"website_id"`
    ContentType string    `gorm:"size:50;not null" json:"content_type"`
    ContentID   uint      `gorm:"not null;index" json:"content_id"`
    URL         string    `gorm:"size:255;not null" json:"url"`
    
    // SEO Scores
    OverallScore    float64   `json:"overall_score"`
    TitleScore      float64   `json:"title_score"`
    DescriptionScore float64  `json:"description_score"`
    ContentScore    float64   `json:"content_score"`
    TechnicalScore  float64   `json:"technical_score"`
    
    // Issues
    Issues      JSON      `gorm:"type:json" json:"issues"`
    Suggestions JSON      `gorm:"type:json" json:"suggestions"`
    
    // Content Analysis
    WordCount       int       `json:"word_count"`
    ReadingTime     int       `json:"reading_time"` // in minutes
    ReadabilityScore float64  `json:"readability_score"`
    KeywordDensity  JSON      `gorm:"type:json" json:"keyword_density"`
    
    // Status
    Status      string    `gorm:"size:20;default:'active'" json:"status"`
    
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}
```

### SEO Sitemap Model

```go
type SEOSitemap struct {
    ID              uint      `gorm:"primarykey" json:"id"`
    WebsiteID       uint      `gorm:"not null;index" json:"website_id"`
    Type            string    `gorm:"size:50;not null" json:"type"` // main, posts, pages, images, news
    URL             string    `gorm:"size:255;not null" json:"url"`
    LastModified    time.Time `json:"last_modified"`
    ChangeFrequency string    `gorm:"size:20" json:"change_frequency"`
    Priority        float64   `gorm:"default:0.5" json:"priority"`
    FilePath        string    `gorm:"size:255" json:"file_path"`
    FileSize        int64     `json:"file_size"`
    URLCount        int       `json:"url_count"`
    
    // Status
    Status          string    `gorm:"size:20;default:'active'" json:"status"`
    
    CreatedAt       time.Time `json:"created_at"`
    UpdatedAt       time.Time `json:"updated_at"`
}
```

### SEO Keywords Model

```go
type SEOKeywords struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    WebsiteID   uint      `gorm:"not null;index" json:"website_id"`
    ContentType string    `gorm:"size:50;not null" json:"content_type"`
    ContentID   uint      `gorm:"not null;index" json:"content_id"`
    
    // Target Keywords
    PrimaryKeyword   string    `gorm:"size:100" json:"primary_keyword"`
    SecondaryKeywords JSON     `gorm:"type:json" json:"secondary_keywords"`
    LongTailKeywords JSON     `gorm:"type:json" json:"long_tail_keywords"`
    
    // Keyword Metrics
    SearchVolume     int       `json:"search_volume"`
    Difficulty       int       `json:"difficulty"` // 1-100
    Competition      float64   `json:"competition"`
    CPC              float64   `json:"cpc"`
    
    // Performance
    CurrentRanking   int       `json:"current_ranking"`
    BestRanking      int       `json:"best_ranking"`
    ClickThrough     float64   `json:"click_through"`
    Impressions      int       `json:"impressions"`
    
    // Status
    Status          string    `gorm:"size:20;default:'active'" json:"status"`
    
    CreatedAt       time.Time `json:"created_at"`
    UpdatedAt       time.Time `json:"updated_at"`
}
```

## JSON Schema Definitions

### Structured Data Schema

```json
{
  "article": {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": "string",
    "datePublished": "2024-01-01T00:00:00Z",
    "dateModified": "2024-01-01T00:00:00Z",
    "author": {
      "@type": "Person",
      "name": "string",
      "url": "string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "string",
      "logo": {
        "@type": "ImageObject",
        "url": "string"
      }
    },
    "image": {
      "@type": "ImageObject",
      "url": "string",
      "width": 1200,
      "height": 630
    },
    "articleBody": "string"
  }
}
```

### Custom Meta Schema

```json
{
  "robots": "string",
  "viewport": "string",
  "theme-color": "string",
  "apple-mobile-web-app-capable": "string",
  "apple-mobile-web-app-status-bar-style": "string",
  "application-name": "string",
  "msapplication-TileColor": "string",
  "custom_tags": [
    {
      "name": "string",
      "content": "string",
      "property": "string"
    }
  ]
}
```

### SEO Issues Schema

```json
{
  "critical": [
    {
      "code": "MISSING_TITLE",
      "message": "Page title is missing",
      "severity": "critical",
      "element": "title"
    }
  ],
  "warning": [
    {
      "code": "TITLE_TOO_LONG",
      "message": "Title exceeds 60 characters",
      "severity": "warning",
      "element": "title",
      "current_length": 75,
      "max_length": 60
    }
  ],
  "info": [
    {
      "code": "MISSING_ALT_TEXT",
      "message": "Image missing alt text",
      "severity": "info",
      "element": "img",
      "count": 3
    }
  ]
}
```

### Keyword Density Schema

```json
{
  "primary_keyword": {
    "keyword": "go programming",
    "density": 2.5,
    "count": 15,
    "positions": [120, 245, 380, 520, 670]
  },
  "secondary_keywords": [
    {
      "keyword": "golang tutorial",
      "density": 1.2,
      "count": 7,
      "positions": [200, 350, 500]
    }
  ],
  "total_words": 600,
  "keyword_variations": [
    "go language",
    "golang development",
    "go programming language"
  ]
}
```

## Database Relationships

### Foreign Key Relationships

```sql
-- SEO Meta belongs to Website
ALTER TABLE seo_meta 
ADD CONSTRAINT fk_seo_meta_website_id 
FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE;

-- URL Redirects belongs to Website
ALTER TABLE url_redirects 
ADD CONSTRAINT fk_url_redirects_website_id 
FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE;

-- SEO Audit belongs to Website
ALTER TABLE seo_audits 
ADD CONSTRAINT fk_seo_audits_website_id 
FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE;

-- SEO Sitemap belongs to Website
ALTER TABLE seo_sitemaps 
ADD CONSTRAINT fk_seo_sitemaps_website_id 
FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE;

-- SEO Keywords belongs to Website
ALTER TABLE seo_keywords 
ADD CONSTRAINT fk_seo_keywords_website_id 
FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE;
```

### Indexes for Performance

```sql
-- SEO Meta indexes
CREATE INDEX idx_seo_meta_website_content ON seo_meta(website_id, content_type, content_id);
CREATE INDEX idx_seo_meta_status ON seo_meta(status);

-- URL Redirects indexes
CREATE INDEX idx_url_redirects_website_from ON url_redirects(website_id, from_url);
CREATE INDEX idx_url_redirects_status ON url_redirects(status);

-- SEO Audit indexes
CREATE INDEX idx_seo_audits_website_content ON seo_audits(website_id, content_type, content_id);
CREATE INDEX idx_seo_audits_score ON seo_audits(overall_score);

-- SEO Sitemap indexes
CREATE INDEX idx_seo_sitemaps_website_type ON seo_sitemaps(website_id, type);
CREATE INDEX idx_seo_sitemaps_last_modified ON seo_sitemaps(last_modified);

-- SEO Keywords indexes
CREATE INDEX idx_seo_keywords_website_content ON seo_keywords(website_id, content_type, content_id);
CREATE INDEX idx_seo_keywords_ranking ON seo_keywords(current_ranking);
```

## Model Validation

### SEO Meta Validation

```go
func (m *SEOMeta) Validate() error {
    if m.WebsiteID == 0 {
        return errors.New("website_id is required")
    }
    
    if m.ContentType == "" {
        return errors.New("content_type is required")
    }
    
    if m.ContentID == 0 {
        return errors.New("content_id is required")
    }
    
    if len(m.MetaTitle) > 70 {
        return errors.New("meta_title must be 70 characters or less")
    }
    
    if len(m.MetaDescription) > 160 {
        return errors.New("meta_description must be 160 characters or less")
    }
    
    if m.Status != "" && !isValidStatus(m.Status) {
        return errors.New("invalid status")
    }
    
    return nil
}
```

### URL Redirect Validation

```go
func (r *URLRedirect) Validate() error {
    if r.WebsiteID == 0 {
        return errors.New("website_id is required")
    }
    
    if r.FromURL == "" {
        return errors.New("from_url is required")
    }
    
    if r.ToURL == "" {
        return errors.New("to_url is required")
    }
    
    if r.FromURL == r.ToURL {
        return errors.New("from_url and to_url cannot be the same")
    }
    
    if !isValidStatusCode(r.StatusCode) {
        return errors.New("invalid status_code")
    }
    
    return nil
}
```
```

## Repository Pattern

### SEO Repository Interface

```go
type SEORepository interface {
    // Meta Management
    GetSEOMeta(websiteID uint, contentType string, contentID uint) (*SEOMeta, error)
    CreateSEOMeta(meta *SEOMeta) error
    UpdateSEOMeta(meta *SEOMeta) error
    DeleteSEOMeta(id uint) error
    
    // URL Redirects
    GetRedirects(websiteID uint) ([]URLRedirect, error)
    CreateRedirect(redirect *URLRedirect) error
    UpdateRedirect(redirect *URLRedirect) error
    DeleteRedirect(id uint) error
    
    // SEO Audit
    GetSEOAudit(websiteID uint, contentType string, contentID uint) (*SEOAudit, error)
    CreateSEOAudit(audit *SEOAudit) error
    UpdateSEOAudit(audit *SEOAudit) error
    
    // Sitemap
    GetSitemaps(websiteID uint) ([]SEOSitemap, error)
    CreateSitemap(sitemap *SEOSitemap) error
    UpdateSitemap(sitemap *SEOSitemap) error
    
    // Keywords
    GetKeywords(websiteID uint, contentType string, contentID uint) (*SEOKeywords, error)
    CreateKeywords(keywords *SEOKeywords) error
    UpdateKeywords(keywords *SEOKeywords) error
}
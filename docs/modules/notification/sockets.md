# Socket Notification System

## 1. Real-time Architecture

```mermaid
flowchart TD
    A[Socket Notification System] --> B[Socket.IO Server]
    A --> C[Redis Adapter]
    A --> D[Room Management]
    A --> E[Event Router]
    
    B --> B1[Connection Handler]
    B --> B2[Authentication]
    B --> B3[Namespace Management]
    B --> B4[Middleware]
    
    C --> C1[Pub/Sub]
    C --> C2[Session Store]
    C --> C3[Presence Tracking]
    C --> C4[Message Queue]
    
    D --> D1[User Rooms]
    D --> D2[Tenant Rooms]
    D --> D3[Topic Rooms]
    D --> D4[Private Rooms]
    
    E --> E1[Event Listeners]
    E --> E2[Event Emitters]
    E --> E3[Event Filters]
    E --> E4[Event Analytics]
```

## 2. Socket Connection Flow

```mermaid
sequenceDiagram
    participant Client as Client App
    participant Socket as Socket.IO Server
    participant Auth as Auth Service
    participant Redis as Redis PubSub
    participant Queue as Notification Queue
    
    Client->>Socket: Connect with JWT token
    Socket->>Auth: Validate token
    Auth->>Socket: User authenticated
    
    Socket->>Socket: Join user room: user:{user_id}
    Socket->>Socket: Join tenant room: tenant:{tenant_id}
    Socket->>Redis: Subscribe to user channel
    
    Queue->>Redis: Publish notification
    Redis->>Socket: Receive notification
    Socket->>Socket: Check user online status
    Socket->>Client: Emit notification event
    
    Client->>Socket: Acknowledge receipt
    Socket->>Queue: Update delivery status
```

## 3. Socket Event Types

```javascript
// Notification Events
const NotificationEvents = {
  // Server -> Client
  NEW_NOTIFICATION: 'notification:new',
  NOTIFICATION_UPDATE: 'notification:update',
  NOTIFICATION_DELETE: 'notification:delete',
  NOTIFICATION_BATCH: 'notification:batch',
  
  // Client -> Server
  NOTIFICATION_READ: 'notification:read',
  NOTIFICATION_READ_ALL: 'notification:read_all',
  NOTIFICATION_DELETE_CLIENT: 'notification:delete',
  
  // System Events
  CONNECTION_STATUS: 'connection:status',
  NOTIFICATION_STATS: 'notification:stats',
  SYNC_REQUEST: 'notification:sync'
};

// Notification Payload
interface NotificationPayload {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'system';
  title: string;
  message: string;
  data?: any;
  actions?: NotificationAction[];
  priority: 'low' | 'normal' | 'high' | 'urgent';
  timestamp: string;
  expires_at?: string;
  tenant_id: string;
  user_id: string;
}

// Notification Action
interface NotificationAction {
  id: string;
  label: string;
  type: 'link' | 'button' | 'api_call';
  url?: string;
  method?: string;
  payload?: any;
}
```

## 4. Client Implementation

```javascript
// Socket.IO Client Connection
class NotificationClient {
  constructor(token) {
    this.socket = io('https://api.yourblog.com', {
      auth: { token },
      transports: ['websocket'],
      reconnection: true,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      reconnectionAttempts: 5
    });
    
    this.setupEventListeners();
  }
  
  setupEventListeners() {
    // Connection events
    this.socket.on('connect', () => {
      console.log('Connected to notification service');
      this.syncNotifications();
    });
    
    // Notification events
    this.socket.on('notification:new', (notification) => {
      this.handleNewNotification(notification);
    });
    
    this.socket.on('notification:batch', (notifications) => {
      notifications.forEach(n => this.handleNewNotification(n));
    });
    
    // Error handling
    this.socket.on('error', (error) => {
      console.error('Socket error:', error);
    });
  }
  
  handleNewNotification(notification) {
    // Display notification in UI
    this.showNotification(notification);
    
    // Update notification badge
    this.updateBadgeCount();
    
    // Play notification sound if enabled
    if (this.soundEnabled && notification.priority === 'high') {
      this.playNotificationSound();
    }
  }
  
  markAsRead(notificationId) {
    this.socket.emit('notification:read', { id: notificationId });
  }
  
  markAllAsRead() {
    this.socket.emit('notification:read_all');
  }
  
  syncNotifications() {
    this.socket.emit('notification:sync', { 
      last_sync: localStorage.getItem('last_notification_sync') 
    });
  }
}
```

## 5. Server Implementation

### Socket Server Setup
```go
type SocketServer struct {
    server      *socketio.Server
    redis       *redis.Client
    auth        *AuthService
    connections map[string]*UserConnection
    rooms       map[string]*Room
    mu          sync.RWMutex
}

type UserConnection struct {
    UserID    uint
    TenantID  uint
    Socket    socketio.Conn
    Rooms     []string
    LastSeen  time.Time
    DeviceID  string
    Platform  string
}

func NewSocketServer(redis *redis.Client, auth *AuthService) *SocketServer {
    server := socketio.NewServer(&engineio.Options{
        Transports: []transport.Transport{
            &polling.Transport{},
            &websocket.Transport{},
        },
    })
    
    s := &SocketServer{
        server:      server,
        redis:       redis,
        auth:        auth,
        connections: make(map[string]*UserConnection),
        rooms:       make(map[string]*Room),
    }
    
    s.setupEventHandlers()
    return s
}

func (s *SocketServer) setupEventHandlers() {
    s.server.OnConnect("/", func(conn socketio.Conn) error {
        return s.handleConnection(conn)
    })
    
    s.server.OnEvent("/", "notification:read", func(conn socketio.Conn, data map[string]interface{}) {
        s.handleNotificationRead(conn, data)
    })
    
    s.server.OnEvent("/", "notification:read_all", func(conn socketio.Conn, data map[string]interface{}) {
        s.handleNotificationReadAll(conn, data)
    })
    
    s.server.OnEvent("/", "notification:sync", func(conn socketio.Conn, data map[string]interface{}) {
        s.handleNotificationSync(conn, data)
    })
    
    s.server.OnDisconnect("/", func(conn socketio.Conn, reason string) {
        s.handleDisconnection(conn, reason)
    })
}

func (s *SocketServer) handleConnection(conn socketio.Conn) error {
    // Authenticate user
    token := conn.Request().Header.Get("Authorization")
    user, err := s.auth.ValidateToken(token)
    if err != nil {
        return err
    }
    
    // Create user connection
    userConn := &UserConnection{
        UserID:   user.ID,
        TenantID: user.TenantID,
        Socket:   conn,
        LastSeen: time.Now(),
        DeviceID: conn.Request().Header.Get("X-Device-ID"),
        Platform: conn.Request().Header.Get("X-Platform"),
    }
    
    // Store connection
    s.mu.Lock()
    s.connections[conn.ID()] = userConn
    s.mu.Unlock()
    
    // Join user-specific rooms
    s.joinUserRooms(conn, user)
    
    // Subscribe to Redis channels
    s.subscribeToUserChannel(user.ID)
    
    return nil
}

func (s *SocketServer) joinUserRooms(conn socketio.Conn, user *User) {
    // User-specific room
    userRoom := fmt.Sprintf("user:%d", user.ID)
    conn.Join(userRoom)
    
    // Tenant-specific room
    tenantRoom := fmt.Sprintf("tenant:%d", user.TenantID)
    conn.Join(tenantRoom)
    
    // Role-based rooms
    for _, role := range user.Roles {
        roleRoom := fmt.Sprintf("role:%s", role.Name)
        conn.Join(roleRoom)
    }
}
```

### Notification Broadcasting
```go
func (s *SocketServer) BroadcastNotification(notification *Notification) error {
    // Serialize notification
    data, err := json.Marshal(notification)
    if err != nil {
        return err
    }
    
    // Broadcast to Redis for cross-server distribution
    channel := fmt.Sprintf("notification:broadcast:%d", notification.TenantID)
    err = s.redis.Publish(context.Background(), channel, data).Err()
    if err != nil {
        return err
    }
    
    // Local broadcast to connected users
    for _, recipient := range notification.Recipients {
        s.sendToUser(recipient.UserID, "notification:new", notification)
    }
    
    return nil
}

func (s *SocketServer) sendToUser(userID uint, event string, data interface{}) {
    room := fmt.Sprintf("user:%d", userID)
    s.server.BroadcastToRoom("/", room, event, data)
}

func (s *SocketServer) sendToTenant(tenantID uint, event string, data interface{}) {
    room := fmt.Sprintf("tenant:%d", tenantID)
    s.server.BroadcastToRoom("/", room, event, data)
}
```

## 6. Advanced Presence System

```mermaid
flowchart TD
    A[Presence System] --> B[Connection Tracking]
    A --> C[Device Management]
    A --> D[Status Broadcasting]
    A --> E[Activity Monitoring]
    
    B --> B1[Online Status]
    B --> B2[Last Seen]
    B --> B3[Connection Quality]
    B --> B4[Session Duration]
    
    C --> C1[Multi-device Support]
    C --> C2[Device Prioritization]
    C --> C3[Device Sync]
    C --> C4[Device Limits]
    
    D --> D1[Presence Updates]
    D --> D2[Typing Indicators]
    D --> D3[Read Receipts]
    D --> D4[Custom Status]
    
    E --> E1[Active Time Tracking]
    E --> E2[Idle Detection]
    E --> E3[Activity Patterns]
    E --> E4[Engagement Metrics]
```

### Presence Tracking Implementation
```go
type UserPresence struct {
    UserID        uint                `json:"user_id"`
    Status        string              `json:"status"` // online, away, offline
    LastSeenAt    time.Time           `json:"last_seen_at"`
    Devices       []DevicePresence    `json:"devices"`
    CustomStatus  string              `json:"custom_status,omitempty"`
    Activities    []UserActivity      `json:"activities,omitempty"`
}

type DevicePresence struct {
    DeviceID      string    `json:"device_id"`
    DeviceType    string    `json:"device_type"` // web, mobile, desktop
    SocketID      string    `json:"socket_id"`
    ConnectedAt   time.Time `json:"connected_at"`
    LastActiveAt  time.Time `json:"last_active_at"`
    IsActive      bool      `json:"is_active"`
    Platform      string    `json:"platform"`
    AppVersion    string    `json:"app_version"`
}

type PresenceManager struct {
    redis         *redis.Client
    presenceCache map[uint]*UserPresence
    mu            sync.RWMutex
}

func (p *PresenceManager) UpdatePresence(userID uint, deviceID string, status string) error {
    p.mu.Lock()
    defer p.mu.Unlock()
    
    presence, exists := p.presenceCache[userID]
    if !exists {
        presence = &UserPresence{
            UserID:     userID,
            Status:     status,
            LastSeenAt: time.Now(),
            Devices:    []DevicePresence{},
        }
    }
    
    // Update device presence
    deviceFound := false
    for i, device := range presence.Devices {
        if device.DeviceID == deviceID {
            presence.Devices[i].LastActiveAt = time.Now()
            presence.Devices[i].IsActive = (status != "offline")
            deviceFound = true
            break
        }
    }
    
    if !deviceFound && status != "offline" {
        presence.Devices = append(presence.Devices, DevicePresence{
            DeviceID:     deviceID,
            ConnectedAt:  time.Now(),
            LastActiveAt: time.Now(),
            IsActive:     true,
        })
    }
    
    // Update overall status
    presence.Status = p.calculateOverallStatus(presence.Devices)
    presence.LastSeenAt = time.Now()
    
    // Store in Redis for cross-server sync
    return p.syncToRedis(userID, presence)
}
```

## 7. Cross-Device Notification Sync

```mermaid
sequenceDiagram
    participant Device1 as Device 1
    participant Server1 as Socket Server 1
    participant Redis as Redis PubSub
    participant Server2 as Socket Server 2
    participant Device2 as Device 2
    
    Device1->>Server1: Mark notification as read
    Server1->>Redis: Publish sync event
    Redis->>Server2: Broadcast sync event
    Server2->>Device2: Update notification status
    
    Device2->>Server2: Acknowledge sync
    Server2->>Redis: Publish acknowledgment
    Redis->>Server1: Broadcast acknowledgment
    Server1->>Device1: Confirm sync complete
```

### Notification Sync Implementation
```go
type NotificationSyncService struct {
    redis    *redis.Client
    sockets  map[string]*SocketConnection
    mu       sync.RWMutex
}

type SyncEvent struct {
    Type      string    `json:"type"` // read, delete, update
    UserID    uint      `json:"user_id"`
    DeviceID  string    `json:"device_id"`
    Timestamp time.Time `json:"timestamp"`
    Data      JSON      `json:"data"`
}

func (s *NotificationSyncService) SyncAcrossDevices(userID uint, event *SyncEvent) error {
    // Get all user's devices
    devices := s.getUserDevices(userID)
    
    // Publish to Redis for cross-server sync
    channel := fmt.Sprintf("user:%d:sync", userID)
    eventData, _ := json.Marshal(event)
    
    err := s.redis.Publish(context.Background(), channel, eventData).Err()
    if err != nil {
        return err
    }
    
    // Local delivery to connected devices
    s.mu.RLock()
    defer s.mu.RUnlock()
    
    for _, device := range devices {
        if socket, exists := s.sockets[device.SocketID]; exists {
            socket.Emit("notification:sync", event)
        }
    }
    
    return nil
}

// Handle notification read on one device
func (s *NotificationSyncService) HandleNotificationRead(
    userID uint, 
    deviceID string, 
    notificationIDs []string,
) error {
    event := &SyncEvent{
        Type:      "read",
        UserID:    userID,
        DeviceID:  deviceID,
        Timestamp: time.Now(),
        Data: map[string]interface{}{
            "notification_ids": notificationIDs,
        },
    }
    
    return s.SyncAcrossDevices(userID, event)
}
```

## 8. Socket Optimization

### Connection Management
```go
// Implement connection pooling
type SocketConnectionPool struct {
    connections map[uint]*UserConnection
    maxConnections int
    mu sync.RWMutex
}

func NewSocketConnectionPool(maxConnections int) *SocketConnectionPool {
    pool := &SocketConnectionPool{
        connections: make(map[uint]*UserConnection),
        maxConnections: maxConnections,
    }
    
    // Start cleanup goroutine
    go pool.cleanup()
    
    return pool
}

func (p *SocketConnectionPool) AddConnection(userID uint, socket socketio.Conn) {
    p.mu.Lock()
    defer p.mu.Unlock()
    
    if len(p.connections) >= p.maxConnections {
        p.removeOldestConnection()
    }
    
    p.connections[userID] = &UserConnection{
        UserID:   userID,
        Socket:   socket,
        LastSeen: time.Now(),
    }
}

func (p *SocketConnectionPool) removeOldestConnection() {
    var oldest uint
    var oldestTime time.Time = time.Now()
    
    for userID, conn := range p.connections {
        if conn.LastSeen.Before(oldestTime) {
            oldest = userID
            oldestTime = conn.LastSeen
        }
    }
    
    if oldest > 0 {
        p.connections[oldest].Socket.Close()
        delete(p.connections, oldest)
    }
}

func (p *SocketConnectionPool) cleanup() {
    ticker := time.NewTicker(5 * time.Minute)
    defer ticker.Stop()
    
    for range ticker.C {
        p.mu.Lock()
        cutoff := time.Now().Add(-30 * time.Minute)
        
        for userID, conn := range p.connections {
            if conn.LastSeen.Before(cutoff) {
                conn.Socket.Close()
                delete(p.connections, userID)
            }
        }
        p.mu.Unlock()
    }
}
```

### Event Batching
```go
// Batch multiple notifications
type NotificationBatcher struct {
    batch []interface{}
    batchSize int
    flushInterval time.Duration
    timer *time.Timer
    socket socketio.Conn
    mu sync.Mutex
}

func NewNotificationBatcher(socket socketio.Conn, batchSize int, flushInterval time.Duration) *NotificationBatcher {
    return &NotificationBatcher{
        batch: make([]interface{}, 0),
        batchSize: batchSize,
        flushInterval: flushInterval,
        socket: socket,
    }
}

func (b *NotificationBatcher) Add(notification interface{}) {
    b.mu.Lock()
    defer b.mu.Unlock()
    
    b.batch = append(b.batch, notification)
    
    if len(b.batch) >= b.batchSize {
        b.flush()
    } else if b.timer == nil {
        b.timer = time.AfterFunc(b.flushInterval, func() {
            b.mu.Lock()
            b.flush()
            b.mu.Unlock()
        })
    }
}

func (b *NotificationBatcher) flush() {
    if len(b.batch) > 0 {
        b.socket.Emit("notification:batch", b.batch)
        b.batch = b.batch[:0]
    }
    
    if b.timer != nil {
        b.timer.Stop()
        b.timer = nil
    }
}
```

## 9. Socket Monitoring

### Real-time Metrics
```go
type SocketMetrics struct {
    ActiveConnections int64
    TotalConnections  int64
    MessagesSent      int64
    MessagesReceived  int64
    ErrorCount        int64
    mu                sync.RWMutex
}

func (m *SocketMetrics) IncrementConnections() {
    m.mu.Lock()
    defer m.mu.Unlock()
    m.ActiveConnections++
    m.TotalConnections++
}

func (m *SocketMetrics) DecrementConnections() {
    m.mu.Lock()
    defer m.mu.Unlock()
    m.ActiveConnections--
}

func (m *SocketMetrics) IncrementMessagesSent() {
    m.mu.Lock()
    defer m.mu.Unlock()
    m.MessagesSent++
}

func (m *SocketMetrics) GetMetrics() map[string]int64 {
    m.mu.RLock()
    defer m.mu.RUnlock()
    
    return map[string]int64{
        "active_connections": m.ActiveConnections,
        "total_connections":  m.TotalConnections,
        "messages_sent":      m.MessagesSent,
        "messages_received":  m.MessagesReceived,
        "error_count":        m.ErrorCount,
    }
}
```

### Health Checks
```go
type SocketHealthChecker struct {
    server  *SocketServer
    metrics *SocketMetrics
}

func (h *SocketHealthChecker) CheckHealth() *HealthStatus {
    metrics := h.metrics.GetMetrics()
    
    status := &HealthStatus{
        Status: "healthy",
        Checks: make(map[string]interface{}),
    }
    
    // Check connection count
    if metrics["active_connections"] > 10000 {
        status.Status = "warning"
        status.Checks["connection_count"] = "high"
    }
    
    // Check error rate
    errorRate := float64(metrics["error_count"]) / float64(metrics["total_connections"])
    if errorRate > 0.05 {
        status.Status = "unhealthy"
        status.Checks["error_rate"] = errorRate
    }
    
    return status
}
```
# API Endpoints

## Tenant Notification Management

### 1. Tenant CMS Interface

```mermaid
flowchart TD
    A[Tenant CMS] --> B[Notification Dashboard]
    A --> C[Template Manager]
    A --> D[Channel Config]
    A --> E[Analytics]
    
    B --> B1[Notification List]
    B --> B2[Filters & Search]
    B --> B3[Bulk Actions]
    B --> B4[Export]
    
    C --> C1[Template CRUD]
    C --> C2[Template Testing]
    C --> C3[Variable Manager]
    C --> C4[Version Control]
    
    D --> D1[Email Config]
    D --> D2[SMS Config]
    D --> D3[Push Config]
    D --> D4[Channel Rules]
    
    E --> E1[Delivery Stats]
    E --> E2[Engagement Metrics]
    E --> E3[Channel Performance]
    E --> E4[User Preferences]
```

### 2. Tenant Notification API

#### List Notifications
```http
GET /cms/v1/notifications?cursor=abc123&limit=50&status=sent&channel=email&date_from=2024-07-01
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Notifications retrieved successfully",
    "success": true,
    "path": "/cms/v1/notifications",
    "timestamp": "2024-07-15T10:30:00Z"
  },
  "data": [
    {
      "id": "notif_abc123",
      "type": "user_welcome",
      "channel": "email",
      "status": "delivered",
      "priority": "normal",
      "recipients": {
        "total": 1,
        "delivered": 1,
        "read": 1,
        "failed": 0
      },
      "data": {
        "user_name": "John Doe",
        "user_email": "<EMAIL>"
      },
      "scheduled_at": null,
      "sent_at": "2024-07-15T10:00:00Z",
      "created_at": "2024-07-15T09:59:00Z"
    }
  ],
  "meta": {
    "next_cursor": "xyz789",
    "has_more": true,
    "total_count": 1250
  }
}
```

#### Get Notification Details
```http
GET /cms/v1/notifications/{notification_id}
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Notification details retrieved",
    "success": true
  },
  "data": {
    "id": "notif_abc123",
    "type": "user_welcome",
    "channel": "email",
    "template_id": "tmpl_welcome_v2",
    "recipients": [
      {
        "user_id": 123,
        "email": "<EMAIL>",
        "status": "delivered",
        "sent_at": "2024-07-15T10:00:00Z",
        "delivered_at": "2024-07-15T10:00:15Z",
        "read_at": "2024-07-15T10:05:00Z",
        "delivery_info": {
          "smtp_response": "250 OK",
          "message_id": "<<EMAIL>>"
        }
      }
    ],
    "events": [
      {
        "type": "queued",
        "timestamp": "2024-07-15T09:59:00Z"
      },
      {
        "type": "sent",
        "timestamp": "2024-07-15T10:00:00Z"
      },
      {
        "type": "delivered",
        "timestamp": "2024-07-15T10:00:15Z"
      },
      {
        "type": "opened",
        "timestamp": "2024-07-15T10:05:00Z"
      }
    ]
  }
}
```

#### Create Notification
```http
POST /cms/v1/notifications
Content-Type: application/json

{
  "type": "custom_announcement",
  "channel": "multi",
  "channels": ["email", "socket", "push"],
  "priority": "high",
  "template_id": "tmpl_announcement",
  "recipients": {
    "type": "segment",
    "segment_id": "active_users"
  },
  "data": {
    "title": "New Feature Launch",
    "message": "Check out our new analytics dashboard!",
    "cta_url": "https://app.yourblog.com/analytics",
    "cta_text": "View Dashboard"
  },
  "scheduled_at": "2024-07-16T14:00:00Z"
}
```

### 3. Tenant Configuration

```yaml
tenant_notification_config:
  tenant_id: 123
  
  channels:
    email:
      enabled: true
      provider: "smtp"
      settings:
        host: "smtp.tenant-domain.com"
        port: 587
        username: "<EMAIL>"
        password: "encrypted_password"
        from_email: "<EMAIL>"
        from_name: "Tenant Brand Name"
      
    socket:
      enabled: true
      namespace: "/tenant-123"
      
    push:
      enabled: true
      fcm:
        server_key: "tenant_fcm_key"
      apns:
        cert_path: "/certs/tenant_123_apns.p12"
        
    sms:
      enabled: false
      provider: "twilio"
      settings:
        account_sid: "tenant_twilio_sid"
        auth_token: "tenant_twilio_token"
        from_number: "+**********"
  
  preferences:
    batch_size: 100
    retry_attempts: 3
    retry_delay: 300 # seconds
    
  templates:
    allow_custom: true
    require_approval: false
    variable_whitelist: ["user", "tenant", "brand", "data"]
    
  analytics:
    track_opens: true
    track_clicks: true
    retention_days: 90
```

## Template Management

### List Templates
```http
GET /cms/v1/notification-templates?channel=email&active=true
```

### Create Template
```http
POST /cms/v1/notification-templates
Content-Type: application/json

{
  "code": "order_confirmation",
  "type": "transactional",
  "channel": "email",
  "versions": [
    {
      "language": "en",
      "subject": "Order #{{order.number}} Confirmed",
      "body_html": "<html>...</html>",
      "body_text": "Plain text version...",
      "variables": ["order", "user", "items"]
    }
  ]
}
```

### Test Template
```http
POST /cms/v1/notification-templates/{template_id}/test
Content-Type: application/json

{
  "recipient": "<EMAIL>",
  "language": "en",
  "test_data": {
    "order": {
      "number": "TEST-12345",
      "total": 99.99
    },
    "user": {
      "name": "Test User"
    }
  }
}
```

## Channel Configuration

### Update Channel Config
```http
PUT /cms/v1/notification-channels/email
Content-Type: application/json

{
  "enabled": true,
  "provider": "smtp",
  "settings": {
    "host": "smtp.example.com",
    "port": 587,
    "username": "<EMAIL>",
    "from_email": "<EMAIL>",
    "from_name": "Your Brand"
  }
}
```

## Analytics

### Get Notification Analytics
```http
GET /cms/v1/notifications/analytics?period=30d&channel=email
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Analytics retrieved successfully",
    "success": true
  },
  "data": {
    "summary": {
      "total_sent": 15420,
      "total_delivered": 15201,
      "total_opened": 8234,
      "total_clicked": 3421,
      "delivery_rate": 98.58,
      "open_rate": 54.15,
      "click_rate": 22.51
    },
    "by_channel": {
      "email": {
        "sent": 10234,
        "delivered": 10100,
        "opened": 6234,
        "clicked": 2834
      },
      "socket": {
        "sent": 3421,
        "delivered": 3401,
        "read": 2987
      },
      "push": {
        "sent": 1765,
        "delivered": 1700,
        "opened": 1013
      }
    },
    "by_type": {
      "user_welcome": {
        "sent": 342,
        "delivery_rate": 99.1,
        "open_rate": 67.8
      },
      "order_confirmation": {
        "sent": 1523,
        "delivery_rate": 98.9,
        "open_rate": 82.3
      }
    },
    "trends": [
      {
        "date": "2024-07-01",
        "sent": 523,
        "delivered": 515,
        "opened": 287
      }
    ]
  }
}
```

## API Authentication

### JWT Token Authentication
```go
type APIAuth struct {
    jwtSecret string
    redis     *redis.Client
}

func (a *APIAuth) ValidateToken(tokenString string) (*User, error) {
    token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
        return []byte(a.jwtSecret), nil
    })
    
    if err != nil {
        return nil, err
    }
    
    if !token.Valid {
        return nil, errors.New("invalid token")
    }
    
    claims, ok := token.Claims.(jwt.MapClaims)
    if !ok {
        return nil, errors.New("invalid token claims")
    }
    
    userID, ok := claims["user_id"].(float64)
    if !ok {
        return nil, errors.New("invalid user_id in token")
    }
    
    // Load user from database
    user, err := a.loadUser(uint(userID))
    if err != nil {
        return nil, err
    }
    
    return user, nil
}
```

### Rate Limiting
```go
type RateLimiter struct {
    redis *redis.Client
    rules map[string]*RateRule
}

type RateRule struct {
    Requests int
    Window   time.Duration
}

func (r *RateLimiter) Allow(key string, rule *RateRule) bool {
    current := r.redis.Incr(context.Background(), key).Val()
    
    if current == 1 {
        r.redis.Expire(context.Background(), key, rule.Window)
    }
    
    return current <= int64(rule.Requests)
}

// Usage in middleware
func (r *RateLimiter) Middleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        userID := c.GetString("user_id")
        key := fmt.Sprintf("rate_limit:%s", userID)
        
        rule := &RateRule{
            Requests: 100,
            Window:   time.Minute,
        }
        
        if !r.Allow(key, rule) {
            c.JSON(429, gin.H{
                "error": "Rate limit exceeded",
                "retry_after": 60,
            })
            c.Abort()
            return
        }
        
        c.Next()
    }
}
```

### API Response Format
```go
type APIResponse struct {
    Status ResponseStatus `json:"status"`
    Data   interface{}    `json:"data,omitempty"`
    Meta   interface{}    `json:"meta,omitempty"`
    Errors []APIError     `json:"errors,omitempty"`
}

type ResponseStatus struct {
    Code      int       `json:"code"`
    Message   string    `json:"message"`
    Success   bool      `json:"success"`
    Path      string    `json:"path"`
    Timestamp time.Time `json:"timestamp"`
}

type APIError struct {
    Code    string `json:"code"`
    Message string `json:"message"`
    Field   string `json:"field,omitempty"`
}

func SuccessResponse(data interface{}) *APIResponse {
    return &APIResponse{
        Status: ResponseStatus{
            Code:      200,
            Message:   "Success",
            Success:   true,
            Timestamp: time.Now(),
        },
        Data: data,
    }
}

func ErrorResponse(code int, message string) *APIResponse {
    return &APIResponse{
        Status: ResponseStatus{
            Code:      code,
            Message:   message,
            Success:   false,
            Timestamp: time.Now(),
        },
    }
}
```
# Push Notifications - FCM/APNS Integration

This document describes the Firebase Cloud Messaging (FCM) and Apple Push Notification Service (APNS) integration in the notification module.

## Overview

The push notification system supports:
- **Firebase Cloud Messaging (FCM)** for Android and iOS devices
- **Apple Push Notification Service (APNS)** for iOS devices
- **Hybrid Provider** that combines both FCM and APNS
- **Mock Provider** for testing and development

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Notification   │───▶│ DeliveryService  │───▶│  PushProvider   │
│     Model       │    │                  │    │   (Interface)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                        │
                                        ┌───────────────┼───────────────┐
                                        │               │               │
                                   ┌─────▼─────┐   ┌───▼────┐   ┌──────▼──────┐
                                   │FCMProvider│   │APNS    │   │Hybrid       │
                                   │           │   │Provider│   │Provider     │
                                   └───────────┘   └────────┘   └─────────────┘
```

## Configuration

### Environment Variables

Create a `.env` file with the following variables:

```bash
# Push Notification Configuration
PUSH_ENABLED=true
PUSH_PROVIDER=hybrid  # fcm, apns, or hybrid

# Firebase Cloud Messaging (FCM)
FCM_SERVICE_ACCOUNT_PATH=/path/to/firebase-service-account.json
FCM_PROJECT_ID=your-firebase-project-id
FCM_DEFAULT_ICON=@drawable/ic_notification
FCM_DEFAULT_SOUND=default
FCM_DEFAULT_CLICK_ACTION=FLUTTER_NOTIFICATION_CLICK
FCM_ENABLE_ANALYTICS=true
FCM_DRY_RUN=false

# Apple Push Notification Service (APNS)
APNS_AUTH_KEY_PATH=/path/to/AuthKey_XXXXXXXXXX.p8
APNS_KEY_ID=XXXXXXXXXX
APNS_TEAM_ID=XXXXXXXXXX
APNS_BUNDLE_ID=com.yourcompany.yourapp
APNS_PRODUCTION=false
APNS_DEFAULT_SOUND=default
```

### Firebase Setup

1. **Create Firebase Project**: Go to [Firebase Console](https://console.firebase.google.com/)
2. **Generate Service Account Key**:
   - Go to Project Settings → Service Accounts
   - Generate new private key (JSON format)
   - Save as `firebase-service-account.json`
3. **Enable FCM**: Firebase Cloud Messaging is enabled by default

### Apple Push Notification Setup

1. **Apple Developer Account**: Requires paid Apple Developer Program membership
2. **Create App ID**: Register your app in Apple Developer Portal
3. **Enable Push Notifications**: In App ID configuration
4. **Create APNs Key** (Token-based authentication - recommended):
   - Go to Certificates, Identifiers & Profiles
   - Create new APNs Auth Key
   - Download `.p8` file
5. **Alternative: Create APNs Certificate** (Certificate-based authentication):
   - Create Certificate Signing Request (CSR)
   - Generate APNs certificate
   - Export as `.p12` file

## API Endpoints

### Send Push Notification

```http
POST /api/cms/v1/notifications
Content-Type: application/json
Authorization: Bearer {token}
X-Tenant-ID: {tenant_id}

{
  "type": "marketing",
  "channel": "push",
  "priority": "high",
  "subject": "New Message",
  "recipients": [
    {
      "recipient_type": "device",
      "recipient_address": "device_token_here",
      "device_token": "fcm_or_apns_token_here"
    }
  ],
  "metadata": {
    "sound": "default",
    "badge": 1,
    "platform": "ios",
    "collapse_id": "message_update"
  }
}
```

### Test Push Notification

```http
POST /api/cms/v1/push/test
Content-Type: application/json

{
  "device_token": "your_device_token",
  "platform": "ios",
  "title": "Test Notification",
  "body": "This is a test push notification",
  "data": {
    "custom_key": "custom_value"
  },
  "sound": "default",
  "badge": 1
}
```

### Update Device Token

```http
POST /api/cms/v1/push/webhooks/token-update
Content-Type: application/json

{
  "old_token": "old_device_token",
  "new_token": "new_device_token",
  "platform": "ios"
}
```

## Webhook Endpoints

### FCM Webhook

```http
POST /api/cms/v1/push/webhooks/fcm
Content-Type: application/json

{
  "message_type": "send_event",
  "message_id": "0:**********",
  "from": "sender_id",
  "to": "device_token",
  "event_type": "delivered",
  "data": {
    "notification_id": "123",
    "recipient_id": "456"
  },
  "timestamp": "2023-07-25T10:30:00Z"
}
```

### APNS Webhook

```http
POST /api/cms/v1/push/webhooks/apns
Content-Type: application/json

{
  "event_type": "delivered",
  "apns_id": "12345678-1234-1234-1234-**********12",
  "device_token": "device_token_here",
  "timestamp": "2023-07-25T10:30:00Z"
}
```

## Features

### FCM Provider Features

- ✅ Single device messaging
- ✅ Multicast messaging (up to 500 devices)
- ✅ Topic messaging
- ✅ Token validation
- ✅ Android and iOS support
- ✅ Custom data payload
- ✅ Analytics integration
- ✅ Delivery receipts

### APNS Provider Features

- ✅ Single device messaging
- ✅ Batch processing for multiple devices
- ✅ Token validation
- ✅ iOS-specific features (badge, sound, etc.)
- ✅ Custom data payload
- ✅ Priority settings
- ✅ Collapse ID support
- ❌ Topic messaging (not natively supported)

### Hybrid Provider Features

- ✅ Automatic platform detection
- ✅ FCM for Android devices
- ✅ APNS for iOS devices
- ✅ Fallback mechanisms
- ✅ Cross-platform topic messaging (FCM only)

## Usage Examples

### Go Code Example

```go
// Create notification
notification := &models.Notification{
    TenantID: 1,
    Type:     "alert",
    Channel:  models.ChannelPush,
    Priority: models.PriorityHigh,
    Subject:  "Important Update",
}

// Create recipient
recipient := &models.NotificationRecipient{
    TenantID:         1,
    RecipientType:    models.RecipientTypeDevice,
    RecipientAddress: "device_token_here",
    DeviceToken:      &deviceToken,
}

// Send via push provider
metadata := map[string]interface{}{
    "platform": "ios",
    "sound":    "default",
    "badge":    1,
}

err := pushProvider.SendPush(recipient, notification, "Your message here", metadata)
if err != nil {
    log.Printf("Failed to send push: %v", err)
}
```

### Flutter/Mobile Integration

```dart
// Initialize Firebase Messaging
FirebaseMessaging messaging = FirebaseMessaging.instance;

// Request permissions
NotificationSettings settings = await messaging.requestPermission(
  alert: true,
  badge: true,
  sound: true,
);

// Get device token
String? token = await messaging.getToken();

// Send token to your backend
await api.updateDeviceToken(token);

// Handle foreground messages
FirebaseMessaging.onMessage.listen((RemoteMessage message) {
  showNotification(message);
});

// Handle background messages
FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
```

## Testing

### Local Testing with Mock Provider

```bash
# Disable push notifications for local testing
PUSH_ENABLED=false
```

### Testing with Real Devices

```bash
# Enable push notifications
PUSH_ENABLED=true
PUSH_PROVIDER=fcm

# Test endpoint
curl -X POST http://localhost:8080/api/cms/v1/push/test \
  -H "Content-Type: application/json" \
  -d '{
    "device_token": "your_test_token",
    "platform": "android",
    "title": "Test",
    "body": "Test message"
  }'
```

## Monitoring and Analytics

### Delivery Tracking

The system automatically tracks:
- **Sent**: Message sent to provider
- **Delivered**: Confirmed delivery (via webhooks)
- **Failed**: Delivery failures with error details

### Logs

All push notification activities are logged:

```go
log := models.CreateSentLog(tenantID, notificationID, recipientID, map[string]interface{}{
    "provider":    "fcm",
    "message_id":  "fcm_message_id",
    "device_token": deviceToken,
    "status":      "sent",
})
```

### Analytics Integration

- FCM analytics can be enabled via `FCM_ENABLE_ANALYTICS=true`
- Custom event tracking through metadata
- Delivery metrics and engagement data

## Error Handling

### Common Error Types

1. **Invalid Token**: Device token is invalid or expired
2. **Quota Exceeded**: Rate limits exceeded
3. **Network Error**: Connection issues with providers
4. **Configuration Error**: Missing or invalid credentials

### Error Recovery

- Automatic retries for transient failures
- Token refresh handling
- Graceful degradation to mock provider

## Security Considerations

1. **Service Account Keys**: Secure storage of Firebase service account files
2. **APNS Keys**: Protect `.p8` auth keys and certificate files
3. **Device Tokens**: Encrypt device tokens in database
4. **Webhook Validation**: Verify webhook authenticity
5. **Rate Limiting**: Implement rate limits for webhook endpoints

## Troubleshooting

### Common Issues

1. **FCM Authentication Failed**:
   - Verify service account JSON file path
   - Check project ID matches Firebase project

2. **APNS Connection Failed**:
   - Verify auth key path and permissions
   - Check bundle ID matches app configuration
   - Confirm production/sandbox environment

3. **Device Token Invalid**:
   - Tokens expire and need refreshing
   - Implement token update mechanism

4. **Messages Not Delivered**:
   - Check device network connectivity
   - Verify app is registered for push notifications
   - Review provider-specific logs

### Debug Mode

Enable debug logging:

```bash
LOG_LEVEL=debug
```

This will log detailed information about push notification processing.

## Future Enhancements

- [ ] Web Push notification support
- [ ] Rich media attachments
- [ ] A/B testing for push campaigns
- [ ] Advanced analytics and reporting
- [ ] Push notification templates
- [ ] Scheduled push notifications
- [ ] Geographic targeting
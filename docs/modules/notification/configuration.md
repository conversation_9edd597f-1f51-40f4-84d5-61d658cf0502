# Configuration

## Module Configuration

```yaml
notification:
  enabled: true
  
  channels:
    email:
      enabled: true
      default_provider: "smtp"
      providers:
        smtp:
          pool: true
          pool_size: 5
          rate_limit: 100/minute
        sendgrid:
          api_key: "${SENDGRID_API_KEY}"
          
    socket:
      enabled: true
      adapter: "redis"
      namespaces:
        - "/notifications"
        - "/tenant-notifications"
        
    push:
      enabled: true
      providers:
        fcm:
          enabled: true
        apns:
          enabled: true
          
  queue:
    provider: "redis"
    prefix: "notification:"
    priorities:
      urgent: 1
      high: 2
      normal: 3
      low: 4
      
  batch:
    enabled: true
    max_size: 1000
    timeout: 5000
    
  retry:
    max_attempts: 3
    backoff: "exponential"
    delays: [1000, 5000, 15000]
    
  tracking:
    open_tracking: true
    click_tracking: true
    pixel_url: "https://track.yourdomain.com/pixel"
    
  storage:
    retention_days: 90
    archive_after_days: 30
    
  templates:
    cache_ttl: 3600
    preview_data: "fixtures/preview-data.json"
```

## Environment Variables

```bash
# Database
DATABASE_URL=mysql://user:password@localhost:3306/database

# Redis
REDIS_URL=redis://localhost:6379/0

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# SendGrid (alternative)
SENDGRID_API_KEY=your-sendgrid-api-key

# Push Notifications
FCM_SERVER_KEY=your-fcm-server-key
APNS_CERT_PATH=/path/to/apns-cert.p12
APNS_KEY_ID=your-apns-key-id
APNS_TEAM_ID=your-apns-team-id

# Socket.IO
SOCKET_PORT=3001
SOCKET_CORS_ORIGINS=http://localhost:3000,https://yourdomain.com

# Webhook Settings
WEBHOOK_SECRET=your-webhook-secret
WEBHOOK_TIMEOUT=30s

# Monitoring
METRICS_PORT=9090
PROMETHEUS_ENDPOINT=/metrics

# Logging
LOG_LEVEL=info
LOG_FORMAT=json
```

## Docker Configuration

```dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN go build -o notification-service ./cmd/notification

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/notification-service .
COPY --from=builder /app/config ./config

EXPOSE 8080 3001 9090

CMD ["./notification-service"]
```

## Docker Compose

```yaml
version: '3.8'

services:
  notification-service:
    build: .
    ports:
      - "8080:8080"
      - "3001:3001"
      - "9090:9090"
    environment:
      - DATABASE_URL=mysql://user:password@mysql:3306/notifications
      - REDIS_URL=redis://redis:6379/0
      - SMTP_HOST=smtp.gmail.com
      - SMTP_PORT=587
    depends_on:
      - mysql
      - redis
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: notifications
      MYSQL_USER: user
      MYSQL_PASSWORD: password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./migrations:/docker-entrypoint-initdb.d

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  prometheus:
    image: prom/prometheus
    ports:
      - "9091:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

volumes:
  mysql_data:
  redis_data:
```

## Kubernetes Configuration

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: notification-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: notification-service
  template:
    metadata:
      labels:
        app: notification-service
    spec:
      containers:
      - name: notification-service
        image: notification-service:latest
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 3001
          name: socket
        - containerPort: 9090
          name: metrics
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: notification-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: notification-config
              key: redis-url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: notification-service
spec:
  selector:
    app: notification-service
  ports:
  - name: http
    port: 80
    targetPort: 8080
  - name: socket
    port: 3001
    targetPort: 3001
  type: ClusterIP

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: notification-config
data:
  redis-url: "redis://redis-service:6379/0"
  socket-cors-origins: "https://yourdomain.com"
  webhook-timeout: "30s"

---
apiVersion: v1
kind: Secret
metadata:
  name: notification-secrets
type: Opaque
data:
  database-url: <base64-encoded-url>
  smtp-password: <base64-encoded-password>
  sendgrid-api-key: <base64-encoded-key>
```

## Health Checks

```go
type HealthChecker struct {
    db    *sql.DB
    redis *redis.Client
}

func (h *HealthChecker) CheckHealth() map[string]interface{} {
    health := map[string]interface{}{
        "status": "healthy",
        "timestamp": time.Now(),
        "checks": make(map[string]interface{}),
    }
    
    // Database check
    if err := h.db.Ping(); err != nil {
        health["status"] = "unhealthy"
        health["checks"]["database"] = map[string]interface{}{
            "status": "down",
            "error": err.Error(),
        }
    } else {
        health["checks"]["database"] = map[string]interface{}{
            "status": "up",
        }
    }
    
    // Redis check
    if err := h.redis.Ping(context.Background()).Err(); err != nil {
        health["status"] = "unhealthy"
        health["checks"]["redis"] = map[string]interface{}{
            "status": "down",
            "error": err.Error(),
        }
    } else {
        health["checks"]["redis"] = map[string]interface{}{
            "status": "up",
        }
    }
    
    return health
}
```

## Logging Configuration

```yaml
logging:
  level: info
  format: json
  outputs:
    - stdout
    - file
  file:
    path: /var/log/notification-service.log
    max_size: 100MB
    max_backups: 5
    max_age: 30
    compress: true
  structured_logging: true
  correlation_id: true
```

## Monitoring Configuration

```yaml
monitoring:
  metrics:
    enabled: true
    port: 9090
    path: /metrics
    
  tracing:
    enabled: true
    jaeger:
      endpoint: http://jaeger:14268/api/traces
      
  alerts:
    enabled: true
    rules:
      - name: high_error_rate
        query: rate(notification_errors[5m]) > 0.1
        duration: 5m
        severity: warning
        
      - name: queue_depth_high
        query: notification_queue_depth > 1000
        duration: 2m
        severity: critical
```

## Security Configuration

```yaml
security:
  authentication:
    jwt:
      secret: "${JWT_SECRET}"
      expiration: 24h
      
  authorization:
    rbac:
      enabled: true
      
  encryption:
    at_rest:
      enabled: true
      key: "${ENCRYPTION_KEY}"
      
  rate_limiting:
    enabled: true
    requests_per_minute: 100
    burst: 20
    
  cors:
    allowed_origins:
      - "https://yourdomain.com"
      - "http://localhost:3000"
    allowed_methods:
      - GET
      - POST
      - PUT
      - DELETE
    allowed_headers:
      - Authorization
      - Content-Type
```
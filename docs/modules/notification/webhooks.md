# Webhook & Integration System

## 1. Webhook Architecture

```mermaid
flowchart TD
    A[Webhook System] --> B[Event Manager]
    A --> C[Endpoint Registry]
    A --> D[Delivery Engine]
    A --> E[Integration Hub]
    
    B --> B1[Event Types]
    B --> B2[Event Filtering]
    B --> B3[Event Transformation]
    B --> B4[Event History]
    
    C --> C1[Tenant Endpoints]
    C --> C2[Third-party Services]
    C --> C3[Custom Webhooks]
    C --> C4[Endpoint Validation]
    
    D --> D1[HTTP Delivery]
    D --> D2[Retry Logic]
    D --> D3[Circuit Breaker]
    D --> D4[Delivery Monitoring]
    
    E --> E1[Slack Integration]
    E --> E2[Discord Integration]
    E --> E3[Teams Integration]
    E --> E4[Custom Integrations]
```

## 2. Webhook Event Types

```yaml
webhook_events:
  notification:
    - notification.created
    - notification.queued
    - notification.sent
    - notification.delivered
    - notification.failed
    - notification.opened
    - notification.clicked
    - notification.unsubscribed
    
  email:
    - email.sent
    - email.delivered
    - email.bounced
    - email.complained
    - email.opened
    - email.clicked
    
  push:
    - push.sent
    - push.delivered
    - push.failed
    - push.opened
    
  sms:
    - sms.sent
    - sms.delivered
    - sms.failed
```

## 3. Webhook Endpoint Configuration

```go
type WebhookEndpoint struct {
    ID              uint      `gorm:"primarykey" json:"id"`
    TenantID        uint      `gorm:"not null;index" json:"tenant_id"`
    Name            string    `gorm:"not null" json:"name"`
    URL             string    `gorm:"not null" json:"url"`
    Secret          string    `json:"-"` // For HMAC-SHA256 signature
    Events          []string  `gorm:"type:json" json:"events"`
    Headers         JSON      `gorm:"type:json" json:"headers"`
    
    // Retry configuration
    MaxRetries      int       `gorm:"default:3" json:"max_retries"`
    TimeoutSeconds  int       `gorm:"default:30" json:"timeout_seconds"`
    RetryStrategy   string    `gorm:"default:'exponential'" json:"retry_strategy"`
    
    // Circuit breaker
    FailureThreshold int      `gorm:"default:5" json:"failure_threshold"`
    RecoveryTimeout  int      `gorm:"default:300" json:"recovery_timeout"`
    
    // Status
    IsActive        bool      `gorm:"default:true" json:"is_active"`
    LastError       string    `json:"last_error,omitempty"`
    LastSuccessAt   *time.Time `json:"last_success_at,omitempty"`
    LastFailureAt   *time.Time `json:"last_failure_at,omitempty"`
    
    CreatedAt       time.Time `json:"created_at"`
    UpdatedAt       time.Time `json:"updated_at"`
}
```

## 4. Webhook Delivery Flow

```mermaid
sequenceDiagram
    participant Event as Event Source
    participant Manager as Webhook Manager
    participant Queue as Delivery Queue
    participant Delivery as Delivery Service
    participant Endpoint as External Endpoint
    participant Monitor as Monitoring
    
    Event->>Manager: Trigger event
    Manager->>Manager: Filter subscribed endpoints
    Manager->>Queue: Queue delivery jobs
    
    Queue->>Delivery: Process delivery
    Delivery->>Delivery: Generate HMAC signature
    Delivery->>Endpoint: POST webhook payload
    
    alt Success
        Endpoint->>Delivery: 200 OK
        Delivery->>Monitor: Record success
    else Failure
        Endpoint->>Delivery: Error response
        Delivery->>Queue: Retry with backoff
        Delivery->>Monitor: Record failure
        
        alt Max retries reached
            Delivery->>Manager: Mark endpoint unhealthy
            Manager->>Manager: Activate circuit breaker
        end
    end
```

## 5. Third-party Integrations

### Slack Integration
```go
type SlackIntegration struct {
    WebhookURL string
    Channel    string
    Username   string
    IconURL    string
}

func (s *SlackService) SendNotification(notification *Notification) error {
    payload := SlackPayload{
        Channel: s.Channel,
        Username: s.Username,
        IconURL: s.IconURL,
        Attachments: []SlackAttachment{{
            Color: s.getColorByType(notification.Type),
            Title: notification.Title,
            Text: notification.Message,
            Fields: []SlackField{
                {Title: "Type", Value: notification.Type, Short: true},
                {Title: "Priority", Value: notification.Priority, Short: true},
            },
            Timestamp: notification.CreatedAt.Unix(),
        }},
    }
    
    return s.httpClient.PostJSON(s.WebhookURL, payload)
}
```

### Discord Integration
```go
type DiscordIntegration struct {
    WebhookURL string
    Username   string
    AvatarURL  string
}

func (d *DiscordService) SendNotification(notification *Notification) error {
    embed := DiscordEmbed{
        Title:       notification.Title,
        Description: notification.Message,
        Color:       d.getColorByType(notification.Type),
        Timestamp:   notification.CreatedAt.Format(time.RFC3339),
        Fields: []DiscordField{
            {Name: "Type", Value: notification.Type, Inline: true},
            {Name: "Priority", Value: notification.Priority, Inline: true},
        },
    }
    
    payload := DiscordPayload{
        Username:  d.Username,
        AvatarURL: d.AvatarURL,
        Embeds:    []DiscordEmbed{embed},
    }
    
    return d.httpClient.PostJSON(d.WebhookURL, payload)
}
```

### Microsoft Teams Integration
```go
type TeamsIntegration struct {
    WebhookURL string
}

func (t *TeamsService) SendNotification(notification *Notification) error {
    card := TeamsAdaptiveCard{
        Type: "AdaptiveCard",
        Version: "1.2",
        Body: []TeamsCardElement{
            {
                Type: "TextBlock",
                Text: notification.Title,
                Size: "Large",
                Weight: "Bolder",
            },
            {
                Type: "TextBlock",
                Text: notification.Message,
                Wrap: true,
            },
        },
        Actions: t.buildActions(notification),
    }
    
    payload := TeamsPayload{
        Type: "message",
        Attachments: []TeamsAttachment{{
            ContentType: "application/vnd.microsoft.card.adaptive",
            Content: card,
        }},
    }
    
    return t.httpClient.PostJSON(t.WebhookURL, payload)
}
```

## 6. Webhook Security

### HMAC Signature Generation
```go
func (w *WebhookService) generateSignature(payload []byte, secret string) string {
    h := hmac.New(sha256.New, []byte(secret))
    h.Write(payload)
    return hex.EncodeToString(h.Sum(nil))
}

func (w *WebhookService) deliverWebhook(endpoint *WebhookEndpoint, event *WebhookEvent) error {
    payload, _ := json.Marshal(event)
    signature := w.generateSignature(payload, endpoint.Secret)
    
    headers := map[string]string{
        "Content-Type": "application/json",
        "X-Webhook-Signature": signature,
        "X-Webhook-Event": event.Type,
        "X-Webhook-Delivery": uuid.New().String(),
    }
    
    // Merge custom headers
    for k, v := range endpoint.Headers {
        headers[k] = v.(string)
    }
    
    return w.httpClient.PostWithHeaders(endpoint.URL, payload, headers)
}
```

## 7. Retry Strategy

```go
type RetryStrategy interface {
    NextDelay(attempt int) time.Duration
}

type ExponentialBackoff struct {
    BaseDelay   time.Duration
    MaxDelay    time.Duration
    Multiplier  float64
}

func (e *ExponentialBackoff) NextDelay(attempt int) time.Duration {
    delay := float64(e.BaseDelay) * math.Pow(e.Multiplier, float64(attempt-1))
    if delay > float64(e.MaxDelay) {
        return e.MaxDelay
    }
    
    // Add jitter to prevent thundering herd
    jitter := rand.Float64() * 0.1 * delay
    return time.Duration(delay + jitter)
}

// Usage in webhook delivery
func (w *WebhookService) deliverWithRetry(endpoint *WebhookEndpoint, event *WebhookEvent) error {
    strategy := &ExponentialBackoff{
        BaseDelay:  time.Second,
        MaxDelay:   time.Minute * 5,
        Multiplier: 2.0,
    }
    
    var lastErr error
    for attempt := 1; attempt <= endpoint.MaxRetries; attempt++ {
        err := w.deliverWebhook(endpoint, event)
        if err == nil {
            return nil
        }
        
        lastErr = err
        if attempt < endpoint.MaxRetries {
            delay := strategy.NextDelay(attempt)
            time.Sleep(delay)
        }
    }
    
    return fmt.Errorf("webhook delivery failed after %d attempts: %w", 
        endpoint.MaxRetries, lastErr)
}
```
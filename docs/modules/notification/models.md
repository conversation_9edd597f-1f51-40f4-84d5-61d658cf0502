# Notification Models

## Overview

The notification system provides a comprehensive multi-channel notification infrastructure with support for templates, preferences, delivery tracking, and analytics. The system is designed to handle email, push notifications, SMS, and real-time socket notifications with tenant isolation and scalable delivery mechanisms.

## Core Models

### Notification
Central entity representing a notification message with metadata for delivery, scheduling, and tracking.

**Key Features:**
- Multi-channel support (email, push, SMS, socket)
- Priority-based delivery (low, normal, high, urgent)
- Scheduled notifications with delayed delivery
- Status tracking throughout delivery lifecycle
- Tenant isolation for multi-tenant environments

**Implementation:** See `internal/modules/notification/models/notification.go`

### NotificationRecipient
Represents individual recipients of notifications with delivery status and channel-specific addresses.

**Key Features:**
- User-specific delivery tracking
- Channel address management (email, phone, device token)
- Delivery confirmation and read receipts
- Failure handling and retry logic
- Detailed delivery information storage

**Implementation:** See `internal/modules/notification/models/recipient.go`

### NotificationTemplate
Template system for consistent notification formatting across channels and languages.

**Key Features:**
- Multi-channel template support
- Version management for template updates
- Variable substitution and personalization
- Template activation/deactivation
- Template configuration per tenant

**Implementation:** See `internal/modules/notification/models/template.go`

### NotificationTemplateVersion
Version-specific template content with language support and variable definitions.

**Key Features:**
- Multi-language template support
- HTML and text format support
- Variable definition and validation
- Version control for template updates
- Template content management

**Implementation:** See `internal/modules/notification/models/template_version.go`

### NotificationPreference
User preferences for notification channels and types with granular control.

**Key Features:**
- Channel-specific preferences (email, push, SMS)
- Type-specific preferences (marketing, transactional, system)
- Tenant-scoped preference management
- Preference configuration and settings
- Opt-in/opt-out functionality

**Implementation:** See `internal/modules/notification/models/preference.go`

### NotificationLog
Audit trail for notification events and delivery tracking.

**Key Features:**
- Event-based logging (created, sent, delivered, opened, clicked)
- Delivery analytics and engagement tracking
- Error logging and debugging information
- Performance monitoring and optimization
- Compliance and audit requirements

**Implementation:** See `internal/modules/notification/models/log.go`

### TenantNotificationConfig
Tenant-specific configuration for notification channels and credentials.

**Key Features:**
- Channel-specific credentials (SMTP, API keys, certificates)
- Tenant-isolated configuration management
- Channel activation/deactivation
- Configuration validation and testing
- Security and credential management

**Implementation:** See `internal/modules/notification/models/tenant_config.go`

## Model Relationships

### Core Relationships
- **Notification** → **NotificationRecipient** (One-to-Many)
- **NotificationTemplate** → **NotificationTemplateVersion** (One-to-Many)
- **NotificationRecipient** → **NotificationLog** (One-to-Many)
- **Notification** → **TenantNotificationConfig** (Many-to-One)

### Cross-Module Relationships
- **NotificationRecipient** → **User** (Many-to-One)
- **NotificationPreference** → **User** (Many-to-One)
- **Notification** → **Tenant** (Many-to-One)
- **NotificationTemplate** → **Tenant** (Many-to-One)

## Database Schema

### Migration Files
The notification system database schema is implemented through migration files:

- **Core Tables:** `internal/database/migrations/j_notification/`
  - `701_create_notifications_table.up.sql`
  - `702_create_notification_recipients_table.up.sql`
  - `703_create_notification_templates_table.up.sql`
  - `704_create_notification_template_versions_table.up.sql`
  - `705_create_notification_preferences_table.up.sql`
  - `706_create_notification_logs_table.up.sql`
  - `707_create_tenant_notification_configs_table.up.sql`

### Key Database Features
- **Tenant Isolation:** All tables include tenant_id with foreign key constraints
- **Performance Indexes:** Optimized indexes for common query patterns
- **JSON Storage:** Flexible data storage for configuration and metadata
- **Enum Constraints:** Status and type validation at database level
- **Cascade Deletes:** Proper cleanup when tenants or users are deleted

## Data Access Layer

### Repository Interfaces
The notification system uses repository pattern for data access:

- **NotificationRepository:** `internal/modules/notification/repositories/notification_repository.go`
- **RecipientRepository:** `internal/modules/notification/repositories/recipient_repository.go`
- **TemplateRepository:** `internal/modules/notification/repositories/template_repository.go`
- **PreferenceRepository:** `internal/modules/notification/repositories/preference_repository.go`
- **LogRepository:** `internal/modules/notification/repositories/log_repository.go`
- **ConfigRepository:** `internal/modules/notification/repositories/config_repository.go`

### Repository Implementation
- **GORM Integration:** All repositories use GORM for database operations
- **Tenant Scoping:** Automatic tenant filtering in all queries
- **Batch Operations:** Support for bulk operations and performance optimization
- **Transaction Support:** Atomic operations for data consistency
- **Error Handling:** Comprehensive error handling and logging

## Business Logic Layer

### Service Layer
The notification system business logic is organized into services:

- **NotificationService:** `internal/modules/notification/services/notification_service.go`
- **DeliveryService:** `internal/modules/notification/services/delivery_service.go`
- **TemplateService:** `internal/modules/notification/services/template_service.go`
- **PreferenceService:** `internal/modules/notification/services/preference_service.go`
- **AnalyticsService:** `internal/modules/notification/services/analytics_service.go`

### Service Features
- **Multi-Channel Delivery:** Unified interface for all notification channels
- **Template Processing:** Variable substitution and template rendering
- **Preference Management:** User preference enforcement and validation
- **Delivery Tracking:** Real-time status updates and analytics
- **Error Handling:** Retry logic and failure management

## API Layer

### HTTP Handlers
The notification system exposes RESTful APIs through handlers:

- **NotificationHandler:** `internal/modules/notification/handlers/notification_handler.go`
- **TemplateHandler:** `internal/modules/notification/handlers/template_handler.go`
- **PreferenceHandler:** `internal/modules/notification/handlers/preference_handler.go`
- **AnalyticsHandler:** `internal/modules/notification/handlers/analytics_handler.go`

### API Features
- **RESTful Design:** Standard HTTP methods and status codes
- **Tenant Isolation:** Automatic tenant scoping in all endpoints
- **Input Validation:** Request validation and error handling
- **Pagination:** Efficient data retrieval for large datasets
- **Rate Limiting:** Protection against abuse and overload

## Constants and Enums

### Status Values
- **Notification Status:** pending, queued, sent, delivered, failed, cancelled
- **Recipient Status:** pending, sent, delivered, read, failed, bounced, blocked
- **Template Types:** transactional, marketing, system, custom
- **Channels:** email, socket, push, sms
- **Priorities:** low, normal, high, urgent

### Event Types
- **Lifecycle Events:** created, queued, sent, delivered, opened, clicked
- **Error Events:** failed, bounced, complaint, unsubscribed
- **Analytics Events:** engagement tracking and performance metrics

## Validation Rules

### Model Validation
Each model implements validation rules for data integrity:

- **Required Fields:** Tenant ID, notification type, channel, recipient information
- **Format Validation:** Email addresses, phone numbers, template codes
- **Enum Validation:** Status values, priority levels, channel types
- **Relationship Validation:** Foreign key constraints and referential integrity
- **Business Rules:** Tenant isolation, user permissions, template activation

### Implementation Files
- **Validation Logic:** Implemented in individual model files
- **Custom Validators:** `internal/modules/notification/validators/`
- **Validation Rules:** Defined in service layer for business logic validation

## Performance Considerations

### Database Optimization
- **Composite Indexes:** Multi-column indexes for efficient tenant-scoped queries
- **JSON Indexing:** Indexes on JSON fields for configuration and metadata queries
- **Partitioning:** Table partitioning for large-scale notification logs
- **Query Optimization:** Efficient query patterns for high-throughput operations

### Caching Strategy
- **Template Caching:** In-memory caching for frequently used templates
- **Preference Caching:** User preference caching for fast delivery decisions
- **Configuration Caching:** Tenant configuration caching for channel setup
- **Analytics Caching:** Pre-computed metrics for dashboard performance

## Security Features

### Data Protection
- **Tenant Isolation:** Complete data separation between tenants
- **Access Control:** Role-based permissions for notification management
- **Credential Security:** Encrypted storage of channel credentials
- **Audit Logging:** Complete audit trail for compliance requirements

### Privacy Compliance
- **Data Retention:** Configurable retention policies for notification data
- **User Consent:** Preference management for privacy compliance
- **Data Anonymization:** User data anonymization for analytics
- **GDPR Compliance:** Support for data export and deletion requests
# Email Template System

## 1. Template Architecture

```mermaid
flowchart TD
    A[Email Template System] --> B[Template Engine]
    A --> C[Template Storage]
    A --> D[Template Builder]
    A --> E[Preview System]
    
    B --> B1[Handlebars Engine]
    B --> B2[Variable Processing]
    B --> B3[Layout System]
    B --> B4[Asset Management]
    
    C --> C1[Database Storage]
    C --> C2[File Storage]
    C --> C3[Version Control]
    C --> C4[Template Cache]
    
    D --> D1[Visual Editor]
    D --> D2[Code Editor]
    D --> D3[Component Library]
    D --> D4[Preview Mode]
    
    E --> E1[Device Preview]
    E --> E2[Email Client Preview]
    E --> E3[Variable Testing]
    E --> E4[Send Test Email]
```

## 2. Template Structure

### Base Email Layout
```html
<!DOCTYPE html>
<html lang="{{language}}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{subject}}</title>
    <style>
        /* Reset styles */
        body, table, td, a { -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; }
        table, td { mso-table-lspace: 0pt; mso-table-rspace: 0pt; }
        img { -ms-interpolation-mode: bicubic; border: 0; outline: none; }
        
        /* Template styles */
        .email-container { max-width: 600px; margin: 0 auto; }
        .header { background-color: {{brand.primary_color}}; padding: 20px; }
        .content { padding: 30px; background-color: #ffffff; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <img src="{{brand.logo_url}}" alt="{{brand.name}}" height="40">
        </div>
        
        <!-- Content -->
        <div class="content">
            {{> content}}
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <p>© {{current_year}} {{brand.name}}. All rights reserved.</p>
            <p>
                <a href="{{unsubscribe_url}}">Unsubscribe</a> | 
                <a href="{{preferences_url}}">Update Preferences</a>
            </p>
        </div>
    </div>
</body>
</html>
```

### Welcome Email Template
```yaml
template:
  code: "user_welcome"
  type: "email"
  channel: "email"
  versions:
    - language: "en"
      subject: "Welcome to {{brand.name}}, {{user.name}}!"
      variables:
        - user.name
        - user.email
        - activation_url
        - brand.name
        - brand.support_email
      body_html: |
        <h1>Welcome, {{user.name}}!</h1>
        <p>Thank you for joining {{brand.name}}. We're excited to have you on board!</p>
        
        <p>To get started, please verify your email address:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="{{activation_url}}" style="background-color: {{brand.primary_color}}; 
             color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px;">
            Verify Email Address
          </a>
        </div>
        
        <p>If you have any questions, feel free to contact us at {{brand.support_email}}.</p>
        
        <p>Best regards,<br>The {{brand.name}} Team</p>
      
    - language: "vi"
      subject: "Chào mừng đến với {{brand.name}}, {{user.name}}!"
      body_html: |
        <h1>Xin chào, {{user.name}}!</h1>
        <p>Cảm ơn bạn đã tham gia {{brand.name}}. Chúng tôi rất vui khi có bạn!</p>
        
        <p>Để bắt đầu, vui lòng xác minh địa chỉ email của bạn:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="{{activation_url}}" style="background-color: {{brand.primary_color}}; 
             color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px;">
            Xác minh Email
          </a>
        </div>
```

## 3. Template Management Flow

```mermaid
sequenceDiagram
    participant Admin as Tenant Admin
    participant CMS as CMS Interface
    participant API as Template API
    participant Engine as Template Engine
    participant Storage as Template Storage
    participant Preview as Preview Service
    
    Admin->>CMS: Create/Edit template
    CMS->>API: Save template draft
    API->>Storage: Store template version
    
    Admin->>CMS: Preview template
    CMS->>Preview: Request preview
    Preview->>Engine: Render with test data
    Engine->>Preview: Return rendered HTML
    Preview->>CMS: Display preview
    
    Admin->>CMS: Send test email
    CMS->>API: Send test request
    API->>Engine: Render template
    Engine->>API: Return rendered email
    API->>Admin: Send test email
    
    Admin->>CMS: Activate template
    CMS->>API: Update template status
    API->>Storage: Mark as active
```

## 4. Template Engine Implementation

### Handlebars Engine
```go
type TemplateEngine struct {
    engine    *handlebars.Engine
    cache     *TemplateCache
    helpers   map[string]interface{}
    partials  map[string]string
}

func NewTemplateEngine() *TemplateEngine {
    engine := handlebars.New()
    
    // Register built-in helpers
    engine.RegisterHelper("formatDate", func(date time.Time, format string) string {
        return date.Format(format)
    })
    
    engine.RegisterHelper("formatCurrency", func(amount float64, currency string) string {
        return fmt.Sprintf("%.2f %s", amount, currency)
    })
    
    engine.RegisterHelper("truncate", func(text string, length int) string {
        if len(text) > length {
            return text[:length] + "..."
        }
        return text
    })
    
    engine.RegisterHelper("safe", func(value interface{}, defaultValue string) string {
        if value == nil {
            return defaultValue
        }
        return fmt.Sprintf("%v", value)
    })
    
    return &TemplateEngine{
        engine:   engine,
        cache:    NewTemplateCache(),
        helpers:  make(map[string]interface{}),
        partials: make(map[string]string),
    }
}

func (e *TemplateEngine) RenderTemplate(templateID string, data map[string]interface{}) (string, error) {
    // Check cache first
    if cached := e.cache.Get(templateID); cached != nil {
        template := cached.(*handlebars.Template)
        return template.Exec(data)
    }
    
    // Load template from database
    templateVersion, err := e.loadTemplateVersion(templateID)
    if err != nil {
        return "", err
    }
    
    // Compile template
    template, err := e.engine.CompileString(templateVersion.BodyHTML)
    if err != nil {
        return "", err
    }
    
    // Cache compiled template
    e.cache.Set(templateID, template, time.Hour)
    
    // Render with data
    return template.Exec(data)
}
```

### Template Cache
```go
type TemplateCache struct {
    cache    map[string]*CacheEntry
    mu       sync.RWMutex
    maxSize  int
    ttl      time.Duration
}

type CacheEntry struct {
    Value     interface{}
    ExpiresAt time.Time
}

func NewTemplateCache() *TemplateCache {
    cache := &TemplateCache{
        cache:   make(map[string]*CacheEntry),
        maxSize: 1000,
        ttl:     time.Hour,
    }
    
    // Start cleanup goroutine
    go cache.cleanup()
    
    return cache
}

func (c *TemplateCache) Get(key string) interface{} {
    c.mu.RLock()
    defer c.mu.RUnlock()
    
    entry, exists := c.cache[key]
    if !exists {
        return nil
    }
    
    if time.Now().After(entry.ExpiresAt) {
        delete(c.cache, key)
        return nil
    }
    
    return entry.Value
}

func (c *TemplateCache) Set(key string, value interface{}, ttl time.Duration) {
    c.mu.Lock()
    defer c.mu.Unlock()
    
    // Remove oldest entries if cache is full
    if len(c.cache) >= c.maxSize {
        c.evictOldest()
    }
    
    c.cache[key] = &CacheEntry{
        Value:     value,
        ExpiresAt: time.Now().Add(ttl),
    }
}

func (c *TemplateCache) cleanup() {
    ticker := time.NewTicker(5 * time.Minute)
    defer ticker.Stop()
    
    for range ticker.C {
        c.mu.Lock()
        now := time.Now()
        for key, entry := range c.cache {
            if now.After(entry.ExpiresAt) {
                delete(c.cache, key)
            }
        }
        c.mu.Unlock()
    }
}
```

## 5. Template Variable System

### Variable Processing
```go
type VariableProcessor struct {
    validators map[string]VariableValidator
    transformers map[string]VariableTransformer
}

type VariableValidator func(value interface{}) error
type VariableTransformer func(value interface{}) interface{}

func NewVariableProcessor() *VariableProcessor {
    processor := &VariableProcessor{
        validators:   make(map[string]VariableValidator),
        transformers: make(map[string]VariableTransformer),
    }
    
    // Register built-in validators
    processor.RegisterValidator("email", func(value interface{}) error {
        email, ok := value.(string)
        if !ok {
            return errors.New("email must be a string")
        }
        if !regexp.MustCompile(`^[^\s@]+@[^\s@]+\.[^\s@]+$`).MatchString(email) {
            return errors.New("invalid email format")
        }
        return nil
    })
    
    processor.RegisterValidator("url", func(value interface{}) error {
        url, ok := value.(string)
        if !ok {
            return errors.New("url must be a string")
        }
        if !regexp.MustCompile(`^https?://`).MatchString(url) {
            return errors.New("invalid url format")
        }
        return nil
    })
    
    // Register transformers
    processor.RegisterTransformer("capitalize", func(value interface{}) interface{} {
        if str, ok := value.(string); ok {
            return strings.Title(str)
        }
        return value
    })
    
    processor.RegisterTransformer("lowercase", func(value interface{}) interface{} {
        if str, ok := value.(string); ok {
            return strings.ToLower(str)
        }
        return value
    })
    
    return processor
}

func (p *VariableProcessor) ProcessVariables(data map[string]interface{}, template *NotificationTemplate) error {
    // Get template variables
    variables := template.GetVariables()
    
    for varName, varConfig := range variables {
        value, exists := data[varName]
        if !exists {
            if varConfig.Required {
                return fmt.Errorf("required variable '%s' is missing", varName)
            }
            // Use default value if provided
            if varConfig.DefaultValue != nil {
                data[varName] = varConfig.DefaultValue
            }
            continue
        }
        
        // Validate variable
        if validator, exists := p.validators[varConfig.Type]; exists {
            if err := validator(value); err != nil {
                return fmt.Errorf("validation failed for variable '%s': %v", varName, err)
            }
        }
        
        // Transform variable
        if transformer, exists := p.transformers[varConfig.Transform]; exists {
            data[varName] = transformer(value)
        }
    }
    
    return nil
}
```

### Variable Configuration
```go
type VariableConfig struct {
    Name         string      `json:"name"`
    Type         string      `json:"type"`
    Required     bool        `json:"required"`
    DefaultValue interface{} `json:"default_value,omitempty"`
    Transform    string      `json:"transform,omitempty"`
    Validation   map[string]interface{} `json:"validation,omitempty"`
    Description  string      `json:"description,omitempty"`
}

// Built-in variable types
const (
    VariableTypeString  = "string"
    VariableTypeNumber  = "number"
    VariableTypeBoolean = "boolean"
    VariableTypeDate    = "date"
    VariableTypeEmail   = "email"
    VariableTypeURL     = "url"
    VariableTypeObject  = "object"
    VariableTypeArray   = "array"
)

// Common variable definitions
var CommonVariables = map[string]VariableConfig{
    "user.name": {
        Name:        "user.name",
        Type:        VariableTypeString,
        Required:    true,
        Transform:   "capitalize",
        Description: "User's full name",
    },
    "user.email": {
        Name:        "user.email",
        Type:        VariableTypeEmail,
        Required:    true,
        Description: "User's email address",
    },
    "brand.name": {
        Name:        "brand.name",
        Type:        VariableTypeString,
        Required:    true,
        Description: "Brand/company name",
    },
    "brand.logo_url": {
        Name:        "brand.logo_url",
        Type:        VariableTypeURL,
        Required:    false,
        Description: "Brand logo URL",
    },
    "current_year": {
        Name:         "current_year",
        Type:         VariableTypeNumber,
        Required:     false,
        DefaultValue: time.Now().Year(),
        Description:  "Current year",
    },
}
```

## 6. Template Best Practices

### Design Guidelines
```markdown
# Email Template Design Best Practices

## Mobile-First Design
- Use responsive design principles
- Test on multiple email clients
- Keep width under 600px for desktop
- Use single-column layout for mobile

## CSS Guidelines
- Use inline CSS for critical styles
- Avoid external stylesheets
- Use table-based layouts for compatibility
- Include fallback fonts

## Content Guidelines
- Keep subject lines under 50 characters
- Use clear, actionable CTAs
- Include plain text version
- Optimize preheader text

## Accessibility
- Use semantic HTML
- Include alt text for images
- Ensure sufficient color contrast
- Use readable font sizes (14px minimum)

## Performance
- Optimize images for web
- Use CDN for assets
- Minimize HTML size
- Test loading times
```

### Template Validation
```go
type TemplateValidator struct {
    rules []ValidationRule
}

type ValidationRule interface {
    Validate(template *NotificationTemplate) error
}

type SubjectLengthRule struct {
    MaxLength int
}

func (r *SubjectLengthRule) Validate(template *NotificationTemplate) error {
    for _, version := range template.Versions {
        if len(version.Subject) > r.MaxLength {
            return fmt.Errorf("subject too long for language %s: %d > %d", 
                version.Language, len(version.Subject), r.MaxLength)
        }
    }
    return nil
}

type RequiredVariablesRule struct {
    RequiredVars []string
}

func (r *RequiredVariablesRule) Validate(template *NotificationTemplate) error {
    for _, version := range template.Versions {
        for _, reqVar := range r.RequiredVars {
            if !strings.Contains(version.BodyHTML, "{{"+reqVar+"}}") {
                return fmt.Errorf("required variable '%s' not found in template", reqVar)
            }
        }
    }
    return nil
}

func NewTemplateValidator() *TemplateValidator {
    return &TemplateValidator{
        rules: []ValidationRule{
            &SubjectLengthRule{MaxLength: 100},
            &RequiredVariablesRule{
                RequiredVars: []string{"unsubscribe_url", "brand.name"},
            },
        },
    }
}
```

## 7. Template Testing Framework

### Test Suite
```go
type TemplateTestSuite struct {
    engine    *TemplateEngine
    testData  map[string]interface{}
    validator *TemplateValidator
}

func NewTemplateTestSuite() *TemplateTestSuite {
    return &TemplateTestSuite{
        engine:    NewTemplateEngine(),
        testData:  getTestData(),
        validator: NewTemplateValidator(),
    }
}

func (t *TemplateTestSuite) TestTemplate(template *NotificationTemplate) (*TestResult, error) {
    result := &TestResult{
        TemplateID: template.ID,
        Tests:      make(map[string]*TestCase),
    }
    
    // Validate template structure
    if err := t.validator.Validate(template); err != nil {
        result.AddError("validation", err)
        return result, nil
    }
    
    // Test each version
    for _, version := range template.Versions {
        testCase := &TestCase{
            Language: version.Language,
            Passed:   true,
        }
        
        // Test rendering
        rendered, err := t.engine.RenderTemplate(version.ID, t.testData)
        if err != nil {
            testCase.Passed = false
            testCase.Error = err.Error()
        } else {
            testCase.RenderedHTML = rendered
        }
        
        // Test email client compatibility
        compatibility, err := t.testEmailClientCompatibility(rendered)
        if err != nil {
            testCase.Warnings = append(testCase.Warnings, err.Error())
        }
        testCase.Compatibility = compatibility
        
        result.Tests[version.Language] = testCase
    }
    
    return result, nil
}

func (t *TemplateTestSuite) testEmailClientCompatibility(html string) (map[string]bool, error) {
    compatibility := make(map[string]bool)
    
    // Test common email clients
    clients := []string{"gmail", "outlook", "apple_mail", "yahoo"}
    
    for _, client := range clients {
        // Simple compatibility checks
        compatible := true
        
        // Check for unsupported CSS
        if strings.Contains(html, "flexbox") && client == "outlook" {
            compatible = false
        }
        
        // Check for table structure
        if !strings.Contains(html, "<table") && client == "outlook" {
            compatible = false
        }
        
        compatibility[client] = compatible
    }
    
    return compatibility, nil
}

func getTestData() map[string]interface{} {
    return map[string]interface{}{
        "user": map[string]interface{}{
            "name":  "John Doe",
            "email": "<EMAIL>",
            "id":    12345,
        },
        "brand": map[string]interface{}{
            "name":          "Your Brand",
            "logo_url":      "https://example.com/logo.png",
            "primary_color": "#007bff",
            "support_email": "<EMAIL>",
        },
        "activation_url":    "https://example.com/activate/token123",
        "unsubscribe_url":   "https://example.com/unsubscribe/token123",
        "preferences_url":   "https://example.com/preferences/token123",
        "current_year":      time.Now().Year(),
    }
}
```

## 8. Advanced Template Features

### Conditional Content
```handlebars
{{#if user.is_premium}}
    <div class="premium-content">
        <h2>Premium Features</h2>
        <p>As a premium member, you have access to exclusive content.</p>
    </div>
{{else}}
    <div class="upgrade-prompt">
        <h2>Upgrade to Premium</h2>
        <p>Unlock exclusive features with our premium plan.</p>
        <a href="{{upgrade_url}}">Upgrade Now</a>
    </div>
{{/if}}
```

### Loop Content
```handlebars
{{#each order.items}}
    <tr>
        <td>{{name}}</td>
        <td>{{quantity}}</td>
        <td>${{price}}</td>
        <td>${{total}}</td>
    </tr>
{{/each}}
```

### Dynamic Styling
```handlebars
<div style="background-color: {{brand.primary_color}}; padding: 20px;">
    <h1 style="color: {{brand.text_color}};">{{title}}</h1>
</div>
```

### Multi-language Support
```handlebars
{{#switch language}}
    {{#case "en"}}
        <p>Thank you for your order!</p>
    {{/case}}
    {{#case "vi"}}
        <p>Cảm ơn bạn đã đặt hàng!</p>
    {{/case}}
    {{#case "es"}}
        <p>¡Gracias por su pedido!</p>
    {{/case}}
{{/switch}}
```
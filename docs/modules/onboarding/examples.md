# Onboarding API Examples

## Overview

This document provides complete code examples for integrating with the onboarding API, including request/response payloads, error handling, and common integration patterns.

## Complete API Examples

### 1. Get Current Onboarding Step

**Request:**
```http
GET /api/cms/v1/onboarding/step
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Success Response (200):**
```json
{
  "step": "create_website"
}
```

**JavaScript Example:**
```javascript
async function getCurrentOnboardingStep() {
  try {
    const response = await fetch('/api/cms/v1/onboarding/step', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${getAccessToken()}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data.step;
  } catch (error) {
    console.error('Failed to get current onboarding step:', error);
    throw error;
  }
}

// Usage
const currentStep = await getCurrentOnboardingStep();
console.log('Current onboarding step:', currentStep);
```

### 2. Update Onboarding Step

**Request:**
```http
PUT /api/cms/v1/onboarding/step
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "step": "create_website",
  "metadata": {
    "tenant_id": 456,
    "tenant_name": "My Company",
    "tenant_created": true
  }
}
```

**Success Response (200):**
```json
{
  "id": 1,
  "user_id": 123,
  "status": "processing",
  "step": "create_website",
  "started_at": "2025-07-22T10:00:00Z",
  "completed_at": null,
  "metadata": {
    "tenant_id": 456,
    "tenant_name": "My Company",
    "tenant_created": true,
    "website_created": false,
    "current_step": "create_website",
    "steps_completed": ["create_tenant"]
  },
  "updated_at": "2025-07-22T10:15:00Z",
  "message": "Onboarding step updated to create_website"
}
```

**Validation Error Response (400):**
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid step value",
    "details": {
      "field": "step",
      "value": "invalid_step",
      "allowed": ["create_tenant", "create_website", "completed"]
    }
  }
}
```

**JavaScript Example:**
```javascript
async function updateOnboardingStep(step, metadata = {}) {
  try {
    const response = await fetch('/api/cms/v1/onboarding/step', {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${getAccessToken()}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        step,
        metadata
      })
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error?.message || `HTTP ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Failed to update onboarding step:', error);
    throw error;
  }
}

// Usage - After tenant creation
async function handleTenantCreated(tenant) {
  try {
    const result = await updateOnboardingStep('create_website', {
      tenant_id: tenant.id,
      tenant_name: tenant.name,
      tenant_created: true,
      tenant_created_at: new Date().toISOString()
    });
    
    console.log('Step updated:', result);
    // Navigate to website creation
    window.location.href = '/onboarding/website';
  } catch (error) {
    showErrorMessage('Failed to update progress. Please try again.');
  }
}
```

### 3. Complete Onboarding

**Request:**
```http
POST /api/cms/v1/onboarding/complete
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "metadata": {
    "website_id": 789,
    "website_name": "My Website",
    "completion_method": "manual",
    "final_step": "create_website"
  }
}
```

**Success Response (200):**
```json
{
  "id": 1,
  "user_id": 123,
  "status": "completed",
  "step": "completed",
  "started_at": "2025-07-22T10:00:00Z",
  "completed_at": "2025-07-22T10:30:00Z",
  "metadata": {
    "tenant_id": 456,
    "tenant_name": "My Company",
    "website_id": 789,
    "website_name": "My Website",
    "completion_method": "manual",
    "final_step": "create_website",
    "completed_at": "2025-07-22T10:30:00Z"
  },
  "message": "Onboarding completed successfully"
}
```

**JavaScript Example:**
```javascript
async function completeOnboarding(metadata = {}) {
  try {
    const response = await fetch('/api/cms/v1/onboarding/complete', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getAccessToken()}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        metadata: {
          completion_method: 'manual',
          completed_at: new Date().toISOString(),
          ...metadata
        }
      })
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error?.message || `HTTP ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Failed to complete onboarding:', error);
    throw error;
  }
}

// Usage - After website creation
async function handleWebsiteCreated(website) {
  try {
    const result = await completeOnboarding({
      website_id: website.id,
      website_name: website.name,
      website_url: website.url
    });
    
    console.log('Onboarding completed:', result);
    
    // Show success message
    showSuccessMessage('Welcome! Your setup is complete.');
    
    // Redirect to dashboard
    setTimeout(() => {
      window.location.href = '/dashboard';
    }, 2000);
  } catch (error) {
    showErrorMessage('Failed to complete onboarding. Please try again.');
  }
}
```

## Complete Integration Example

### React Component with Full Flow

```javascript
import React, { useState, useEffect } from 'react';

function OnboardingFlow() {
  const [currentStep, setCurrentStep] = useState('create_tenant');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    loadCurrentStep();
  }, []);
  
  const loadCurrentStep = async () => {
    try {
      setLoading(true);
      const step = await getCurrentOnboardingStep();
      setCurrentStep(step);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  const handleStepComplete = async (step, metadata) => {
    try {
      setLoading(true);
      
      if (step === 'completed') {
        const result = await completeOnboarding(metadata);
        setCurrentStep('completed');
        
        // Redirect to dashboard after completion
        setTimeout(() => {
          window.location.href = '/dashboard';
        }, 2000);
      } else {
        const result = await updateOnboardingStep(step, metadata);
        setCurrentStep(result.step);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  if (loading) {
    return <div className="loading">Loading onboarding...</div>;
  }
  
  if (error) {
    return (
      <div className="error">
        <h3>Error</h3>
        <p>{error}</p>
        <button onClick={loadCurrentStep}>Retry</button>
      </div>
    );
  }
  
  if (currentStep === 'completed') {
    return (
      <div className="success">
        <h2>🎉 Welcome!</h2>
        <p>Your onboarding is complete. Redirecting to dashboard...</p>
      </div>
    );
  }
  
  return (
    <div className="onboarding-flow">
      <ProgressIndicator currentStep={currentStep} />
      
      {currentStep === 'create_tenant' && (
        <TenantCreationStep onComplete={handleStepComplete} />
      )}
      
      {currentStep === 'create_website' && (
        <WebsiteCreationStep onComplete={handleStepComplete} />
      )}
    </div>
  );
}

function TenantCreationStep({ onComplete }) {
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });
  const [submitting, setSubmitting] = useState(false);
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    
    try {
      // Create tenant via tenant API
      const response = await fetch('/api/cms/v1/tenants', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAccessToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });
      
      if (!response.ok) {
        throw new Error('Failed to create tenant');
      }
      
      const tenant = await response.json();
      
      // Update onboarding step
      await onComplete('create_website', {
        tenant_id: tenant.id,
        tenant_name: tenant.name,
        tenant_created: true
      });
    } catch (error) {
      console.error('Tenant creation failed:', error);
      alert('Failed to create organization. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <h2>Create Your Organization</h2>
      
      <div className="form-group">
        <label>Organization Name *</label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData({...formData, name: e.target.value})}
          required
        />
      </div>
      
      <div className="form-group">
        <label>Description</label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData({...formData, description: e.target.value})}
        />
      </div>
      
      <button type="submit" disabled={submitting}>
        {submitting ? 'Creating...' : 'Continue'}
      </button>
    </form>
  );
}

function WebsiteCreationStep({ tenantId, onComplete }) {
  const [formData, setFormData] = useState({
    name: '',
    domain: '',
    description: ''
  });
  const [submitting, setSubmitting] = useState(false);
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    
    try {
      // Create website via website API
      const response = await fetch('/api/cms/v1/websites', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAccessToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...formData,
          tenant_id: tenantId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to create website');
      }
      
      const website = await response.json();
      
      // Complete onboarding
      await onComplete('completed', {
        website_id: website.id,
        website_name: website.name,
        website_created: true
      });
    } catch (error) {
      console.error('Website creation failed:', error);
      alert('Failed to create website. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <h2>Setup Your Website</h2>
      
      <div className="form-group">
        <label>Website Name *</label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData({...formData, name: e.target.value})}
          required
        />
      </div>
      
      <div className="form-group">
        <label>Domain</label>
        <input
          type="text"
          value={formData.domain}
          onChange={(e) => setFormData({...formData, domain: e.target.value})}
          placeholder="example.com"
        />
      </div>
      
      <div className="form-group">
        <label>Description</label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData({...formData, description: e.target.value})}
        />
      </div>
      
      <button type="submit" disabled={submitting}>
        {submitting ? 'Creating...' : 'Complete Setup'}
      </button>
    </form>
  );
}

export default OnboardingFlow;
```

## Error Handling Examples

### Network Error Recovery

```javascript
async function apiCallWithRetry(apiCall, maxRetries = 3) {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await apiCall();
    } catch (error) {
      lastError = error;
      
      // Don't retry on client errors (4xx)
      if (error.status >= 400 && error.status < 500) {
        throw error;
      }
      
      // Wait before retrying (exponential backoff)
      if (attempt < maxRetries) {
        const delay = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  throw lastError;
}

// Usage
try {
  const progress = await apiCallWithRetry(() => getOnboardingProgress());
  console.log('Progress loaded:', progress);
} catch (error) {
  console.error('Failed after retries:', error);
  showErrorMessage('Unable to load onboarding progress. Please check your connection.');
}
```

### Validation Error Display

```javascript
function handleValidationError(error) {
  if (error.response?.status === 400) {
    const errorData = error.response.data;
    
    if (errorData.error?.code === 'VALIDATION_ERROR') {
      const details = errorData.error.details;
      
      // Show field-specific error
      if (details.field) {
        showFieldError(details.field, details.message || errorData.error.message);
      } else {
        showGeneralError(errorData.error.message);
      }
    } else {
      showGeneralError(errorData.error?.message || 'Validation failed');
    }
  } else {
    showGeneralError('An unexpected error occurred');
  }
}

function showFieldError(fieldName, message) {
  const field = document.querySelector(`[name="${fieldName}"]`);
  if (field) {
    field.classList.add('error');
    
    // Remove existing error message
    const existingError = field.parentNode.querySelector('.error-message');
    if (existingError) {
      existingError.remove();
    }
    
    // Add new error message
    const errorElement = document.createElement('div');
    errorElement.className = 'error-message';
    errorElement.textContent = message;
    field.parentNode.appendChild(errorElement);
  }
}
```

## Testing Examples

### Mock API for Development

```javascript
// mockOnboardingAPI.js
const mockDelay = (ms = 1000) => new Promise(resolve => setTimeout(resolve, ms));

export const mockOnboardingAPI = {
  async getCurrentStep() {
    await mockDelay();
    return {
      step: 'create_tenant'
    };
  },
  
  async updateStep(step, metadata) {
    await mockDelay();
    return {
      id: 1,
      user_id: 123,
      status: 'processing',
      step,
      metadata,
      updated_at: new Date().toISOString(),
      message: `Onboarding step updated to ${step}`
    };
  },
  
  async completeOnboarding(metadata) {
    await mockDelay();
    return {
      id: 1,
      user_id: 123,
      status: 'completed',
      step: 'completed',
      started_at: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
      completed_at: new Date().toISOString(),
      metadata,
      message: 'Onboarding completed successfully'
    };
  }
};

// Usage in development
const isDevelopment = process.env.NODE_ENV === 'development';
const onboardingAPI = isDevelopment ? mockOnboardingAPI : realOnboardingAPI;
```

### Unit Test Example

```javascript
// onboarding.test.js
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import OnboardingFlow from './OnboardingFlow';
import * as api from './onboardingAPI';

// Mock the API
jest.mock('./onboardingAPI');

describe('OnboardingFlow', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  test('loads current step on mount', async () => {
    api.getCurrentOnboardingStep.mockResolvedValue('create_tenant');
    
    render(<OnboardingFlow />);
    
    await waitFor(() => {
      expect(api.getCurrentOnboardingStep).toHaveBeenCalled();
    });
    
    expect(screen.getByText(/create your organization/i)).toBeInTheDocument();
  });
  
  test('shows website creation for website step', async () => {
    api.getCurrentOnboardingStep.mockResolvedValue('create_website');
    
    render(<OnboardingFlow />);
    
    await waitFor(() => {
      expect(screen.getByText(/setup your website/i)).toBeInTheDocument();
    });
  });
  
  test('handles API errors gracefully', async () => {
    api.getCurrentOnboardingStep.mockRejectedValue(new Error('Network error'));
    
    render(<OnboardingFlow />);
    
    await waitFor(() => {
      expect(screen.getByText(/error/i)).toBeInTheDocument();
      expect(screen.getByText(/network error/i)).toBeInTheDocument();
    });
    
    // Test retry functionality
    api.getCurrentOnboardingStep.mockResolvedValue('create_tenant');
    
    fireEvent.click(screen.getByText(/retry/i));
    
    await waitFor(() => {
      expect(screen.getByText(/create your organization/i)).toBeInTheDocument();
    });
  });
});
```

This comprehensive examples documentation provides developers with practical, copy-paste code examples for implementing the onboarding flow, handling errors, and testing their integration.

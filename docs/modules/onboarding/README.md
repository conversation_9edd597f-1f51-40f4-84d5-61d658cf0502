# Onboarding Module Documentation

## Overview

The Onboarding Module provides a streamlined user onboarding experience for new users in the WN API v3 system. It automatically initializes when users verify their email and guides them through essential setup steps including tenant creation and website setup.

## Purpose and Functionality

The onboarding module serves several key purposes:

- **Automatic Initialization**: Creates onboarding records automatically during email verification
- **Guided User Experience**: Provides a step-by-step introduction for new users
- **Progress Tracking**: Monitors user completion of essential setup tasks
- **State Management**: Maintains onboarding status across user sessions
- **Analytics**: Offers insights into user onboarding completion rates
- **Simplified API**: Streamlined endpoints focused on essential functionality

## Core Features

### Onboarding Flow Steps

1. **create_tenant**: User creates their organization/tenant
2. **create_website**: User sets up their first website
3. **completed**: Onboarding process is finished

### Status Management

- **pending**: User hasn't started onboarding
- **processing**: User is actively going through onboarding
- **completed**: User has finished all required steps

## Database Schema

### onboarding_progress Table

```sql
CREATE TABLE onboarding_progress (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    step VARCHAR(50) NOT NULL DEFAULT 'create_tenant',
    started_at TIMESTAMP NULL DEFAULT NULL,
    completed_at TIMESTAMP NULL DEFAULT NULL,
    metadata JSON DEFAULT (JSON_OBJECT()),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_onboarding_progress_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_onboarding_progress_user_id (user_id)
);
```

#### Field Descriptions

- **id**: Primary key, auto-incrementing identifier
- **user_id**: Foreign key reference to the users table (unique per user)
- **status**: Current onboarding status (pending, processing, completed)
- **step**: Current step in the onboarding flow
- **started_at**: Timestamp when user began onboarding
- **completed_at**: Timestamp when user completed onboarding
- **metadata**: JSON field for additional context and tracking data
- **created_at/updated_at**: Standard audit timestamps

## Automatic Onboarding Initialization

Onboarding records are automatically created when users verify their email address. This streamlined approach eliminates the need for manual onboarding initialization.

### Email Verification Integration

When a user verifies their email:
1. The auth module calls the onboarding integration
2. An onboarding record is created with status `pending` and step `create_tenant`
3. The user can immediately begin the onboarding process

### Skip Onboarding Configuration

If `SKIP_ONBOARDING` is enabled in the auth configuration:
- Tenant and website are automatically created during email verification
- No onboarding record is created
- Users skip the guided onboarding flow

## Authentication Requirements

All onboarding endpoints require:

1. **JWT Authentication**: Valid JWT token in Authorization header
2. **User Context**: JWT middleware must set `user_id` in request context
3. **Session Validation**: Active user session required

### Authentication Header Format

```
Authorization: Bearer <jwt_token>
```

The JWT token must contain valid user claims, and the middleware will extract the `user_id` automatically.

## API Endpoints

### Base URL
```
/api/cms/v1/onboarding
```

### User Endpoints (Authenticated)

> **Note**: Onboarding records are automatically created when users verify their email. No manual initialization is required.

#### Update Onboarding Step
```http
PUT /step
```

**Description**: Updates the current step in the onboarding process. Automatically starts onboarding if it's pending.

**Request Body**:
```json
{
  "step": "create_website",
  "metadata": {
    "tenant_id": 456,
    "tenant_name": "My Company"
  }
}
```

**Response**:
```json
{
  "id": 1,
  "user_id": 123,
  "status": "processing",
  "step": "create_website",
  "started_at": "2025-07-22T10:00:00Z",
  "completed_at": null,
  "metadata": {
    "tenant_id": 456,
    "tenant_name": "My Company"
  },
  "updated_at": "2025-07-22T10:15:00Z",
  "message": "Onboarding step updated to create_website"
}
```

#### Complete Onboarding
```http
POST /complete
```

**Description**: Marks the onboarding process as completed

**Request Body**:
```json
{
  "metadata": {
    "completion_method": "manual",
    "final_step": "create_website"
  }
}
```

**Response**:
```json
{
  "id": 1,
  "user_id": 123,
  "status": "completed",
  "step": "completed",
  "started_at": "2025-07-22T10:00:00Z",
  "completed_at": "2025-07-22T10:45:00Z",
  "metadata": {
    "completion_method": "manual",
    "final_step": "create_website"
  },
  "message": "Onboarding completed successfully"
}
```

#### Get Current Step
```http
GET /step
```

**Description**: Retrieves the current onboarding step for the user

**Response**:
```json
{
  "step": "create_website"
}
```

### Admin Endpoints (Admin Role Required)

#### List All Progress
```http
GET /admin/progress?status=processing&limit=10&offset=0
```

**Description**: Lists onboarding progress with optional filters

**Query Parameters**:
- `status`: Filter by status (pending, processing, completed)
- `step`: Filter by step (create_tenant, create_website, completed)
- `limit`: Number of results (default: 10, max: 100)
- `offset`: Pagination offset (default: 0)

**Response**:
```json
{
  "progress": [
    {
      "id": 1,
      "user_id": 123,
      "status": "processing",
      "step": "create_website",
      "started_at": "2025-07-22T10:00:00Z",
      "completed_at": null,
      "metadata": {},
      "created_at": "2025-07-22T10:00:00Z",
      "updated_at": "2025-07-22T10:30:00Z"
    }
  ],
  "total": 1,
  "limit": 10,
  "offset": 0
}
```

#### Get Statistics
```http
GET /admin/stats
```

**Description**: Retrieves onboarding statistics

**Response**:
```json
{
  "total_users": 100,
  "pending_users": 20,
  "processing_users": 30,
  "completed_users": 50,
  "completion_rate": 50.0
}
```

### Health Check
```http
GET /health
```

**Description**: Health check endpoint for the onboarding module

**Response**:
```json
{
  "module": "onboarding",
  "status": "healthy",
  "version": "1.0.0",
  "checks": {
    "database": true,
    "progress_repo": true,
    "onboarding_service": true
  }
}
```

## Error Responses

### Common Error Codes

- **400 Bad Request**: Invalid request format or validation errors
- **401 Unauthorized**: Missing or invalid authentication
- **404 Not Found**: Onboarding progress not found
- **409 Conflict**: Onboarding already completed
- **500 Internal Server Error**: Server-side errors

### Error Response Format

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid step value",
    "details": {
      "field": "step",
      "value": "invalid_step",
      "allowed": ["create_tenant", "create_website", "completed"]
    }
  }
}
```

## Configuration

The onboarding module supports the following configuration options:

```go
type OnboardingConfig struct {
    AutoStart           bool          // Automatically start onboarding for new users
    RequiredSteps       []string      // List of required steps
    OptionalSteps       []string      // List of optional steps
    DefaultTimeout      time.Duration // Default timeout for onboarding completion
    AllowSkip           bool          // Allow users to skip onboarding
    EnableNotifications bool          // Enable onboarding notifications
}
```

## Integration Notes

1. **User Registration**: Consider automatically starting onboarding after user registration
2. **Middleware**: Ensure JWT authentication middleware is properly configured
3. **Frontend State**: Use the progress endpoint to maintain UI state
4. **Error Handling**: Implement proper error handling for all API calls
5. **Analytics**: Use the admin endpoints for tracking onboarding metrics

## Related Modules

- **Auth Module**: Provides JWT authentication and user context
- **User Module**: Manages user accounts and profiles
- **Tenant Module**: Handles tenant creation during onboarding
- **Website Module**: Manages website creation during onboarding

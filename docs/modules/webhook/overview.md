# Webhook System Overview

## <PERSON><PERSON><PERSON><PERSON> thi<PERSON>u

<PERSON>ệ thống webhook trong project đ<PERSON><PERSON><PERSON> thiết kế để cho phép các module khác nhau có thể gửi và nhận thông báo real-time về các sự kiện quan trọng. Hi<PERSON><PERSON> tại, webhook đã được implement trong các module:

1. **Notification Module** - Xử lý webhook từ các email/SMS/push providers
2. **Payment Module** (Plugin) - Xử lý webhook từ payment gateways như Stripe

## Kiến trúc hiện tại

### 1. Notification Module Webhooks

Module notification cung cấp endpoints để nhận webhook từ các provider:

```
POST /webhooks/notifications/sendgrid    - SendGrid email events
POST /webhooks/notifications/mailgun     - Mailgun email events  
POST /webhooks/notifications/ses         - AWS SES email events
POST /webhooks/notifications/fcm         - Firebase Cloud Messaging events
POST /webhooks/notifications/apns        - Apple Push Notification events
POST /webhooks/notifications/twilio      - Twilio SMS events
POST /webhooks/notifications/sns         - AWS SNS events
```

### 2. Payment Plugin Webhooks

Payment plugins implement webhook handler interface:

```go
HandleWebhook(ctx context.Context, payload []byte, headers map[string]string) (*WebhookResult, error)
```

Stripe plugin xử lý các events:
- payment_intent.succeeded
- payment_intent.failed
- customer.subscription.created/updated/deleted
- invoice.payment_succeeded/failed
- charge.dispute.created

## Thiết kế Webhook System chung cho tất cả modules

### Kiến trúc đề xuất

```mermaid
graph TB
    subgraph "Webhook Core System"
        WR[Webhook Registry]
        WM[Webhook Manager]
        WD[Webhook Dispatcher]
        WQ[Webhook Queue]
        WS[Webhook Storage]
    end
    
    subgraph "Module Integration"
        M1[Blog Module]
        M2[User Module]
        M3[Media Module]
        M4[Any Module]
    end
    
    subgraph "External Services"
        ES1[External API 1]
        ES2[External API 2]
        ES3[Third Party Service]
    end
    
    M1 --> WR
    M2 --> WR
    M3 --> WR
    M4 --> WR
    
    WR --> WM
    WM --> WQ
    WQ --> WD
    WD --> ES1
    WD --> ES2
    WD --> ES3
    
    WM --> WS
```

### Core Components

#### 1. Webhook Registry
- Đăng ký webhook endpoints cho mỗi module
- Quản lý event types mà mỗi module hỗ trợ
- Validation rules cho webhook payloads

#### 2. Webhook Manager
- Quản lý webhook subscriptions theo tenant
- CRUD operations cho webhook configurations
- Event filtering và routing logic

#### 3. Webhook Dispatcher
- Gửi webhook requests với retry logic
- Circuit breaker pattern cho failed endpoints
- Request signing với HMAC-SHA256
- Parallel delivery với rate limiting

#### 4. Webhook Queue
- Async processing với priority queue
- Failure handling và dead letter queue
- Batch processing cho high volume

#### 5. Webhook Storage
- Lưu webhook configurations
- Event history và delivery logs
- Analytics và monitoring data

## Database Schema

### webhook_endpoints table
```sql
CREATE TABLE webhook_endpoints (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    module_name VARCHAR(50) NOT NULL,
    name VARCHAR(255) NOT NULL,
    url VARCHAR(500) NOT NULL,
    secret VARCHAR(255) NOT NULL,
    events JSON NOT NULL,
    headers JSON,
    
    -- Retry configuration
    max_retries INT DEFAULT 3,
    timeout_seconds INT DEFAULT 30,
    retry_strategy ENUM('linear', 'exponential') DEFAULT 'exponential',
    
    -- Circuit breaker
    failure_threshold INT DEFAULT 5,
    recovery_timeout INT DEFAULT 300,
    consecutive_failures INT DEFAULT 0,
    circuit_status ENUM('closed', 'open', 'half_open') DEFAULT 'closed',
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    last_error TEXT,
    last_success_at TIMESTAMP NULL,
    last_failure_at TIMESTAMP NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    INDEX idx_webhook_endpoints_tenant_module (tenant_id, module_name),
    INDEX idx_webhook_endpoints_active (is_active, circuit_status)
);
```

### webhook_events table
```sql
CREATE TABLE webhook_events (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    module_name VARCHAR(50) NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    event_id VARCHAR(255) NOT NULL,
    payload JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    UNIQUE KEY uk_webhook_events_event (tenant_id, module_name, event_type, event_id),
    INDEX idx_webhook_events_created (created_at)
);
```

### webhook_deliveries table
```sql
CREATE TABLE webhook_deliveries (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    endpoint_id INT UNSIGNED NOT NULL,
    event_id INT UNSIGNED NOT NULL,
    
    -- Delivery details
    request_headers JSON,
    request_body JSON,
    response_status INT,
    response_headers JSON,
    response_body TEXT,
    
    -- Tracking
    attempt_number INT DEFAULT 1,
    delivery_status ENUM('pending', 'success', 'failed', 'retrying') DEFAULT 'pending',
    delivered_at TIMESTAMP NULL,
    next_retry_at TIMESTAMP NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (endpoint_id) REFERENCES webhook_endpoints(id) ON DELETE CASCADE,
    FOREIGN KEY (event_id) REFERENCES webhook_events(id) ON DELETE CASCADE,
    INDEX idx_webhook_deliveries_status (delivery_status, next_retry_at),
    INDEX idx_webhook_deliveries_endpoint (endpoint_id, created_at)
);
```

## Module Integration Guide

### 1. Đăng ký Webhook Events

Mỗi module cần implement WebhookProvider interface:

```go
type WebhookProvider interface {
    // GetSupportedEvents returns list of events this module can emit
    GetSupportedEvents() []WebhookEventType
    
    // ValidateEventPayload validates payload for specific event type
    ValidateEventPayload(eventType string, payload interface{}) error
    
    // TransformEventPayload transforms internal data to webhook payload
    TransformEventPayload(eventType string, data interface{}) (interface{}, error)
}
```

### 2. Emit Webhook Events

```go
// Example trong Blog module
func (s *BlogService) PublishPost(ctx context.Context, postID uint) error {
    // ... publish logic
    
    // Emit webhook event
    event := &WebhookEvent{
        ModuleName: "blog",
        EventType:  "post.published",
        EventID:    fmt.Sprintf("post_%d", postID),
        Payload: map[string]interface{}{
            "post_id": postID,
            "title": post.Title,
            "author_id": post.AuthorID,
            "published_at": time.Now(),
        },
    }
    
    if err := s.webhookManager.EmitEvent(ctx, event); err != nil {
        // Log error but don't fail the operation
        s.logger.Error("Failed to emit webhook event", "error", err)
    }
    
    return nil
}
```

### 3. Configure Webhook Endpoints

```go
// API endpoint để configure webhooks
POST /api/webhooks/endpoints
{
    "module_name": "blog",
    "name": "Blog Post Notifications",
    "url": "https://example.com/webhooks/blog",
    "events": ["post.published", "post.updated", "post.deleted"],
    "headers": {
        "X-Custom-Header": "value"
    },
    "max_retries": 5,
    "timeout_seconds": 30
}
```

## Event Types theo Module

### Blog Module
- `blog.post.created` - Post mới được tạo
- `blog.post.published` - Post được publish
- `blog.post.updated` - Post được update
- `blog.post.deleted` - Post bị xóa
- `blog.comment.created` - Comment mới
- `blog.comment.approved` - Comment được duyệt

### User Module  
- `user.created` - User mới đăng ký
- `user.updated` - Profile được update
- `user.deleted` - User bị xóa
- `user.login` - User đăng nhập
- `user.password_changed` - Password thay đổi

### Media Module
- `media.uploaded` - File được upload
- `media.processed` - File xử lý xong
- `media.deleted` - File bị xóa

### Tenant Module
- `tenant.created` - Tenant mới
- `tenant.subscription.changed` - Plan thay đổi
- `tenant.suspended` - Tenant bị suspend
- `tenant.activated` - Tenant được activate

## Security Best Practices

### 1. Webhook Signature Verification

```go
func VerifyWebhookSignature(payload []byte, signature string, secret string) bool {
    expectedSignature := GenerateSignature(payload, secret)
    return hmac.Equal([]byte(signature), []byte(expectedSignature))
}

func GenerateSignature(payload []byte, secret string) string {
    h := hmac.New(sha256.New, []byte(secret))
    h.Write(payload)
    return "sha256=" + hex.EncodeToString(h.Sum(nil))
}
```

### 2. Request Headers

Mỗi webhook request nên include:
- `X-Webhook-Signature`: HMAC signature
- `X-Webhook-Event`: Event type
- `X-Webhook-Delivery`: Unique delivery ID
- `X-Webhook-Timestamp`: Request timestamp

### 3. Payload Structure

```json
{
    "id": "evt_**********",
    "type": "blog.post.published",
    "created": **********,
    "data": {
        // Event specific data
    },
    "tenant_id": 123,
    "module": "blog"
}
```

## Monitoring và Analytics

### Metrics cần track:
- Total webhook deliveries
- Success/failure rates
- Average delivery time
- Retry attempts
- Circuit breaker activations

### Health checks:
- Endpoint availability
- Response time monitoring  
- Error rate thresholds
- Queue depth monitoring

## Implementation Roadmap

### Phase 1: Core Infrastructure
- [ ] Webhook registry service
- [ ] Basic delivery mechanism
- [ ] Database schema implementation

### Phase 2: Module Integration
- [ ] Blog module webhooks
- [ ] User module webhooks
- [ ] Standard event types

### Phase 3: Advanced Features
- [ ] Circuit breaker implementation
- [ ] Advanced retry strategies
- [ ] Webhook analytics dashboard

### Phase 4: Developer Experience
- [ ] Webhook testing tools
- [ ] Documentation portal
- [ ] SDK/libraries for consumers
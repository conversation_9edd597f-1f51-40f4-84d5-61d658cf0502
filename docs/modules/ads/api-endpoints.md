# Ads Module - API Endpoints

## Overview
This document describes all REST API endpoints for the Ads module, including request/response formats, authentication requirements, and usage examples.

## Base URL
All ads endpoints are prefixed with `/api/v1/ads`

## Authentication
All endpoints require authentication via <PERSON><PERSON><PERSON> token in the `Authorization` header:
```
Authorization: Bearer <jwt_token>
```

## Common Response Format
All API responses follow the standard format:
```json
{
  "success": true,
  "data": {...},
  "message": "Operation successful",
  "meta": {
    "timestamp": "2025-01-17T10:30:00Z",
    "request_id": "req_123456"
  }
}
```

## Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request data",
    "details": [
      {
        "field": "title",
        "message": "Title is required"
      }
    ]
  },
  "meta": {
    "timestamp": "2025-01-17T10:30:00Z",
    "request_id": "req_123456"
  }
}
```

## Campaign Management Endpoints

### Create Campaign
**POST** `/api/v1/ads/campaigns`

Creates a new advertising campaign.

**Request Body:**
```json
{
  "name": "Spring Sale Campaign",
  "description": "Promotional campaign for spring products",
  "budget": 1000.00,
  "start_date": "2025-03-01T00:00:00Z",
  "end_date": "2025-03-31T23:59:59Z",
  "status": "draft"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "tenant_id": 1,
    "name": "Spring Sale Campaign",
    "description": "Promotional campaign for spring products",
    "budget": 1000.00,
    "status": "draft",
    "start_date": "2025-03-01T00:00:00Z",
    "end_date": "2025-03-31T23:59:59Z",
    "created_at": "2025-01-17T10:30:00Z",
    "updated_at": "2025-01-17T10:30:00Z"
  }
}
```

### Get Campaign
**GET** `/api/v1/ads/campaigns/{id}`

Retrieves a specific campaign by ID.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "tenant_id": 1,
    "name": "Spring Sale Campaign",
    "description": "Promotional campaign for spring products",
    "budget": 1000.00,
    "status": "active",
    "start_date": "2025-03-01T00:00:00Z",
    "end_date": "2025-03-31T23:59:59Z",
    "created_at": "2025-01-17T10:30:00Z",
    "updated_at": "2025-01-17T10:30:00Z",
    "advertisements": [
      {
        "id": 1,
        "title": "Spring Sale Banner",
        "status": "active",
        "ad_type": "banner"
      }
    ]
  }
}
```

### List Campaigns
**GET** `/api/v1/ads/campaigns`

Retrieves all campaigns for the current tenant.

**Query Parameters:**
- `status` (optional): Filter by campaign status
- `limit` (optional): Number of records per page (default: 20)
- `cursor` (optional): Cursor for pagination

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "tenant_id": 1,
      "name": "Spring Sale Campaign",
      "status": "active",
      "budget": 1000.00,
      "start_date": "2025-03-01T00:00:00Z",
      "end_date": "2025-03-31T23:59:59Z",
      "created_at": "2025-01-17T10:30:00Z"
    }
  ],
  "meta": {
    "pagination": {
      "limit": 20,
      "next_cursor": "eyJpZCI6MX0=",
      "has_more": false
    }
  }
}
```

### Update Campaign
**PUT** `/api/v1/ads/campaigns/{id}`

Updates an existing campaign.

**Request Body:**
```json
{
  "name": "Updated Spring Sale Campaign",
  "description": "Updated promotional campaign",
  "budget": 1200.00,
  "status": "active"
}
```

### Delete Campaign
**DELETE** `/api/v1/ads/campaigns/{id}`

Soft deletes a campaign (sets status to 'deleted').

**Response:**
```json
{
  "success": true,
  "message": "Campaign deleted successfully"
}
```

## Advertisement Management Endpoints

### Create Advertisement
**POST** `/api/v1/ads/advertisements`

Creates a new advertisement within a campaign.

**Request Body:**
```json
{
  "campaign_id": 1,
  "title": "Spring Sale Banner",
  "description": "Promotional banner for spring products",
  "image_url": "https://example.com/banner.jpg",
  "link_url": "https://example.com/spring-sale",
  "ad_type": "banner",
  "device_targeting": "both",
  "page_targeting": ["homepage", "category"],
  "position": "header",
  "priority": 1,
  "status": "draft"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "tenant_id": 1,
    "campaign_id": 1,
    "title": "Spring Sale Banner",
    "description": "Promotional banner for spring products",
    "image_url": "https://example.com/banner.jpg",
    "link_url": "https://example.com/spring-sale",
    "ad_type": "banner",
    "device_targeting": "both",
    "page_targeting": ["homepage", "category"],
    "position": "header",
    "priority": 1,
    "status": "draft",
    "created_at": "2025-01-17T10:30:00Z",
    "updated_at": "2025-01-17T10:30:00Z"
  }
}
```

### Get Advertisement
**GET** `/api/v1/ads/advertisements/{id}`

Retrieves a specific advertisement by ID.

### List Advertisements
**GET** `/api/v1/ads/advertisements`

Retrieves all advertisements for the current tenant.

**Query Parameters:**
- `campaign_id` (optional): Filter by campaign ID
- `status` (optional): Filter by advertisement status
- `ad_type` (optional): Filter by advertisement type
- `device_targeting` (optional): Filter by device targeting
- `limit` (optional): Number of records per page (default: 20)
- `cursor` (optional): Cursor for pagination

### Update Advertisement
**PUT** `/api/v1/ads/advertisements/{id}`

Updates an existing advertisement.

### Delete Advertisement
**DELETE** `/api/v1/ads/advertisements/{id}`

Soft deletes an advertisement.

## Schedule Management Endpoints

### Create Schedule
**POST** `/api/v1/ads/schedules`

Creates a new schedule for an advertisement.

**Request Body:**
```json
{
  "advertisement_id": 1,
  "start_time": "2025-03-01T08:00:00Z",
  "end_time": "2025-03-01T18:00:00Z",
  "timezone": "America/New_York",
  "recurring_pattern": {
    "type": "daily",
    "days": ["monday", "tuesday", "wednesday", "thursday", "friday"]
  },
  "status": "active"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "tenant_id": 1,
    "advertisement_id": 1,
    "start_time": "2025-03-01T08:00:00Z",
    "end_time": "2025-03-01T18:00:00Z",
    "timezone": "America/New_York",
    "recurring_pattern": {
      "type": "daily",
      "days": ["monday", "tuesday", "wednesday", "thursday", "friday"]
    },
    "status": "active",
    "created_at": "2025-01-17T10:30:00Z",
    "updated_at": "2025-01-17T10:30:00Z"
  }
}
```

### Get Schedule
**GET** `/api/v1/ads/schedules/{id}`

### List Schedules
**GET** `/api/v1/ads/schedules`

**Query Parameters:**
- `advertisement_id` (optional): Filter by advertisement ID
- `status` (optional): Filter by schedule status

### Update Schedule
**PUT** `/api/v1/ads/schedules/{id}`

### Delete Schedule
**DELETE** `/api/v1/ads/schedules/{id}`

## Placement Management Endpoints

### Create Placement
**POST** `/api/v1/ads/placements`

Creates a new ad placement configuration.

**Request Body:**
```json
{
  "page_type": "homepage",
  "position": "header",
  "targeting_rules": {
    "device_types": ["web_pc", "web_mobile"],
    "min_screen_width": 768
  },
  "max_ads": 1,
  "status": "active"
}
```

### Get Placement
**GET** `/api/v1/ads/placements/{id}`

### List Placements
**GET** `/api/v1/ads/placements`

**Query Parameters:**
- `page_type` (optional): Filter by page type
- `position` (optional): Filter by position
- `status` (optional): Filter by placement status

### Update Placement
**PUT** `/api/v1/ads/placements/{id}`

### Delete Placement
**DELETE** `/api/v1/ads/placements/{id}`

## Analytics Endpoints

### Get Campaign Analytics
**GET** `/api/v1/ads/campaigns/{id}/analytics`

Retrieves analytics data for a specific campaign.

**Query Parameters:**
- `start_date` (optional): Start date for analytics (default: 7 days ago)
- `end_date` (optional): End date for analytics (default: today)
- `granularity` (optional): Data granularity (daily, weekly, monthly)

**Response:**
```json
{
  "success": true,
  "data": {
    "campaign_id": 1,
    "date_range": {
      "start_date": "2025-01-10T00:00:00Z",
      "end_date": "2025-01-17T23:59:59Z"
    },
    "summary": {
      "total_impressions": 15000,
      "total_clicks": 450,
      "ctr_rate": 0.0300,
      "total_revenue": 135.00,
      "cost_per_click": 0.30
    },
    "daily_data": [
      {
        "date": "2025-01-10",
        "impressions": 2000,
        "clicks": 60,
        "ctr_rate": 0.0300,
        "revenue": 18.00
      }
    ],
    "device_breakdown": {
      "web_pc": {
        "impressions": 9000,
        "clicks": 270,
        "ctr_rate": 0.0300
      },
      "web_mobile": {
        "impressions": 6000,
        "clicks": 180,
        "ctr_rate": 0.0300
      }
    }
  }
}
```

### Get Advertisement Analytics
**GET** `/api/v1/ads/advertisements/{id}/analytics`

Retrieves analytics data for a specific advertisement.

### Get Impression Stats
**GET** `/api/v1/ads/impressions/stats`

Retrieves impression statistics.

**Query Parameters:**
- `start_date` (optional): Start date for stats
- `end_date` (optional): End date for stats
- `advertisement_id` (optional): Filter by advertisement ID
- `device_type` (optional): Filter by device type

### Get Click Stats
**GET** `/api/v1/ads/clicks/stats`

Retrieves click statistics.

## Asset Management Endpoints

**Note**: Asset management for advertisements is handled through the Media module integration. Use the Media module API endpoints for uploading and managing advertisement assets.

### Attach Media to Advertisement
**POST** `/api/v1/ads/advertisements/{id}/media`

Attaches existing media files to an advertisement.

**Request Body:**
```json
{
  "media_file_ids": [1, 2, 3]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "advertisement_id": 1,
    "attached_media": [
      {
        "id": 1,
        "file_name": "banner.jpg",
        "file_path": "/uploads/media/1/banner.jpg",
        "mime_type": "image/jpeg"
      }
    ]
  }
}
```

### Get Advertisement Media
**GET** `/api/v1/ads/advertisements/{id}/media`

Retrieves all media files associated with an advertisement.

### Remove Media from Advertisement
**DELETE** `/api/v1/ads/advertisements/{id}/media/{media_id}`

Removes a media file association from an advertisement.

## Targeting Rules Endpoints

### Create Targeting Rule
**POST** `/api/v1/ads/targeting-rules`

Creates a new targeting rule for an advertisement.

**Request Body:**
```json
{
  "advertisement_id": 1,
  "rule_type": "page_url",
  "rule_key": "url",
  "rule_value": "/products/*",
  "operator": "starts_with",
  "priority": 1,
  "status": "active"
}
```

### Get Targeting Rule
**GET** `/api/v1/ads/targeting-rules/{id}`

### List Targeting Rules
**GET** `/api/v1/ads/targeting-rules`

**Query Parameters:**
- `advertisement_id` (optional): Filter by advertisement ID
- `rule_type` (optional): Filter by rule type

### Update Targeting Rule
**PUT** `/api/v1/ads/targeting-rules/{id}`

### Delete Targeting Rule
**DELETE** `/api/v1/ads/targeting-rules/{id}`

## Tracking Endpoints

### Record Impression
**POST** `/api/v1/ads/impressions`

Records an ad impression event.

**Request Body:**
```json
{
  "advertisement_id": 1,
  "placement_id": 1,
  "user_agent": "Mozilla/5.0...",
  "referrer": "https://example.com/page",
  "device_type": "web_pc",
  "page_url": "https://example.com/homepage",
  "view_duration": 5000
}
```

### Record Click
**POST** `/api/v1/ads/clicks`

Records an ad click event.

**Request Body:**
```json
{
  "advertisement_id": 1,
  "impression_id": 1,
  "user_agent": "Mozilla/5.0...",
  "referrer": "https://example.com/page",
  "device_type": "web_pc",
  "page_url": "https://example.com/homepage",
  "destination_url": "https://example.com/spring-sale"
}
```

### Get Tracking Stats
**GET** `/api/v1/ads/tracking/stats`

Retrieves combined tracking statistics.

## Ad Serving Endpoints

### Get Ads for Page
**GET** `/api/v1/ads/serve`

Retrieves ads to display on a specific page.

**Query Parameters:**
- `page_type` (required): Type of page (homepage, category, article, tag)
- `position` (required): Ad position (header, sidebar, footer, inline)
- `device_type` (optional): Device type (web_pc, web_mobile)
- `page_url` (optional): Current page URL
- `limit` (optional): Maximum number of ads to return (default: 1)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "Spring Sale Banner",
      "description": "Promotional banner for spring products",
      "image_url": "https://example.com/banner.jpg",
      "link_url": "https://example.com/spring-sale",
      "ad_type": "banner",
      "position": "header",
      "tracking_id": "trk_123456"
    }
  ]
}
```

## Administrative Endpoints

### Get Tenant Ad Stats
**GET** `/api/v1/ads/admin/stats`

Retrieves comprehensive ad statistics for the current tenant.

### Bulk Update Campaign Status
**PUT** `/api/v1/ads/admin/campaigns/status`

Updates status for multiple campaigns.

**Request Body:**
```json
{
  "campaign_ids": [1, 2, 3],
  "status": "paused"
}
```

### Export Analytics Data
**GET** `/api/v1/ads/admin/export`

Exports analytics data in various formats.

**Query Parameters:**
- `format` (required): Export format (csv, json, xlsx)
- `start_date` (optional): Start date for export
- `end_date` (optional): End date for export
- `campaign_ids` (optional): Specific campaigns to export

## Error Codes

| Code | Description |
|------|-------------|
| `CAMPAIGN_NOT_FOUND` | Campaign not found |
| `ADVERTISEMENT_NOT_FOUND` | Advertisement not found |
| `INVALID_SCHEDULE` | Invalid schedule configuration |
| `PLACEMENT_CONFLICT` | Placement configuration conflict |
| `ASSET_UPLOAD_FAILED` | Asset upload failed |
| `TARGETING_RULE_INVALID` | Invalid targeting rule |
| `IMPRESSION_TRACKING_FAILED` | Impression tracking failed |
| `CLICK_TRACKING_FAILED` | Click tracking failed |
| `ANALYTICS_UNAVAILABLE` | Analytics data unavailable |
| `UNAUTHORIZED_ACCESS` | Unauthorized access to resource |
| `TENANT_QUOTA_EXCEEDED` | Tenant quota exceeded |
| `INVALID_DATE_RANGE` | Invalid date range |
| `CAMPAIGN_BUDGET_EXCEEDED` | Campaign budget exceeded |

## Rate Limiting

All endpoints are subject to rate limiting:
- **Standard endpoints**: 100 requests per minute per user
- **Analytics endpoints**: 50 requests per minute per user
- **Tracking endpoints**: 1000 requests per minute per user
- **Asset upload endpoints**: 10 requests per minute per user

Rate limit headers are included in all responses:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642428000
```

## Pagination

All list endpoints support cursor-based pagination:
- Use `cursor` parameter for next page
- Use `limit` parameter to control page size (max 100)
- Response includes `next_cursor` and `has_more` in meta

## Filtering and Sorting

Most list endpoints support filtering and sorting:
- Filter by status, date ranges, and entity relationships
- Sort by creation date, name, or performance metrics
- Use query parameters for filtering and sorting options
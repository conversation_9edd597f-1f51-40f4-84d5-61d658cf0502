# Ads Module - Folder Structure

## Overview
This document outlines the complete folder structure for the Ads module, following the project's modular architecture and Go best practices.

## Module Directory Structure

### Main Module Directory
```
internal/modules/ads/
├── handlers/           # HTTP handlers (presentation layer)
├── models/            # Domain models and entities
├── repositories/      # Data access layer
├── services/         # Business logic layer
├── routes.go         # Route registration
└── module.go         # Module initialization
```

## Detailed Folder Structure

### 1. Handlers Directory
```
internal/modules/ads/handlers/
├── campaign_handler.go          # Campaign management endpoints
├── advertisement_handler.go     # Advertisement CRUD operations
├── schedule_handler.go          # Ad scheduling endpoints
├── placement_handler.go         # Ad placement management
├── analytics_handler.go         # Performance analytics endpoints
├── media_handler.go             # Media integration endpoints
├── targeting_handler.go         # Targeting rules endpoints
├── click_tracking_handler.go    # Click event tracking
├── impression_handler.go        # Impression tracking
├── admin_handler.go             # Administrative functions
└── middleware.go                # Ads-specific middleware
```

### 2. Models Directory
```
internal/modules/ads/models/
├── campaign.go                  # Campaign model and validation
├── advertisement.go             # Advertisement model and types
├── schedule.go                  # Schedule model and time handling
├── placement.go                 # Placement configuration model
├── impression.go                # Impression tracking model
├── click.go                     # Click event model
├── analytics.go                 # Analytics aggregation model
├── targeting_rule.go            # Targeting rules model
├── media_integration.go         # Media module integration types
├── requests.go                  # Request/response DTOs
├── responses.go                 # Response models
├── enums.go                     # Enum definitions
└── validators.go                # Custom validation rules
```

### 3. Repositories Directory
```
internal/modules/ads/repositories/
├── interfaces.go                # Repository interfaces
├── campaign_repository.go       # Campaign data access
├── advertisement_repository.go  # Advertisement data access
├── schedule_repository.go       # Schedule data access
├── placement_repository.go      # Placement data access
├── impression_repository.go     # Impression data access
├── click_repository.go          # Click data access
├── analytics_repository.go      # Analytics data access
├── targeting_repository.go      # Targeting rules data access
├── media_integration_repository.go # Media module integration
└── mysql/                       # MySQL implementations
    ├── campaign_mysql.go
    ├── advertisement_mysql.go
    ├── schedule_mysql.go
    ├── placement_mysql.go
    ├── impression_mysql.go
    ├── click_mysql.go
    ├── analytics_mysql.go
    ├── targeting_mysql.go
    └── media_integration_mysql.go
```

### 4. Services Directory
```
internal/modules/ads/services/
├── interfaces.go                # Service interfaces
├── campaign_service.go          # Campaign business logic
├── advertisement_service.go     # Advertisement business logic
├── schedule_service.go          # Scheduling business logic
├── placement_service.go         # Placement management logic
├── impression_service.go        # Impression tracking logic
├── click_service.go             # Click tracking logic
├── analytics_service.go         # Analytics processing logic
├── targeting_service.go         # Targeting engine logic
├── media_integration_service.go # Media module integration service
├── ad_serving_service.go        # Ad selection and serving
├── performance_service.go       # Performance optimization
└── integration_service.go       # External service integration
```

### 5. Database Migrations
```
internal/database/migrations/k_ads/
├── 1001_create_ads_campaigns_table.up.sql
├── 1001_create_ads_campaigns_table.down.sql
├── 1002_create_ads_advertisements_table.up.sql
├── 1002_create_ads_advertisements_table.down.sql
├── 1003_create_ads_schedules_table.up.sql
├── 1003_create_ads_schedules_table.down.sql
├── 1004_create_ads_placements_table.up.sql
├── 1004_create_ads_placements_table.down.sql
├── 1005_create_ads_impressions_table.up.sql
├── 1005_create_ads_impressions_table.down.sql
├── 1006_create_ads_clicks_table.up.sql
├── 1006_create_ads_clicks_table.down.sql
├── 1007_create_ads_analytics_table.up.sql
├── 1007_create_ads_analytics_table.down.sql
├── 1008_create_ads_assets_table.up.sql
├── 1008_create_ads_assets_table.down.sql
├── 1009_create_ads_targeting_rules_table.up.sql
└── 1009_create_ads_targeting_rules_table.down.sql
```

### 6. Seeders Directory
```
internal/database/seeders/ads/
├── ads_seeder.go                # Main seeder interface
├── campaign_seeder.go           # Campaign seed data
├── advertisement_seeder.go      # Advertisement seed data
├── placement_seeder.go          # Placement seed data
├── analytics_seeder.go          # Analytics seed data
└── register.go                  # Seeder registration
```

### 7. API Tests Directory
```
api-tests/bruno/Ads/
├── collection.bru               # Collection configuration
├── Campaigns/
│   ├── Create Campaign.bru
│   ├── Get Campaign.bru
│   ├── Update Campaign.bru
│   ├── Delete Campaign.bru
│   └── List Campaigns.bru
├── Advertisements/
│   ├── Create Advertisement.bru
│   ├── Get Advertisement.bru
│   ├── Update Advertisement.bru
│   ├── Delete Advertisement.bru
│   └── List Advertisements.bru
├── Schedules/
│   ├── Create Schedule.bru
│   ├── Get Schedule.bru
│   ├── Update Schedule.bru
│   └── Delete Schedule.bru
├── Placements/
│   ├── Create Placement.bru
│   ├── Get Placement.bru
│   ├── Update Placement.bru
│   └── List Placements.bru
├── Analytics/
│   ├── Get Campaign Analytics.bru
│   ├── Get Advertisement Analytics.bru
│   ├── Get Impression Stats.bru
│   └── Get Click Stats.bru
├── Assets/
│   ├── Upload Asset.bru
│   ├── Get Asset.bru
│   ├── Delete Asset.bru
│   └── List Assets.bru
├── Targeting/
│   ├── Create Targeting Rule.bru
│   ├── Get Targeting Rule.bru
│   ├── Update Targeting Rule.bru
│   └── Delete Targeting Rule.bru
└── Tracking/
    ├── Record Impression.bru
    ├── Record Click.bru
    └── Get Tracking Stats.bru
```

## File Naming Conventions

### Handler Files
- **Pattern**: `{entity}_handler.go`
- **Examples**: `campaign_handler.go`, `advertisement_handler.go`
- **Purpose**: HTTP request handling and response formatting

### Model Files
- **Pattern**: `{entity}.go`
- **Examples**: `campaign.go`, `advertisement.go`
- **Purpose**: Domain models, validation, and business rules

### Repository Files
- **Pattern**: `{entity}_repository.go`
- **Examples**: `campaign_repository.go`, `advertisement_repository.go`
- **Purpose**: Data access layer interfaces and implementations

### Service Files
- **Pattern**: `{entity}_service.go`
- **Examples**: `campaign_service.go`, `advertisement_service.go`
- **Purpose**: Business logic and service layer operations

### Migration Files
- **Pattern**: `{number}_{action}_{table}_table.{up|down}.sql`
- **Examples**: `1001_create_ads_campaigns_table.up.sql`
- **Purpose**: Database schema management

## Key Architecture Principles

### 1. Layer Separation
- **Handlers**: HTTP presentation layer
- **Services**: Business logic layer
- **Repositories**: Data access layer
- **Models**: Domain entities and DTOs

### 2. Dependency Injection
- Interfaces defined in each layer
- Implementation dependency injection
- Easy testing and mocking

### 3. Multi-Tenant Support
- Tenant context in all operations
- Tenant-specific data isolation
- Proper authorization checks

### 4. Error Handling
- Consistent error responses
- Proper error logging
- Graceful error recovery

## Integration Points

### With Other Modules
```
internal/modules/ads/services/integration_service.go
├── AuthIntegration()           # User authentication
├── TenantIntegration()         # Multi-tenant context
├── BlogIntegration()           # Content-based targeting
├── MediaIntegration()          # Asset management
├── AnalyticsIntegration()      # Performance tracking
└── NotificationIntegration()   # Alert systems
```

### External Services
```
internal/modules/ads/services/external/
├── google_ads_service.go       # Google Ads integration
├── facebook_ads_service.go     # Facebook Ads integration
├── analytics_service.go        # Google Analytics integration
├── cdn_service.go              # CDN for asset delivery
└── payment_service.go          # Revenue tracking
```

## Testing Structure

### Unit Tests
```
internal/modules/ads/
├── handlers/
│   └── *_test.go               # Handler unit tests
├── services/
│   └── *_test.go               # Service unit tests
├── repositories/
│   └── *_test.go               # Repository unit tests
└── models/
    └── *_test.go               # Model validation tests
```

### Integration Tests
```
internal/modules/ads/tests/
├── integration/
│   ├── campaign_test.go
│   ├── advertisement_test.go
│   └── analytics_test.go
├── fixtures/
│   ├── campaigns.json
│   ├── advertisements.json
│   └── analytics.json
└── helpers/
    ├── test_helper.go
    └── mock_helper.go
```

## Configuration Files

### Module Configuration
```
internal/modules/ads/config/
├── config.go                   # Module configuration
├── validation.go               # Validation rules
├── constants.go                # Module constants
└── types.go                    # Configuration types
```

### Environment Variables
```
# Ads Module Configuration
ADS_MAX_CAMPAIGNS_PER_TENANT=100
ADS_MAX_ADVERTISEMENTS_PER_CAMPAIGN=50
ADS_MAX_ASSET_SIZE_MB=5
ADS_DEFAULT_TIMEZONE=UTC
ADS_ANALYTICS_RETENTION_DAYS=365
ADS_CLICK_TRACKING_ENABLED=true
ADS_IMPRESSION_TRACKING_ENABLED=true
```

## Documentation Structure
```
docs/modules/ads/
├── README.md                   # Module overview
├── flow.md                     # Business workflows
├── models-schema.md            # Database schema
├── api-endpoints.md            # API documentation
├── folder-structure.md         # This file
└── diagrams/
    ├── ad-flow.mermaid         # Advertisement flow
    └── database-erd.mermaid    # Database ERD
```

## Best Practices

### 1. Code Organization
- Group related functionality together
- Use clear, descriptive file names
- Maintain consistent naming conventions
- Keep files focused on single responsibility

### 2. Error Handling
- Use structured error types
- Provide meaningful error messages
- Log errors appropriately
- Return proper HTTP status codes

### 3. Performance
- Implement caching where appropriate
- Use database indexes effectively
- Optimize query performance
- Handle concurrent requests safely

### 4. Security
- Validate all inputs
- Implement proper authorization
- Sanitize user-generated content
- Use secure asset handling

### 5. Testing
- Write comprehensive unit tests
- Include integration tests
- Test error scenarios
- Maintain test data fixtures

This folder structure provides a clean, maintainable, and scalable architecture for the Ads module while following Go best practices and the project's established patterns.
erDiagram
    tenants {
        int id PK
        string name
        string slug
        enum status
        datetime created_at
        datetime updated_at
    }
    
    ads_campaigns {
        int id PK
        int tenant_id FK
        string name
        string description
        decimal budget
        enum status
        datetime start_date
        datetime end_date
        datetime created_at
        datetime updated_at
    }
    
    ads_advertisements {
        int id PK
        int tenant_id FK
        int campaign_id FK
        string title
        text description
        string image_url
        string link_url
        enum ad_type
        enum device_targeting
        json page_targeting
        enum position
        int priority
        enum status
        datetime created_at
        datetime updated_at
    }
    
    ads_schedules {
        int id PK
        int tenant_id FK
        int advertisement_id FK
        datetime start_time
        datetime end_time
        string timezone
        json recurring_pattern
        enum status
        datetime created_at
        datetime updated_at
    }
    
    ads_placements {
        int id PK
        int tenant_id FK
        string page_type
        string position
        json targeting_rules
        int max_ads
        enum status
        datetime created_at
        datetime updated_at
    }
    
    ads_impressions {
        int id PK
        int tenant_id FK
        int advertisement_id FK
        int placement_id FK
        string user_agent
        string ip_address
        string referrer
        enum device_type
        string page_url
        datetime viewed_at
        int view_duration
        datetime created_at
    }
    
    ads_clicks {
        int id PK
        int tenant_id FK
        int advertisement_id FK
        int impression_id FK
        string user_agent
        string ip_address
        string referrer
        enum device_type
        string page_url
        datetime clicked_at
        string destination_url
        datetime created_at
    }
    
    ads_analytics {
        int id PK
        int tenant_id FK
        int advertisement_id FK
        date analytics_date
        int impressions_count
        int clicks_count
        decimal ctr_rate
        decimal cost_per_click
        decimal revenue
        json device_breakdown
        json page_breakdown
        datetime created_at
        datetime updated_at
    }
    
    ads_assets {
        int id PK
        int tenant_id FK
        int advertisement_id FK
        string asset_type
        string file_path
        string file_name
        int file_size
        string mime_type
        json metadata
        enum status
        datetime created_at
        datetime updated_at
    }
    
    ads_targeting_rules {
        int id PK
        int tenant_id FK
        int advertisement_id FK
        string rule_type
        string rule_key
        string rule_value
        string operator
        int priority
        enum status
        datetime created_at
        datetime updated_at
    }

    %% Relationships
    tenants ||--o{ ads_campaigns : "has many"
    tenants ||--o{ ads_advertisements : "has many"
    tenants ||--o{ ads_schedules : "has many"
    tenants ||--o{ ads_placements : "has many"
    tenants ||--o{ ads_impressions : "has many"
    tenants ||--o{ ads_clicks : "has many"
    tenants ||--o{ ads_analytics : "has many"
    tenants ||--o{ ads_assets : "has many"
    tenants ||--o{ ads_targeting_rules : "has many"
    
    ads_campaigns ||--o{ ads_advertisements : "contains"
    ads_advertisements ||--o{ ads_schedules : "has schedules"
    ads_advertisements ||--o{ ads_impressions : "generates"
    ads_advertisements ||--o{ ads_clicks : "receives"
    ads_advertisements ||--o{ ads_analytics : "tracked in"
    ads_advertisements ||--o{ ads_assets : "uses"
    ads_advertisements ||--o{ ads_targeting_rules : "has rules"
    
    ads_impressions ||--o{ ads_clicks : "may result in"
    ads_placements ||--o{ ads_impressions : "displays"
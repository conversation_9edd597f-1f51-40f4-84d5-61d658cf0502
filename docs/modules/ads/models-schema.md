# Ads Module - Database Schema & Models

## Overview
This document defines the complete database schema for the Ads module, including table structures, relationships, and model definitions following the project's MySQL 8 conventions.

## Database Tables

### 1. ads_campaigns
Manages advertising campaigns for tenant organizations.

```sql
CREATE TABLE IF NOT EXISTS ads_campaigns (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    budget DECIMAL(10,2) DEFAULT 0.00,
    status ENUM('draft', 'active', 'paused', 'completed', 'cancelled', 'deleted') NOT NULL DEFAULT 'draft',
    start_date DATETIME NOT NULL,
    end_date DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_ads_campaigns_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Unique Constraints
    UNIQUE KEY uk_ads_campaigns_tenant_name (tenant_id, name),
    
    -- Indexes
    INDEX idx_ads_campaigns_tenant_id (tenant_id),
    INDEX idx_ads_campaigns_status (status),
    INDEX idx_ads_campaigns_tenant_status (tenant_id, status),
    INDEX idx_ads_campaigns_dates (start_date, end_date),
    
    -- Check Constraints
    CONSTRAINT chk_ads_campaigns_dates CHECK (end_date > start_date),
    CONSTRAINT chk_ads_campaigns_budget CHECK (budget >= 0)
);
```

### 2. ads_advertisements
Stores individual advertisement content and configuration.

```sql
CREATE TABLE IF NOT EXISTS ads_advertisements (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    campaign_id INT UNSIGNED NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url VARCHAR(500),
    link_url VARCHAR(500) NOT NULL,
    ad_type ENUM('banner', 'text', 'rich_media', 'native', 'video') NOT NULL DEFAULT 'banner',
    device_targeting ENUM('web_pc', 'web_mobile', 'both') NOT NULL DEFAULT 'both',
    page_targeting JSON DEFAULT (JSON_ARRAY()),
    position ENUM('header', 'sidebar', 'footer', 'inline', 'popup') NOT NULL DEFAULT 'sidebar',
    priority INT NOT NULL DEFAULT 5,
    status ENUM('draft', 'active', 'paused', 'expired', 'deleted') NOT NULL DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_ads_advertisements_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_ads_advertisements_campaign_id FOREIGN KEY (campaign_id) REFERENCES ads_campaigns(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_ads_advertisements_tenant_id (tenant_id),
    INDEX idx_ads_advertisements_campaign_id (campaign_id),
    INDEX idx_ads_advertisements_status (status),
    INDEX idx_ads_advertisements_tenant_status (tenant_id, status),
    INDEX idx_ads_advertisements_device_targeting (device_targeting),
    INDEX idx_ads_advertisements_position (position),
    INDEX idx_ads_advertisements_priority (priority),
    
    -- Check Constraints
    CONSTRAINT chk_ads_advertisements_priority CHECK (priority >= 1 AND priority <= 10)
);
```

### 3. ads_schedules
Manages time-based advertisement scheduling.

```sql
CREATE TABLE IF NOT EXISTS ads_schedules (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    advertisement_id INT UNSIGNED NOT NULL,
    start_time DATETIME NOT NULL,
    end_time DATETIME NOT NULL,
    timezone VARCHAR(50) DEFAULT 'UTC',
    recurring_pattern JSON DEFAULT (JSON_OBJECT()),
    status ENUM('active', 'paused', 'expired', 'deleted') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_ads_schedules_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_ads_schedules_advertisement_id FOREIGN KEY (advertisement_id) REFERENCES ads_advertisements(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_ads_schedules_tenant_id (tenant_id),
    INDEX idx_ads_schedules_advertisement_id (advertisement_id),
    INDEX idx_ads_schedules_status (status),
    INDEX idx_ads_schedules_time_range (start_time, end_time),
    INDEX idx_ads_schedules_active_time (status, start_time, end_time),
    
    -- Check Constraints
    CONSTRAINT chk_ads_schedules_time CHECK (end_time > start_time)
);
```

### 4. ads_placements
Defines available ad placement positions on pages.

```sql
CREATE TABLE IF NOT EXISTS ads_placements (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    page_type ENUM('homepage', 'category', 'article', 'tag', 'custom') NOT NULL,
    position ENUM('header', 'sidebar', 'footer', 'inline', 'popup') NOT NULL,
    targeting_rules JSON DEFAULT (JSON_OBJECT()),
    max_ads INT NOT NULL DEFAULT 1,
    status ENUM('active', 'inactive', 'deleted') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_ads_placements_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Unique Constraints
    UNIQUE KEY uk_ads_placements_tenant_page_position (tenant_id, page_type, position),
    
    -- Indexes
    INDEX idx_ads_placements_tenant_id (tenant_id),
    INDEX idx_ads_placements_page_type (page_type),
    INDEX idx_ads_placements_position (position),
    INDEX idx_ads_placements_status (status),
    
    -- Check Constraints
    CONSTRAINT chk_ads_placements_max_ads CHECK (max_ads >= 1 AND max_ads <= 10)
);
```

### 5. ads_impressions
Tracks advertisement impression events.

```sql
CREATE TABLE IF NOT EXISTS ads_impressions (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    advertisement_id INT UNSIGNED NOT NULL,
    placement_id INT UNSIGNED NOT NULL,
    user_agent VARCHAR(500),
    ip_address VARCHAR(45),
    referrer VARCHAR(500),
    device_type ENUM('web_pc', 'web_mobile', 'unknown') NOT NULL DEFAULT 'unknown',
    page_url VARCHAR(500),
    viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    view_duration INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_ads_impressions_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_ads_impressions_advertisement_id FOREIGN KEY (advertisement_id) REFERENCES ads_advertisements(id) ON DELETE CASCADE,
    CONSTRAINT fk_ads_impressions_placement_id FOREIGN KEY (placement_id) REFERENCES ads_placements(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_ads_impressions_tenant_id (tenant_id),
    INDEX idx_ads_impressions_advertisement_id (advertisement_id),
    INDEX idx_ads_impressions_placement_id (placement_id),
    INDEX idx_ads_impressions_viewed_at (viewed_at),
    INDEX idx_ads_impressions_device_type (device_type),
    INDEX idx_ads_impressions_tenant_date (tenant_id, viewed_at),
    
    -- Check Constraints
    CONSTRAINT chk_ads_impressions_view_duration CHECK (view_duration >= 0)
);
```

### 6. ads_clicks
Records advertisement click events.

```sql
CREATE TABLE IF NOT EXISTS ads_clicks (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    advertisement_id INT UNSIGNED NOT NULL,
    impression_id INT UNSIGNED,
    user_agent VARCHAR(500),
    ip_address VARCHAR(45),
    referrer VARCHAR(500),
    device_type ENUM('web_pc', 'web_mobile', 'unknown') NOT NULL DEFAULT 'unknown',
    page_url VARCHAR(500),
    clicked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    destination_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_ads_clicks_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_ads_clicks_advertisement_id FOREIGN KEY (advertisement_id) REFERENCES ads_advertisements(id) ON DELETE CASCADE,
    CONSTRAINT fk_ads_clicks_impression_id FOREIGN KEY (impression_id) REFERENCES ads_impressions(id) ON DELETE SET NULL,
    
    -- Indexes
    INDEX idx_ads_clicks_tenant_id (tenant_id),
    INDEX idx_ads_clicks_advertisement_id (advertisement_id),
    INDEX idx_ads_clicks_impression_id (impression_id),
    INDEX idx_ads_clicks_clicked_at (clicked_at),
    INDEX idx_ads_clicks_device_type (device_type),
    INDEX idx_ads_clicks_tenant_date (tenant_id, clicked_at)
);
```

### 7. ads_analytics
Stores aggregated analytics data for performance tracking.

```sql
CREATE TABLE IF NOT EXISTS ads_analytics (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    advertisement_id INT UNSIGNED NOT NULL,
    analytics_date DATE NOT NULL,
    impressions_count INT DEFAULT 0,
    clicks_count INT DEFAULT 0,
    ctr_rate DECIMAL(5,4) DEFAULT 0.0000,
    cost_per_click DECIMAL(10,2) DEFAULT 0.00,
    revenue DECIMAL(10,2) DEFAULT 0.00,
    device_breakdown JSON DEFAULT (JSON_OBJECT()),
    page_breakdown JSON DEFAULT (JSON_OBJECT()),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_ads_analytics_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_ads_analytics_advertisement_id FOREIGN KEY (advertisement_id) REFERENCES ads_advertisements(id) ON DELETE CASCADE,
    
    -- Unique Constraints
    UNIQUE KEY uk_ads_analytics_tenant_ad_date (tenant_id, advertisement_id, analytics_date),
    
    -- Indexes
    INDEX idx_ads_analytics_tenant_id (tenant_id),
    INDEX idx_ads_analytics_advertisement_id (advertisement_id),
    INDEX idx_ads_analytics_date (analytics_date),
    INDEX idx_ads_analytics_tenant_date (tenant_id, analytics_date),
    
    -- Check Constraints
    CONSTRAINT chk_ads_analytics_counts CHECK (impressions_count >= 0 AND clicks_count >= 0),
    CONSTRAINT chk_ads_analytics_ctr CHECK (ctr_rate >= 0.0000 AND ctr_rate <= 1.0000),
    CONSTRAINT chk_ads_analytics_financial CHECK (cost_per_click >= 0 AND revenue >= 0)
);
```

### 8. ads_targeting_rules
Stores complex targeting rules for advertisements.

```sql
CREATE TABLE IF NOT EXISTS ads_targeting_rules (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    advertisement_id INT UNSIGNED NOT NULL,
    rule_type ENUM('page_url', 'referrer', 'device', 'time', 'location', 'custom') NOT NULL,
    rule_key VARCHAR(255) NOT NULL,
    rule_value TEXT NOT NULL,
    operator ENUM('equals', 'not_equals', 'contains', 'not_contains', 'starts_with', 'ends_with', 'regex') NOT NULL DEFAULT 'equals',
    priority INT NOT NULL DEFAULT 1,
    status ENUM('active', 'inactive', 'deleted') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_ads_targeting_rules_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_ads_targeting_rules_advertisement_id FOREIGN KEY (advertisement_id) REFERENCES ads_advertisements(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_ads_targeting_rules_tenant_id (tenant_id),
    INDEX idx_ads_targeting_rules_advertisement_id (advertisement_id),
    INDEX idx_ads_targeting_rules_rule_type (rule_type),
    INDEX idx_ads_targeting_rules_status (status),
    INDEX idx_ads_targeting_rules_priority (priority),
    
    -- Check Constraints
    CONSTRAINT chk_ads_targeting_rules_priority CHECK (priority >= 1 AND priority <= 100)
);
```

## Go Model Definitions

### Campaign Model
```go
type Campaign struct {
    ID          uint      `json:"id" gorm:"primaryKey"`
    TenantID    uint      `json:"tenant_id" gorm:"not null;index"`
    Name        string    `json:"name" gorm:"not null;size:255"`
    Description string    `json:"description,omitempty" gorm:"type:text"`
    Budget      float64   `json:"budget" gorm:"type:decimal(10,2);default:0.00"`
    Status      string    `json:"status" gorm:"type:enum('draft','active','paused','completed','cancelled','deleted');default:'draft'"`
    StartDate   time.Time `json:"start_date" gorm:"not null"`
    EndDate     time.Time `json:"end_date" gorm:"not null"`
    CreatedAt   time.Time `json:"created_at" gorm:"default:CURRENT_TIMESTAMP"`
    UpdatedAt   time.Time `json:"updated_at" gorm:"default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
    
    // Relationships
    Advertisements []Advertisement `json:"advertisements,omitempty" gorm:"foreignKey:CampaignID"`
}
```

### Advertisement Model
```go
type Advertisement struct {
    ID              uint            `json:"id" gorm:"primaryKey"`
    TenantID        uint            `json:"tenant_id" gorm:"not null;index"`
    CampaignID      uint            `json:"campaign_id" gorm:"not null;index"`
    Title           string          `json:"title" gorm:"not null;size:255"`
    Description     string          `json:"description,omitempty" gorm:"type:text"`
    ImageURL        string          `json:"image_url,omitempty" gorm:"size:500"`
    LinkURL         string          `json:"link_url" gorm:"not null;size:500"`
    AdType          string          `json:"ad_type" gorm:"type:enum('banner','text','rich_media','native','video');default:'banner'"`
    DeviceTargeting string          `json:"device_targeting" gorm:"type:enum('web_pc','web_mobile','both');default:'both'"`
    PageTargeting   datatypes.JSON  `json:"page_targeting" gorm:"type:json"`
    Position        string          `json:"position" gorm:"type:enum('header','sidebar','footer','inline','popup');default:'sidebar'"`
    Priority        int             `json:"priority" gorm:"not null;default:5"`
    Status          string          `json:"status" gorm:"type:enum('draft','active','paused','expired','deleted');default:'draft'"`
    CreatedAt       time.Time       `json:"created_at" gorm:"default:CURRENT_TIMESTAMP"`
    UpdatedAt       time.Time       `json:"updated_at" gorm:"default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
    
    // Relationships
    Campaign     Campaign          `json:"campaign,omitempty" gorm:"foreignKey:CampaignID"`
    Schedules    []Schedule        `json:"schedules,omitempty" gorm:"foreignKey:AdvertisementID"`
    Impressions  []Impression      `json:"impressions,omitempty" gorm:"foreignKey:AdvertisementID"`
    Clicks       []Click           `json:"clicks,omitempty" gorm:"foreignKey:AdvertisementID"`
    Analytics    []Analytics       `json:"analytics,omitempty" gorm:"foreignKey:AdvertisementID"`
    // Note: Assets are managed through the Media module integration
    TargetingRules []TargetingRule `json:"targeting_rules,omitempty" gorm:"foreignKey:AdvertisementID"`
}
```

### Schedule Model
```go
type Schedule struct {
    ID               uint            `json:"id" gorm:"primaryKey"`
    TenantID         uint            `json:"tenant_id" gorm:"not null;index"`
    AdvertisementID  uint            `json:"advertisement_id" gorm:"not null;index"`
    StartTime        time.Time       `json:"start_time" gorm:"not null"`
    EndTime          time.Time       `json:"end_time" gorm:"not null"`
    Timezone         string          `json:"timezone" gorm:"size:50;default:'UTC'"`
    RecurringPattern datatypes.JSON  `json:"recurring_pattern" gorm:"type:json"`
    Status           string          `json:"status" gorm:"type:enum('active','paused','expired','deleted');default:'active'"`
    CreatedAt        time.Time       `json:"created_at" gorm:"default:CURRENT_TIMESTAMP"`
    UpdatedAt        time.Time       `json:"updated_at" gorm:"default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
    
    // Relationships
    Advertisement Advertisement `json:"advertisement,omitempty" gorm:"foreignKey:AdvertisementID"`
}
```

### Impression Model
```go
type Impression struct {
    ID              uint      `json:"id" gorm:"primaryKey"`
    TenantID        uint      `json:"tenant_id" gorm:"not null;index"`
    AdvertisementID uint      `json:"advertisement_id" gorm:"not null;index"`
    PlacementID     uint      `json:"placement_id" gorm:"not null;index"`
    UserAgent       string    `json:"user_agent,omitempty" gorm:"size:500"`
    IPAddress       string    `json:"ip_address,omitempty" gorm:"size:45"`
    Referrer        string    `json:"referrer,omitempty" gorm:"size:500"`
    DeviceType      string    `json:"device_type" gorm:"type:enum('web_pc','web_mobile','unknown');default:'unknown'"`
    PageURL         string    `json:"page_url,omitempty" gorm:"size:500"`
    ViewedAt        time.Time `json:"viewed_at" gorm:"default:CURRENT_TIMESTAMP"`
    ViewDuration    int       `json:"view_duration" gorm:"default:0"`
    CreatedAt       time.Time `json:"created_at" gorm:"default:CURRENT_TIMESTAMP"`
    
    // Relationships
    Advertisement Advertisement `json:"advertisement,omitempty" gorm:"foreignKey:AdvertisementID"`
    Placement     Placement     `json:"placement,omitempty" gorm:"foreignKey:PlacementID"`
    Clicks        []Click       `json:"clicks,omitempty" gorm:"foreignKey:ImpressionID"`
}
```

### Click Model
```go
type Click struct {
    ID              uint      `json:"id" gorm:"primaryKey"`
    TenantID        uint      `json:"tenant_id" gorm:"not null;index"`
    AdvertisementID uint      `json:"advertisement_id" gorm:"not null;index"`
    ImpressionID    *uint     `json:"impression_id,omitempty" gorm:"index"`
    UserAgent       string    `json:"user_agent,omitempty" gorm:"size:500"`
    IPAddress       string    `json:"ip_address,omitempty" gorm:"size:45"`
    Referrer        string    `json:"referrer,omitempty" gorm:"size:500"`
    DeviceType      string    `json:"device_type" gorm:"type:enum('web_pc','web_mobile','unknown');default:'unknown'"`
    PageURL         string    `json:"page_url,omitempty" gorm:"size:500"`
    ClickedAt       time.Time `json:"clicked_at" gorm:"default:CURRENT_TIMESTAMP"`
    DestinationURL  string    `json:"destination_url,omitempty" gorm:"size:500"`
    CreatedAt       time.Time `json:"created_at" gorm:"default:CURRENT_TIMESTAMP"`
    
    // Relationships
    Advertisement Advertisement `json:"advertisement,omitempty" gorm:"foreignKey:AdvertisementID"`
    Impression    *Impression   `json:"impression,omitempty" gorm:"foreignKey:ImpressionID"`
}
```

## Key Features

### Multi-Tenant Isolation
- All tables include `tenant_id` with proper foreign key constraints
- Tenant-specific indexes for performance optimization
- Cascading deletes maintain data integrity

### Performance Optimization
- Strategic indexing on commonly queried fields
- Composite indexes for multi-column queries
- Separate analytics table for aggregated data

### Data Integrity
- CHECK constraints for business rules
- Proper foreign key relationships
- Enum values for controlled data entry

### Scalability Considerations
- Partitioning support for large analytics tables
- Efficient indexing for time-based queries
- JSON fields for flexible configuration storage

## Migration Files Structure
```
internal/database/migrations/k_ads/
├── 1001_create_ads_campaigns_table.up.sql
├── 1001_create_ads_campaigns_table.down.sql
├── 1002_create_ads_advertisements_table.up.sql
├── 1002_create_ads_advertisements_table.down.sql
├── 1003_create_ads_schedules_table.up.sql
├── 1003_create_ads_schedules_table.down.sql
├── 1004_create_ads_placements_table.up.sql
├── 1004_create_ads_placements_table.down.sql
├── 1005_create_ads_impressions_table.up.sql
├── 1005_create_ads_impressions_table.down.sql
├── 1006_create_ads_clicks_table.up.sql
├── 1006_create_ads_clicks_table.down.sql
├── 1007_create_ads_analytics_table.up.sql
├── 1007_create_ads_analytics_table.down.sql
├── 1009_create_ads_targeting_rules_table.up.sql
└── 1009_create_ads_targeting_rules_table.down.sql
```

## Media Module Integration

**Note**: The ads_assets table has been removed from this module as asset management is handled through the existing Media module integration. 

### Asset Management Approach
- Advertisement assets (images, videos, etc.) are stored in the `media_files` table
- Use the `advertisement_id` as a reference in the media file metadata
- Asset relationships are managed through the Media module's API
- This approach provides:
  - Unified asset management across all modules
  - Better CDN integration and optimization
  - Consistent file handling and security
  - Reduced database complexity

### Integration Pattern
```go
// In Advertisement model
type Advertisement struct {
    // ... other fields
    MediaFileIDs []uint `json:"media_file_ids,omitempty"` // References to media module
    // ... other fields
}

// Service layer integration
func (s *AdvertisementService) AttachMedia(adID uint, mediaIDs []uint) error {
    // Use Media module service to associate files with advertisement
    return s.mediaService.AssociateWithEntity("advertisement", adID, mediaIDs)
}
```
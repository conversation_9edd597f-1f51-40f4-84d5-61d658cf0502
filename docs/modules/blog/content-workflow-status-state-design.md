# Status vs Workflow State Design for Blog Content Management

## Overview
This document analyzes the separation of `status` (content state) and `workflow_state` (editorial process state) for better system design and flexibility.

## Current Problem
Currently, the system mixes content status and workflow state in a single `status` field, which creates several issues:
- Difficult to track both content state and editorial process separately
- Complex status transitions that mix business logic
- Hard to customize workflow without affecting content states
- Unclear separation of concerns

## Proposed Solution: Separate Status and Workflow State

### 1. Content Status (status)
Represents the actual state of the content itself:

```go
type BlogPostStatus string

const (
    BlogPostStatusDraft      BlogPostStatus = "draft"      // Nội dung nháp
    BlogPostStatusPublished  BlogPostStatus = "published"  // Đã xuất bản
    BlogPostStatusScheduled  BlogPostStatus = "scheduled"  // Hẹn giờ xuất bản
    BlogPostStatusArchived   BlogPostStatus = "archived"   // Lưu trữ
    BlogPostStatusDeleted    BlogPostStatus = "deleted"    // Đã xóa (soft delete)
)
```

### 2. Workflow State (workflow_state)
Represents where the content is in the editorial process:

```go
type WorkflowState string

const (
    WorkflowStateCreation          WorkflowState = "creation"           // Đang soạn thảo
    WorkflowStatePendingReview     WorkflowState = "pending_review"     // Chờ biên tập
    WorkflowStateInReview          WorkflowState = "in_review"          // Đang biên tập
    WorkflowStatePendingApproval   WorkflowState = "pending_approval"   // Chờ duyệt xuất bản
    WorkflowStatePendingEIC        WorkflowState = "pending_eic"        // Chờ TBT duyệt
    WorkflowStateApproved          WorkflowState = "approved"           // Đã duyệt
    WorkflowStateReturned          WorkflowState = "returned"           // Bị trả lại
    WorkflowStateRejected          WorkflowState = "rejected"           // Bị từ chối
    WorkflowStateCompleted         WorkflowState = "completed"          // Hoàn thành
)
```

## Comparison Table

| Scenario | Current Design (Single Status) | New Design (Status + Workflow State) |
|----------|-------------------------------|--------------------------------------|
| New draft article | status = "draft" | status = "draft", workflow_state = "creation" |
| Draft submitted for review | status = "review" | status = "draft", workflow_state = "pending_review" |
| Article being edited | status = "review" | status = "draft", workflow_state = "in_review" |
| Article approved for publication | status = "pending_publication" | status = "draft", workflow_state = "pending_approval" |
| Article published | status = "published" | status = "published", workflow_state = "completed" |
| Published article sent for re-edit | status = "review" (??) | status = "published", workflow_state = "in_review" |
| Scheduled article in review | status = "scheduled" or "review" (??) | status = "scheduled", workflow_state = "in_review" |

## Benefits of Separation

### 1. Clear Separation of Concerns
- **Status**: What is the content's state?
- **Workflow State**: Where is it in the editorial process?

### 2. More Flexible Workflows
- Can have published articles go through review again
- Can review scheduled articles without changing their scheduling
- Can archive articles at any workflow stage

### 3. Better Business Logic
```go
// Check if article can be edited
func CanEdit(post BlogPost) bool {
    // Old way: complex status checks
    // return post.Status == "draft" || post.Status == "review" || post.Status == "returned"
    
    // New way: clear workflow state check
    return post.WorkflowState != WorkflowStateCompleted && 
           post.WorkflowState != WorkflowStateRejected
}

// Check if article is visible to public
func IsPubliclyVisible(post BlogPost) bool {
    // Old way: mixed with workflow
    // return post.Status == "published"
    
    // New way: clear content status
    return post.Status == BlogPostStatusPublished && 
           post.PublishedAt != nil && 
           post.PublishedAt.Before(time.Now())
}
```

### 4. Easier Reporting and Analytics
- Can report on content states separately from workflow efficiency
- Track how long articles spend in each workflow state
- Identify workflow bottlenecks without mixing content metrics

## Database Schema Updates

### blog_posts table
```sql
ALTER TABLE blog_posts 
-- Keep status for content state
MODIFY COLUMN status ENUM('draft', 'published', 'scheduled', 'archived', 'deleted') NOT NULL DEFAULT 'draft',
-- Add workflow_state for editorial process
ADD COLUMN workflow_state VARCHAR(50) DEFAULT 'creation' AFTER status,
-- Add workflow metadata
ADD COLUMN workflow_assigned_to INT UNSIGNED DEFAULT NULL,
ADD COLUMN workflow_assigned_at TIMESTAMP NULL,
ADD COLUMN workflow_due_at TIMESTAMP NULL,
-- Add indexes
ADD INDEX idx_blog_posts_workflow_state (workflow_state),
ADD INDEX idx_blog_posts_workflow_assigned (workflow_assigned_to, workflow_state);
```

## State Transition Rules

### Content Status Transitions
```mermaid
stateDiagram-v2
    [*] --> draft: Create
    draft --> published: Publish
    draft --> scheduled: Schedule
    scheduled --> published: Auto-publish
    published --> archived: Archive
    draft --> deleted: Delete
    published --> deleted: Delete
    archived --> deleted: Delete
    
    published --> draft: Unpublish
    scheduled --> draft: Unschedule
    archived --> published: Restore
```

### Workflow State Transitions
```mermaid
stateDiagram-v2
    [*] --> creation: Start
    creation --> pending_review: Submit
    pending_review --> in_review: Accept Review
    in_review --> pending_approval: Approve
    in_review --> returned: Return
    pending_approval --> pending_eic: Escalate
    pending_approval --> approved: Approve
    pending_eic --> approved: EIC Approve
    pending_eic --> returned: EIC Return
    approved --> completed: Publish
    
    returned --> creation: Revise
    in_review --> rejected: Reject
    pending_approval --> rejected: Reject
    pending_eic --> rejected: Reject
    
    completed --> pending_review: Re-edit
```

## Implementation Strategy

### Phase 1: Add Workflow State
1. Add `workflow_state` column to blog_posts
2. Migrate existing data:
   - `status = "draft"` → `workflow_state = "creation"`
   - `status = "review"` → `workflow_state = "in_review"`
   - `status = "published"` → `workflow_state = "completed"`
   - etc.

### Phase 2: Update Business Logic
1. Update services to use both fields
2. Separate status and workflow validations
3. Update API responses to include both fields

### Phase 3: Refactor Status Field
1. Update status enum to remove workflow states
2. Clean up redundant status values
3. Update all queries and logic

## API Response Example

### Current Response
```json
{
  "id": 123,
  "title": "Article Title",
  "status": "review",
  "author_id": 456
}
```

### New Response
```json
{
  "id": 123,
  "title": "Article Title",
  "status": "draft",
  "workflow": {
    "state": "in_review",
    "assigned_to": 789,
    "assigned_at": "2025-01-25T10:00:00Z",
    "due_at": "2025-01-26T10:00:00Z",
    "can_edit": true,
    "can_publish": false,
    "available_actions": ["approve", "return", "reject"]
  },
  "author_id": 456
}
```

## Workflow Configuration per Tenant

With separated concerns, workflow configuration becomes cleaner:

```json
{
  "workflow_states": {
    "pending_review": {
      "name": "Chờ biên tập",
      "assigned_role": "editor",
      "auto_assign": true,
      "sla_hours": 24
    },
    "pending_approval": {
      "name": "Chờ xuất bản", 
      "assigned_role": "secretary",
      "requires_approval": true,
      "can_skip": false
    }
  },
  "transitions": {
    "creation->pending_review": {
      "allowed_roles": ["reporter", "editor"],
      "conditions": ["has_content", "has_title"]
    },
    "pending_review->in_review": {
      "allowed_roles": ["editor"],
      "auto_transition": true
    }
  }
}
```

## Migration Plan

### Step 1: Database Migration (Week 1)
```sql
-- Add new columns
ALTER TABLE blog_posts ADD COLUMN workflow_state VARCHAR(50) DEFAULT 'creation';

-- Migrate data
UPDATE blog_posts 
SET workflow_state = CASE
    WHEN status = 'draft' THEN 'creation'
    WHEN status = 'review' THEN 'in_review'
    WHEN status = 'pending_publication' THEN 'pending_approval'
    WHEN status = 'pending_eic_approval' THEN 'pending_eic'
    WHEN status = 'published' THEN 'completed'
    WHEN status = 'returned' THEN 'returned'
    WHEN status = 'rejected' THEN 'rejected'
    ELSE 'creation'
END;
```

### Step 2: Code Updates (Week 2-3)
1. Update models to include workflow_state
2. Update repositories to handle both fields
3. Update services with new business logic
4. Update API responses

### Step 3: Testing & Rollout (Week 4)
1. Comprehensive testing of all scenarios
2. Gradual rollout per tenant
3. Monitor and fix issues

### Step 4: Cleanup (Week 5)
1. Remove old status values
2. Update documentation
3. Training for users

## Conclusion

Separating `status` and `workflow_state` provides:
- Clearer data model
- More flexible workflows
- Better maintainability
- Easier customization per tenant
- Cleaner business logic

This separation is essential for a professional content management system that needs to handle complex editorial workflows while maintaining simple content states.
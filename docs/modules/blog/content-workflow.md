# Content Workflow - Editorial Management System

## Overview
This document describes the complete editorial content workflow system with a 4-level hierarchical management structure designed for professional newsroom operations in the Blog API v3 system.

**🎯 KEY DESIGN DECISION**: This system separates `status` (content state) and `workflow_state` (editorial process) for maximum flexibility and clarity.

## Problem Analysis: Why Separate Status and Workflow State?

### Current Single Status Problems
The current single `status` field creates ambiguity in complex scenarios:

| Scenario | Single Status Issue | Our Solution |
|----------|-------------------|--------------|
| Published article needs editing | `"review"` (loses public visibility) or `"published"` (can't track editing)? | `status="published"` + `workflow_state="in_review"` |
| Scheduled article needs review | `"scheduled"` or `"review"`? | `status="scheduled"` + `workflow_state="in_review"` |
| Bulk operations on mixed states | Complex logic mixing content and workflow | Separate filters for content vs workflow |
| Published article reverted to draft | Loses publish history | `status="draft"` + workflow history preserved |
| Track editorial efficiency | Mixed metrics | Pure workflow analytics |

## System Architecture

### Content Status (status) - "What is the content?"
```go
type BlogPostStatus string

const (
    BlogPostStatusDraft      BlogPostStatus = "draft"      // Nội dung nháp
    BlogPostStatusPublished  BlogPostStatus = "published"  // Đã xuất bản công khai
    BlogPostStatusScheduled  BlogPostStatus = "scheduled"  // Hẹn giờ xuất bản
    BlogPostStatusArchived   BlogPostStatus = "archived"   // Lưu trữ
    BlogPostStatusDeleted    BlogPostStatus = "deleted"    // Đã xóa
)
```

### Workflow State (workflow_state) - "Where in the editorial process?"
```go
type WorkflowState string

const (
    WorkflowStateCreation          WorkflowState = "creation"           // Đang soạn thảo
    WorkflowStatePendingReview     WorkflowState = "pending_review"     // Chờ biên tập
    WorkflowStateInReview          WorkflowState = "in_review"          // Đang biên tập
    WorkflowStatePendingApproval   WorkflowState = "pending_approval"   // Chờ duyệt xuất bản
    WorkflowStatePendingEIC        WorkflowState = "pending_eic"        // Chờ TBT duyệt
    WorkflowStateApproved          WorkflowState = "approved"           // Đã duyệt
    WorkflowStateReturned          WorkflowState = "returned"           // Bị trả lại
    WorkflowStateRejected          WorkflowState = "rejected"           // Bị từ chối
    WorkflowStateCompleted         WorkflowState = "completed"          // Hoàn thành
)
```

## Editorial Hierarchy & Responsibilities

### 1. Reporter (Phóng viên)
**Workflow States**: `creation`, `returned`
- Create and edit articles in `creation` state
- Revise articles when `returned` by editors
- Submit articles to `pending_review`

**Permissions**:
- ✅ Create new articles (`status=draft`, `workflow_state=creation`)
- ✅ Edit own articles in `creation` or `returned` states
- ✅ Submit for review (`creation` → `pending_review`)
- ✅ Withdraw unaccepted submissions (`pending_review` → `creation`)
- ❌ Edit articles once accepted by editors

### 2. Editor (Biên tập viên)
**Workflow States**: `pending_review`, `in_review`
- Accept articles from `pending_review` → `in_review`
- Edit and improve content quality
- Forward to `pending_approval` or return to `returned`

**Permissions**:
- ✅ All reporter permissions PLUS
- ✅ Accept review assignments (`pending_review` → `in_review`)
- ✅ Edit articles in `in_review` state
- ✅ Approve for publication (`in_review` → `pending_approval`)
- ✅ Return to reporter (`in_review` → `returned`)
- ✅ Reject articles (`in_review` → `rejected`)

### 3. Editorial Secretary (Thư ký tòa soạn)
**Workflow States**: `pending_approval`, `approved`
- Final review before publication
- Decide on publication or escalation to EIC
- Manage publication workflow

**Permissions**:
- ✅ All editor permissions PLUS
- ✅ Review for publication (`pending_approval`)
- ✅ Approve for publication (`pending_approval` → `approved`)
- ✅ Escalate sensitive content (`pending_approval` → `pending_eic`)
- ✅ Publish articles (`approved` → `completed` + `status=published`)
- ✅ Schedule articles (`approved` → `completed` + `status=scheduled`)

### 4. Editor-in-Chief (Tổng biên tập)
**Workflow States**: `pending_eic`, ALL states
- Final authority on sensitive content
- Override any workflow decision
- Post-publication management

**Permissions**:
- ✅ ALL previous permissions PLUS
- ✅ Handle EIC escalations (`pending_eic`)
- ✅ Edit published content (`status=published` + `workflow_state=in_review`)
- ✅ Unpublish articles (`status=published` → `status=draft`)
- ✅ Archive content (`status=*` → `status=archived`)
- ✅ Override any workflow restrictions

## Workflow State Diagrams

### Content Status Flow
```mermaid
stateDiagram-v2
    [*] --> draft: Create Article
    draft --> published: Publish
    draft --> scheduled: Schedule
    scheduled --> published: Auto-publish
    published --> archived: Archive
    
    published --> draft: Unpublish
    scheduled --> draft: Unschedule
    archived --> published: Restore
    
    draft --> deleted: Delete
    published --> deleted: Delete
    archived --> deleted: Delete
    
    note right of draft
        Content: Draft
        Public: No
        Editable: Depends on workflow
    end note
    
    note right of published
        Content: Published
        Public: Yes
        Editable: Only by EIC
    end note
    
    note right of scheduled
        Content: Scheduled
        Public: Future
        Auto-publish at scheduled_at
    end note
```

### Editorial Workflow Flow
```mermaid
stateDiagram-v2
    [*] --> creation: Reporter starts
    creation --> pending_review: Submit for review
    pending_review --> in_review: Editor accepts
    in_review --> pending_approval: Editor approves
    in_review --> returned: Editor returns
    pending_approval --> pending_eic: Secretary escalates
    pending_approval --> approved: Secretary approves
    pending_eic --> approved: EIC approves
    pending_eic --> returned: EIC returns
    approved --> completed: Publish/Schedule
    
    returned --> creation: Reporter revises
    in_review --> rejected: Editor rejects
    pending_approval --> rejected: Secretary rejects
    pending_eic --> rejected: EIC rejects
    
    completed --> pending_review: Re-edit request
    
    note right of in_review
        Handler: Editor
        Content: Still draft
        Reporter: No access
    end note
    
    note right of pending_approval
        Handler: Secretary
        Content: Ready for publication
        Can escalate to EIC
    end note
    
    note right of completed
        Workflow: Done
        Content: Published/Scheduled
        Can restart for edits
    end note
```

## Status + Workflow State Matrix

| Content Status | Workflow State | Description | Public | Editable | Handler |
|---------------|----------------|-------------|---------|----------|---------|
| draft | creation | Đang soạn thảo | ❌ | ✅ | Reporter |
| draft | pending_review | Nháp chờ duyệt | ❌ | ❌ | Editor Queue |
| draft | in_review | Đang được biên tập | ❌ | ❌ | Editor |
| draft | pending_approval | Chờ duyệt xuất bản | ❌ | ❌ | Secretary |
| draft | pending_eic | Chờ TBT duyệt | ❌ | ❌ | EIC |
| draft | approved | Đã duyệt, chờ xuất bản | ❌ | ❌ | Secretary |
| draft | returned | Bị trả lại chỉnh sửa | ❌ | ✅ | Reporter |
| draft | rejected | Bị từ chối | ❌ | ❌ | None |
| published | completed | Đã xuất bản hoàn tất | ✅ | ❌ | EIC only |
| published | in_review | Bài đăng cần chỉnh sửa | ✅ | ❌ | Editor |
| scheduled | approved | Hẹn giờ đã duyệt | ⏰ | ❌ | Auto-publish |
| scheduled | in_review | Bài hẹn giờ cần sửa | ⏰ | ❌ | Editor |
| archived | completed | Lưu trữ | ❌ | ❌ | EIC only |

## Database Schema Integration

### Updated blog_posts Table
```sql
-- Add new workflow columns to existing table
ALTER TABLE blog_posts 
-- Keep existing status column, update enum
MODIFY COLUMN status ENUM('draft', 'published', 'scheduled', 'archived', 'deleted') 
    NOT NULL DEFAULT 'draft',

-- Add workflow state
ADD COLUMN workflow_state VARCHAR(50) DEFAULT 'creation' AFTER status,

-- Add workflow metadata
ADD COLUMN workflow_assigned_to INT UNSIGNED DEFAULT NULL AFTER workflow_state,
ADD COLUMN workflow_assigned_at TIMESTAMP NULL,
ADD COLUMN workflow_due_at TIMESTAMP NULL,
ADD COLUMN workflow_notes TEXT DEFAULT NULL,

-- Add constraints
ADD CONSTRAINT fk_blog_posts_workflow_assigned_to 
    FOREIGN KEY (workflow_assigned_to) REFERENCES users(id) ON DELETE SET NULL,

-- Add indexes for performance
ADD INDEX idx_blog_posts_workflow_state (tenant_id, workflow_state),
ADD INDEX idx_blog_posts_assigned (workflow_assigned_to, workflow_state),
ADD INDEX idx_blog_posts_status_workflow (status, workflow_state);
```

### New Workflow Tables
```sql
-- Workflow history/audit trail
CREATE TABLE blog_workflow_logs (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    post_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    action VARCHAR(50) NOT NULL,
    from_status VARCHAR(50),
    to_status VARCHAR(50),
    from_workflow_state VARCHAR(50),
    to_workflow_state VARCHAR(50),
    reason TEXT,
    metadata JSON DEFAULT (JSON_OBJECT()),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_workflow_logs_tenant FOREIGN KEY (tenant_id) 
        REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_workflow_logs_post FOREIGN KEY (post_id) 
        REFERENCES blog_posts(id) ON DELETE CASCADE,
    CONSTRAINT fk_workflow_logs_user FOREIGN KEY (user_id) 
        REFERENCES users(id) ON DELETE CASCADE,
        
    INDEX idx_workflow_logs_post (tenant_id, post_id),
    INDEX idx_workflow_logs_user (user_id, created_at)
);

-- Tenant workflow configuration
CREATE TABLE blog_workflow_configs (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    workflow_type VARCHAR(50) DEFAULT 'standard',
    role_mappings JSON DEFAULT (JSON_OBJECT()),
    state_transitions JSON DEFAULT (JSON_OBJECT()),
    auto_assignments JSON DEFAULT (JSON_OBJECT()),
    notifications JSON DEFAULT (JSON_OBJECT()),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_workflow_configs_tenant FOREIGN KEY (tenant_id) 
        REFERENCES tenants(id) ON DELETE CASCADE,
    UNIQUE KEY uk_workflow_configs_tenant_type (tenant_id, workflow_type)
);
```

## API Integration with Current Blog Module

### Updated Models
```go
// Enhanced BlogPost model
type BlogPost struct {
    // ... existing fields ...
    Status               BlogPostStatus `gorm:"type:varchar(20);not null;default:'draft'" json:"status"`
    WorkflowState        WorkflowState  `gorm:"type:varchar(50);default:'creation'" json:"workflow_state"`
    WorkflowAssignedTo   *uint         `gorm:"index" json:"workflow_assigned_to,omitempty"`
    WorkflowAssignedAt   *time.Time    `json:"workflow_assigned_at,omitempty"`
    WorkflowDueAt        *time.Time    `json:"workflow_due_at,omitempty"`
    WorkflowNotes        string        `gorm:"type:text" json:"workflow_notes,omitempty"`
}

// New workflow-specific DTOs
type BlogPostWorkflowResponse struct {
    State           WorkflowState  `json:"state"`
    AssignedTo      *uint         `json:"assigned_to,omitempty"`
    AssignedAt      *time.Time    `json:"assigned_at,omitempty"`
    DueAt           *time.Time    `json:"due_at,omitempty"`
    CanEdit         bool          `json:"can_edit"`
    CanPublish      bool          `json:"can_publish"`
    AvailableActions []string     `json:"available_actions"`
    Notes           string        `json:"notes,omitempty"`
}
```

### Enhanced API Responses
```json
{
  "id": 123,
  "title": "Article Title",
  "status": "draft",
  "workflow": {
    "state": "in_review",
    "assigned_to": 456,
    "assigned_at": "2025-01-25T10:00:00Z",
    "due_at": "2025-01-26T18:00:00Z",
    "can_edit": false,
    "can_publish": false,
    "available_actions": ["approve", "return", "reject"],
    "notes": "Please review the introduction section"
  },
  "content": "...",
  "published_at": null
}
```

### New API Endpoints
```go
// Workflow management endpoints
POST   /api/cms/v1/blog/posts/{id}/workflow/submit        // Submit to next stage
POST   /api/cms/v1/blog/posts/{id}/workflow/accept        // Accept assignment
POST   /api/cms/v1/blog/posts/{id}/workflow/approve       // Approve to next stage
POST   /api/cms/v1/blog/posts/{id}/workflow/return        // Return to previous stage
POST   /api/cms/v1/blog/posts/{id}/workflow/reject        // Reject article
POST   /api/cms/v1/blog/posts/{id}/workflow/assign        // Assign to specific user
PUT    /api/cms/v1/blog/posts/{id}/workflow/notes         // Update workflow notes

// Workflow queries
GET    /api/cms/v1/blog/workflow/my-tasks                 // Get assigned tasks
GET    /api/cms/v1/blog/workflow/queue/{state}            // Get articles in state
GET    /api/cms/v1/blog/workflow/{id}/history             // Get workflow history
GET    /api/cms/v1/blog/workflow/{id}/actions             // Get available actions

// Enhanced filtering
GET    /api/cms/v1/blog/posts?status=draft&workflow_state=in_review
GET    /api/cms/v1/blog/posts?workflow_assigned_to=123
```

## Business Logic Rules

### Content Visibility Rules
```go
func IsPubliclyVisible(post BlogPost) bool {
    return post.Status == BlogPostStatusPublished && 
           post.PublishedAt != nil && 
           post.PublishedAt.Before(time.Now())
}

func IsScheduledVisible(post BlogPost) bool {
    return post.Status == BlogPostStatusScheduled &&
           post.ScheduledAt != nil &&
           post.ScheduledAt.Before(time.Now())
}
```

### Edit Permission Rules
```go
func CanEdit(post BlogPost, userRole string) bool {
    // Deleted content cannot be edited
    if post.Status == BlogPostStatusDeleted {
        return false
    }
    
    // Content-based rules
    switch post.Status {
    case BlogPostStatusDraft:
        return post.WorkflowState == WorkflowStateCreation || 
               post.WorkflowState == WorkflowStateReturned
    case BlogPostStatusPublished:
        return userRole == "editor_in_chief" && 
               post.WorkflowState == WorkflowStateInReview
    case BlogPostStatusScheduled:
        return userRole == "editor_in_chief" ||
               (userRole == "editor" && post.WorkflowState == WorkflowStateInReview)
    default:
        return false
    }
}
```

### Workflow Transition Rules
```go
func GetAllowedTransitions(current WorkflowState, userRole string) []WorkflowState {
    transitions := map[WorkflowState]map[string][]WorkflowState{
        WorkflowStateCreation: {
            "reporter": {WorkflowStatePendingReview},
            "editor":   {WorkflowStatePendingReview, WorkflowStateInReview},
        },
        WorkflowStatePendingReview: {
            "editor": {WorkflowStateInReview, WorkflowStateReturned},
        },
        WorkflowStateInReview: {
            "editor": {WorkflowStatePendingApproval, WorkflowStateReturned, WorkflowStateRejected},
        },
        WorkflowStatePendingApproval: {
            "secretary": {WorkflowStateApproved, WorkflowStatePendingEIC, WorkflowStateReturned},
        },
        WorkflowStatePendingEIC: {
            "eic": {WorkflowStateApproved, WorkflowStateReturned, WorkflowStateRejected},
        },
        WorkflowStateApproved: {
            "secretary": {WorkflowStateCompleted},
            "eic":       {WorkflowStateCompleted},
        },
    }
    
    if roleTransitions, exists := transitions[current]; exists {
        if allowed, exists := roleTransitions[userRole]; exists {
            return allowed
        }
    }
    return []WorkflowState{}
}
```

## Migration Strategy

### Phase 1: Database Migration (Week 1)
```sql
-- Step 1: Add new columns with defaults
ALTER TABLE blog_posts 
ADD COLUMN workflow_state VARCHAR(50) DEFAULT 'creation',
ADD COLUMN workflow_assigned_to INT UNSIGNED DEFAULT NULL,
ADD COLUMN workflow_assigned_at TIMESTAMP NULL,
ADD COLUMN workflow_due_at TIMESTAMP NULL,
ADD COLUMN workflow_notes TEXT DEFAULT NULL;

-- Step 2: Migrate existing data
UPDATE blog_posts 
SET workflow_state = CASE
    WHEN status = 'draft' THEN 'creation'
    WHEN status = 'review' THEN 'in_review'
    WHEN status = 'published' THEN 'completed'
    WHEN status = 'scheduled' THEN 'approved'
    WHEN status = 'archived' THEN 'completed'
    WHEN status = 'rejected' THEN 'rejected'
    WHEN status = 'deleted' THEN 'completed'
    ELSE 'creation'
END;

-- Step 3: Update status enum to remove workflow states
ALTER TABLE blog_posts 
MODIFY COLUMN status ENUM('draft', 'published', 'scheduled', 'archived', 'deleted') 
DEFAULT 'draft';

-- Step 4: Update existing status values
UPDATE blog_posts 
SET status = CASE
    WHEN status = 'review' THEN 'draft'
    WHEN status = 'rejected' THEN 'draft'
    ELSE status
END;
```

### Phase 2: Code Migration (Week 2-3)
1. Update Go models and DTOs
2. Create WorkflowService with transition logic
3. Update repository methods for dual-field queries
4. Add new API endpoints
5. Update existing endpoints to include workflow data

### Phase 3: Testing & Rollout (Week 4)
1. Comprehensive integration testing
2. Performance testing with dual-field queries
3. Gradual tenant rollout with rollback capability
4. User training and documentation

## Tenant Customization

### Workflow Configuration Example
```json
{
  "workflow_type": "newspaper",
  "role_mappings": {
    "reporter": ["journalist", "correspondent"],
    "editor": ["section_editor", "copy_editor"],
    "secretary": ["managing_editor", "news_director"],
    "eic": ["editor_in_chief", "publisher"]
  },
  "state_transitions": {
    "creation": {
      "allowed_next": ["pending_review"],
      "auto_assign": false,
      "sla_hours": null
    },
    "pending_review": {
      "allowed_next": ["in_review", "returned"],
      "auto_assign": true,
      "sla_hours": 24
    },
    "in_review": {
      "allowed_next": ["pending_approval", "returned", "rejected"],
      "auto_assign": false,
      "sla_hours": 48
    }
  },
  "auto_assignments": {
    "pending_review": {
      "strategy": "round_robin",
      "pool": "editors"
    },
    "pending_approval": {
      "strategy": "designated",
      "user_id": 123
    }
  },
  "notifications": {
    "state_change": true,
    "assignment": true,
    "due_date": true,
    "escalation": true
  }
}
```

## Performance Considerations

### Optimized Queries
```sql
-- Efficient workflow queue queries
SELECT * FROM blog_posts 
WHERE tenant_id = ? 
  AND workflow_state = 'pending_review' 
  AND status != 'deleted'
ORDER BY created_at ASC;

-- My assigned tasks
SELECT * FROM blog_posts 
WHERE tenant_id = ? 
  AND workflow_assigned_to = ? 
  AND workflow_state IN ('in_review', 'pending_approval')
ORDER BY workflow_due_at ASC;

-- Public articles (no workflow consideration needed)
SELECT * FROM blog_posts 
WHERE tenant_id = ? 
  AND status = 'published' 
  AND published_at <= NOW()
ORDER BY published_at DESC;
```

### Indexes for Performance
```sql
-- Composite indexes for common queries
CREATE INDEX idx_workflow_queue ON blog_posts (tenant_id, workflow_state, status, created_at);
CREATE INDEX idx_assigned_tasks ON blog_posts (workflow_assigned_to, workflow_state, workflow_due_at);
CREATE INDEX idx_public_content ON blog_posts (tenant_id, status, published_at);
```

## Conclusion

This dual-field design (status + workflow_state) solves all the "(??)" ambiguity cases while providing:

✅ **Clear separation**: Content state vs editorial process  
✅ **Flexibility**: Published articles can be edited without losing public visibility  
✅ **Auditability**: Complete workflow history and state tracking  
✅ **Customization**: Tenant-specific workflow configuration  
✅ **Performance**: Optimized queries for both content and workflow needs  
✅ **Scalability**: Clean architecture that supports complex editorial workflows  

The system now handles all editorial scenarios clearly and provides a solid foundation for advanced newsroom operations.
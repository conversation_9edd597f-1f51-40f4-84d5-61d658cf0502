# Quy Trình Quản Lý Nội Dung - Mô Hình Quản Trị Tòa Soạn Báo <PERSON>

## Tổng Quan
Tài liệu này mô tả hệ thống quy trình quản lý nội dung tòa soạn với cấu trúc quản lý phân cấp 4 tầng được thiết kế cho hoạt động tòa soạn báo chuyên nghiệp.

## C<PERSON>u Trúc Phân Cấp Tòa Soạn

Hệ thống triển khai 4 cấp quản lý:

1. **Phóng viên** - Người tạo nội dung
2. **Biên tập viên** - Người biên tập và kiểm duyệt nội dung
3. **Thư ký tòa soạn** - Kiểm duyệt cấp cao
4. **Tổng biên tập** - Quyền phê duyệt cuối cùng

## Quyền Hạn và Quy Trình Làm Việc Từng Cấp

### 1. Cấp Phóng Viên

**Tr<PERSON><PERSON> nhiệm chính:**
- Viết bài mới
- Nộp bài để biên tập
- Quản lý nội dung của mình

**Quyền hạn:**
- Viết bài mới
- Xem trước bài viết
- Nộp bài viết
- Chỉ thấy bài viết của mình trong các tab:
  - Bài viết của tôi
  - Bài hẹn giờ xuất bản
  - Bài viết đã xuất bản
  - Bài chờ biên tập
  - Bài trả lại
  - Bài viết nháp
- Thấy phần THỐNG KÊ trang web

**Quy tắc quy trình:**
- Có thể rút lại, chỉnh sửa và nộp lại bài nếu cấp cao hơn chưa nhận
- Có quyền hủy bài viết trước khi được nhận
- Mất toàn bộ quyền chỉnh sửa sau khi cấp cao hơn đã nhận bài
- Không thể chỉnh sửa hoặc cập nhật bài đã xuất bản

### 2. Cấp Biên Tập Viên

**Trách nhiệm chính:**
- Nhận và biên tập bài từ phóng viên
- Đảm bảo chất lượng nội dung
- Chuyển bài đã duyệt lên Thư ký tòa soạn

**Quyền hạn:**
- Tất cả quyền của Phóng viên CỘNG THÊM:
- Cập nhật bài viết (hiển thị nút cập nhật sau khi chỉnh sửa)
- Nộp bài viết lên cấp cao hơn
- Trả bài viết cho phóng viên
- Truy cập tất cả các tab bài viết:
  - Tất cả bài viết
  - Bài hẹn giờ xuất bản
  - Bài viết đã xuất bản
  - Bài chờ xuất bản
  - Bài chờ biên tập
  - Bài trả lại
  - Bài viết nháp
- Thấy Tab THỐNG KÊ trang web
- Thấy Tab Bài lấy nguồn tự động
- Thấy Tab Quản lý hiển thị trang chủ
- Thấy tab Quản lý tính năng

**Quy tắc quy trình:**
- Nhận bài từ tab "Bài chờ biên tập"
- Phải có nút cập nhật sau khi chỉnh sửa
- Có thể rút lại, chỉnh sửa, nộp lại bài nếu Thư ký tòa soạn chưa nhận
- Có thể trả bài cho phóng viên
- Mất quyền chỉnh sửa sau khi gửi lên Thư ký tòa soạn
- Không thể chỉnh sửa bài đã xuất bản

### 3. Cấp Thư Ký Tòa Soạn

**Trách nhiệm chính:**
- Kiểm duyệt nội dung cuối cùng
- Quyết định xuất bản
- Chuyển nội dung nhạy cảm lên cấp cao hơn

**Quyền hạn:**
- Tất cả quyền của Biên tập viên CỘNG THÊM:
- Xuất bản bài viết (có thể cấu hình)
- Trả bài viết cho phóng viên
- Truy cập thêm các tab:
  - Bài đã gỡ
  - Bài đã xóa
- Thấy tab Quản lý Nâng cao

**Tính năng đặc biệt:**
- Nút xuất bản có thể bật/tắt theo cấu hình
- Có thể chuyển bài nhạy cảm lên tab mới: "Bài chờ Tổng biên tập duyệt"

**Quy tắc quy trình:**
- Nhận bài từ tab "Bài chờ xuất bản"
- Phải có nút cập nhật sau khi chỉnh sửa
- Có thể rút lại, chỉnh sửa, nộp lại bài nếu Tổng biên tập chưa nhận
- Có thể trả bài cho phóng viên
- Mất quyền chỉnh sửa sau khi Tổng biên tập nhận bài
- Không thể chỉnh sửa bài đã xuất bản

### 4. Cấp Tổng Biên Tập

**Trách nhiệm chính:**
- Quyền quyết định biên tập cuối cùng
- Quyết định nội dung chiến lược
- Quản lý sau xuất bản

**Quyền hạn:**
- Toàn quyền hệ thống bao gồm:
- Tất cả quyền của các cấp trước
- Gỡ bài viết
- Xóa bài viết
- Quản lý kế toán
- Cấu hình hệ thống
- Chỉnh sửa sau xuất bản

**Quyền độc quyền:**
- Cấp duy nhất có thể chỉnh sửa bài đã xuất bản
- Có thể cập nhật mọi trạng thái bài viết
- Có thể gỡ hoặc xóa bài viết

## Quy Trình Trạng Thái Bài Viết

### Sơ Đồ Luồng Trạng Thái
```
Nháp → Chờ biên tập → Chờ xuất bản → Đã xuất bản
  ↓          ↓              ↓              ↓
Trả lại    Trả lại       Trả lại       Đã gỡ
                            ↓              ↓
                   Chờ TBT duyệt       Đã xóa
```

### Chuyển Đổi Trạng Thái Chính

1. **Nháp → Chờ biên tập**
   - Hành động: Phóng viên nộp bài
   - Người nhận: Biên tập viên

2. **Chờ biên tập → Chờ xuất bản**
   - Hành động: Biên tập viên duyệt và chuyển
   - Người nhận: Thư ký tòa soạn

3. **Chờ xuất bản → Đã xuất bản**
   - Hành động: Thư ký tòa soạn xuất bản
   - Điều kiện: Nếu có quyền xuất bản

4. **Chờ xuất bản → Chờ TBT duyệt**
   - Hành động: Thư ký tòa soạn chuyển nội dung nhạy cảm
   - Người nhận: Tổng biên tập

5. **Mọi trạng thái → Trả lại**
   - Hành động: Cấp cao hơn trả lại để chỉnh sửa
   - Người nhận: Phóng viên

## Ghi Chú Triển Khai Kỹ Thuật

### Giá Trị Enum Trạng Thái Bài Viết
```go
const (
    ArticleStatusDraft              = "draft"              // Nháp
    ArticleStatusPendingEdit        = "pending_edit"       // Chờ biên tập
    ArticleStatusPendingPublication = "pending_publication" // Chờ xuất bản
    ArticleStatusPendingEIC         = "pending_eic_approval" // Chờ TBT duyệt
    ArticleStatusPublished          = "published"          // Đã xuất bản
    ArticleStatusReturned           = "returned"           // Trả lại
    ArticleStatusUnpublished        = "unpublished"        // Đã gỡ
    ArticleStatusDeleted            = "deleted"            // Đã xóa
)
```

### Quy Tắc Nghiệp Vụ Chính

1. **Chuyển Giao Quyền Sở Hữu Bài Viết**
   - Bài viết bị "khóa" với cấp thấp hơn sau khi cấp cao hơn nhận
   - Tác giả gốc giữ quyền đọc nhưng mất quyền chỉnh sửa

2. **Quyền Rút Bài**
   - Người dùng có thể rút bài đã nộp nếu chưa được nhận
   - Rút bài sẽ đưa bài về trạng thái trước đó

3. **Quy Trình Trả Bài**
   - Mọi cấp cao hơn đều có thể trả bài cho phóng viên
   - Trả bài phải kèm lý do/phản hồi bắt buộc

4. **Thẩm Quyền Xuất Bản**
   - Thư ký tòa soạn: Quyền xuất bản có thể cấu hình
   - Tổng biên tập: Luôn có quyền xuất bản

5. **Kiểm Soát Sau Xuất Bản**
   - Chỉ Tổng biên tập có thể chỉnh sửa nội dung đã xuất bản
   - Bao gồm khả năng gỡ và xóa bài

### Yêu Cầu Cơ Sở Dữ Liệu

1. **Yêu Cầu Bảng Bài Viết**
   - Trường trạng thái với ràng buộc enum
   - Theo dõi người phụ trách hiện tại
   - Ghi log lịch sử quy trình
   - Lưu trữ lý do trả lại

2. **Lịch Sử Kiểm Toán**
   - Theo dõi mọi thay đổi trạng thái
   - Ghi lại hành động người dùng với timestamp
   - Duy trì lịch sử phiên bản

3. **Kiểm Tra Quyền**
   - Kiểm soát truy cập dựa trên vai trò
   - Xác thực hành động dựa trên trạng thái
   - Cô lập theo tenant

## Cấu Trúc API Endpoints

### Quản Lý Bài Viết
- `POST /articles` - Tạo bài mới
- `PUT /articles/{id}` - Cập nhật bài viết
- `POST /articles/{id}/submit` - Nộp lên cấp tiếp theo
- `POST /articles/{id}/withdraw` - Rút lại bài đã nộp
- `POST /articles/{id}/return` - Trả lại cho phóng viên
- `POST /articles/{id}/publish` - Xuất bản bài viết
- `POST /articles/{id}/unpublish` - Gỡ bài viết
- `DELETE /articles/{id}` - Xóa bài viết

### Trạng Thái Quy Trình
- `GET /articles/workflow/{id}/history` - Lấy lịch sử quy trình
- `GET /articles/workflow/{id}/actions` - Lấy các hành động khả dụng

### Truy Vấn Theo Tab
- `GET /articles?status=draft&my_articles=true` - Bài nháp của tôi
- `GET /articles?status=pending_edit` - Bài chờ biên tập
- `GET /articles?status=pending_publication` - Bài chờ xuất bản
- `GET /articles?status=pending_eic_approval` - Bài chờ TBT duyệt

## Các Vấn Đề Bảo Mật

1. **Kiểm Soát Truy Cập**
   - Quyền nghiêm ngặt dựa trên vai trò
   - Xác thực hành động dựa trên trạng thái
   - Cô lập tenant cho thiết lập đa tenant

2. **Bảo Vệ Dữ Liệu**
   - Kiểm toán mọi hành động biên tập
   - Duy trì lịch sử phiên bản đầy đủ
   - Triển khai xóa mềm để tuân thủ

3. **Toàn Vẹn Quy Trình**
   - Ngăn chặn chuyển trạng thái trái phép
   - Xác thực quyền vai trò trước hành động
   - Ghi log mọi vi phạm quyền

## Cải Tiến Tương Lai

1. **Tùy Chỉnh Quy Trình**
   - Cấp phê duyệt có thể cấu hình
   - Định nghĩa trạng thái tùy chỉnh
   - Mẫu quyền vai trò

2. **Tính Năng Nâng Cao**
   - Thao tác hàng loạt
   - Tự động hóa quy trình
   - Kiểm duyệt nội dung hỗ trợ AI

3. **Phân Tích**
   - Số liệu hiệu suất quy trình
   - Xác định điểm nghẽn
   - Theo dõi năng suất biên tập
# Trello Workflows - <PERSON><PERSON>ng <PERSON><PERSON> Lý Chi Tiết

## 1. User Registration và Authentication Flow

```mermaid
flowchart TD
    A[User truy cập Trello] --> B{Đã có account?}
    B -->|Không| C[Sign Up Form]
    B -->|Có| D[Login Form]
    
    C --> E[Nhập email/password]
    E --> F[Verify email]
    F --> G[Create profile]
    G --> H[Onboarding flow]
    
    D --> I[Nhập credentials]
    I --> J{Valid?}
    J -->|Không| K[Show error]
    K --> D
    J -->|Có| L{2FA enabled?}
    L -->|Có| M[Enter 2FA code]
    L -->|Không| N[Dashboard]
    M --> N
    
    H --> N[Dashboard]
```

## 2. Workspace Creation Flow

```mermaid
flowchart TD
    A[Click Create Workspace] --> B[Enter workspace name]
    B --> C[Choose type<br/>Business/Personal/Education]
    C --> D[Set visibility]
    D --> E{Invite members?}
    E -->|Có| F[Add member emails]
    E -->|Không| G[Create workspace]
    F --> G
    G --> H[Workspace created]
    H --> I[Redirect to workspace]
    I --> J[Show onboarding tips]
```

## 3. Board Creation và Setup Flow

```mermaid
flowchart TD
    A[Click Create Board] --> B{From template?}
    B -->|Có| C[Choose template]
    B -->|Không| D[Blank board]
    
    C --> E[Preview template]
    E --> F[Customize template]
    
    D --> G[Enter board name]
    G --> H[Choose background]
    H --> I[Set visibility]
    
    F --> I
    I --> J[Select workspace]
    J --> K[Create board]
    K --> L[Board created]
    L --> M[Add first list]
    M --> N[Ready to use]
```

## 4. Card Lifecycle Flow

```mermaid
flowchart TD
    A[Create Card] --> B[Enter title]
    B --> C[Card created in list]
    C --> D{Add details?}
    
    D -->|Có| E[Open card]
    E --> F[Add description]
    F --> G[Assign members]
    G --> H[Set due date]
    H --> I[Add labels]
    I --> J[Create checklist]
    
    D -->|Không| K[Card ready]
    J --> K
    
    K --> L{Work on card}
    L --> M[Move to next list]
    M --> N{Complete?}
    N -->|Không| L
    N -->|Có| O[Move to Done]
    O --> P{Archive?}
    P -->|Có| Q[Archive card]
    P -->|Không| R[Keep in Done]
```

## 5. Member Invitation Flow

```mermaid
flowchart TD
    A[Click Invite] --> B{Board or Workspace?}
    B -->|Board| C[Board invite dialog]
    B -->|Workspace| D[Workspace invite dialog]
    
    C --> E[Enter email/username]
    D --> E
    
    E --> F[Select permissions]
    F --> G{Send invite?}
    G -->|Có| H[Send invitation email]
    H --> I[Add as pending member]
    
    I --> J[Member receives email]
    J --> K[Click accept link]
    K --> L{Has account?}
    L -->|Có| M[Login]
    L -->|Không| N[Sign up]
    
    M --> O[Join board/workspace]
    N --> P[Create account]
    P --> O
    
    O --> Q[Access granted]
```

## 6. Search và Filter Flow

```mermaid
flowchart TD
    A[Enter search query] --> B[Parse query]
    B --> C{Search scope}
    
    C -->|Global| D[Search all accessible]
    C -->|Board| E[Search current board]
    C -->|Card| F[Search cards only]
    
    D --> G[Query database]
    E --> G
    F --> G
    
    G --> H[Apply filters]
    H --> I{Has filters?}
    I -->|Có| J[Filter by:<br/>- Members<br/>- Labels<br/>- Due date<br/>- Lists]
    I -->|Không| K[Return all results]
    
    J --> K
    K --> L[Sort results]
    L --> M[Display results]
    M --> N{Select result?}
    N -->|Có| O[Navigate to item]
    N -->|Không| P[Refine search]
```

## 7. Automation (Butler) Setup Flow

```mermaid
flowchart TD
    A[Open Butler] --> B[Choose automation type]
    B --> C{Type?}
    
    C -->|Rules| D[Select trigger]
    C -->|Commands| E[Create button]
    C -->|Calendar| F[Set schedule]
    
    D --> G[When this happens...]
    G --> H[Choose trigger event]
    H --> I[Add conditions]
    I --> J[Then do this...]
    J --> K[Select actions]
    
    E --> L[Button trigger]
    F --> M[Time trigger]
    
    L --> K
    M --> K
    
    K --> N[Configure action details]
    N --> O[Test automation]
    O --> P{Works?}
    P -->|Không| Q[Debug]
    Q --> N
    P -->|Có| R[Save automation]
    R --> S[Automation active]
```

## 8. Power-Up Installation Flow

```mermaid
flowchart TD
    A[Browse Power-Ups] --> B[Select Power-Up]
    B --> C[View details]
    C --> D{Free or Paid?}
    
    D -->|Free| E[Click Add]
    D -->|Paid| F[Check pricing]
    F --> G{Subscribe?}
    G -->|Không| H[Cancel]
    G -->|Có| I[Payment flow]
    I --> E
    
    E --> J[Authorize permissions]
    J --> K[Configure settings]
    K --> L[Power-Up active]
    L --> M[Show in board menu]
```

## 9. Card Move với Validation Flow

```mermaid
flowchart TD
    A[Drag card] --> B[Hover over target list]
    B --> C{Valid move?}
    
    C -->|Không| D[Show restriction]
    D --> E[Cancel move]
    
    C -->|Có| F[Preview position]
    F --> G[Drop card]
    G --> H[Update position]
    H --> I{Has automation?}
    
    I -->|Có| J[Trigger Butler rules]
    J --> K[Execute actions]
    
    I -->|Không| L[Update UI]
    K --> L
    
    L --> M[Notify members]
    M --> N[Log activity]
    N --> O[Sync across clients]
```

## 10. Comment và Mention Flow

```mermaid
flowchart TD
    A[Open card] --> B[Click comment field]
    B --> C[Type comment]
    C --> D{Has @mention?}
    
    D -->|Có| E[Show member list]
    E --> F[Select member]
    F --> G[Add to comment]
    
    D -->|Không| H[Continue typing]
    G --> H
    
    H --> I[Post comment]
    I --> J[Save to database]
    J --> K[Update activity]
    K --> L{Mentioned users?}
    
    L -->|Có| M[Send notifications]
    M --> N[Email if enabled]
    M --> O[Push if mobile]
    M --> P[In-app notification]
    
    L -->|Không| Q[Update card]
    N --> Q
    O --> Q
    P --> Q
```

## 11. Due Date Management Flow

```mermaid
flowchart TD
    A[Set due date] --> B[Select date/time]
    B --> C[Save due date]
    C --> D[Calculate reminders]
    D --> E{Reminder settings?}
    
    E -->|None| F[No reminders]
    E -->|24h before| G[Schedule 24h reminder]
    E -->|Custom| H[Set custom time]
    
    G --> I[Monitor time]
    H --> I
    
    I --> J{Time reached?}
    J -->|Không| I
    J -->|Có| K[Send notifications]
    
    K --> L[Check completion]
    L --> M{Completed?}
    M -->|Có| N[Mark complete]
    M -->|Không| O{Overdue?}
    
    O -->|Có| P[Mark overdue]
    P --> Q[Notify assignees]
    O -->|Không| R[Continue monitoring]
```

## 12. File Attachment Flow

```mermaid
flowchart TD
    A[Click Attach] --> B{Source?}
    
    B -->|Computer| C[File picker]
    B -->|Cloud| D[Select service]
    B -->|Link| E[Enter URL]
    
    C --> F[Select file]
    F --> G{Size OK?}
    G -->|Không| H[Show error]
    G -->|Có| I[Upload file]
    
    D --> J[Authenticate service]
    J --> K[Browse files]
    K --> L[Select file]
    L --> I
    
    E --> M[Validate URL]
    M --> N{Valid?}
    N -->|Không| O[Show error]
    N -->|Có| P[Create link attachment]
    
    I --> Q[Process file]
    Q --> R[Generate preview]
    R --> S[Save attachment]
    
    P --> S
    S --> T[Update card]
    T --> U[Show in attachments]
```

## 13. Board Template Creation Flow

```mermaid
flowchart TD
    A[Select board] --> B[Menu > Create Template]
    B --> C[Template settings]
    C --> D[Enter name/description]
    D --> E[Select category]
    E --> F{Keep cards?}
    
    F -->|Có| G[Include sample cards]
    F -->|Không| H[Lists only]
    
    G --> I[Review content]
    H --> I
    
    I --> J[Set visibility]
    J --> K{Public template?}
    
    K -->|Có| L[Submit for review]
    L --> M[Atlassian review]
    M --> N{Approved?}
    N -->|Có| O[Publish to gallery]
    N -->|Không| P[Request changes]
    
    K -->|Không| Q[Save as private]
    O --> R[Template available]
    Q --> R
```

## 14. Checklist Management Flow

```mermaid
flowchart TD
    A[Add Checklist] --> B[Name checklist]
    B --> C[Add first item]
    C --> D{More items?}
    
    D -->|Có| E[Add item]
    E --> D
    D -->|Không| F[Checklist ready]
    
    F --> G[Work on items]
    G --> H[Check item]
    H --> I[Update progress]
    I --> J{All complete?}
    
    J -->|Không| K[Continue work]
    K --> G
    J -->|Có| L[Checklist complete]
    L --> M{Has automation?}
    
    M -->|Có| N[Trigger Butler]
    N --> O[Execute actions]
    M -->|Không| P[Update card status]
    O --> P
```

## 15. Mobile Offline Sync Flow

```mermaid
flowchart TD
    A[Open mobile app] --> B{Internet connected?}
    
    B -->|Không| C[Offline mode]
    C --> D[Load cached data]
    D --> E[Allow local edits]
    E --> F[Queue changes]
    
    B -->|Có| G[Sync with server]
    G --> H[Download updates]
    H --> I[Normal operation]
    
    F --> J{Connection restored?}
    J -->|Không| K[Continue offline]
    K --> F
    J -->|Có| L[Upload queued changes]
    
    L --> M{Conflicts?}
    M -->|Có| N[Resolve conflicts]
    N --> O[Merge changes]
    M -->|Không| O
    
    O --> P[Sync complete]
    P --> I
```
# Media Module - Storage

## Storage Adapters

## Media Storage Strategy - Tenant & Website Isolation

### Phương án lưu trữ theo Tenant và Website

Hệ thống media được thiết kế với 2 cấp độ isolation:

1. **Tenant Level**: Mỗi tenant có storage riêng biệt
2. **Website Level**: Trong mỗi tenant, mỗi website có thư mục riêng

### Lợi ích của phương án này:

#### 🔒 **Security & Isolation**
- **Tenant Isolation**: Tenant A không thể truy cập media của Tenant B
- **Website Isolation**: Website 1 không thể truy cập media của Website 2 cùng tenant
- **Access Control**: Ki<PERSON><PERSON> soát quyền truy cập theo từng cấp độ

#### 📊 **Resource Management**
- **Storage Quotas**: Theo dõi usage theo tenant và website
- **Backup Strategy**: Backup riêng biệt cho từng tenant/website
- **Performance**: Tối ưu cache và CDN theo website

#### 🚀 **Scalability**
- **Horizontal Scaling**: <PERSON><PERSON> dàng scale theo tenant
- **Multi-Region**: Phân phối theo địa lý
- **Load Balancing**: Cân bằng tải theo website

### Database Schema Integration

```sql
-- Media files table với tenant_id và website_id
CREATE TABLE media_files (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    filename VARCHAR(255) NOT NULL,
    path VARCHAR(500) NOT NULL,
    url VARCHAR(500) NOT NULL,
    -- ... other fields
    
    CONSTRAINT fk_media_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    CONSTRAINT fk_media_website FOREIGN KEY (website_id) REFERENCES websites(id),
    INDEX idx_media_tenant_website (tenant_id, website_id)
);
```

### URL Structure

```
# Local Storage
https://yourdomain.com/media/tenant_1/website_1/2024/01/images/uuid.jpg

# MinIO/S3
https://cdn.yourdomain.com/tenant_1/website_1/images/uuid.jpg

# CDN với custom domain
https://tenant1-website1.cdn.yourdomain.com/images/uuid.jpg
```

### Local Storage

Local storage adapter lưu trữ file trực tiếp trên filesystem của server.

```mermaid
flowchart LR
    A[File Upload] --> B[Local Filesystem]
    B --> C[Directory Structure]
    C --> D[/media/tenant_id/website_id/year/month/]
    D --> E[File stored]
    E --> F[Generate URL]
    F --> G[Update Database]
```

#### Cấu hình Local Storage
```yaml
storage:
  type: "local"
  local:
    base_path: "./storage/media"
    url_prefix: "https://yourdomain.com/media"
    directory_structure: "tenant/website/year/month"
    permissions: 0755
    max_file_size: "100MB"
    allowed_extensions: ["jpg", "png", "gif", "pdf", "mp4"]
```

#### Cấu trúc thư mục Local
```
storage/media/
├── tenant_1/
│   ├── website_1/
│   │   ├── 2024/
│   │   │   ├── 01/
│   │   │   │   ├── images/
│   │   │   │   ├── videos/
│   │   │   │   └── documents/
│   │   │   └── 02/
│   │   └── thumbnails/
│   │       ├── small/
│   │       ├── medium/
│   │       └── large/
│   └── website_2/
│       └── 2024/
│           └── 01/
└── tenant_2/
    └── website_3/
        └── 2024/
            └── 01/
```

#### Ưu điểm Local Storage
- **Cost-effective**: Không có chi phí cloud storage
- **Low latency**: Truy cập nhanh cho server cùng vị trí
- **Simple setup**: Dễ cấu hình và triển khai
- **Full control**: Kiểm soát hoàn toàn dữ liệu

#### Nhược điểm Local Storage
- **Scalability**: Hạn chế khả năng mở rộng
- **Backup complexity**: Phức tạp trong backup
- **Single point failure**: Rủi ro mất dữ liệu
- **Geographic distribution**: Không phân phối địa lý

### MinIO Storage

MinIO là object storage server S3-compatible, phù hợp cho self-hosted deployments.

```mermaid
flowchart LR
    A[File Upload] --> B[MinIO Server]
    B --> C[S3-Compatible API]
    C --> D[Bucket: media-bucket]
    D --> E[Object Key: tenant/website/type/uuid.ext]
    E --> F[MinIO URL]
    F --> G[Optional CDN]
```

#### Cấu hình MinIO
```yaml
storage:
  type: "minio"
  minio:
    endpoint: "http://minio:9000"
    access_key: "minioadmin"
    secret_key: "minioadmin"
    bucket: "media-bucket"
    region: "us-east-1"
    ssl: false
    path_style: true
    presigned_url_expires: "24h"
```

#### MinIO Deployment với Docker
```yaml
# docker-compose.yml
version: '3.8'
services:
  minio:
    image: minio/minio:latest
    container_name: minio
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    command: server /data --console-address ":9001"
    
  minio-mc:
    image: minio/mc:latest
    depends_on:
      - minio
    entrypoint: >
      /bin/sh -c "
      /usr/bin/mc alias set myminio http://minio:9000 minioadmin minioadmin;
      /usr/bin/mc mb myminio/media-bucket;
      /usr/bin/mc policy set public myminio/media-bucket;
      exit 0;
      "

volumes:
  minio_data:
```

#### MinIO Bucket Structure
```
media-bucket/
├── tenant_1/
│   ├── website_1/
│   │   ├── images/
│   │   │   ├── 2024-01-15_uuid1.jpg
│   │   │   └── 2024-01-15_uuid2.png
│   │   ├── videos/
│   │   │   └── 2024-01-15_uuid3.mp4
│   │   └── thumbnails/
│   │       ├── small/
│   │       ├── medium/
│   │       └── large/
│   └── website_2/
│       └── images/
│           └── 2024-01-15_uuid4.jpg
└── tenant_2/
    └── website_3/
        └── images/
            └── 2024-01-15_uuid5.jpg
```

#### Ưu điểm MinIO
- **S3 Compatible**: API tương thích với AWS S3
- **Self-hosted**: Kiểm soát hoàn toàn infrastructure
- **High Performance**: Hiệu suất cao cho workload intensive
- **Distributed**: Hỗ trợ distributed deployment
- **Cost Effective**: Tiết kiệm chi phí so với cloud storage

#### Nhược điểm MinIO
- **Infrastructure Management**: Cần quản lý infrastructure
- **Backup Responsibility**: Tự chịu trách nhiệm backup
- **Scaling Complexity**: Phức tạp khi scale
- **Support**: Ít support so với cloud providers

### AWS S3 Storage

```mermaid
flowchart LR
    A[File Upload] --> B[AWS S3 Bucket]
    B --> C[S3 Key Structure]
    C --> D[media/tenant/website/type/uuid.ext]
    D --> E[S3 URL returned]
    E --> F[CloudFront CDN]
```

#### Cấu hình AWS S3
```yaml
storage:
  type: "s3"
  s3:
    region: "us-west-2"
    bucket: "my-media-bucket"
    access_key: "AKIAIOSFODNN7EXAMPLE"
    secret_key: "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
    cloudfront_domain: "d123456abcdef8.cloudfront.net"
    presigned_url_expires: "1h"
```

### Multi-Storage Strategy

Sử dụng nhiều storage adapter dựa trên tiêu chí khác nhau.

```mermaid
flowchart TD
    A[Upload Request] --> B{Storage Strategy?}
    
    B -->|File Size| C{Size < 10MB?}
    C -->|Yes| D[Local Storage]
    C -->|No| E[MinIO/S3]
    
    B -->|File Type| F{Media Type?}
    F -->|Images| G[Local + CDN]
    F -->|Videos| H[MinIO/S3]
    F -->|Documents| I[Local Storage]
    
    B -->|Tenant Plan| J{Plan Type?}
    J -->|Free/Basic| K[Local Storage]
    J -->|Premium| L[MinIO Storage]
    J -->|Enterprise| M[AWS S3]
    
    D --> N[Store & Serve]
    E --> N
    G --> N
    H --> N
    I --> N
    K --> N
    L --> N
    M --> N
```

### Storage Configuration

#### Environment-based Configuration
```yaml
# Development
storage:
  type: "local"
  local:
    base_path: "./storage/media"
    url_prefix: "http://localhost:9077/media"

# Staging  
storage:
  type: "minio"
  minio:
    endpoint: "http://staging-minio:9000"
    bucket: "staging-media"

# Production
storage:
  type: "s3"
  s3:
    region: "us-west-2"
    bucket: "prod-media-bucket"
    cloudfront_domain: "cdn.example.com"
```

#### Dynamic Storage Selection
```mermaid
flowchart TD
    A[File Upload] --> B[Storage Selector]
    B --> C{Environment?}
    
    C -->|Development| D[Local Storage]
    C -->|Staging| E[MinIO Storage]
    C -->|Production| F[AWS S3]
    
    D --> G[File Handler]
    E --> G
    F --> G
    
    G --> H[Process File]
    H --> I[Generate Thumbnails]
    I --> J[Update Database]
    J --> K[Return URLs]
```

### Storage Migration

#### Migration between Storage Types
```mermaid
sequenceDiagram
    participant Admin as Admin
    participant API as Migration API
    participant Source as Source Storage
    participant Target as Target Storage
    participant DB as Database
    
    Admin->>API: Start migration
    API->>DB: Get file list
    DB->>API: Return file records
    
    loop For each file
        API->>Source: Download file
        Source->>API: File data
        API->>Target: Upload file
        Target->>API: New URL
        API->>DB: Update file record
    end
    
    API->>Admin: Migration complete
```

### Hybrid Storage

Kết hợp nhiều storage để tối ưu cost và performance.

```mermaid
flowchart TD
    A[Upload Request] --> B{File Analysis}
    
    B --> C[File Size Check]
    B --> D[Access Frequency]
    B --> E[Tenant Tier]
    
    C -->|< 5MB| F[Local Storage]
    C -->|5-50MB| G[MinIO Storage]
    C -->|> 50MB| H[S3 Storage]
    
    D -->|High Access| I[Local + CDN]
    D -->|Medium Access| J[MinIO]
    D -->|Low Access| K[S3 Glacier]
    
    E -->|Free Tier| L[Local Only]
    E -->|Paid Tier| M[Multi-Storage]
    
    F --> N[Serve Direct]
    G --> O[Serve via MinIO]
    H --> P[Serve via S3/CDN]
    I --> N
    J --> O
    K --> Q[Archive Storage]
    L --> N
    M --> R[Intelligent Tiering]
```

## Tài liệu liên quan

- [Processing Documentation](./processing.md)
- [CDN Documentation](./cdn.md)
- [Security Documentation](./security.md)
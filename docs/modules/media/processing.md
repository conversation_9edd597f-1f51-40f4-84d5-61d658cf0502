# Media Module - Processing

## Image Processing Pipeline

### Thumbnail Generation
```yaml
thumbnail_sizes:
  small:
    width: 150
    height: 150
    crop: true
  medium:
    width: 300
    height: 300
    crop: false
  large:
    width: 800
    height: 600
    crop: false
  xl:
    width: 1200
    height: 900
    crop: false
```

### Format Optimization
```mermaid
flowchart TD
    A[Original Image] --> B{Format?}
    B -->|JPEG| C[Optimize JPEG]
    B -->|PNG| D[PNG Compression]
    B -->|GIF| E[GIF Optimization]
    
    C --> F[Generate WebP]
    D --> F
    E --> F
    
    F --> G[Generate AVIF]
    G --> H[Choose best format]
    H --> I[Save optimized]
```

## Video Processing

### Transcoding Workflow
```mermaid
sequenceDiagram
    participant Upload as Video Upload
    participant Queue as Processing Queue
    participant FFmpeg as FFmpeg Worker
    participant Storage as Storage
    participant CDN as CDN
    
    Upload->>Queue: Add video job
    Queue->>FFmpeg: Start transcoding
    FFmpeg->>FFmpeg: Extract thumbnail
    FFmpeg->>FFmpeg: Generate 480p
    FFmpeg->>FFmpeg: Generate 720p
    FFmpeg->>FFmpeg: Generate 1080p
    FFmpeg->>Storage: Save all versions
    Storage->>CDN: Upload to CDN
    CDN->>Queue: Transcoding complete
```

### Adaptive Streaming
```mermaid
flowchart LR
    A[Original Video] --> B[FFmpeg]
    B --> C[Multiple Bitrates]
    C --> D[480p - 1Mbps]
    C --> E[720p - 3Mbps]
    C --> F[1080p - 6Mbps]
    
    D --> G[HLS Playlist]
    E --> G
    F --> G
    
    G --> H[Adaptive Streaming]
```

## CDN Integration

### Multi-CDN Strategy
```mermaid
flowchart TD
    A[Media Request] --> B[CDN Router]
    B --> C{User Location?}
    C -->|Asia| D[CloudFlare Asia]
    C -->|Europe| E[AWS CloudFront EU]
    C -->|Americas| F[KeyCDN Americas]
    
    D --> G[Optimized Delivery]
    E --> G
    F --> G
```

### Cache Strategy
- **Static Assets**: Cache 1 year
- **Images**: Cache 30 days
- **Videos**: Cache 7 days
- **Thumbnails**: Cache 90 days
- **Processing Status**: No cache

## Performance Optimization

### Caching Strategy
```mermaid
flowchart LR
    A[Media Request] --> B[Browser Cache]
    B --> C{Cache Hit?}
    C -->|Có| D[Return Cached]
    C -->|Không| E[CDN Cache]
    E --> F{CDN Hit?}
    F -->|Có| G[Return from CDN]
    F -->|Không| H[Origin Server]
    H --> I[Process & Cache]
```

### Lazy Loading
- **Progressive JPEG**: Load incrementally
- **Placeholder Images**: Show while loading
- **Intersection Observer**: Load when visible
- **Priority Loading**: Above-fold content first

## Monitoring và Analytics

### File Usage Metrics
```mermaid
graph LR
    A[Media Analytics] --> B[Upload Stats]
    A --> C[View Counts]
    A --> D[Download Stats]
    A --> E[Storage Usage]
    A --> F[CDN Performance]
    
    B --> B1[Files per day]
    B --> B2[File types]
    B --> B3[User activity]
    
    C --> C1[Popular files]
    C --> C2[View trends]
    
    D --> D1[Download volume]
    D --> D2[Bandwidth usage]
    
    E --> E1[Storage per tenant]
    E --> E2[Growth rate]
    
    F --> F1[Cache hit rate]
    F --> F2[Response times]
```

### Performance Monitoring
- **Upload Success Rate**: Tỷ lệ upload thành công
- **Processing Time**: Thời gian xử lý file
- **CDN Hit Rate**: Tỷ lệ cache hit
- **Error Rates**: Tỷ lệ lỗi theo loại
- **Storage Growth**: Tăng trưởng dung lượng

## Tài liệu liên quan

- [Storage Documentation](./storage.md)
- [CDN Documentation](./cdn.md)
- [API Documentation](./api-processing.md)
# Media Module - Models

## <PERSON><PERSON> hình dữ liệu

### MediaFile
- **ID**: Định danh file
- **TenantID**: ID tenant sở hữu
- **WebsiteID**: ID website sở hữu
- **UserID**: ID người upload
- **FolderID**: ID thư mục chứa
- **Filename**: Tên file gốc
- **Path**: Đường dẫn lưu trữ
- **URL**: URL truy cập public
- **MimeType**: Loại MIME
- **Size**: <PERSON>í<PERSON> thước file (bytes)
- **Width/Height**: <PERSON><PERSON><PERSON> thước hình ảnh
- **Duration**: <PERSON>h<PERSON><PERSON> lượ<PERSON> (video/audio)
- **Metadata**: Thông tin EXIF, tags
- **Status**: Trạng thái (uploading, processing, ready, error)

### MediaFolder
- **ID**: Định danh thư mục
- **TenantID**: ID tenant
- **WebsiteID**: ID website
- **ParentID**: <PERSON> thư mục cha
- **Name**: <PERSON><PERSON><PERSON> th<PERSON> mục
- **Path**: Đường dẫn đầy đủ
- **Type**: <PERSON><PERSON><PERSON> thư mục (user, system, public)
- **Permissions**: Quyền truy cập

### MediaThumbnail
- **ID**: Định danh thumbnail
- **FileID**: ID file gốc
- **Size**: Kích thước (small, medium, large)
- **Width/Height**: Kích thước pixel
- **Path**: Đường dẫn thumbnail
- **URL**: URL truy cập

### MediaMetadata
- **FileID**: ID file
- **Type**: Loại metadata (exif, custom, auto)
- **Key**: Tên trường
- **Value**: Giá trị
- **Source**: Nguồn (user, system, extracted)

## Luồng hoạt động chính

### 1. Upload File

```mermaid
sequenceDiagram
    participant User as Người dùng
    participant UI as Frontend
    participant API as Media API
    participant Validator as File Validator
    participant Storage as Storage Service
    participant Processor as File Processor
    participant CDN as CDN Service
    
    User->>UI: Chọn file upload
    UI->>API: POST /media/upload
    API->>Validator: Validate file
    Validator->>API: Validation result
    API->>Storage: Store original file
    Storage->>API: File stored
    API->>Processor: Queue processing
    API->>UI: Upload success
    
    Processor->>Processor: Generate thumbnails
    Processor->>Processor: Extract metadata
    Processor->>Processor: Optimize file
    Processor->>Storage: Store processed files
    Processor->>CDN: Sync to CDN
    Processor->>API: Processing complete
```

### 2. Xử lý hình ảnh

```mermaid
flowchart TD
    A[Upload hình ảnh] --> B[Validate format]
    B --> C{Format hợp lệ?}
    C -->|Không| D[Reject upload]
    C -->|Có| E[Lưu file gốc]
    E --> F[Extract EXIF data]
    F --> G[Generate thumbnails]
    G --> H[Optimize for web]
    H --> I[Create WebP version]
    I --> J[Apply watermark]
    J --> K[Update database]
    K --> L[Sync to CDN]
    L --> M[Notify completion]
```

### 3. Video Processing Pipeline

```mermaid
flowchart LR
    A[Video Upload] --> B[Extract metadata]
    B --> C[Generate thumbnail]
    C --> D[Create preview GIF]
    D --> E[Transcode formats]
    E --> F[Generate subtitles]
    F --> G[Create streaming playlist]
    G --> H[Upload to CDN]
    H --> I[Update status]
```

### 4. Tìm kiếm và Filter

```mermaid
flowchart TD
    A[Search Request] --> B{Search Type?}
    B -->|Text| C[Search filename/tags]
    B -->|Filter| D[Apply filters]
    B -->|Visual| E[Image similarity]
    
    C --> F[Elasticsearch query]
    D --> G[Database filter]
    E --> H[AI visual search]
    
    F --> I[Merge results]
    G --> I
    H --> I
    
    I --> J[Sort by relevance]
    J --> K[Paginate results]
    K --> L[Return to user]
```

## Tài liệu liên quan

- [Storage Documentation](./storage.md)
- [Tenant & Website Isolation](./tenant-website-isolation.md)
- [Processing Documentation](./processing.md)
- [API Documentation](./api-file-management.md)
# Media Module - Tenant & Website Isolation Strategy

## Tổng quan

Hệ thống media được thiết kế với 2 cấp độ isolation để đảm bảo security, performance và scalability:

1. **Tenant Level**: Mỗi tenant có storage riêng biệt
2. **Website Level**: Trong mỗi tenant, mỗi website có thư mục riêng

## Kiến trúc Storage

### Storage Hierarchy

```mermaid
flowchart TD
    A[Media Storage] --> B[Tenant 1]
    A --> C[Tenant 2]
    A --> D[Tenant N]
    
    B --> B1[Website 1]
    B --> B2[Website 2]
    B --> B3[Website N]
    
    C --> C1[Website 1]
    C --> C2[Website 2]
    
    B1 --> B1A[Images]
    B1 --> B1B[Videos]
    B1 --> B1C[Documents]
    B1 --> B1D[Thumbnails]
    
    B2 --> B2A[Images]
    B2 --> B2B[Videos]
    
    C1 --> C1A[Images]
    C1 --> C1B[Videos]
```

### Directory Structure

#### Local Storage
```
storage/media/
├── tenant_1/
│   ├── website_1/
│   │   ├── 2024/
│   │   │   ├── 01/
│   │   │   │   ├── images/
│   │   │   │   │   ├── uuid1.jpg
│   │   │   │   │   └── uuid2.png
│   │   │   │   ├── videos/
│   │   │   │   │   └── uuid3.mp4
│   │   │   │   └── documents/
│   │   │   │       └── uuid4.pdf
│   │   │   └── 02/
│   │   ├── thumbnails/
│   │   │   ├── small/
│   │   │   ├── medium/
│   │   │   └── large/
│   │   └── cache/
│   │       └── processed/
│   └── website_2/
│       ├── 2024/
│       │   └── 01/
│       │       └── images/
│       │           └── uuid5.jpg
│       └── thumbnails/
└── tenant_2/
    └── website_3/
        ├── 2024/
        │   └── 01/
        │       └── images/
        │           └── uuid6.jpg
        └── thumbnails/
```

#### MinIO/S3 Object Storage
```
media-bucket/
├── tenant_1/
│   ├── website_1/
│   │   ├── images/
│   │   │   ├── 2024-01-15_uuid1.jpg
│   │   │   └── 2024-01-15_uuid2.png
│   │   ├── videos/
│   │   │   └── 2024-01-15_uuid3.mp4
│   │   ├── documents/
│   │   │   └── 2024-01-15_uuid4.pdf
│   │   └── thumbnails/
│   │       ├── small/
│   │       │   └── uuid1_small.jpg
│   │       ├── medium/
│   │       │   └── uuid1_medium.jpg
│   │       └── large/
│   │           └── uuid1_large.jpg
│   └── website_2/
│       └── images/
│           └── 2024-01-15_uuid5.jpg
└── tenant_2/
    └── website_3/
        └── images/
            └── 2024-01-15_uuid6.jpg
```

## Database Schema

### Core Tables

```sql
-- Media files table với tenant_id và website_id
CREATE TABLE media_files (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    folder_id INT UNSIGNED NULL,
    user_id INT UNSIGNED NOT NULL,
    
    -- File information
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_size BIGINT UNSIGNED NOT NULL,
    
    -- Storage information
    storage_type ENUM('local', 'minio', 's3', 'gcs') NOT NULL,
    storage_path VARCHAR(500) NOT NULL,
    public_url VARCHAR(500) NOT NULL,
    
    -- Media metadata
    width INT UNSIGNED NULL,
    height INT UNSIGNED NULL,
    duration INT UNSIGNED NULL,
    metadata JSON DEFAULT (JSON_OBJECT()),
    
    -- Status and timestamps
    status ENUM('uploading', 'processing', 'ready', 'error', 'deleted') NOT NULL DEFAULT 'uploading',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign keys
    CONSTRAINT fk_media_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_media_website FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_media_folder FOREIGN KEY (folder_id) REFERENCES media_folders(id) ON DELETE SET NULL,
    CONSTRAINT fk_media_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT,
    
    -- Indexes
    INDEX idx_media_tenant_website (tenant_id, website_id),
    INDEX idx_media_status (status),
    INDEX idx_media_type (mime_type),
    INDEX idx_media_created (created_at),
    UNIQUE KEY uk_media_tenant_website_path (tenant_id, website_id, storage_path)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Media folders table
CREATE TABLE media_folders (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    parent_id INT UNSIGNED NULL,
    
    name VARCHAR(255) NOT NULL,
    path VARCHAR(500) NOT NULL,
    type ENUM('user', 'system', 'public') NOT NULL DEFAULT 'user',
    
    status ENUM('active', 'deleted') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_folder_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_folder_website FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_folder_parent FOREIGN KEY (parent_id) REFERENCES media_folders(id) ON DELETE CASCADE,
    
    INDEX idx_folder_tenant_website (tenant_id, website_id),
    INDEX idx_folder_parent (parent_id),
    UNIQUE KEY uk_folder_tenant_website_path (tenant_id, website_id, path)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Media thumbnails table
CREATE TABLE media_thumbnails (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    file_id INT UNSIGNED NOT NULL,
    
    size_name VARCHAR(50) NOT NULL, -- small, medium, large, xl
    width INT UNSIGNED NOT NULL,
    height INT UNSIGNED NOT NULL,
    
    storage_path VARCHAR(500) NOT NULL,
    public_url VARCHAR(500) NOT NULL,
    file_size BIGINT UNSIGNED NOT NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_thumbnail_file FOREIGN KEY (file_id) REFERENCES media_files(id) ON DELETE CASCADE,
    
    INDEX idx_thumbnail_file (file_id),
    UNIQUE KEY uk_thumbnail_file_size (file_id, size_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## URL Structure & Access Patterns

### Public URLs

```
# Local Storage
https://yourdomain.com/media/tenant_1/website_1/2024/01/images/uuid.jpg

# MinIO Storage
https://minio.yourdomain.com/media-bucket/tenant_1/website_1/images/uuid.jpg

# AWS S3
https://s3.amazonaws.com/my-media-bucket/tenant_1/website_1/images/uuid.jpg

# CDN URLs
https://cdn.yourdomain.com/tenant_1/website_1/images/uuid.jpg

# Custom CDN per website
https://website1.cdn.yourdomain.com/images/uuid.jpg
https://website2.cdn.yourdomain.com/images/uuid.jpg
```

### API Access Patterns

```
# Get media files for a website
GET /api/cms/v1/media/files?website_id=1

# Upload file to specific website
POST /api/cms/v1/media/upload
{
  "website_id": 1,
  "folder_path": "/images/blog/",
  "file": "..."
}

# Get media usage by tenant
GET /api/cms/v1/media/usage?tenant_id=1

# Get media usage by website
GET /api/cms/v1/media/usage?website_id=1
```

## Security & Access Control

### Access Control Matrix

| Role | Tenant Media | Website Media | Other Website Media |
|------|-------------|---------------|-------------------|
| **Super Admin** | ✅ Full | ✅ Full | ✅ Full |
| **Tenant Admin** | ✅ Full | ✅ Full | ❌ No Access |
| **Website Admin** | ❌ No Access | ✅ Full | ❌ No Access |
| **Website Editor** | ❌ No Access | ✅ Read/Write | ❌ No Access |
| **Website Viewer** | ❌ No Access | ✅ Read Only | ❌ No Access |

### Middleware Implementation

```go
// TenantWebsiteMediaMiddleware validates access to media resources
func TenantWebsiteMediaMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        userID := c.GetUint("user_id")
        tenantID := c.GetUint("tenant_id")
        websiteID := c.GetUint("website_id")
        
        // Validate user has access to this tenant
        if !hasTenantAccess(userID, tenantID) {
            c.JSON(403, gin.H{"error": "Access denied to tenant"})
            c.Abort()
            return
        }
        
        // Validate user has access to this website
        if !hasWebsiteAccess(userID, websiteID) {
            c.JSON(403, gin.H{"error": "Access denied to website"})
            c.Abort()
            return
        }
        
        c.Next()
    }
}
```

## Performance Optimization

### CDN Strategy per Website

```mermaid
flowchart TD
    A[User Request] --> B{Website Detection}
    B --> C[Website 1] 
    B --> D[Website 2]
    B --> E[Website N]
    
    C --> C1[CDN Edge: website1.cdn.com]
    D --> D1[CDN Edge: website2.cdn.com]
    E --> E1[CDN Edge: websiteN.cdn.com]
    
    C1 --> F[Origin: tenant1/website1/]
    D1 --> G[Origin: tenant1/website2/]
    E1 --> H[Origin: tenantN/websiteN/]
```

### Caching Strategy

```yaml
cache_config:
  levels:
    - browser: 
        ttl: "30 days"
        headers: ["Cache-Control", "ETag"]
    - cdn:
        ttl: "7 days"
        vary_by: ["tenant_id", "website_id"]
    - application:
        ttl: "1 hour"
        keys: "tenant:{tenant_id}:website:{website_id}:media:{file_id}"
```

## Storage Quotas & Billing

### Quota Management

```sql
-- Storage quotas per tenant and website
CREATE TABLE storage_quotas (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NULL, -- NULL for tenant-level quota
    
    quota_type ENUM('storage', 'bandwidth', 'requests') NOT NULL,
    limit_value BIGINT UNSIGNED NOT NULL, -- in bytes/requests
    current_usage BIGINT UNSIGNED DEFAULT 0,
    
    period_type ENUM('daily', 'monthly', 'yearly') NOT NULL,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_quota_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_quota_website FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    
    INDEX idx_quota_tenant (tenant_id),
    INDEX idx_quota_website (website_id),
    UNIQUE KEY uk_quota_tenant_website_type (tenant_id, website_id, quota_type, period_start)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### Usage Tracking

```go
// Update usage when file is uploaded
func (s *MediaService) UpdateUsage(ctx context.Context, tenantID, websiteID uint, fileSize int64) error {
    // Update tenant-level usage
    if err := s.quotaRepo.UpdateUsage(ctx, tenantID, 0, "storage", fileSize); err != nil {
        return err
    }
    
    // Update website-level usage
    if err := s.quotaRepo.UpdateUsage(ctx, tenantID, websiteID, "storage", fileSize); err != nil {
        return err
    }
    
    return nil
}
```

## Backup & Disaster Recovery

### Backup Strategy per Website

```mermaid
flowchart TD
    A[Media Backup] --> B[Tenant Level]
    A --> C[Website Level]
    
    B --> B1[Daily Incremental]
    B --> B2[Weekly Full]
    B --> B3[Monthly Archive]
    
    C --> C1[Real-time Sync]
    C --> C2[Point-in-time Recovery]
    C --> C3[Cross-region Replication]
    
    B1 --> D[Backup Storage]
    B2 --> D
    B3 --> D
    
    C1 --> E[DR Storage]
    C2 --> E
    C3 --> E
```

### Recovery Procedures

```bash
# Restore specific website media
./backup-restore --tenant-id=1 --website-id=1 --date=2024-01-15

# Restore entire tenant media
./backup-restore --tenant-id=1 --date=2024-01-15

# Selective restore by file type
./backup-restore --tenant-id=1 --website-id=1 --type=images --date=2024-01-15
```

## Monitoring & Analytics

### Metrics Collection

```yaml
metrics:
  tenant_level:
    - storage_usage_bytes
    - bandwidth_usage_bytes
    - request_count
    - error_rate
    
  website_level:
    - storage_usage_bytes
    - popular_files
    - upload_frequency
    - cdn_hit_rate
    
  file_level:
    - view_count
    - download_count
    - processing_time
    - error_logs
```

### Analytics Dashboard

```mermaid
flowchart LR
    A[Media Analytics] --> B[Tenant Dashboard]
    A --> C[Website Dashboard]
    A --> D[File Analytics]
    
    B --> B1[Storage Usage]
    B --> B2[Bandwidth Usage]
    B --> B3[Cost Analysis]
    
    C --> C1[Popular Content]
    C --> C2[Upload Patterns]
    C --> C3[Performance Metrics]
    
    D --> D1[View Statistics]
    D --> D2[Download History]
    D --> D3[Processing Status]
```

## Migration & Maintenance

### Storage Migration

```mermaid
sequenceDiagram
    participant Admin as Admin
    participant API as Migration API
    participant Source as Source Storage
    participant Target as Target Storage
    participant DB as Database
    
    Admin->>API: Start tenant migration
    API->>DB: Get tenant websites
    DB->>API: Website list
    
    loop For each website
        API->>DB: Get website files
        DB->>API: File list
        
        loop For each file
            API->>Source: Download file
            Source->>API: File data
            API->>Target: Upload to new location
            Target->>API: New URL
            API->>DB: Update file record
        end
    end
    
    API->>Admin: Migration complete
```

### Maintenance Tasks

```bash
# Clean up orphaned files
./media-cleanup --tenant-id=1 --dry-run

# Optimize storage usage
./media-optimize --website-id=1 --compress-images

# Generate missing thumbnails
./media-regenerate --tenant-id=1 --thumbnails

# Verify file integrity
./media-verify --website-id=1 --checksum
```

## Implementation Considerations

### Service Layer

```go
type MediaService struct {
    tenantRepo   TenantRepository
    websiteRepo  WebsiteRepository
    mediaRepo    MediaRepository
    storageService StorageService
    quotaService QuotaService
}

func (s *MediaService) UploadFile(ctx context.Context, input UploadInput) (*MediaFile, error) {
    // Validate tenant and website access
    if err := s.validateAccess(ctx, input.TenantID, input.WebsiteID); err != nil {
        return nil, err
    }
    
    // Check quota limits
    if err := s.quotaService.CheckQuota(ctx, input.TenantID, input.WebsiteID, input.FileSize); err != nil {
        return nil, err
    }
    
    // Generate storage path
    storagePath := s.generateStoragePath(input.TenantID, input.WebsiteID, input.FileType)
    
    // Upload to storage
    url, err := s.storageService.Upload(ctx, storagePath, input.File)
    if err != nil {
        return nil, err
    }
    
    // Save to database
    mediaFile := &MediaFile{
        TenantID:    input.TenantID,
        WebsiteID:   input.WebsiteID,
        StoragePath: storagePath,
        PublicURL:   url,
        // ... other fields
    }
    
    if err := s.mediaRepo.Create(ctx, mediaFile); err != nil {
        return nil, err
    }
    
    // Update usage metrics
    s.quotaService.UpdateUsage(ctx, input.TenantID, input.WebsiteID, input.FileSize)
    
    return mediaFile, nil
}
```

## Best Practices

### 1. **Security First**
- Always validate tenant and website access
- Use signed URLs for sensitive content
- Implement rate limiting per tenant/website
- Regular security audits

### 2. **Performance Optimization**
- Use CDN with website-specific caching
- Implement lazy loading for media lists
- Optimize image delivery with multiple formats
- Cache frequently accessed metadata

### 3. **Cost Management**
- Monitor storage usage per tenant/website
- Implement automated cleanup of old files
- Use tiered storage for different access patterns
- Regular cost analysis and optimization

### 4. **Scalability**
- Design for horizontal scaling
- Use async processing for heavy operations
- Implement proper indexing strategies
- Plan for multi-region deployment

## Tài liệu liên quan

- [Storage Documentation](./storage.md)
- [Security Documentation](./security.md)
- [Performance Documentation](./performance.md)
- [Integration Documentation](./integration.md)
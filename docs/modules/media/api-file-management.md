# Media Module - File Management API

## API Endpoints

### File Management
- `GET /api/cms/v1/media/files` - Danh sách files
- `POST /api/cms/v1/media/upload` - Upload file
- `GET /api/cms/v1/media/files/{id}` - Chi tiết file
- `PUT /api/cms/v1/media/files/{id}` - Cập nhật metadata
- `DELETE /api/cms/v1/media/files/{id}` - Xóa file

### Folder Management
- `GET /api/cms/v1/media/folders` - <PERSON>h sách thư mục
- `POST /api/cms/v1/media/folders` - Tạo thư mục
- `PUT /api/cms/v1/media/folders/{id}` - S<PERSON>a thư mục
- `DELETE /api/cms/v1/media/folders/{id}` - X<PERSON><PERSON> thư mục

### Search & Filter
- `GET /api/cms/v1/media/search?q={query}` - <PERSON><PERSON><PERSON> kiếm
- `GET /api/cms/v1/media/filter?type=image&size=large` - Filter
- `GET /api/cms/v1/media/recent` - File gần đây
- `GET /api/cms/v1/media/popular` - File phổ biến

### Processing
- `POST /api/cms/v1/media/files/{id}/thumbnail` - Tạo thumbnail
- `POST /api/cms/v1/media/files/{id}/resize` - Resize hình ảnh
- `POST /api/cms/v1/media/files/{id}/watermark` - Thêm watermark
- `GET /api/cms/v1/media/files/{id}/status` - Trạng thái xử lý

## Tài liệu liên quan

- [API Folder Management](./api-folder-management.md)
- [API Search & Filter](./api-search-filter.md)
- [API Processing](./api-processing.md)
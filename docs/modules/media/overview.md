# Media Module Overview

## Tổng quan

Media Module cung cấp hệ thống quản lý file đa phương tiện toàn diện, bao gồ<PERSON> upload, l<PERSON><PERSON> tr<PERSON>, <PERSON><PERSON> lý, tối ưu hóa và phân phối file hình ảnh, video, audio và documents cho hệ thống blog.

## Mục tiêu

- **Quản lý file tập trung**: Hệ thống quản lý tất cả media files
- **Tối ưu hóa performance**: Nén, resize, format conversion tự động
- **Bảo mật**: <PERSON><PERSON><PERSON> soát quyền truy cập và virus scanning
- **Scalability**: Hỗ trợ multiple storage providers
- **CDN Integration**: Phân phối nhanh toàn cầu

## Tính năng chính

- **File Upload**: Multi-file upload với drag & drop
- **Media Library**: Thư viện quản lý file organized
- **Image Processing**: Resize, crop, watermark, format conversion
- **Video Processing**: Thumbnail generation, format conversion, streaming
- **Storage Management**: Multiple storage backends
- **CDN Integration**: CloudFlare, AWS CloudFront, etc.
- **Access Control**: Permission-based file access
- **Metadata Extraction**: EXIF, file info, dimensions
- **Search & Filter**: Tìm kiếm file theo nhiều tiêu chí

## Kiến trúc Module

### Cấu trúc thư mục
```
internal/modules/media/
├── models/                   # Các model media
│   ├── file.go              # Model file chính
│   ├── folder.go            # Model thư mục
│   ├── thumbnail.go         # Model thumbnail
│   └── metadata.go          # Model metadata
├── services/                # Logic nghiệp vụ
│   ├── upload_service.go    # Dịch vụ upload
│   ├── processing_service.go # Dịch vụ xử lý file
│   ├── storage_service.go   # Dịch vụ lưu trữ
│   ├── cdn_service.go       # Dịch vụ CDN
│   └── security_service.go  # Dịch vụ bảo mật
├── handlers/                # HTTP handlers
├── repositories/            # Truy cập dữ liệu
├── processors/              # File processors
│   ├── image_processor.go   # Xử lý hình ảnh
│   ├── video_processor.go   # Xử lý video
│   └── document_processor.go # Xử lý document
├── storage/                 # Storage adapters
│   ├── local_storage.go     # Local filesystem
│   ├── s3_storage.go        # AWS S3
│   ├── minio_storage.go     # MinIO S3-compatible
│   └── gcs_storage.go       # Google Cloud Storage
└── validators/              # File validation
```

## Tài liệu chi tiết

### Core Components
- **[Models](./models.md)** - Database models và data structures
- **[Storage](./storage.md)** - Storage adapters và configuration
- **[Tenant & Website Isolation](./tenant-website-isolation.md)** - Phương án lưu trữ theo tenant và website
- **[Processing](./processing.md)** - File processing và thumbnail generation
- **[CDN](./cdn.md)** - CDN integration và caching strategies

### API Documentation
- **[File Management API](./api-file-management.md)** - File upload, management endpoints
- **[Folder Management API](./api-folder-management.md)** - Folder operations
- **[Search & Filter API](./api-search-filter.md)** - Search và filtering endpoints
- **[Processing API](./api-processing.md)** - File processing endpoints

### Advanced Features
- **[Security](./security.md)** - Access control, virus scanning, content moderation
- **[Performance](./performance.md)** - Caching, optimization, monitoring
- **[Backup](./backup.md)** - Backup strategies và disaster recovery

### Integration
- **[Module Integration](./integration.md)** - Integration với các modules khác
- **[Best Practices](./best-practices.md)** - Development và deployment best practices

## Các loại File được hỗ trợ

### Hình ảnh
- **Raster**: JPEG, PNG, GIF, WebP, TIFF, BMP
- **Vector**: SVG
- **Raw**: CR2, NEF, ARW (Camera raw)
- **Features**: Resize, crop, watermark, format conversion

### Video
- **Formats**: MP4, AVI, MOV, WebM, MKV, FLV
- **Codecs**: H.264, H.265, VP9, AV1
- **Features**: Thumbnail generation, transcoding, streaming

### Audio
- **Formats**: MP3, WAV, AAC, OGG, FLAC
- **Features**: Metadata extraction, waveform generation

### Documents
- **Office**: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX
- **Text**: TXT, RTF, MD
- **Features**: Thumbnail preview, text extraction

### Archives
- **Formats**: ZIP, RAR, 7Z, TAR
- **Features**: Content listing, virus scanning

## Tích hợp với các module khác

### Blog Module
- **Featured Images**: Hình đại diện bài viết
- **Content Images**: Hình trong nội dung
- **Gallery Posts**: Bài viết gallery
- **Media Embedding**: Embed video/audio

### Website Module
- **Theme Assets**: Logo, background, icons
- **Page Media**: Hình ảnh trong pages
- **Widget Media**: Media trong widgets
- **Custom Assets**: CSS, JS files

### Tenant Module
- **Isolated Storage**: Media riêng cho mỗi tenant
- **Storage Quotas**: Giới hạn dung lượng
- **Branding Assets**: Logo, watermark riêng
- **Custom CDN**: CDN endpoint riêng

## Tài liệu liên quan

- [Module System Overview](../overview.md)
- [Website Module](../website.md)
- [Blog Module](../blog.md)
- [Performance Best Practices](../../best-practices/performance.md)
- [Security Guidelines](../../best-practices/security.md)
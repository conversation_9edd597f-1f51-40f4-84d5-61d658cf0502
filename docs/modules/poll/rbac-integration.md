# Poll Module - RBAC Integration

## Overview

The Poll module integrates with the Role-Based Access Control (RBAC) system to provide fine-grained permissions for poll management, voting, and analytics access. This ensures secure and controlled access to polling functionality based on user roles and permissions.

## RBAC Architecture

```mermaid
graph TB
    subgraph "Users"
        Admin[Admin User]
        Editor[Editor User]
        Viewer[Viewer User]
        Guest[Guest User]
    end
    
    subgraph "Roles"
        AdminRole[Admin Role]
        EditorRole[Editor Role]
        ViewerRole[Viewer Role]
        GuestRole[Guest Role]
    end
    
    subgraph "Permissions"
        ManagePolls[polls.manage]
        CreatePolls[polls.create]
        EditPolls[polls.edit]
        DeletePolls[polls.delete]
        ViewPolls[polls.view]
        Vote[polls.vote]
        ViewAnalytics[polls.analytics.view]
        ExportData[polls.export]
    end
    
    subgraph "Poll Actions"
        CreatePoll[Create Poll]
        EditPoll[Edit Poll]
        DeletePoll[Delete Poll]
        ViewPoll[View Poll]
        SubmitVote[Submit Vote]
        ViewResults[View Results]
        ExportResults[Export Results]
    end
    
    Admin --> AdminRole
    Editor --> Editor<PERSON>ole
    Viewer --> ViewerRole
    Guest --> GuestRole
    
    AdminRole --> ManagePolls
    AdminRole --> CreatePolls
    AdminRole --> EditPolls
    AdminRole --> DeletePolls
    AdminRole --> ViewPolls
    AdminRole --> Vote
    AdminRole --> ViewAnalytics
    AdminRole --> ExportData
    
    EditorRole --> CreatePolls
    EditorRole --> EditPolls
    EditorRole --> ViewPolls
    EditorRole --> Vote
    EditorRole --> ViewAnalytics
    
    ViewerRole --> ViewPolls
    ViewerRole --> Vote
    
    GuestRole --> ViewPolls
    GuestRole --> Vote
    
    ManagePolls --> CreatePoll
    ManagePolls --> EditPoll
    ManagePolls --> DeletePoll
    CreatePolls --> CreatePoll
    EditPolls --> EditPoll
    DeletePolls --> DeletePoll
    ViewPolls --> ViewPoll
    Vote --> SubmitVote
    ViewAnalytics --> ViewResults
    ExportData --> ExportResults
```

## Permission Definitions

### Core Poll Permissions

```go
const (
    // Poll Management
    PermissionPollsManage = "polls.manage"       // Full poll management
    PermissionPollsCreate = "polls.create"       // Create new polls
    PermissionPollsEdit   = "polls.edit"         // Edit existing polls
    PermissionPollsDelete = "polls.delete"       // Delete polls
    PermissionPollsView   = "polls.view"         // View polls
    
    // Voting
    PermissionPollsVote         = "polls.vote"           // Submit votes
    PermissionPollsVoteOverride = "polls.vote.override"  // Override vote limits
    
    // Analytics
    PermissionPollsAnalyticsView   = "polls.analytics.view"   // View analytics
    PermissionPollsAnalyticsManage = "polls.analytics.manage" // Manage analytics
    
    // Data Export
    PermissionPollsExport = "polls.export"       // Export poll data
    
    // Advanced Features
    PermissionPollsSchedule = "polls.schedule"   // Schedule polls
    PermissionPollsFeature  = "polls.feature"    // Feature polls
    PermissionPollsModerate = "polls.moderate"   // Moderate polls
)
```

### Permission Groups

```go
type PermissionGroup struct {
    Name        string   `json:"name"`
    Description string   `json:"description"`
    Permissions []string `json:"permissions"`
}

var PollPermissionGroups = []PermissionGroup{
    {
        Name:        "poll_admin",
        Description: "Full poll administration access",
        Permissions: []string{
            PermissionPollsManage,
            PermissionPollsCreate,
            PermissionPollsEdit,
            PermissionPollsDelete,
            PermissionPollsView,
            PermissionPollsVote,
            PermissionPollsVoteOverride,
            PermissionPollsAnalyticsView,
            PermissionPollsAnalyticsManage,
            PermissionPollsExport,
            PermissionPollsSchedule,
            PermissionPollsFeature,
            PermissionPollsModerate,
        },
    },
    {
        Name:        "poll_editor",
        Description: "Poll content management",
        Permissions: []string{
            PermissionPollsCreate,
            PermissionPollsEdit,
            PermissionPollsView,
            PermissionPollsVote,
            PermissionPollsAnalyticsView,
            PermissionPollsSchedule,
        },
    },
    {
        Name:        "poll_viewer",
        Description: "Poll viewing and voting",
        Permissions: []string{
            PermissionPollsView,
            PermissionPollsVote,
        },
    },
}
```

## Permission Enforcement

### 1. Service-Level Authorization

```go
type PollService struct {
    repository  PollRepository
    rbacService RBACService
    cache       CacheService
    logger      *zap.Logger
}

func (s *PollService) CreatePoll(ctx context.Context, req *CreatePollRequest) (*Poll, error) {
    // Get user context
    userCtx := GetUserContext(ctx)
    if userCtx == nil {
        return nil, ErrUserContextRequired
    }
    
    // Check create permission
    if err := s.rbacService.CheckPermission(ctx, userCtx.UserID, PermissionPollsCreate); err != nil {
        return nil, ErrPermissionDenied
    }
    
    // Additional checks for advanced features
    if req.IsScheduled {
        if err := s.rbacService.CheckPermission(ctx, userCtx.UserID, PermissionPollsSchedule); err != nil {
            return nil, ErrPermissionDenied
        }
    }
    
    if req.IsFeatured {
        if err := s.rbacService.CheckPermission(ctx, userCtx.UserID, PermissionPollsFeature); err != nil {
            return nil, ErrPermissionDenied
        }
    }
    
    // Create poll
    poll, err := s.repository.CreatePoll(ctx, req)
    if err != nil {
        return nil, err
    }
    
    // Log authorization event
    s.logger.Info("Poll created",
        zap.Uint("user_id", userCtx.UserID),
        zap.Uint("poll_id", poll.ID),
        zap.String("action", "create"),
    )
    
    return poll, nil
}

func (s *PollService) UpdatePoll(ctx context.Context, pollID uint, req *UpdatePollRequest) (*Poll, error) {
    userCtx := GetUserContext(ctx)
    if userCtx == nil {
        return nil, ErrUserContextRequired
    }
    
    // Get existing poll
    poll, err := s.repository.GetPollByID(ctx, pollID)
    if err != nil {
        return nil, err
    }
    
    // Check ownership or edit permission
    if poll.CreatedBy == userCtx.UserID {
        // User owns the poll - allow edit
    } else {
        // Check edit permission
        if err := s.rbacService.CheckPermission(ctx, userCtx.UserID, PermissionPollsEdit); err != nil {
            return nil, ErrPermissionDenied
        }
    }
    
    // Update poll
    updatedPoll, err := s.repository.UpdatePoll(ctx, pollID, req)
    if err != nil {
        return nil, err
    }
    
    return updatedPoll, nil
}
```

### 2. Resource-Level Authorization

```go
type ResourceAuthorizer struct {
    rbacService RBACService
    pollService PollService
}

func (a *ResourceAuthorizer) CanAccessPoll(ctx context.Context, pollID uint, action string) error {
    userCtx := GetUserContext(ctx)
    if userCtx == nil {
        return ErrUserContextRequired
    }
    
    // Get poll details
    poll, err := a.pollService.GetPollByID(ctx, pollID)
    if err != nil {
        return err
    }
    
    // Check basic permission
    permission := getPermissionForAction(action)
    if err := a.rbacService.CheckPermission(ctx, userCtx.UserID, permission); err != nil {
        return err
    }
    
    // Additional resource-specific checks
    switch action {
    case "edit", "delete":
        // Check ownership or manage permission
        if poll.CreatedBy != userCtx.UserID {
            if err := a.rbacService.CheckPermission(ctx, userCtx.UserID, PermissionPollsManage); err != nil {
                return ErrPermissionDenied
            }
        }
    case "vote":
        // Check voting eligibility
        if err := a.checkVotingEligibility(ctx, poll, userCtx.UserID); err != nil {
            return err
        }
    }
    
    return nil
}

func (a *ResourceAuthorizer) checkVotingEligibility(ctx context.Context, poll *Poll, userID uint) error {
    // Check if poll is active
    if poll.Status != "active" {
        return ErrPollNotActive
    }
    
    // Check if user has already voted
    hasVoted, err := a.pollService.HasUserVoted(ctx, poll.ID, userID)
    if err != nil {
        return err
    }
    
    if hasVoted {
        return ErrAlreadyVoted
    }
    
    // Check time constraints
    now := time.Now()
    if poll.StartDate != nil && now.Before(*poll.StartDate) {
        return ErrPollNotStarted
    }
    
    if poll.EndDate != nil && now.After(*poll.EndDate) {
        return ErrPollEnded
    }
    
    return nil
}
```

### 3. Handler-Level Authorization

```go
type PollHandler struct {
    service    PollService
    authorizer ResourceAuthorizer
    validator  Validator
}

func (h *PollHandler) CreatePoll(c *gin.Context) {
    var req CreatePollRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(400, gin.H{"error": "Invalid request format"})
        return
    }
    
    // Validate request
    if err := h.validator.Validate(&req); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }
    
    // Authorization is handled in service layer
    poll, err := h.service.CreatePoll(c.Request.Context(), &req)
    if err != nil {
        h.handleError(c, err)
        return
    }
    
    c.JSON(201, gin.H{
        "status": "success",
        "data":   poll,
    })
}

func (h *PollHandler) GetPoll(c *gin.Context) {
    pollID, err := strconv.ParseUint(c.Param("id"), 10, 32)
    if err != nil {
        c.JSON(400, gin.H{"error": "Invalid poll ID"})
        return
    }
    
    // Check view permission
    if err := h.authorizer.CanAccessPoll(c.Request.Context(), uint(pollID), "view"); err != nil {
        h.handleError(c, err)
        return
    }
    
    poll, err := h.service.GetPollByID(c.Request.Context(), uint(pollID))
    if err != nil {
        h.handleError(c, err)
        return
    }
    
    c.JSON(200, gin.H{
        "status": "success",
        "data":   poll,
    })
}

func (h *PollHandler) SubmitVote(c *gin.Context) {
    pollID, err := strconv.ParseUint(c.Param("id"), 10, 32)
    if err != nil {
        c.JSON(400, gin.H{"error": "Invalid poll ID"})
        return
    }
    
    var req SubmitVoteRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(400, gin.H{"error": "Invalid request format"})
        return
    }
    
    req.PollID = uint(pollID)
    
    // Check vote permission
    if err := h.authorizer.CanAccessPoll(c.Request.Context(), uint(pollID), "vote"); err != nil {
        h.handleError(c, err)
        return
    }
    
    vote, err := h.service.SubmitVote(c.Request.Context(), &req)
    if err != nil {
        h.handleError(c, err)
        return
    }
    
    c.JSON(200, gin.H{
        "status": "success",
        "data":   vote,
    })
}
```

## Role-Based Features

### 1. Role-Specific UI Components

```go
type PollUIConfig struct {
    CanCreate     bool `json:"can_create"`
    CanEdit       bool `json:"can_edit"`
    CanDelete     bool `json:"can_delete"`
    CanSchedule   bool `json:"can_schedule"`
    CanFeature    bool `json:"can_feature"`
    CanModerate   bool `json:"can_moderate"`
    CanViewAnalytics bool `json:"can_view_analytics"`
    CanExport     bool `json:"can_export"`
}

func (h *PollHandler) GetUIConfig(c *gin.Context) {
    userCtx := GetUserContext(c.Request.Context())
    if userCtx == nil {
        c.JSON(401, gin.H{"error": "Authentication required"})
        return
    }
    
    config := PollUIConfig{
        CanCreate:     h.hasPermission(c, userCtx.UserID, PermissionPollsCreate),
        CanEdit:       h.hasPermission(c, userCtx.UserID, PermissionPollsEdit),
        CanDelete:     h.hasPermission(c, userCtx.UserID, PermissionPollsDelete),
        CanSchedule:   h.hasPermission(c, userCtx.UserID, PermissionPollsSchedule),
        CanFeature:    h.hasPermission(c, userCtx.UserID, PermissionPollsFeature),
        CanModerate:   h.hasPermission(c, userCtx.UserID, PermissionPollsModerate),
        CanViewAnalytics: h.hasPermission(c, userCtx.UserID, PermissionPollsAnalyticsView),
        CanExport:     h.hasPermission(c, userCtx.UserID, PermissionPollsExport),
    }
    
    c.JSON(200, gin.H{
        "status": "success",
        "data":   config,
    })
}

func (h *PollHandler) hasPermission(c *gin.Context, userID uint, permission string) bool {
    err := h.authorizer.rbacService.CheckPermission(c.Request.Context(), userID, permission)
    return err == nil
}
```

### 2. Dynamic Menu Generation

```go
type MenuItem struct {
    ID          string     `json:"id"`
    Label       string     `json:"label"`
    Icon        string     `json:"icon"`
    URL         string     `json:"url"`
    Permission  string     `json:"permission"`
    Children    []MenuItem `json:"children,omitempty"`
}

func (h *PollHandler) GetPollMenu(c *gin.Context) {
    userCtx := GetUserContext(c.Request.Context())
    if userCtx == nil {
        c.JSON(401, gin.H{"error": "Authentication required"})
        return
    }
    
    allMenuItems := []MenuItem{
        {
            ID:         "polls",
            Label:      "Polls",
            Icon:       "poll",
            URL:        "/polls",
            Permission: PermissionPollsView,
        },
        {
            ID:         "create-poll",
            Label:      "Create Poll",
            Icon:       "add",
            URL:        "/polls/create",
            Permission: PermissionPollsCreate,
        },
        {
            ID:         "poll-analytics",
            Label:      "Analytics",
            Icon:       "analytics",
            URL:        "/polls/analytics",
            Permission: PermissionPollsAnalyticsView,
        },
        {
            ID:         "poll-admin",
            Label:      "Administration",
            Icon:       "admin",
            URL:        "/polls/admin",
            Permission: PermissionPollsManage,
            Children: []MenuItem{
                {
                    ID:         "moderate-polls",
                    Label:      "Moderate Polls",
                    Icon:       "moderate",
                    URL:        "/polls/moderate",
                    Permission: PermissionPollsModerate,
                },
                {
                    ID:         "export-data",
                    Label:      "Export Data",
                    Icon:       "download",
                    URL:        "/polls/export",
                    Permission: PermissionPollsExport,
                },
            },
        },
    }
    
    // Filter menu items based on permissions
    filteredMenu := h.filterMenuByPermissions(c.Request.Context(), userCtx.UserID, allMenuItems)
    
    c.JSON(200, gin.H{
        "status": "success",
        "data":   filteredMenu,
    })
}

func (h *PollHandler) filterMenuByPermissions(ctx context.Context, userID uint, items []MenuItem) []MenuItem {
    var filtered []MenuItem
    
    for _, item := range items {
        if item.Permission == "" || h.hasPermission(ctx, userID, item.Permission) {
            // Filter children recursively
            if len(item.Children) > 0 {
                item.Children = h.filterMenuByPermissions(ctx, userID, item.Children)
            }
            
            // Only include items that have permission or have visible children
            if item.Permission == "" || len(item.Children) > 0 {
                filtered = append(filtered, item)
            }
        }
    }
    
    return filtered
}
```

## Permission Middleware

### 1. Route-Level Permission Middleware

```go
func RequirePermission(permission string) gin.HandlerFunc {
    return func(c *gin.Context) {
        userCtx := GetUserContext(c.Request.Context())
        if userCtx == nil {
            c.JSON(401, gin.H{"error": "Authentication required"})
            c.Abort()
            return
        }
        
        rbacService := GetRBACService(c)
        if err := rbacService.CheckPermission(c.Request.Context(), userCtx.UserID, permission); err != nil {
            c.JSON(403, gin.H{"error": "Permission denied"})
            c.Abort()
            return
        }
        
        c.Next()
    }
}

// Usage in routes
func (h *PollHandler) RegisterRoutes(r *gin.RouterGroup) {
    polls := r.Group("/polls")
    
    // Public routes
    polls.GET("/:id", h.GetPoll)
    polls.POST("/:id/vote", h.SubmitVote)
    
    // Protected routes
    polls.POST("", RequirePermission(PermissionPollsCreate), h.CreatePoll)
    polls.PUT("/:id", RequirePermission(PermissionPollsEdit), h.UpdatePoll)
    polls.DELETE("/:id", RequirePermission(PermissionPollsDelete), h.DeletePoll)
    
    // Admin routes
    polls.GET("/:id/analytics", RequirePermission(PermissionPollsAnalyticsView), h.GetAnalytics)
    polls.POST("/:id/export", RequirePermission(PermissionPollsExport), h.ExportData)
    polls.POST("/:id/moderate", RequirePermission(PermissionPollsModerate), h.ModeratePoll)
}
```

### 2. Resource-Specific Permission Middleware

```go
func RequireResourcePermission(action string) gin.HandlerFunc {
    return func(c *gin.Context) {
        pollID, err := strconv.ParseUint(c.Param("id"), 10, 32)
        if err != nil {
            c.JSON(400, gin.H{"error": "Invalid poll ID"})
            c.Abort()
            return
        }
        
        authorizer := GetResourceAuthorizer(c)
        if err := authorizer.CanAccessPoll(c.Request.Context(), uint(pollID), action); err != nil {
            c.JSON(403, gin.H{"error": err.Error()})
            c.Abort()
            return
        }
        
        c.Next()
    }
}

// Usage
polls.PUT("/:id", RequireResourcePermission("edit"), h.UpdatePoll)
polls.DELETE("/:id", RequireResourcePermission("delete"), h.DeletePoll)
```

## Audit Logging

### 1. Permission Check Logging

```go
type PermissionAuditLog struct {
    ID          uint      `json:"id"`
    UserID      uint      `json:"user_id"`
    TenantID    uint      `json:"tenant_id"`
    Permission  string    `json:"permission"`
    Resource    string    `json:"resource"`
    ResourceID  uint      `json:"resource_id"`
    Action      string    `json:"action"`
    Result      string    `json:"result"` // granted, denied
    IPAddress   string    `json:"ip_address"`
    UserAgent   string    `json:"user_agent"`
    CreatedAt   time.Time `json:"created_at"`
}

func (s *RBACService) CheckPermissionWithAudit(ctx context.Context, userID uint, permission string) error {
    // Get request context
    reqCtx := GetRequestContext(ctx)
    
    // Check permission
    err := s.CheckPermission(ctx, userID, permission)
    
    // Log audit event
    auditLog := &PermissionAuditLog{
        UserID:     userID,
        TenantID:   reqCtx.TenantID,
        Permission: permission,
        Resource:   "poll",
        Action:     "check_permission",
        Result:     "granted",
        IPAddress:  reqCtx.IPAddress,
        UserAgent:  reqCtx.UserAgent,
        CreatedAt:  time.Now(),
    }
    
    if err != nil {
        auditLog.Result = "denied"
    }
    
    // Async audit logging
    go s.auditLogger.LogPermissionCheck(auditLog)
    
    return err
}
```

### 2. Poll Action Logging

```go
type PollActionLog struct {
    ID         uint      `json:"id"`
    UserID     uint      `json:"user_id"`
    TenantID   uint      `json:"tenant_id"`
    PollID     uint      `json:"poll_id"`
    Action     string    `json:"action"`
    Details    string    `json:"details"`
    IPAddress  string    `json:"ip_address"`
    UserAgent  string    `json:"user_agent"`
    CreatedAt  time.Time `json:"created_at"`
}

func (s *PollService) logPollAction(ctx context.Context, pollID uint, action string, details interface{}) {
    userCtx := GetUserContext(ctx)
    tenantCtx := GetTenantContext(ctx)
    reqCtx := GetRequestContext(ctx)
    
    detailsJSON, _ := json.Marshal(details)
    
    actionLog := &PollActionLog{
        UserID:    userCtx.UserID,
        TenantID:  tenantCtx.TenantID,
        PollID:    pollID,
        Action:    action,
        Details:   string(detailsJSON),
        IPAddress: reqCtx.IPAddress,
        UserAgent: reqCtx.UserAgent,
        CreatedAt: time.Now(),
    }
    
    // Async logging
    go s.auditLogger.LogPollAction(actionLog)
}
```

## Testing RBAC Integration

### 1. Permission Tests

```go
func TestPollPermissions(t *testing.T) {
    // Setup test data
    tenant := createTestTenant(t)
    
    adminUser := createTestUser(t, "admin")
    editorUser := createTestUser(t, "editor")
    viewerUser := createTestUser(t, "viewer")
    
    // Assign roles
    assignRole(t, adminUser.ID, "poll_admin")
    assignRole(t, editorUser.ID, "poll_editor")
    assignRole(t, viewerUser.ID, "poll_viewer")
    
    // Test admin permissions
    t.Run("Admin can create polls", func(t *testing.T) {
        ctx := createUserContext(adminUser.ID, tenant.ID)
        poll, err := pollService.CreatePoll(ctx, &CreatePollRequest{
            Title: "Test Poll",
            Type:  "single_choice",
        })
        assert.NoError(t, err)
        assert.NotNil(t, poll)
    })
    
    // Test editor permissions
    t.Run("Editor can create polls", func(t *testing.T) {
        ctx := createUserContext(editorUser.ID, tenant.ID)
        poll, err := pollService.CreatePoll(ctx, &CreatePollRequest{
            Title: "Test Poll",
            Type:  "single_choice",
        })
        assert.NoError(t, err)
        assert.NotNil(t, poll)
    })
    
    // Test viewer permissions
    t.Run("Viewer cannot create polls", func(t *testing.T) {
        ctx := createUserContext(viewerUser.ID, tenant.ID)
        _, err := pollService.CreatePoll(ctx, &CreatePollRequest{
            Title: "Test Poll",
            Type:  "single_choice",
        })
        assert.Error(t, err)
        assert.Equal(t, ErrPermissionDenied, err)
    })
}
```

### 2. Resource Access Tests

```go
func TestResourceAccess(t *testing.T) {
    // Create test users and poll
    owner := createTestUser(t, "owner")
    editor := createTestUser(t, "editor")
    viewer := createTestUser(t, "viewer")
    
    ownerCtx := createUserContext(owner.ID, tenant.ID)
    poll, err := pollService.CreatePoll(ownerCtx, &CreatePollRequest{
        Title: "Test Poll",
        Type:  "single_choice",
    })
    require.NoError(t, err)
    
    // Test owner can edit their own poll
    t.Run("Owner can edit own poll", func(t *testing.T) {
        err := pollService.UpdatePoll(ownerCtx, poll.ID, &UpdatePollRequest{
            Title: "Updated Poll",
        })
        assert.NoError(t, err)
    })
    
    // Test editor with permission can edit
    t.Run("Editor can edit poll", func(t *testing.T) {
        assignRole(t, editor.ID, "poll_editor")
        editorCtx := createUserContext(editor.ID, tenant.ID)
        
        err := pollService.UpdatePoll(editorCtx, poll.ID, &UpdatePollRequest{
            Title: "Editor Updated Poll",
        })
        assert.NoError(t, err)
    })
    
    // Test viewer cannot edit
    t.Run("Viewer cannot edit poll", func(t *testing.T) {
        assignRole(t, viewer.ID, "poll_viewer")
        viewerCtx := createUserContext(viewer.ID, tenant.ID)
        
        err := pollService.UpdatePoll(viewerCtx, poll.ID, &UpdatePollRequest{
            Title: "Viewer Updated Poll",
        })
        assert.Error(t, err)
        assert.Equal(t, ErrPermissionDenied, err)
    })
}
```

## Best Practices

### 1. Principle of Least Privilege
- Grant minimum necessary permissions
- Use role hierarchies effectively
- Regular permission audits

### 2. Resource-Based Authorization
- Check resource ownership
- Validate cross-resource access
- Implement resource-specific rules

### 3. Caching Strategy
- Cache permission checks
- Invalidate on role changes
- Use distributed caching for scalability

### 4. Error Handling
- Consistent error messages
- Audit failed attempts
- Rate limiting for security

### 5. Testing Coverage
- Unit tests for all permissions
- Integration tests for complex scenarios
- Performance tests for authorization overhead
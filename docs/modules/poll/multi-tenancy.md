# Poll Module - Multi-Tenancy

## Overview

The Poll module implements comprehensive multi-tenant architecture ensuring complete data isolation, security, and scalability across different tenants. All poll data is properly isolated while maintaining performance and consistency.

## Multi-Tenant Architecture

```mermaid
graph TB
    subgraph "Tenant A"
        TA_Polls[Polls]
        TA_Votes[Votes]
        TA_Analytics[Analytics]
    end
    
    subgraph "Tenant B"
        TB_Polls[Polls]
        TB_Votes[Votes]
        TB_Analytics[Analytics]
    end
    
    subgraph "Tenant C"
        TC_Polls[Polls]
        TC_Votes[Votes]
        TC_Analytics[Analytics]
    end
    
    subgraph "Shared Infrastructure"
        Database[(MySQL)]
        Cache[(Redis)]
        Queue[(Message Queue)]
    end
    
    subgraph "Application Layer"
        TenantMiddleware[Tenant Middleware]
        PollService[Poll Service]
        VoteService[Vote Service]
        AnalyticsService[Analytics Service]
    end
    
    TenantMiddleware --> PollService
    TenantMiddleware --> VoteService
    TenantMiddleware --> AnalyticsService
    
    PollService --> TA_Polls
    PollService --> TB_Polls
    PollService --> TC_Polls
    
    VoteService --> TA_Votes
    VoteService --> TB_Votes
    VoteService --> TC_Votes
    
    AnalyticsService --> TA_Analytics
    AnalyticsService --> TB_Analytics
    AnalyticsService --> TC_Analytics
    
    TA_Polls --> Database
    TB_Polls --> Database
    TC_Polls --> Database
    
    TA_Votes --> Database
    TB_Votes --> Database
    TC_Votes --> Database
    
    TA_Analytics --> Database
    TB_Analytics --> Database
    TC_Analytics --> Database
```

## Tenant Isolation Strategy

### 1. Database-Level Isolation

All poll-related tables include `tenant_id` as a foreign key with proper indexing:

```sql
-- All queries automatically include tenant_id
SELECT * FROM polls WHERE tenant_id = ? AND status = 'active';

-- Composite indexes for performance
CREATE INDEX idx_polls_tenant_status ON polls(tenant_id, status);
CREATE INDEX idx_poll_votes_tenant_poll ON poll_votes(tenant_id, poll_id);
CREATE INDEX idx_poll_analytics_tenant_date ON poll_analytics(tenant_id, date_recorded);
```

### 2. Service-Level Isolation

```go
type TenantContext struct {
    TenantID  uint   `json:"tenant_id"`
    UserID    uint   `json:"user_id"`
    WebsiteID uint   `json:"website_id"`
    Domain    string `json:"domain"`
}

type PollService struct {
    repository PollRepository
    cache      CacheService
    logger     *zap.Logger
}

func (s *PollService) CreatePoll(ctx context.Context, req *CreatePollRequest) (*Poll, error) {
    // Extract tenant context
    tenantCtx := GetTenantContext(ctx)
    if tenantCtx == nil {
        return nil, ErrTenantContextRequired
    }
    
    // Validate tenant permissions
    if err := s.validateTenantPermissions(ctx, tenantCtx.TenantID); err != nil {
        return nil, err
    }
    
    // Set tenant_id in request
    req.TenantID = tenantCtx.TenantID
    req.CreatedBy = tenantCtx.UserID
    
    // Create poll with tenant isolation
    poll, err := s.repository.CreatePoll(ctx, req)
    if err != nil {
        return nil, err
    }
    
    // Cache with tenant-specific key
    cacheKey := fmt.Sprintf("poll:%d:%d", tenantCtx.TenantID, poll.ID)
    s.cache.Set(ctx, cacheKey, poll, time.Hour)
    
    return poll, nil
}
```

### 3. Repository-Level Isolation

```go
type PollRepository interface {
    CreatePoll(ctx context.Context, req *CreatePollRequest) (*Poll, error)
    GetPollByID(ctx context.Context, tenantID, pollID uint) (*Poll, error)
    GetPollsByTenant(ctx context.Context, tenantID uint, filters *PollFilters) ([]*Poll, error)
    UpdatePoll(ctx context.Context, tenantID, pollID uint, updates *PollUpdates) error
    DeletePoll(ctx context.Context, tenantID, pollID uint) error
}

type pollRepository struct {
    db *sql.DB
}

func (r *pollRepository) GetPollByID(ctx context.Context, tenantID, pollID uint) (*Poll, error) {
    query := `
        SELECT id, tenant_id, website_id, created_by, title, description, 
               poll_type, status, created_at, updated_at
        FROM polls 
        WHERE tenant_id = ? AND id = ? AND status != 'deleted'
    `
    
    var poll Poll
    err := r.db.QueryRowContext(ctx, query, tenantID, pollID).Scan(
        &poll.ID, &poll.TenantID, &poll.WebsiteID, &poll.CreatedBy,
        &poll.Title, &poll.Description, &poll.PollType, &poll.Status,
        &poll.CreatedAt, &poll.UpdatedAt,
    )
    
    if err != nil {
        if err == sql.ErrNoRows {
            return nil, ErrPollNotFound
        }
        return nil, err
    }
    
    return &poll, nil
}
```

## Tenant Context Management

### 1. Middleware Implementation

```go
func TenantMiddleware(tenantService TenantService) gin.HandlerFunc {
    return func(c *gin.Context) {
        // Extract tenant from domain, header, or JWT
        tenantID, err := extractTenantID(c)
        if err != nil {
            c.JSON(401, gin.H{"error": "Invalid tenant context"})
            c.Abort()
            return
        }
        
        // Validate tenant exists and is active
        tenant, err := tenantService.GetTenantByID(c, tenantID)
        if err != nil {
            c.JSON(404, gin.H{"error": "Tenant not found"})
            c.Abort()
            return
        }
        
        if tenant.Status != "active" {
            c.JSON(403, gin.H{"error": "Tenant suspended"})
            c.Abort()
            return
        }
        
        // Create tenant context
        tenantCtx := &TenantContext{
            TenantID:  tenant.ID,
            UserID:    getUserID(c),
            WebsiteID: getWebsiteID(c),
            Domain:    tenant.Domain,
        }
        
        // Set context
        ctx := context.WithValue(c.Request.Context(), TenantContextKey, tenantCtx)
        c.Request = c.Request.WithContext(ctx)
        
        c.Next()
    }
}
```

### 2. Context Extraction

```go
const TenantContextKey = "tenant_context"

func GetTenantContext(ctx context.Context) *TenantContext {
    if ctx == nil {
        return nil
    }
    
    tenantCtx, ok := ctx.Value(TenantContextKey).(*TenantContext)
    if !ok {
        return nil
    }
    
    return tenantCtx
}

func extractTenantID(c *gin.Context) (uint, error) {
    // Method 1: From subdomain
    if subdomain := getSubdomain(c.Request.Host); subdomain != "" {
        return getTenantIDBySubdomain(subdomain)
    }
    
    // Method 2: From custom header
    if tenantHeader := c.GetHeader("X-Tenant-ID"); tenantHeader != "" {
        return strconv.ParseUint(tenantHeader, 10, 32)
    }
    
    // Method 3: From JWT token
    if claims := getJWTClaims(c); claims != nil {
        return claims.TenantID, nil
    }
    
    return 0, ErrTenantNotFound
}
```

## Cross-Tenant Security

### 1. Authorization Checks

```go
type TenantAuthorizer struct {
    rbacService RBACService
    cache       CacheService
}

func (a *TenantAuthorizer) CanAccessPoll(ctx context.Context, pollID uint) error {
    tenantCtx := GetTenantContext(ctx)
    if tenantCtx == nil {
        return ErrTenantContextRequired
    }
    
    // Check if poll belongs to tenant
    poll, err := a.pollService.GetPollByID(ctx, tenantCtx.TenantID, pollID)
    if err != nil {
        return err
    }
    
    // Additional RBAC checks
    if err := a.rbacService.CheckPermission(ctx, tenantCtx.UserID, "polls.read"); err != nil {
        return err
    }
    
    return nil
}

func (a *TenantAuthorizer) CanManagePoll(ctx context.Context, pollID uint) error {
    tenantCtx := GetTenantContext(ctx)
    if tenantCtx == nil {
        return ErrTenantContextRequired
    }
    
    poll, err := a.pollService.GetPollByID(ctx, tenantCtx.TenantID, pollID)
    if err != nil {
        return err
    }
    
    // Check if user is poll creator or has manage permissions
    if poll.CreatedBy != tenantCtx.UserID {
        if err := a.rbacService.CheckPermission(ctx, tenantCtx.UserID, "polls.manage"); err != nil {
            return err
        }
    }
    
    return nil
}
```

### 2. Data Validation

```go
func (s *PollService) validateTenantBoundaries(ctx context.Context, req *CreatePollRequest) error {
    tenantCtx := GetTenantContext(ctx)
    if tenantCtx == nil {
        return ErrTenantContextRequired
    }
    
    // Validate website belongs to tenant
    website, err := s.websiteService.GetWebsiteByID(ctx, req.WebsiteID)
    if err != nil {
        return err
    }
    
    if website.TenantID != tenantCtx.TenantID {
        return ErrCrossTenantAccess
    }
    
    // Validate user belongs to tenant
    membership, err := s.userService.GetTenantMembership(ctx, tenantCtx.UserID, tenantCtx.TenantID)
    if err != nil {
        return err
    }
    
    if membership.Status != "active" {
        return ErrInactiveMembership
    }
    
    return nil
}
```

## Performance Optimization

### 1. Tenant-Specific Caching

```go
type TenantCache struct {
    redis      *redis.Client
    keyPrefix  string
    ttl        time.Duration
}

func (c *TenantCache) Set(ctx context.Context, tenantID uint, key string, value interface{}) error {
    tenantKey := fmt.Sprintf("%s:tenant:%d:%s", c.keyPrefix, tenantID, key)
    
    data, err := json.Marshal(value)
    if err != nil {
        return err
    }
    
    return c.redis.Set(ctx, tenantKey, data, c.ttl).Err()
}

func (c *TenantCache) Get(ctx context.Context, tenantID uint, key string, dest interface{}) error {
    tenantKey := fmt.Sprintf("%s:tenant:%d:%s", c.keyPrefix, tenantID, key)
    
    data, err := c.redis.Get(ctx, tenantKey).Result()
    if err != nil {
        return err
    }
    
    return json.Unmarshal([]byte(data), dest)
}
```

### 2. Database Query Optimization

```go
// Optimized query with tenant-first indexing
func (r *pollRepository) GetPollsByTenant(ctx context.Context, tenantID uint, filters *PollFilters) ([]*Poll, error) {
    query := `
        SELECT id, tenant_id, website_id, created_by, title, description, 
               poll_type, status, total_votes, created_at, updated_at
        FROM polls 
        WHERE tenant_id = ? 
    `
    
    args := []interface{}{tenantID}
    
    if filters.Status != "" {
        query += " AND status = ?"
        args = append(args, filters.Status)
    }
    
    if filters.WebsiteID != 0 {
        query += " AND website_id = ?"
        args = append(args, filters.WebsiteID)
    }
    
    query += " ORDER BY created_at DESC LIMIT ? OFFSET ?"
    args = append(args, filters.Limit, filters.Offset)
    
    rows, err := r.db.QueryContext(ctx, query, args...)
    if err != nil {
        return nil, err
    }
    defer rows.Close()
    
    var polls []*Poll
    for rows.Next() {
        var poll Poll
        err := rows.Scan(
            &poll.ID, &poll.TenantID, &poll.WebsiteID, &poll.CreatedBy,
            &poll.Title, &poll.Description, &poll.PollType, &poll.Status,
            &poll.TotalVotes, &poll.CreatedAt, &poll.UpdatedAt,
        )
        if err != nil {
            return nil, err
        }
        polls = append(polls, &poll)
    }
    
    return polls, nil
}
```

## Resource Isolation

### 1. Database Connection Pooling

```go
type TenantConnectionPool struct {
    pools map[uint]*sql.DB
    mutex sync.RWMutex
    config *DatabaseConfig
}

func (p *TenantConnectionPool) GetConnection(tenantID uint) (*sql.DB, error) {
    p.mutex.RLock()
    db, exists := p.pools[tenantID]
    p.mutex.RUnlock()
    
    if exists {
        return db, nil
    }
    
    p.mutex.Lock()
    defer p.mutex.Unlock()
    
    // Double-check after acquiring write lock
    if db, exists := p.pools[tenantID]; exists {
        return db, nil
    }
    
    // Create new connection pool for tenant
    db, err := p.createTenantConnection(tenantID)
    if err != nil {
        return nil, err
    }
    
    p.pools[tenantID] = db
    return db, nil
}
```

### 2. Resource Limits

```go
type TenantResourceLimits struct {
    MaxPollsPerTenant    int
    MaxVotesPerPoll      int
    MaxOptionsPerPoll    int
    MaxAnalyticsHistory  time.Duration
    MaxConcurrentVotes   int
}

func (s *PollService) enforceResourceLimits(ctx context.Context, tenantID uint) error {
    limits, err := s.getTenantLimits(ctx, tenantID)
    if err != nil {
        return err
    }
    
    // Check poll count limit
    pollCount, err := s.repository.GetPollCount(ctx, tenantID)
    if err != nil {
        return err
    }
    
    if pollCount >= limits.MaxPollsPerTenant {
        return ErrTenantPollLimitExceeded
    }
    
    return nil
}
```

## Monitoring & Observability

### 1. Tenant-Specific Metrics

```go
type TenantMetrics struct {
    prometheus.Collector
    
    pollsCreated    *prometheus.CounterVec
    votesSubmitted  *prometheus.CounterVec
    apiRequests     *prometheus.CounterVec
    responseTimes   *prometheus.HistogramVec
    errorRates      *prometheus.CounterVec
}

func NewTenantMetrics() *TenantMetrics {
    return &TenantMetrics{
        pollsCreated: prometheus.NewCounterVec(
            prometheus.CounterOpts{
                Name: "polls_created_total",
                Help: "Total number of polls created by tenant",
            },
            []string{"tenant_id"},
        ),
        votesSubmitted: prometheus.NewCounterVec(
            prometheus.CounterOpts{
                Name: "votes_submitted_total",
                Help: "Total number of votes submitted by tenant",
            },
            []string{"tenant_id"},
        ),
        apiRequests: prometheus.NewCounterVec(
            prometheus.CounterOpts{
                Name: "api_requests_total",
                Help: "Total number of API requests by tenant",
            },
            []string{"tenant_id", "endpoint", "method"},
        ),
    }
}

func (m *TenantMetrics) RecordPollCreated(tenantID uint) {
    m.pollsCreated.WithLabelValues(fmt.Sprintf("%d", tenantID)).Inc()
}
```

### 2. Tenant Health Monitoring

```go
type TenantHealthChecker struct {
    pollService   PollService
    voteService   VoteService
    cacheService  CacheService
}

func (h *TenantHealthChecker) CheckTenantHealth(ctx context.Context, tenantID uint) (*TenantHealthReport, error) {
    report := &TenantHealthReport{
        TenantID:  tenantID,
        CheckTime: time.Now(),
        Status:    "healthy",
        Issues:    []string{},
    }
    
    // Check database connectivity
    if err := h.checkDatabaseHealth(ctx, tenantID); err != nil {
        report.Status = "unhealthy"
        report.Issues = append(report.Issues, fmt.Sprintf("Database: %v", err))
    }
    
    // Check cache connectivity
    if err := h.checkCacheHealth(ctx, tenantID); err != nil {
        report.Status = "degraded"
        report.Issues = append(report.Issues, fmt.Sprintf("Cache: %v", err))
    }
    
    // Check resource utilization
    if err := h.checkResourceUtilization(ctx, tenantID); err != nil {
        report.Status = "warning"
        report.Issues = append(report.Issues, fmt.Sprintf("Resources: %v", err))
    }
    
    return report, nil
}
```

## Data Migration & Backup

### 1. Tenant Data Export

```go
func (s *PollService) ExportTenantData(ctx context.Context, tenantID uint) (*TenantDataExport, error) {
    export := &TenantDataExport{
        TenantID:    tenantID,
        ExportTime:  time.Now(),
        Version:     "1.0",
    }
    
    // Export polls
    polls, err := s.repository.GetAllPollsByTenant(ctx, tenantID)
    if err != nil {
        return nil, err
    }
    export.Polls = polls
    
    // Export votes
    votes, err := s.voteService.GetAllVotesByTenant(ctx, tenantID)
    if err != nil {
        return nil, err
    }
    export.Votes = votes
    
    // Export analytics
    analytics, err := s.analyticsService.GetAllAnalyticsByTenant(ctx, tenantID)
    if err != nil {
        return nil, err
    }
    export.Analytics = analytics
    
    return export, nil
}
```

### 2. Tenant Data Cleanup

```go
func (s *PollService) CleanupTenantData(ctx context.Context, tenantID uint) error {
    // Start transaction
    tx, err := s.db.BeginTx(ctx, nil)
    if err != nil {
        return err
    }
    defer tx.Rollback()
    
    // Clean up in reverse dependency order
    tables := []string{
        "poll_sessions",
        "poll_analytics", 
        "poll_votes",
        "poll_options",
        "polls",
    }
    
    for _, table := range tables {
        query := fmt.Sprintf("DELETE FROM %s WHERE tenant_id = ?", table)
        if _, err := tx.ExecContext(ctx, query, tenantID); err != nil {
            return err
        }
    }
    
    // Clean up cache
    cachePattern := fmt.Sprintf("poll:tenant:%d:*", tenantID)
    if err := s.cache.DeletePattern(ctx, cachePattern); err != nil {
        log.Warn("Failed to cleanup tenant cache", zap.Error(err))
    }
    
    return tx.Commit()
}
```

## Error Handling

### 1. Tenant-Specific Errors

```go
var (
    ErrTenantContextRequired     = errors.New("tenant context is required")
    ErrTenantNotFound           = errors.New("tenant not found")
    ErrCrossTenantAccess        = errors.New("cross-tenant access denied")
    ErrTenantPollLimitExceeded  = errors.New("tenant poll limit exceeded")
    ErrTenantSuspended          = errors.New("tenant account suspended")
    ErrInvalidTenantDomain      = errors.New("invalid tenant domain")
    ErrInactiveMembership       = errors.New("inactive tenant membership")
)

type TenantError struct {
    TenantID uint
    Message  string
    Cause    error
}

func (e *TenantError) Error() string {
    return fmt.Sprintf("tenant %d: %s", e.TenantID, e.Message)
}
```

### 2. Error Response Format

```go
type TenantErrorResponse struct {
    Status string      `json:"status"`
    Error  ErrorDetail `json:"error"`
}

type ErrorDetail struct {
    Code     string `json:"code"`
    Message  string `json:"message"`
    TenantID uint   `json:"tenant_id,omitempty"`
}

func HandleTenantError(c *gin.Context, err error) {
    var tenantErr *TenantError
    if errors.As(err, &tenantErr) {
        c.JSON(403, TenantErrorResponse{
            Status: "error",
            Error: ErrorDetail{
                Code:     "TENANT_ACCESS_DENIED",
                Message:  tenantErr.Message,
                TenantID: tenantErr.TenantID,
            },
        })
        return
    }
    
    // Handle other errors...
}
```

## Testing Multi-Tenancy

### 1. Tenant Isolation Tests

```go
func TestTenantIsolation(t *testing.T) {
    // Setup test tenants
    tenant1 := createTestTenant(t, "tenant1")
    tenant2 := createTestTenant(t, "tenant2")
    
    // Create polls for each tenant
    poll1 := createTestPoll(t, tenant1.ID, "Poll 1")
    poll2 := createTestPoll(t, tenant2.ID, "Poll 2")
    
    // Test tenant 1 cannot access tenant 2's polls
    ctx1 := createTenantContext(tenant1.ID)
    _, err := pollService.GetPollByID(ctx1, poll2.ID)
    assert.Error(t, err)
    assert.Equal(t, ErrPollNotFound, err)
    
    // Test tenant 2 cannot access tenant 1's polls
    ctx2 := createTenantContext(tenant2.ID)
    _, err = pollService.GetPollByID(ctx2, poll1.ID)
    assert.Error(t, err)
    assert.Equal(t, ErrPollNotFound, err)
    
    // Test tenants can access their own polls
    _, err = pollService.GetPollByID(ctx1, poll1.ID)
    assert.NoError(t, err)
    
    _, err = pollService.GetPollByID(ctx2, poll2.ID)
    assert.NoError(t, err)
}
```

### 2. Cross-Tenant Security Tests

```go
func TestCrossTenantSecurity(t *testing.T) {
    tenant1 := createTestTenant(t, "tenant1")
    tenant2 := createTestTenant(t, "tenant2")
    
    user1 := createTestUser(t, tenant1.ID)
    user2 := createTestUser(t, tenant2.ID)
    
    poll1 := createTestPoll(t, tenant1.ID, "Poll 1")
    
    // Test user from tenant2 cannot vote on tenant1's poll
    ctx := createUserContext(user2.ID, tenant2.ID)
    _, err := voteService.SubmitVote(ctx, &SubmitVoteRequest{
        PollID:   poll1.ID,
        OptionID: 1,
    })
    assert.Error(t, err)
    assert.Equal(t, ErrCrossTenantAccess, err)
}
```
# Poll Module - Testing

## Overview

The Poll module implements comprehensive testing strategies including unit tests, integration tests, end-to-end tests, and performance tests. This ensures reliability, security, and performance of the polling system.

## Testing Architecture

```mermaid
graph TB
    subgraph "Test Types"
        UnitTests[Unit Tests]
        IntegrationTests[Integration Tests]
        E2ETests[End-to-End Tests]
        PerformanceTests[Performance Tests]
        SecurityTests[Security Tests]
    end
    
    subgraph "Test Layers"
        HandlerTests[Handler Tests]
        ServiceTests[Service Tests]
        RepositoryTests[Repository Tests]
        ModelTests[Model Tests]
    end
    
    subgraph "Test Infrastructure"
        TestDatabase[Test Database]
        MockServices[Mock Services]
        TestFixtures[Test Fixtures]
        TestHelpers[Test Helpers]
    end
    
    subgraph "Test Tools"
        Ginkgo[Ginkgo BDD]
        Gomega[Gomega Matchers]
        Testify[Testify Framework]
        MockGen[Mock Generator]
    end
    
    UnitTests --> HandlerTests
    UnitTests --> ServiceTests
    UnitTests --> RepositoryTests
    UnitTests --> ModelTests
    
    IntegrationTests --> TestDatabase
    IntegrationTests --> MockServices
    
    E2ETests --> TestFixtures
    E2ETests --> TestHelpers
    
    HandlerTests --> Ginkgo
    ServiceTests --> Gomega
    RepositoryTests --> Testify
    ModelTests --> MockGen
```

## Unit Testing

### 1. Service Layer Tests

```go
package services_test

import (
    "context"
    "testing"
    "time"

    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
    "github.com/stretchr/testify/require"

    "github.com/tranthanhloi/wn-api-v3/internal/modules/poll/models"
    "github.com/tranthanhloi/wn-api-v3/internal/modules/poll/services"
    "github.com/tranthanhloi/wn-api-v3/internal/tests/mocks"
)

type PollServiceTestSuite struct {
    service          *services.PollService
    mockRepo         *mocks.MockPollRepository
    mockRBACService  *mocks.MockRBACService
    mockCache        *mocks.MockCacheService
    ctx              context.Context
}

func setupPollServiceTest(t *testing.T) *PollServiceTestSuite {
    mockRepo := &mocks.MockPollRepository{}
    mockRBACService := &mocks.MockRBACService{}
    mockCache := &mocks.MockCacheService{}
    
    service := services.NewPollService(mockRepo, mockRBACService, mockCache)
    
    ctx := context.WithValue(context.Background(), "user_context", &models.UserContext{
        UserID:   1,
        TenantID: 1,
    })
    
    return &PollServiceTestSuite{
        service:         service,
        mockRepo:        mockRepo,
        mockRBACService: mockRBACService,
        mockCache:       mockCache,
        ctx:             ctx,
    }
}

func TestPollService_CreatePoll(t *testing.T) {
    suite := setupPollServiceTest(t)
    
    t.Run("successful poll creation", func(t *testing.T) {
        // Setup mocks
        suite.mockRBACService.On("CheckPermission", mock.Anything, uint(1), "polls.create").Return(nil)
        
        expectedPoll := &models.Poll{
            ID:          1,
            TenantID:    1,
            Title:       "Test Poll",
            Description: "Test Description",
            PollType:    "single_choice",
            Status:      "draft",
            CreatedBy:   1,
            CreatedAt:   time.Now(),
        }
        
        suite.mockRepo.On("CreatePoll", mock.Anything, mock.AnythingOfType("*models.CreatePollRequest")).Return(expectedPoll, nil)
        suite.mockCache.On("Set", mock.Anything, mock.AnythingOfType("string"), mock.Anything, mock.AnythingOfType("time.Duration")).Return(nil)
        
        // Execute
        req := &models.CreatePollRequest{
            Title:       "Test Poll",
            Description: "Test Description",
            PollType:    "single_choice",
            Options: []models.PollOption{
                {OptionText: "Option 1", OptionOrder: 1},
                {OptionText: "Option 2", OptionOrder: 2},
            },
        }
        
        result, err := suite.service.CreatePoll(suite.ctx, req)
        
        // Assertions
        require.NoError(t, err)
        assert.NotNil(t, result)
        assert.Equal(t, "Test Poll", result.Title)
        assert.Equal(t, uint(1), result.TenantID)
        assert.Equal(t, "draft", result.Status)
        
        // Verify mocks
        suite.mockRBACService.AssertExpectations(t)
        suite.mockRepo.AssertExpectations(t)
        suite.mockCache.AssertExpectations(t)
    })
    
    t.Run("permission denied", func(t *testing.T) {
        // Setup mocks
        suite.mockRBACService.On("CheckPermission", mock.Anything, uint(1), "polls.create").Return(models.ErrPermissionDenied)
        
        // Execute
        req := &models.CreatePollRequest{
            Title:    "Test Poll",
            PollType: "single_choice",
        }
        
        result, err := suite.service.CreatePoll(suite.ctx, req)
        
        // Assertions
        assert.Error(t, err)
        assert.Nil(t, result)
        assert.Equal(t, models.ErrPermissionDenied, err)
        
        // Verify mocks
        suite.mockRBACService.AssertExpectations(t)
    })
    
    t.Run("invalid poll type", func(t *testing.T) {
        // Setup mocks
        suite.mockRBACService.On("CheckPermission", mock.Anything, uint(1), "polls.create").Return(nil)
        
        // Execute
        req := &models.CreatePollRequest{
            Title:    "Test Poll",
            PollType: "invalid_type",
        }
        
        result, err := suite.service.CreatePoll(suite.ctx, req)
        
        // Assertions
        assert.Error(t, err)
        assert.Nil(t, result)
        assert.Contains(t, err.Error(), "invalid poll type")
    })
}

func TestPollService_SubmitVote(t *testing.T) {
    suite := setupPollServiceTest(t)
    
    t.Run("successful vote submission", func(t *testing.T) {
        // Setup test data
        poll := &models.Poll{
            ID:       1,
            TenantID: 1,
            Status:   "active",
            PollType: "single_choice",
        }
        
        // Setup mocks
        suite.mockRepo.On("GetPollByID", mock.Anything, uint(1)).Return(poll, nil)
        suite.mockRepo.On("HasUserVoted", mock.Anything, uint(1), uint(1)).Return(false, nil)
        
        expectedVote := &models.Vote{
            ID:       1,
            PollID:   1,
            UserID:   1,
            OptionID: 1,
            VotedAt:  time.Now(),
        }
        
        suite.mockRepo.On("CreateVote", mock.Anything, mock.AnythingOfType("*models.SubmitVoteRequest")).Return(expectedVote, nil)
        suite.mockRepo.On("UpdateVoteCounts", mock.Anything, uint(1)).Return(nil)
        
        // Execute
        req := &models.SubmitVoteRequest{
            PollID:   1,
            OptionID: 1,
            UserID:   1,
        }
        
        result, err := suite.service.SubmitVote(suite.ctx, req)
        
        // Assertions
        require.NoError(t, err)
        assert.NotNil(t, result)
        assert.Equal(t, uint(1), result.PollID)
        assert.Equal(t, uint(1), result.OptionID)
        
        // Verify mocks
        suite.mockRepo.AssertExpectations(t)
    })
    
    t.Run("poll not found", func(t *testing.T) {
        // Setup mocks
        suite.mockRepo.On("GetPollByID", mock.Anything, uint(1)).Return(nil, models.ErrPollNotFound)
        
        // Execute
        req := &models.SubmitVoteRequest{
            PollID:   1,
            OptionID: 1,
            UserID:   1,
        }
        
        result, err := suite.service.SubmitVote(suite.ctx, req)
        
        // Assertions
        assert.Error(t, err)
        assert.Nil(t, result)
        assert.Equal(t, models.ErrPollNotFound, err)
    })
    
    t.Run("poll not active", func(t *testing.T) {
        // Setup test data
        poll := &models.Poll{
            ID:       1,
            TenantID: 1,
            Status:   "draft",
            PollType: "single_choice",
        }
        
        // Setup mocks
        suite.mockRepo.On("GetPollByID", mock.Anything, uint(1)).Return(poll, nil)
        
        // Execute
        req := &models.SubmitVoteRequest{
            PollID:   1,
            OptionID: 1,
            UserID:   1,
        }
        
        result, err := suite.service.SubmitVote(suite.ctx, req)
        
        // Assertions
        assert.Error(t, err)
        assert.Nil(t, result)
        assert.Equal(t, models.ErrPollNotActive, err)
    })
    
    t.Run("user already voted", func(t *testing.T) {
        // Setup test data
        poll := &models.Poll{
            ID:       1,
            TenantID: 1,
            Status:   "active",
            PollType: "single_choice",
        }
        
        // Setup mocks
        suite.mockRepo.On("GetPollByID", mock.Anything, uint(1)).Return(poll, nil)
        suite.mockRepo.On("HasUserVoted", mock.Anything, uint(1), uint(1)).Return(true, nil)
        
        // Execute
        req := &models.SubmitVoteRequest{
            PollID:   1,
            OptionID: 1,
            UserID:   1,
        }
        
        result, err := suite.service.SubmitVote(suite.ctx, req)
        
        // Assertions
        assert.Error(t, err)
        assert.Nil(t, result)
        assert.Equal(t, models.ErrAlreadyVoted, err)
    })
}
```

### 2. Repository Layer Tests

```go
package repositories_test

import (
    "context"
    "testing"
    "time"

    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/require"

    "github.com/tranthanhloi/wn-api-v3/internal/modules/poll/models"
    "github.com/tranthanhloi/wn-api-v3/internal/modules/poll/repositories"
    "github.com/tranthanhloi/wn-api-v3/internal/tests/helpers"
)

func TestPollRepository_CreatePoll(t *testing.T) {
    db := helpers.SetupTestDB(t)
    defer helpers.CleanupTestDB(t, db)
    
    repo := repositories.NewPollRepository(db)
    ctx := context.Background()
    
    t.Run("successful poll creation", func(t *testing.T) {
        // Setup test data
        req := &models.CreatePollRequest{
            TenantID:    1,
            WebsiteID:   1,
            CreatedBy:   1,
            Title:       "Test Poll",
            Description: "Test Description",
            PollType:    "single_choice",
            Options: []models.PollOption{
                {OptionText: "Option 1", OptionOrder: 1},
                {OptionText: "Option 2", OptionOrder: 2},
            },
        }
        
        // Execute
        result, err := repo.CreatePoll(ctx, req)
        
        // Assertions
        require.NoError(t, err)
        assert.NotNil(t, result)
        assert.NotZero(t, result.ID)
        assert.Equal(t, "Test Poll", result.Title)
        assert.Equal(t, uint(1), result.TenantID)
        assert.Equal(t, "draft", result.Status)
        assert.NotZero(t, result.CreatedAt)
        
        // Verify poll was saved to database
        var count int
        err = db.QueryRow("SELECT COUNT(*) FROM polls WHERE id = ?", result.ID).Scan(&count)
        require.NoError(t, err)
        assert.Equal(t, 1, count)
        
        // Verify options were saved
        var optionCount int
        err = db.QueryRow("SELECT COUNT(*) FROM poll_options WHERE poll_id = ?", result.ID).Scan(&optionCount)
        require.NoError(t, err)
        assert.Equal(t, 2, optionCount)
    })
    
    t.Run("duplicate slug within tenant", func(t *testing.T) {
        // Create first poll
        req1 := &models.CreatePollRequest{
            TenantID:  1,
            WebsiteID: 1,
            CreatedBy: 1,
            Title:     "Test Poll",
            Slug:      "test-poll",
            PollType:  "single_choice",
            Options: []models.PollOption{
                {OptionText: "Option 1", OptionOrder: 1},
            },
        }
        
        _, err := repo.CreatePoll(ctx, req1)
        require.NoError(t, err)
        
        // Try to create second poll with same slug in same tenant
        req2 := &models.CreatePollRequest{
            TenantID:  1,
            WebsiteID: 1,
            CreatedBy: 1,
            Title:     "Another Poll",
            Slug:      "test-poll",
            PollType:  "single_choice",
            Options: []models.PollOption{
                {OptionText: "Option 1", OptionOrder: 1},
            },
        }
        
        _, err = repo.CreatePoll(ctx, req2)
        
        // Should fail due to unique constraint
        assert.Error(t, err)
        assert.Contains(t, err.Error(), "duplicate")
    })
    
    t.Run("same slug different tenant", func(t *testing.T) {
        // Create poll in tenant 1
        req1 := &models.CreatePollRequest{
            TenantID:  1,
            WebsiteID: 1,
            CreatedBy: 1,
            Title:     "Test Poll",
            Slug:      "shared-slug",
            PollType:  "single_choice",
            Options: []models.PollOption{
                {OptionText: "Option 1", OptionOrder: 1},
            },
        }
        
        _, err := repo.CreatePoll(ctx, req1)
        require.NoError(t, err)
        
        // Create poll with same slug in tenant 2
        req2 := &models.CreatePollRequest{
            TenantID:  2,
            WebsiteID: 2,
            CreatedBy: 2,
            Title:     "Another Poll",
            Slug:      "shared-slug",
            PollType:  "single_choice",
            Options: []models.PollOption{
                {OptionText: "Option 1", OptionOrder: 1},
            },
        }
        
        result, err := repo.CreatePoll(ctx, req2)
        
        // Should succeed due to tenant isolation
        require.NoError(t, err)
        assert.NotNil(t, result)
        assert.Equal(t, uint(2), result.TenantID)
    })
}

func TestPollRepository_GetPollByID(t *testing.T) {
    db := helpers.SetupTestDB(t)
    defer helpers.CleanupTestDB(t, db)
    
    repo := repositories.NewPollRepository(db)
    ctx := context.Background()
    
    // Create test poll
    testPoll := helpers.CreateTestPoll(t, db, &models.Poll{
        TenantID:    1,
        WebsiteID:   1,
        CreatedBy:   1,
        Title:       "Test Poll",
        Description: "Test Description",
        PollType:    "single_choice",
        Status:      "active",
    })
    
    t.Run("successful poll retrieval", func(t *testing.T) {
        result, err := repo.GetPollByID(ctx, testPoll.TenantID, testPoll.ID)
        
        require.NoError(t, err)
        assert.NotNil(t, result)
        assert.Equal(t, testPoll.ID, result.ID)
        assert.Equal(t, testPoll.Title, result.Title)
        assert.Equal(t, testPoll.TenantID, result.TenantID)
    })
    
    t.Run("poll not found", func(t *testing.T) {
        result, err := repo.GetPollByID(ctx, testPoll.TenantID, 999)
        
        assert.Error(t, err)
        assert.Nil(t, result)
        assert.Equal(t, models.ErrPollNotFound, err)
    })
    
    t.Run("cross-tenant access denied", func(t *testing.T) {
        result, err := repo.GetPollByID(ctx, 2, testPoll.ID)
        
        assert.Error(t, err)
        assert.Nil(t, result)
        assert.Equal(t, models.ErrPollNotFound, err)
    })
    
    t.Run("deleted poll not returned", func(t *testing.T) {
        // Create deleted poll
        deletedPoll := helpers.CreateTestPoll(t, db, &models.Poll{
            TenantID:  1,
            WebsiteID: 1,
            CreatedBy: 1,
            Title:     "Deleted Poll",
            Status:    "deleted",
        })
        
        result, err := repo.GetPollByID(ctx, deletedPoll.TenantID, deletedPoll.ID)
        
        assert.Error(t, err)
        assert.Nil(t, result)
        assert.Equal(t, models.ErrPollNotFound, err)
    })
}
```

### 3. Handler Layer Tests

```go
package handlers_test

import (
    "bytes"
    "encoding/json"
    "net/http"
    "net/http/httptest"
    "testing"

    "github.com/gin-gonic/gin"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
    "github.com/stretchr/testify/require"

    "github.com/tranthanhloi/wn-api-v3/internal/modules/poll/handlers"
    "github.com/tranthanhloi/wn-api-v3/internal/modules/poll/models"
    "github.com/tranthanhloi/wn-api-v3/internal/tests/mocks"
)

func setupHandlerTest(t *testing.T) (*gin.Engine, *mocks.MockPollService, *mocks.MockResourceAuthorizer) {
    gin.SetMode(gin.TestMode)
    
    mockService := &mocks.MockPollService{}
    mockAuthorizer := &mocks.MockResourceAuthorizer{}
    
    handler := handlers.NewPollHandler(mockService, mockAuthorizer)
    
    router := gin.New()
    handler.RegisterRoutes(router.Group("/api/v1"))
    
    return router, mockService, mockAuthorizer
}

func TestPollHandler_CreatePoll(t *testing.T) {
    router, mockService, _ := setupHandlerTest(t)
    
    t.Run("successful poll creation", func(t *testing.T) {
        // Setup request
        reqBody := map[string]interface{}{
            "title":     "Test Poll",
            "poll_type": "single_choice",
            "options": []map[string]interface{}{
                {"option_text": "Option 1", "option_order": 1},
                {"option_text": "Option 2", "option_order": 2},
            },
        }
        
        jsonBody, _ := json.Marshal(reqBody)
        req := httptest.NewRequest(http.MethodPost, "/api/v1/polls", bytes.NewBuffer(jsonBody))
        req.Header.Set("Content-Type", "application/json")
        req.Header.Set("Authorization", "Bearer valid-token")
        
        // Setup mock
        expectedPoll := &models.Poll{
            ID:       1,
            Title:    "Test Poll",
            PollType: "single_choice",
            Status:   "draft",
        }
        
        mockService.On("CreatePoll", mock.Anything, mock.AnythingOfType("*models.CreatePollRequest")).Return(expectedPoll, nil)
        
        // Execute
        w := httptest.NewRecorder()
        router.ServeHTTP(w, req)
        
        // Assertions
        assert.Equal(t, http.StatusCreated, w.Code)
        
        var response map[string]interface{}
        err := json.Unmarshal(w.Body.Bytes(), &response)
        require.NoError(t, err)
        
        assert.Equal(t, "success", response["status"])
        assert.NotNil(t, response["data"])
        
        data := response["data"].(map[string]interface{})
        assert.Equal(t, "Test Poll", data["title"])
        assert.Equal(t, "single_choice", data["poll_type"])
        assert.Equal(t, "draft", data["status"])
        
        mockService.AssertExpectations(t)
    })
    
    t.Run("invalid request body", func(t *testing.T) {
        // Setup request with invalid JSON
        req := httptest.NewRequest(http.MethodPost, "/api/v1/polls", bytes.NewBuffer([]byte("invalid json")))
        req.Header.Set("Content-Type", "application/json")
        req.Header.Set("Authorization", "Bearer valid-token")
        
        // Execute
        w := httptest.NewRecorder()
        router.ServeHTTP(w, req)
        
        // Assertions
        assert.Equal(t, http.StatusBadRequest, w.Code)
        
        var response map[string]interface{}
        err := json.Unmarshal(w.Body.Bytes(), &response)
        require.NoError(t, err)
        
        assert.Equal(t, "error", response["status"])
        assert.Contains(t, response["error"], "Invalid request format")
    })
    
    t.Run("validation error", func(t *testing.T) {
        // Setup request with missing required fields
        reqBody := map[string]interface{}{
            "poll_type": "single_choice",
            // Missing title and options
        }
        
        jsonBody, _ := json.Marshal(reqBody)
        req := httptest.NewRequest(http.MethodPost, "/api/v1/polls", bytes.NewBuffer(jsonBody))
        req.Header.Set("Content-Type", "application/json")
        req.Header.Set("Authorization", "Bearer valid-token")
        
        // Execute
        w := httptest.NewRecorder()
        router.ServeHTTP(w, req)
        
        // Assertions
        assert.Equal(t, http.StatusBadRequest, w.Code)
        
        var response map[string]interface{}
        err := json.Unmarshal(w.Body.Bytes(), &response)
        require.NoError(t, err)
        
        assert.Equal(t, "error", response["status"])
        assert.Contains(t, response["error"], "validation")
    })
    
    t.Run("permission denied", func(t *testing.T) {
        // Setup request
        reqBody := map[string]interface{}{
            "title":     "Test Poll",
            "poll_type": "single_choice",
            "options": []map[string]interface{}{
                {"option_text": "Option 1", "option_order": 1},
            },
        }
        
        jsonBody, _ := json.Marshal(reqBody)
        req := httptest.NewRequest(http.MethodPost, "/api/v1/polls", bytes.NewBuffer(jsonBody))
        req.Header.Set("Content-Type", "application/json")
        req.Header.Set("Authorization", "Bearer valid-token")
        
        // Setup mock
        mockService.On("CreatePoll", mock.Anything, mock.AnythingOfType("*models.CreatePollRequest")).Return(nil, models.ErrPermissionDenied)
        
        // Execute
        w := httptest.NewRecorder()
        router.ServeHTTP(w, req)
        
        // Assertions
        assert.Equal(t, http.StatusForbidden, w.Code)
        
        var response map[string]interface{}
        err := json.Unmarshal(w.Body.Bytes(), &response)
        require.NoError(t, err)
        
        assert.Equal(t, "error", response["status"])
        assert.Contains(t, response["error"], "permission")
        
        mockService.AssertExpectations(t)
    })
}

func TestPollHandler_SubmitVote(t *testing.T) {
    router, mockService, mockAuthorizer := setupHandlerTest(t)
    
    t.Run("successful vote submission", func(t *testing.T) {
        // Setup request
        reqBody := map[string]interface{}{
            "option_id": 1,
        }
        
        jsonBody, _ := json.Marshal(reqBody)
        req := httptest.NewRequest(http.MethodPost, "/api/v1/polls/1/vote", bytes.NewBuffer(jsonBody))
        req.Header.Set("Content-Type", "application/json")
        req.Header.Set("Authorization", "Bearer valid-token")
        
        // Setup mocks
        mockAuthorizer.On("CanAccessPoll", mock.Anything, uint(1), "vote").Return(nil)
        
        expectedVote := &models.Vote{
            ID:       1,
            PollID:   1,
            OptionID: 1,
            UserID:   1,
        }
        
        mockService.On("SubmitVote", mock.Anything, mock.AnythingOfType("*models.SubmitVoteRequest")).Return(expectedVote, nil)
        
        // Execute
        w := httptest.NewRecorder()
        router.ServeHTTP(w, req)
        
        // Assertions
        assert.Equal(t, http.StatusOK, w.Code)
        
        var response map[string]interface{}
        err := json.Unmarshal(w.Body.Bytes(), &response)
        require.NoError(t, err)
        
        assert.Equal(t, "success", response["status"])
        assert.NotNil(t, response["data"])
        
        mockAuthorizer.AssertExpectations(t)
        mockService.AssertExpectations(t)
    })
    
    t.Run("access denied", func(t *testing.T) {
        // Setup request
        reqBody := map[string]interface{}{
            "option_id": 1,
        }
        
        jsonBody, _ := json.Marshal(reqBody)
        req := httptest.NewRequest(http.MethodPost, "/api/v1/polls/1/vote", bytes.NewBuffer(jsonBody))
        req.Header.Set("Content-Type", "application/json")
        req.Header.Set("Authorization", "Bearer valid-token")
        
        // Setup mock
        mockAuthorizer.On("CanAccessPoll", mock.Anything, uint(1), "vote").Return(models.ErrPermissionDenied)
        
        // Execute
        w := httptest.NewRecorder()
        router.ServeHTTP(w, req)
        
        // Assertions
        assert.Equal(t, http.StatusForbidden, w.Code)
        
        mockAuthorizer.AssertExpectations(t)
    })
}
```

## Integration Testing

### 1. Database Integration Tests

```go
package integration_test

import (
    "context"
    "testing"
    
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/require"
    
    "github.com/tranthanhloi/wn-api-v3/internal/modules/poll/models"
    "github.com/tranthanhloi/wn-api-v3/internal/modules/poll/repositories"
    "github.com/tranthanhloi/wn-api-v3/internal/modules/poll/services"
    "github.com/tranthanhloi/wn-api-v3/internal/tests/helpers"
)

func TestPollIntegration(t *testing.T) {
    // Setup test database
    db := helpers.SetupTestDB(t)
    defer helpers.CleanupTestDB(t, db)
    
    // Setup repositories
    pollRepo := repositories.NewPollRepository(db)
    voteRepo := repositories.NewVoteRepository(db)
    
    // Setup services
    pollService := services.NewPollService(pollRepo, nil, nil)
    voteService := services.NewVoteService(voteRepo, pollRepo, nil)
    
    ctx := context.Background()
    
    t.Run("complete poll lifecycle", func(t *testing.T) {
        // Create tenant and user
        tenant := helpers.CreateTestTenant(t, db)
        user := helpers.CreateTestUser(t, db, tenant.ID)
        
        ctx := context.WithValue(ctx, "user_context", &models.UserContext{
            UserID:   user.ID,
            TenantID: tenant.ID,
        })
        
        // Create poll
        pollReq := &models.CreatePollRequest{
            Title:     "Integration Test Poll",
            PollType:  "single_choice",
            Options: []models.PollOption{
                {OptionText: "Option 1", OptionOrder: 1},
                {OptionText: "Option 2", OptionOrder: 2},
            },
        }
        
        poll, err := pollService.CreatePoll(ctx, pollReq)
        require.NoError(t, err)
        assert.NotNil(t, poll)
        
        // Activate poll
        err = pollService.UpdatePollStatus(ctx, poll.ID, "active")
        require.NoError(t, err)
        
        // Submit vote
        voteReq := &models.SubmitVoteRequest{
            PollID:   poll.ID,
            OptionID: 1, // Assuming we know the option ID
            UserID:   user.ID,
        }
        
        vote, err := voteService.SubmitVote(ctx, voteReq)
        require.NoError(t, err)
        assert.NotNil(t, vote)
        
        // Verify vote was recorded
        hasVoted, err := voteRepo.HasUserVoted(ctx, poll.ID, user.ID)
        require.NoError(t, err)
        assert.True(t, hasVoted)
        
        // Get updated poll with vote counts
        updatedPoll, err := pollService.GetPollByID(ctx, poll.ID)
        require.NoError(t, err)
        assert.Equal(t, 1, updatedPoll.TotalVotes)
        
        // End poll
        err = pollService.UpdatePollStatus(ctx, poll.ID, "ended")
        require.NoError(t, err)
        
        // Verify final state
        finalPoll, err := pollService.GetPollByID(ctx, poll.ID)
        require.NoError(t, err)
        assert.Equal(t, "ended", finalPoll.Status)
    })
    
    t.Run("multi-tenant isolation", func(t *testing.T) {
        // Create two tenants
        tenant1 := helpers.CreateTestTenant(t, db)
        tenant2 := helpers.CreateTestTenant(t, db)
        
        user1 := helpers.CreateTestUser(t, db, tenant1.ID)
        user2 := helpers.CreateTestUser(t, db, tenant2.ID)
        
        ctx1 := context.WithValue(ctx, "user_context", &models.UserContext{
            UserID:   user1.ID,
            TenantID: tenant1.ID,
        })
        
        ctx2 := context.WithValue(ctx, "user_context", &models.UserContext{
            UserID:   user2.ID,
            TenantID: tenant2.ID,
        })
        
        // Create poll for tenant 1
        pollReq := &models.CreatePollRequest{
            Title:    "Tenant 1 Poll",
            PollType: "single_choice",
            Options: []models.PollOption{
                {OptionText: "Option 1", OptionOrder: 1},
            },
        }
        
        poll1, err := pollService.CreatePoll(ctx1, pollReq)
        require.NoError(t, err)
        
        // Try to access poll from tenant 2
        _, err = pollService.GetPollByID(ctx2, poll1.ID)
        assert.Error(t, err)
        assert.Equal(t, models.ErrPollNotFound, err)
        
        // Create poll for tenant 2
        poll2, err := pollService.CreatePoll(ctx2, pollReq)
        require.NoError(t, err)
        
        // Verify tenant 1 cannot access tenant 2's poll
        _, err = pollService.GetPollByID(ctx1, poll2.ID)
        assert.Error(t, err)
        assert.Equal(t, models.ErrPollNotFound, err)
    })
}
```

### 2. API Integration Tests

```go
package integration_test

import (
    "bytes"
    "encoding/json"
    "net/http"
    "net/http/httptest"
    "testing"
    
    "github.com/gin-gonic/gin"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/require"
    
    "github.com/tranthanhloi/wn-api-v3/internal/api"
    "github.com/tranthanhloi/wn-api-v3/internal/tests/helpers"
)

func TestPollAPIIntegration(t *testing.T) {
    // Setup test server
    server := helpers.SetupTestServer(t)
    defer server.Close()
    
    // Create test data
    tenant := helpers.CreateTestTenant(t, server.DB)
    user := helpers.CreateTestUser(t, server.DB, tenant.ID)
    token := helpers.GenerateTestJWT(t, user.ID, tenant.ID)
    
    t.Run("poll CRUD operations", func(t *testing.T) {
        // Create poll
        createReq := map[string]interface{}{
            "title":     "API Integration Test Poll",
            "poll_type": "single_choice",
            "options": []map[string]interface{}{
                {"option_text": "Option 1", "option_order": 1},
                {"option_text": "Option 2", "option_order": 2},
            },
        }
        
        createBody, _ := json.Marshal(createReq)
        req := httptest.NewRequest(http.MethodPost, "/api/v1/polls", bytes.NewBuffer(createBody))
        req.Header.Set("Content-Type", "application/json")
        req.Header.Set("Authorization", "Bearer "+token)
        
        w := httptest.NewRecorder()
        server.Router.ServeHTTP(w, req)
        
        require.Equal(t, http.StatusCreated, w.Code)
        
        var createResponse map[string]interface{}
        err := json.Unmarshal(w.Body.Bytes(), &createResponse)
        require.NoError(t, err)
        
        pollData := createResponse["data"].(map[string]interface{})
        pollID := int(pollData["id"].(float64))
        
        // Get poll
        req = httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1/polls/%d", pollID), nil)
        req.Header.Set("Authorization", "Bearer "+token)
        
        w = httptest.NewRecorder()
        server.Router.ServeHTTP(w, req)
        
        require.Equal(t, http.StatusOK, w.Code)
        
        var getResponse map[string]interface{}
        err = json.Unmarshal(w.Body.Bytes(), &getResponse)
        require.NoError(t, err)
        
        pollData = getResponse["data"].(map[string]interface{})
        assert.Equal(t, "API Integration Test Poll", pollData["title"])
        
        // Update poll
        updateReq := map[string]interface{}{
            "title":  "Updated Poll Title",
            "status": "active",
        }
        
        updateBody, _ := json.Marshal(updateReq)
        req = httptest.NewRequest(http.MethodPUT, fmt.Sprintf("/api/v1/polls/%d", pollID), bytes.NewBuffer(updateBody))
        req.Header.Set("Content-Type", "application/json")
        req.Header.Set("Authorization", "Bearer "+token)
        
        w = httptest.NewRecorder()
        server.Router.ServeHTTP(w, req)
        
        require.Equal(t, http.StatusOK, w.Code)
        
        // Submit vote
        voteReq := map[string]interface{}{
            "option_id": 1,
        }
        
        voteBody, _ := json.Marshal(voteReq)
        req = httptest.NewRequest(http.MethodPost, fmt.Sprintf("/api/v1/polls/%d/vote", pollID), bytes.NewBuffer(voteBody))
        req.Header.Set("Content-Type", "application/json")
        req.Header.Set("Authorization", "Bearer "+token)
        
        w = httptest.NewRecorder()
        server.Router.ServeHTTP(w, req)
        
        require.Equal(t, http.StatusOK, w.Code)
        
        // Verify vote was recorded
        req = httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1/polls/%d", pollID), nil)
        req.Header.Set("Authorization", "Bearer "+token)
        
        w = httptest.NewRecorder()
        server.Router.ServeHTTP(w, req)
        
        require.Equal(t, http.StatusOK, w.Code)
        
        err = json.Unmarshal(w.Body.Bytes(), &getResponse)
        require.NoError(t, err)
        
        pollData = getResponse["data"].(map[string]interface{})
        assert.Equal(t, float64(1), pollData["total_votes"])
        
        // Delete poll
        req = httptest.NewRequest(http.MethodDelete, fmt.Sprintf("/api/v1/polls/%d", pollID), nil)
        req.Header.Set("Authorization", "Bearer "+token)
        
        w = httptest.NewRecorder()
        server.Router.ServeHTTP(w, req)
        
        require.Equal(t, http.StatusOK, w.Code)
        
        // Verify poll is deleted
        req = httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1/polls/%d", pollID), nil)
        req.Header.Set("Authorization", "Bearer "+token)
        
        w = httptest.NewRecorder()
        server.Router.ServeHTTP(w, req)
        
        assert.Equal(t, http.StatusNotFound, w.Code)
    })
}
```

## Performance Testing

### 1. Load Testing

```go
package performance_test

import (
    "context"
    "sync"
    "testing"
    "time"
    
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/require"
    
    "github.com/tranthanhloi/wn-api-v3/internal/modules/poll/models"
    "github.com/tranthanhloi/wn-api-v3/internal/modules/poll/services"
    "github.com/tranthanhloi/wn-api-v3/internal/tests/helpers"
)

func TestPollPerformance(t *testing.T) {
    if testing.Short() {
        t.Skip("Skipping performance test in short mode")
    }
    
    // Setup test environment
    db := helpers.SetupTestDB(t)
    defer helpers.CleanupTestDB(t, db)
    
    service := helpers.SetupPollService(t, db)
    
    t.Run("concurrent vote submissions", func(t *testing.T) {
        // Create test poll
        poll := helpers.CreateTestPoll(t, db, &models.Poll{
            TenantID:  1,
            Title:     "Performance Test Poll",
            Status:    "active",
            PollType:  "single_choice",
        })
        
        // Create multiple users
        numUsers := 1000
        users := make([]*models.User, numUsers)
        for i := 0; i < numUsers; i++ {
            users[i] = helpers.CreateTestUser(t, db, 1)
        }
        
        // Submit votes concurrently
        var wg sync.WaitGroup
        start := time.Now()
        errors := make([]error, numUsers)
        
        for i := 0; i < numUsers; i++ {
            wg.Add(1)
            go func(userIndex int) {
                defer wg.Done()
                
                ctx := context.WithValue(context.Background(), "user_context", &models.UserContext{
                    UserID:   users[userIndex].ID,
                    TenantID: 1,
                })
                
                voteReq := &models.SubmitVoteRequest{
                    PollID:   poll.ID,
                    OptionID: 1,
                    UserID:   users[userIndex].ID,
                }
                
                _, err := service.SubmitVote(ctx, voteReq)
                errors[userIndex] = err
            }(i)
        }
        
        wg.Wait()
        duration := time.Since(start)
        
        // Verify results
        successCount := 0
        for _, err := range errors {
            if err == nil {
                successCount++
            }
        }
        
        t.Logf("Submitted %d votes in %v", successCount, duration)
        t.Logf("Average time per vote: %v", duration/time.Duration(successCount))
        
        // Performance assertions
        assert.Equal(t, numUsers, successCount, "All votes should succeed")
        assert.Less(t, duration, 10*time.Second, "Should complete within 10 seconds")
        
        // Verify final vote count
        updatedPoll, err := service.GetPollByID(context.Background(), poll.ID)
        require.NoError(t, err)
        assert.Equal(t, numUsers, updatedPoll.TotalVotes)
    })
    
    t.Run("poll retrieval performance", func(t *testing.T) {
        // Create multiple polls
        numPolls := 100
        polls := make([]*models.Poll, numPolls)
        for i := 0; i < numPolls; i++ {
            polls[i] = helpers.CreateTestPoll(t, db, &models.Poll{
                TenantID:  1,
                Title:     fmt.Sprintf("Poll %d", i),
                Status:    "active",
                PollType:  "single_choice",
            })
        }
        
        // Retrieve polls concurrently
        var wg sync.WaitGroup
        start := time.Now()
        
        for i := 0; i < numPolls; i++ {
            wg.Add(1)
            go func(pollIndex int) {
                defer wg.Done()
                
                ctx := context.WithValue(context.Background(), "user_context", &models.UserContext{
                    UserID:   1,
                    TenantID: 1,
                })
                
                _, err := service.GetPollByID(ctx, polls[pollIndex].ID)
                assert.NoError(t, err)
            }(i)
        }
        
        wg.Wait()
        duration := time.Since(start)
        
        t.Logf("Retrieved %d polls in %v", numPolls, duration)
        t.Logf("Average time per retrieval: %v", duration/time.Duration(numPolls))
        
        // Performance assertions
        assert.Less(t, duration, 5*time.Second, "Should complete within 5 seconds")
    })
}
```

### 2. Benchmark Tests

```go
package performance_test

import (
    "context"
    "testing"
    
    "github.com/tranthanhloi/wn-api-v3/internal/modules/poll/models"
    "github.com/tranthanhloi/wn-api-v3/internal/tests/helpers"
)

func BenchmarkPollService_CreatePoll(b *testing.B) {
    db := helpers.SetupTestDB(b)
    defer helpers.CleanupTestDB(b, db)
    
    service := helpers.SetupPollService(b, db)
    
    ctx := context.WithValue(context.Background(), "user_context", &models.UserContext{
        UserID:   1,
        TenantID: 1,
    })
    
    b.ResetTimer()
    
    for i := 0; i < b.N; i++ {
        req := &models.CreatePollRequest{
            Title:    fmt.Sprintf("Benchmark Poll %d", i),
            PollType: "single_choice",
            Options: []models.PollOption{
                {OptionText: "Option 1", OptionOrder: 1},
                {OptionText: "Option 2", OptionOrder: 2},
            },
        }
        
        _, err := service.CreatePoll(ctx, req)
        if err != nil {
            b.Fatal(err)
        }
    }
}

func BenchmarkPollService_GetPollByID(b *testing.B) {
    db := helpers.SetupTestDB(b)
    defer helpers.CleanupTestDB(b, db)
    
    service := helpers.SetupPollService(b, db)
    
    // Create test poll
    poll := helpers.CreateTestPoll(b, db, &models.Poll{
        TenantID:  1,
        Title:     "Benchmark Poll",
        Status:    "active",
        PollType:  "single_choice",
    })
    
    ctx := context.WithValue(context.Background(), "user_context", &models.UserContext{
        UserID:   1,
        TenantID: 1,
    })
    
    b.ResetTimer()
    
    for i := 0; i < b.N; i++ {
        _, err := service.GetPollByID(ctx, poll.ID)
        if err != nil {
            b.Fatal(err)
        }
    }
}

func BenchmarkPollService_SubmitVote(b *testing.B) {
    db := helpers.SetupTestDB(b)
    defer helpers.CleanupTestDB(b, db)
    
    service := helpers.SetupPollService(b, db)
    
    // Create test poll
    poll := helpers.CreateTestPoll(b, db, &models.Poll{
        TenantID:  1,
        Title:     "Benchmark Poll",
        Status:    "active",
        PollType:  "single_choice",
    })
    
    b.ResetTimer()
    
    for i := 0; i < b.N; i++ {
        // Create unique user for each vote
        user := helpers.CreateTestUser(b, db, 1)
        
        ctx := context.WithValue(context.Background(), "user_context", &models.UserContext{
            UserID:   user.ID,
            TenantID: 1,
        })
        
        req := &models.SubmitVoteRequest{
            PollID:   poll.ID,
            OptionID: 1,
            UserID:   user.ID,
        }
        
        _, err := service.SubmitVote(ctx, req)
        if err != nil {
            b.Fatal(err)
        }
    }
}
```

## Security Testing

### 1. Authorization Tests

```go
package security_test

import (
    "context"
    "testing"
    
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/require"
    
    "github.com/tranthanhloi/wn-api-v3/internal/modules/poll/models"
    "github.com/tranthanhloi/wn-api-v3/internal/tests/helpers"
)

func TestPollSecurity(t *testing.T) {
    db := helpers.SetupTestDB(t)
    defer helpers.CleanupTestDB(t, db)
    
    service := helpers.SetupPollService(t, db)
    
    t.Run("unauthorized access prevention", func(t *testing.T) {
        // Create test poll
        poll := helpers.CreateTestPoll(t, db, &models.Poll{
            TenantID:  1,
            CreatedBy: 1,
            Title:     "Security Test Poll",
            Status:    "active",
        })
        
        // Try to access without context
        _, err := service.GetPollByID(context.Background(), poll.ID)
        assert.Error(t, err)
        assert.Equal(t, models.ErrUserContextRequired, err)
        
        // Try to access with wrong tenant
        wrongTenantCtx := context.WithValue(context.Background(), "user_context", &models.UserContext{
            UserID:   2,
            TenantID: 2,
        })
        
        _, err = service.GetPollByID(wrongTenantCtx, poll.ID)
        assert.Error(t, err)
        assert.Equal(t, models.ErrPollNotFound, err)
    })
    
    t.Run("SQL injection prevention", func(t *testing.T) {
        ctx := context.WithValue(context.Background(), "user_context", &models.UserContext{
            UserID:   1,
            TenantID: 1,
        })
        
        // Try SQL injection in poll creation
        req := &models.CreatePollRequest{
            Title:    "'; DROP TABLE polls; --",
            PollType: "single_choice",
            Options: []models.PollOption{
                {OptionText: "Option 1", OptionOrder: 1},
            },
        }
        
        poll, err := service.CreatePoll(ctx, req)
        require.NoError(t, err)
        
        // Verify the malicious input was treated as literal text
        assert.Equal(t, "'; DROP TABLE polls; --", poll.Title)
        
        // Verify table still exists by creating another poll
        req2 := &models.CreatePollRequest{
            Title:    "Normal Poll",
            PollType: "single_choice",
            Options: []models.PollOption{
                {OptionText: "Option 1", OptionOrder: 1},
            },
        }
        
        _, err = service.CreatePoll(ctx, req2)
        require.NoError(t, err)
    })
    
    t.Run("input validation", func(t *testing.T) {
        ctx := context.WithValue(context.Background(), "user_context", &models.UserContext{
            UserID:   1,
            TenantID: 1,
        })
        
        // Test various invalid inputs
        testCases := []struct {
            name    string
            request *models.CreatePollRequest
            wantErr bool
        }{
            {
                name: "empty title",
                request: &models.CreatePollRequest{
                    Title:    "",
                    PollType: "single_choice",
                    Options: []models.PollOption{
                        {OptionText: "Option 1", OptionOrder: 1},
                    },
                },
                wantErr: true,
            },
            {
                name: "invalid poll type",
                request: &models.CreatePollRequest{
                    Title:    "Test Poll",
                    PollType: "invalid_type",
                    Options: []models.PollOption{
                        {OptionText: "Option 1", OptionOrder: 1},
                    },
                },
                wantErr: true,
            },
            {
                name: "no options",
                request: &models.CreatePollRequest{
                    Title:    "Test Poll",
                    PollType: "single_choice",
                    Options:  []models.PollOption{},
                },
                wantErr: true,
            },
            {
                name: "too many options",
                request: &models.CreatePollRequest{
                    Title:    "Test Poll",
                    PollType: "single_choice",
                    Options:  make([]models.PollOption, 20), // Assuming max is 10
                },
                wantErr: true,
            },
        }
        
        for _, tc := range testCases {
            t.Run(tc.name, func(t *testing.T) {
                _, err := service.CreatePoll(ctx, tc.request)
                if tc.wantErr {
                    assert.Error(t, err)
                } else {
                    assert.NoError(t, err)
                }
            })
        }
    })
}
```

### 2. Rate Limiting Tests

```go
func TestPollRateLimiting(t *testing.T) {
    db := helpers.SetupTestDB(t)
    defer helpers.CleanupTestDB(t, db)
    
    service := helpers.SetupPollServiceWithRateLimit(t, db)
    
    ctx := context.WithValue(context.Background(), "user_context", &models.UserContext{
        UserID:   1,
        TenantID: 1,
    })
    
    t.Run("poll creation rate limiting", func(t *testing.T) {
        // Try to create many polls quickly
        successCount := 0
        rateLimitedCount := 0
        
        for i := 0; i < 20; i++ {
            req := &models.CreatePollRequest{
                Title:    fmt.Sprintf("Rate Limit Test Poll %d", i),
                PollType: "single_choice",
                Options: []models.PollOption{
                    {OptionText: "Option 1", OptionOrder: 1},
                },
            }
            
            _, err := service.CreatePoll(ctx, req)
            if err != nil {
                if err == models.ErrRateLimitExceeded {
                    rateLimitedCount++
                } else {
                    t.Errorf("Unexpected error: %v", err)
                }
            } else {
                successCount++
            }
        }
        
        // Should have some successful creations and some rate limited
        assert.Greater(t, successCount, 0, "Should have some successful creations")
        assert.Greater(t, rateLimitedCount, 0, "Should have some rate limited attempts")
        
        t.Logf("Successful: %d, Rate Limited: %d", successCount, rateLimitedCount)
    })
    
    t.Run("voting rate limiting", func(t *testing.T) {
        // Create test poll
        poll := helpers.CreateTestPoll(t, db, &models.Poll{
            TenantID:  1,
            Title:     "Rate Limit Vote Test",
            Status:    "active",
            PollType:  "single_choice",
        })
        
        // Try to vote multiple times (should be limited to 1)
        voteCount := 0
        rateLimitedCount := 0
        
        for i := 0; i < 5; i++ {
            req := &models.SubmitVoteRequest{
                PollID:   poll.ID,
                OptionID: 1,
                UserID:   1,
            }
            
            _, err := service.SubmitVote(ctx, req)
            if err != nil {
                if err == models.ErrAlreadyVoted {
                    rateLimitedCount++
                } else {
                    t.Errorf("Unexpected error: %v", err)
                }
            } else {
                voteCount++
            }
        }
        
        // Should only allow one vote
        assert.Equal(t, 1, voteCount, "Should only allow one vote")
        assert.Equal(t, 4, rateLimitedCount, "Should reject 4 duplicate votes")
    })
}
```

## Test Utilities

### 1. Test Helpers

```go
package helpers

import (
    "database/sql"
    "testing"
    "time"
    
    "github.com/stretchr/testify/require"
    
    "github.com/tranthanhloi/wn-api-v3/internal/modules/poll/models"
)

// SetupTestDB creates a test database connection
func SetupTestDB(t *testing.T) *sql.DB {
    db, err := sql.Open("mysql", "test:test@tcp(localhost:3306)/test_db")
    require.NoError(t, err)
    
    // Run migrations
    err = runTestMigrations(db)
    require.NoError(t, err)
    
    return db
}

// CleanupTestDB cleans up test database
func CleanupTestDB(t *testing.T, db *sql.DB) {
    // Clean up test data
    tables := []string{
        "poll_votes",
        "poll_options", 
        "polls",
        "users",
        "tenants",
    }
    
    for _, table := range tables {
        _, err := db.Exec("DELETE FROM " + table)
        require.NoError(t, err)
    }
    
    db.Close()
}

// CreateTestTenant creates a test tenant
func CreateTestTenant(t *testing.T, db *sql.DB) *models.Tenant {
    tenant := &models.Tenant{
        Name:      "Test Tenant",
        Domain:    "test.example.com",
        Status:    "active",
        CreatedAt: time.Now(),
    }
    
    query := `
        INSERT INTO tenants (name, domain, status, created_at)
        VALUES (?, ?, ?, ?)
    `
    
    result, err := db.Exec(query, tenant.Name, tenant.Domain, tenant.Status, tenant.CreatedAt)
    require.NoError(t, err)
    
    id, err := result.LastInsertId()
    require.NoError(t, err)
    
    tenant.ID = uint(id)
    return tenant
}

// CreateTestUser creates a test user
func CreateTestUser(t *testing.T, db *sql.DB, tenantID uint) *models.User {
    user := &models.User{
        TenantID:  tenantID,
        Email:     "<EMAIL>",
        Name:      "Test User",
        Status:    "active",
        CreatedAt: time.Now(),
    }
    
    query := `
        INSERT INTO users (tenant_id, email, name, status, created_at)
        VALUES (?, ?, ?, ?, ?)
    `
    
    result, err := db.Exec(query, user.TenantID, user.Email, user.Name, user.Status, user.CreatedAt)
    require.NoError(t, err)
    
    id, err := result.LastInsertId()
    require.NoError(t, err)
    
    user.ID = uint(id)
    return user
}

// CreateTestPoll creates a test poll
func CreateTestPoll(t *testing.T, db *sql.DB, poll *models.Poll) *models.Poll {
    if poll.CreatedAt.IsZero() {
        poll.CreatedAt = time.Now()
    }
    
    query := `
        INSERT INTO polls (tenant_id, website_id, created_by, title, description, 
                          poll_type, status, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `
    
    result, err := db.Exec(query, poll.TenantID, poll.WebsiteID, poll.CreatedBy,
        poll.Title, poll.Description, poll.PollType, poll.Status, poll.CreatedAt)
    require.NoError(t, err)
    
    id, err := result.LastInsertId()
    require.NoError(t, err)
    
    poll.ID = uint(id)
    return poll
}

// GenerateTestJWT generates a test JWT token
func GenerateTestJWT(t *testing.T, userID, tenantID uint) string {
    // Implementation depends on your JWT library
    // This is a placeholder
    return "test-jwt-token"
}
```

### 2. Mock Generators

```go
//go:generate mockgen -source=../../internal/modules/poll/services/interface.go -destination=mocks/poll_service_mock.go
//go:generate mockgen -source=../../internal/modules/poll/repositories/interface.go -destination=mocks/poll_repository_mock.go
//go:generate mockgen -source=../../internal/modules/rbac/services/interface.go -destination=mocks/rbac_service_mock.go
```

## Test Configuration

### 1. Test Database Configuration

```yaml
# test-config.yaml
database:
  host: localhost
  port: 3306
  user: test
  password: test
  database: test_db
  max_connections: 10
  
cache:
  type: memory
  
logging:
  level: debug
  
testing:
  cleanup_after_test: true
  parallel_tests: true
  timeout: 30s
```

### 2. Test Runner Scripts

```bash
#!/bin/bash
# scripts/run-tests.sh

set -e

echo "Running Poll Module Tests..."

# Unit tests
echo "Running unit tests..."
go test -v -race -cover ./internal/modules/poll/services/... ./internal/modules/poll/repositories/... ./internal/modules/poll/handlers/...

# Integration tests
echo "Running integration tests..."
go test -v -race -cover -tags=integration ./internal/modules/poll/...

# Performance tests
echo "Running performance tests..."
go test -v -race -bench=. -benchmem ./internal/modules/poll/...

# Security tests
echo "Running security tests..."
go test -v -race -cover -tags=security ./internal/modules/poll/...

echo "All tests completed successfully!"
```

## Best Practices

### 1. Test Organization
- Separate unit, integration, and performance tests
- Use descriptive test names
- Group related tests in test suites
- Use table-driven tests for multiple scenarios

### 2. Test Data Management
- Use test fixtures for consistent test data
- Clean up test data after each test
- Use factories for creating test objects
- Avoid hardcoded values in tests

### 3. Mocking Strategy
- Mock external dependencies
- Use interface-based mocking
- Verify mock expectations
- Keep mocks simple and focused

### 4. Performance Testing
- Use benchmarks for performance-critical code
- Test with realistic data volumes
- Monitor memory usage
- Set performance budgets

### 5. Security Testing
- Test authorization at all levels
- Validate input sanitization
- Test for SQL injection vulnerabilities
- Test rate limiting implementation
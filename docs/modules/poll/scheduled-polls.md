# Poll Module - Scheduled Polls

## Overview

The Scheduled Polls feature allows users to create polls that automatically activate and deactivate at specific times. This enables time-based polling campaigns, event-driven polls, and automated poll lifecycle management.

## Scheduling Architecture

```mermaid
graph TB
    subgraph "Poll Scheduling System"
        Scheduler[Cron Scheduler]
        JobQueue[Job Queue]
        PollProcessor[Poll Processor]
        NotificationService[Notification Service]
    end
    
    subgraph "Database"
        PollsTable[(polls)]
        ScheduleTable[(poll_schedules)]
        JobsTable[(scheduled_jobs)]
    end
    
    subgraph "External Services"
        WebSocket[WebSocket]
        EmailService[Email Service]
        Analytics[Analytics]
    end
    
    Scheduler --> JobQueue
    JobQueue --> PollProcessor
    PollProcessor --> PollsTable
    PollProcessor --> ScheduleTable
    PollProcessor --> NotificationService
    
    NotificationService --> WebSocket
    NotificationService --> EmailService
    NotificationService --> Analytics
    
    PollsTable --> JobsTable
    ScheduleTable --> JobsTable
```

## Database Schema

### Poll Scheduling Fields

The main `polls` table includes scheduling fields:

```sql
-- In polls table
start_date TIMESTAMP NULL,
end_date TIMESTAMP NULL,
status ENUM('draft', 'scheduled', 'active', 'ended', 'archived', 'deleted') NOT NULL DEFAULT 'draft',
```

### Schedule Jobs Table

```sql
CREATE TABLE IF NOT EXISTS poll_schedule_jobs (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    poll_id INT UNSIGNED NOT NULL,
    
    -- Job details
    job_type ENUM('activate', 'deactivate', 'remind', 'cleanup') NOT NULL,
    scheduled_time TIMESTAMP NOT NULL,
    
    -- Execution details
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') NOT NULL DEFAULT 'pending',
    attempts INT UNSIGNED DEFAULT 0,
    max_attempts INT UNSIGNED DEFAULT 3,
    
    -- Results
    executed_at TIMESTAMP NULL,
    error_message TEXT,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_poll_schedule_jobs_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_poll_schedule_jobs_poll_id FOREIGN KEY (poll_id) REFERENCES polls(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_poll_schedule_jobs_tenant_id (tenant_id),
    INDEX idx_poll_schedule_jobs_poll_id (poll_id),
    INDEX idx_poll_schedule_jobs_scheduled_time (scheduled_time),
    INDEX idx_poll_schedule_jobs_status (status),
    INDEX idx_poll_schedule_jobs_tenant_time (tenant_id, scheduled_time)
);
```

## Scheduling Logic

### 1. Poll Creation with Scheduling

```go
type CreatePollRequest struct {
    Title       string    `json:"title" validate:"required,min=1,max=255"`
    Description string    `json:"description"`
    PollType    string    `json:"poll_type" validate:"required,oneof=single_choice multiple_choice ranked"`
    StartDate   *time.Time `json:"start_date"`
    EndDate     *time.Time `json:"end_date"`
    Options     []PollOption `json:"options" validate:"required,min=2,max=10"`
    
    // Scheduling options
    NotifyOnStart bool `json:"notify_on_start"`
    NotifyOnEnd   bool `json:"notify_on_end"`
    RemindBefore  *time.Duration `json:"remind_before"` // e.g., 1 hour before
}

func (s *PollService) CreatePoll(ctx context.Context, req *CreatePollRequest) (*Poll, error) {
    // Validate scheduling
    if err := s.validateScheduling(req); err != nil {
        return nil, err
    }
    
    // Create poll
    poll, err := s.repository.CreatePoll(ctx, req)
    if err != nil {
        return nil, err
    }
    
    // Schedule jobs if dates are provided
    if req.StartDate != nil || req.EndDate != nil {
        if err := s.scheduleJobs(ctx, poll, req); err != nil {
            // Log error but don't fail poll creation
            s.logger.Error("Failed to schedule jobs", zap.Error(err))
        }
    }
    
    return poll, nil
}

func (s *PollService) validateScheduling(req *CreatePollRequest) error {
    now := time.Now()
    
    // Start date validation
    if req.StartDate != nil {
        if req.StartDate.Before(now) {
            return ErrStartDateInPast
        }
    }
    
    // End date validation
    if req.EndDate != nil {
        if req.EndDate.Before(now) {
            return ErrEndDateInPast
        }
        
        if req.StartDate != nil && req.EndDate.Before(*req.StartDate) {
            return ErrEndDateBeforeStart
        }
    }
    
    // Remind before validation
    if req.RemindBefore != nil && req.StartDate != nil {
        reminderTime := req.StartDate.Add(-*req.RemindBefore)
        if reminderTime.Before(now) {
            return ErrReminderTooLate
        }
    }
    
    return nil
}
```

### 2. Job Scheduling

```go
func (s *PollService) scheduleJobs(ctx context.Context, poll *Poll, req *CreatePollRequest) error {
    var jobs []*ScheduleJob
    
    // Schedule activation job
    if req.StartDate != nil {
        jobs = append(jobs, &ScheduleJob{
            TenantID:      poll.TenantID,
            PollID:        poll.ID,
            JobType:       "activate",
            ScheduledTime: *req.StartDate,
        })
        
        // Schedule reminder job
        if req.RemindBefore != nil {
            reminderTime := req.StartDate.Add(-*req.RemindBefore)
            jobs = append(jobs, &ScheduleJob{
                TenantID:      poll.TenantID,
                PollID:        poll.ID,
                JobType:       "remind",
                ScheduledTime: reminderTime,
            })
        }
    }
    
    // Schedule deactivation job
    if req.EndDate != nil {
        jobs = append(jobs, &ScheduleJob{
            TenantID:      poll.TenantID,
            PollID:        poll.ID,
            JobType:       "deactivate",
            ScheduledTime: *req.EndDate,
        })
    }
    
    // Create schedule jobs
    for _, job := range jobs {
        if err := s.scheduleRepository.CreateJob(ctx, job); err != nil {
            return err
        }
    }
    
    return nil
}
```

## Scheduler Implementation

### 1. Cron Job Scheduler

```go
type PollScheduler struct {
    cron              *cron.Cron
    scheduleService   ScheduleService
    pollService       PollService
    notificationService NotificationService
    logger            *zap.Logger
}

func NewPollScheduler(
    scheduleService ScheduleService,
    pollService PollService,
    notificationService NotificationService,
    logger *zap.Logger,
) *PollScheduler {
    return &PollScheduler{
        cron:                cron.New(),
        scheduleService:     scheduleService,
        pollService:         pollService,
        notificationService: notificationService,
        logger:              logger,
    }
}

func (s *PollScheduler) Start() error {
    // Run every minute to check for pending jobs
    s.cron.AddFunc("* * * * *", s.processPendingJobs)
    
    // Cleanup completed jobs daily
    s.cron.AddFunc("0 2 * * *", s.cleanupCompletedJobs)
    
    s.cron.Start()
    s.logger.Info("Poll scheduler started")
    
    return nil
}

func (s *PollScheduler) Stop() {
    s.cron.Stop()
    s.logger.Info("Poll scheduler stopped")
}

func (s *PollScheduler) processPendingJobs() {
    ctx := context.Background()
    
    // Get pending jobs that are due
    jobs, err := s.scheduleService.GetPendingJobs(ctx, time.Now())
    if err != nil {
        s.logger.Error("Failed to get pending jobs", zap.Error(err))
        return
    }
    
    for _, job := range jobs {
        if err := s.processJob(ctx, job); err != nil {
            s.logger.Error("Failed to process job",
                zap.Uint("job_id", job.ID),
                zap.String("job_type", job.JobType),
                zap.Error(err),
            )
        }
    }
}

func (s *PollScheduler) processJob(ctx context.Context, job *ScheduleJob) error {
    // Mark job as processing
    if err := s.scheduleService.UpdateJobStatus(ctx, job.ID, "processing"); err != nil {
        return err
    }
    
    var err error
    switch job.JobType {
    case "activate":
        err = s.activatePoll(ctx, job.PollID)
    case "deactivate":
        err = s.deactivatePoll(ctx, job.PollID)
    case "remind":
        err = s.sendReminder(ctx, job.PollID)
    case "cleanup":
        err = s.cleanupPoll(ctx, job.PollID)
    default:
        err = fmt.Errorf("unknown job type: %s", job.JobType)
    }
    
    // Update job status
    if err != nil {
        job.Attempts++
        if job.Attempts >= job.MaxAttempts {
            s.scheduleService.UpdateJobStatus(ctx, job.ID, "failed")
            s.scheduleService.UpdateJobError(ctx, job.ID, err.Error())
        } else {
            s.scheduleService.UpdateJobStatus(ctx, job.ID, "pending")
        }
        return err
    }
    
    // Mark as completed
    s.scheduleService.UpdateJobStatus(ctx, job.ID, "completed")
    s.scheduleService.UpdateJobExecutedAt(ctx, job.ID, time.Now())
    
    return nil
}
```

### 2. Job Processing

```go
func (s *PollScheduler) activatePoll(ctx context.Context, pollID uint) error {
    s.logger.Info("Activating poll", zap.Uint("poll_id", pollID))
    
    // Update poll status
    if err := s.pollService.UpdatePollStatus(ctx, pollID, "active"); err != nil {
        return err
    }
    
    // Send activation notification
    if err := s.notificationService.SendPollActivated(ctx, pollID); err != nil {
        s.logger.Error("Failed to send activation notification", zap.Error(err))
    }
    
    // Broadcast real-time update
    s.broadcastPollStatusUpdate(ctx, pollID, "active")
    
    return nil
}

func (s *PollScheduler) deactivatePoll(ctx context.Context, pollID uint) error {
    s.logger.Info("Deactivating poll", zap.Uint("poll_id", pollID))
    
    // Update poll status
    if err := s.pollService.UpdatePollStatus(ctx, pollID, "ended"); err != nil {
        return err
    }
    
    // Generate final results
    if err := s.pollService.GenerateFinalResults(ctx, pollID); err != nil {
        s.logger.Error("Failed to generate final results", zap.Error(err))
    }
    
    // Send completion notification
    if err := s.notificationService.SendPollEnded(ctx, pollID); err != nil {
        s.logger.Error("Failed to send completion notification", zap.Error(err))
    }
    
    // Broadcast real-time update
    s.broadcastPollStatusUpdate(ctx, pollID, "ended")
    
    return nil
}

func (s *PollScheduler) sendReminder(ctx context.Context, pollID uint) error {
    s.logger.Info("Sending poll reminder", zap.Uint("poll_id", pollID))
    
    // Send reminder notification
    if err := s.notificationService.SendPollReminder(ctx, pollID); err != nil {
        return err
    }
    
    return nil
}

func (s *PollScheduler) cleanupPoll(ctx context.Context, pollID uint) error {
    s.logger.Info("Cleaning up poll", zap.Uint("poll_id", pollID))
    
    // Archive old poll data
    if err := s.pollService.ArchivePollData(ctx, pollID); err != nil {
        return err
    }
    
    return nil
}
```

## API Endpoints

### 1. Schedule Poll

```http
POST /api/v1/polls/{id}/schedule
Authorization: Bearer {token}
Content-Type: application/json

{
  "start_date": "2024-02-01T09:00:00Z",
  "end_date": "2024-02-07T17:00:00Z",
  "notify_on_start": true,
  "notify_on_end": true,
  "remind_before": "1h"
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "poll_id": 1,
    "start_date": "2024-02-01T09:00:00Z",
    "end_date": "2024-02-07T17:00:00Z",
    "scheduled_jobs": [
      {
        "id": 1,
        "job_type": "remind",
        "scheduled_time": "2024-02-01T08:00:00Z",
        "status": "pending"
      },
      {
        "id": 2,
        "job_type": "activate",
        "scheduled_time": "2024-02-01T09:00:00Z",
        "status": "pending"
      },
      {
        "id": 3,
        "job_type": "deactivate",
        "scheduled_time": "2024-02-07T17:00:00Z",
        "status": "pending"
      }
    ]
  }
}
```

### 2. Get Schedule Status

```http
GET /api/v1/polls/{id}/schedule
Authorization: Bearer {token}
```

Response:
```json
{
  "status": "success",
  "data": {
    "poll_id": 1,
    "current_status": "scheduled",
    "start_date": "2024-02-01T09:00:00Z",
    "end_date": "2024-02-07T17:00:00Z",
    "time_until_start": "2h30m",
    "time_until_end": "6d14h30m",
    "scheduled_jobs": [
      {
        "id": 1,
        "job_type": "remind",
        "scheduled_time": "2024-02-01T08:00:00Z",
        "status": "pending"
      },
      {
        "id": 2,
        "job_type": "activate",
        "scheduled_time": "2024-02-01T09:00:00Z",
        "status": "pending"
      },
      {
        "id": 3,
        "job_type": "deactivate",
        "scheduled_time": "2024-02-07T17:00:00Z",
        "status": "pending"
      }
    ]
  }
}
```

### 3. Update Schedule

```http
PUT /api/v1/polls/{id}/schedule
Authorization: Bearer {token}
Content-Type: application/json

{
  "start_date": "2024-02-01T10:00:00Z",
  "end_date": "2024-02-07T18:00:00Z"
}
```

### 4. Cancel Schedule

```http
DELETE /api/v1/polls/{id}/schedule
Authorization: Bearer {token}
```

## Advanced Scheduling Features

### 1. Recurring Polls

```go
type RecurringPollConfig struct {
    Pattern      string        `json:"pattern"`      // "daily", "weekly", "monthly"
    Interval     int           `json:"interval"`     // Every N days/weeks/months
    DaysOfWeek   []int         `json:"days_of_week"` // For weekly: 0=Sunday, 1=Monday, etc.
    DayOfMonth   int           `json:"day_of_month"` // For monthly
    Duration     time.Duration `json:"duration"`     // How long each poll runs
    EndAfter     *time.Time    `json:"end_after"`    // Stop recurring after this date
    MaxOccurrences int         `json:"max_occurrences"` // Maximum number of occurrences
}

func (s *PollService) CreateRecurringPoll(ctx context.Context, req *CreateRecurringPollRequest) error {
    // Create base poll template
    template, err := s.repository.CreatePollTemplate(ctx, req.PollTemplate)
    if err != nil {
        return err
    }
    
    // Generate schedule occurrences
    occurrences := s.generateRecurringSchedule(req.RecurringConfig)
    
    // Create scheduled jobs for each occurrence
    for i, occurrence := range occurrences {
        pollReq := &CreatePollRequest{
            Title:       fmt.Sprintf("%s #%d", req.PollTemplate.Title, i+1),
            Description: req.PollTemplate.Description,
            PollType:    req.PollTemplate.PollType,
            StartDate:   &occurrence.StartTime,
            EndDate:     &occurrence.EndTime,
            Options:     req.PollTemplate.Options,
        }
        
        if err := s.scheduleRecurringPoll(ctx, pollReq, template.ID); err != nil {
            return err
        }
    }
    
    return nil
}
```

### 2. Conditional Scheduling

```go
type ConditionalSchedule struct {
    Condition     string    `json:"condition"`      // "after_poll_ends", "on_event", "threshold_reached"
    SourcePollID  uint      `json:"source_poll_id"` // For "after_poll_ends"
    EventType     string    `json:"event_type"`     // For "on_event"
    ThresholdType string    `json:"threshold_type"` // "vote_count", "participation_rate"
    ThresholdValue int      `json:"threshold_value"`
    Delay         *time.Duration `json:"delay"`      // Optional delay after condition is met
}

func (s *PollService) CreateConditionalPoll(ctx context.Context, req *CreateConditionalPollRequest) error {
    // Create poll in scheduled state
    poll, err := s.repository.CreatePoll(ctx, req.Poll)
    if err != nil {
        return err
    }
    
    // Set up condition monitoring
    condition := &ConditionalSchedule{
        Condition:     req.Condition.Condition,
        SourcePollID:  req.Condition.SourcePollID,
        EventType:     req.Condition.EventType,
        ThresholdType: req.Condition.ThresholdType,
        ThresholdValue: req.Condition.ThresholdValue,
        Delay:         req.Condition.Delay,
    }
    
    if err := s.scheduleRepository.CreateConditionalTrigger(ctx, poll.ID, condition); err != nil {
        return err
    }
    
    return nil
}
```

### 3. Dynamic Scheduling

```go
type DynamicScheduleRule struct {
    Type         string            `json:"type"`         // "timezone_based", "audience_based", "performance_based"
    Parameters   map[string]interface{} `json:"parameters"`
    Priority     int               `json:"priority"`
}

func (s *PollService) SchedulePollDynamically(ctx context.Context, pollID uint, rules []DynamicScheduleRule) error {
    // Sort rules by priority
    sort.Slice(rules, func(i, j int) bool {
        return rules[i].Priority > rules[j].Priority
    })
    
    // Apply rules in order
    for _, rule := range rules {
        switch rule.Type {
        case "timezone_based":
            if err := s.applyTimezoneBasedScheduling(ctx, pollID, rule.Parameters); err != nil {
                return err
            }
        case "audience_based":
            if err := s.applyAudienceBasedScheduling(ctx, pollID, rule.Parameters); err != nil {
                return err
            }
        case "performance_based":
            if err := s.applyPerformanceBasedScheduling(ctx, pollID, rule.Parameters); err != nil {
                return err
            }
        }
    }
    
    return nil
}
```

## Notifications

### 1. Email Notifications

```go
type PollNotificationService struct {
    emailService EmailService
    templateService TemplateService
    userService  UserService
}

func (s *PollNotificationService) SendPollActivated(ctx context.Context, pollID uint) error {
    poll, err := s.pollService.GetPollByID(ctx, pollID)
    if err != nil {
        return err
    }
    
    // Get subscribers
    subscribers, err := s.userService.GetPollSubscribers(ctx, pollID)
    if err != nil {
        return err
    }
    
    // Send notification to each subscriber
    for _, subscriber := range subscribers {
        template := s.templateService.GetTemplate("poll_activated")
        
        emailData := map[string]interface{}{
            "poll_title":   poll.Title,
            "poll_url":     fmt.Sprintf("/polls/%d", poll.ID),
            "user_name":    subscriber.Name,
            "end_date":     poll.EndDate,
        }
        
        if err := s.emailService.SendTemplatedEmail(
            subscriber.Email,
            "Poll is now active: " + poll.Title,
            template,
            emailData,
        ); err != nil {
            s.logger.Error("Failed to send activation email",
                zap.Uint("poll_id", pollID),
                zap.String("email", subscriber.Email),
                zap.Error(err),
            )
        }
    }
    
    return nil
}
```

### 2. WebSocket Notifications

```go
func (s *PollScheduler) broadcastPollStatusUpdate(ctx context.Context, pollID uint, status string) {
    message := map[string]interface{}{
        "type":     "poll_status_update",
        "poll_id":  pollID,
        "status":   status,
        "timestamp": time.Now(),
    }
    
    channel := fmt.Sprintf("poll:%d", pollID)
    
    if err := s.websocketService.BroadcastToChannel(ctx, channel, message); err != nil {
        s.logger.Error("Failed to broadcast poll status update",
            zap.Uint("poll_id", pollID),
            zap.String("status", status),
            zap.Error(err),
        )
    }
}
```

## Monitoring & Analytics

### 1. Schedule Performance Metrics

```go
type ScheduleMetrics struct {
    JobsScheduled   int     `json:"jobs_scheduled"`
    JobsCompleted   int     `json:"jobs_completed"`
    JobsFailed      int     `json:"jobs_failed"`
    AverageDelay    float64 `json:"average_delay_seconds"`
    SuccessRate     float64 `json:"success_rate"`
    NextJobTime     time.Time `json:"next_job_time"`
}

func (s *ScheduleService) GetScheduleMetrics(ctx context.Context, tenantID uint) (*ScheduleMetrics, error) {
    metrics := &ScheduleMetrics{}
    
    // Get job statistics
    stats, err := s.repository.GetJobStatistics(ctx, tenantID)
    if err != nil {
        return nil, err
    }
    
    metrics.JobsScheduled = stats.TotalJobs
    metrics.JobsCompleted = stats.CompletedJobs
    metrics.JobsFailed = stats.FailedJobs
    metrics.AverageDelay = stats.AverageDelay
    
    if stats.TotalJobs > 0 {
        metrics.SuccessRate = float64(stats.CompletedJobs) / float64(stats.TotalJobs) * 100
    }
    
    // Get next job time
    nextJob, err := s.repository.GetNextScheduledJob(ctx, tenantID)
    if err == nil && nextJob != nil {
        metrics.NextJobTime = nextJob.ScheduledTime
    }
    
    return metrics, nil
}
```

### 2. Schedule Health Check

```go
func (s *ScheduleService) HealthCheck(ctx context.Context) error {
    // Check for stuck jobs
    stuckJobs, err := s.repository.GetStuckJobs(ctx, time.Hour)
    if err != nil {
        return err
    }
    
    if len(stuckJobs) > 0 {
        return fmt.Errorf("found %d stuck jobs", len(stuckJobs))
    }
    
    // Check for failed jobs
    failedJobs, err := s.repository.GetRecentFailedJobs(ctx, 24*time.Hour)
    if err != nil {
        return err
    }
    
    if len(failedJobs) > 10 {
        return fmt.Errorf("too many failed jobs: %d", len(failedJobs))
    }
    
    return nil
}
```

## Error Handling

### 1. Job Retry Logic

```go
func (s *PollScheduler) processJobWithRetry(ctx context.Context, job *ScheduleJob) error {
    for attempt := 1; attempt <= job.MaxAttempts; attempt++ {
        err := s.processJob(ctx, job)
        if err == nil {
            return nil
        }
        
        s.logger.Warn("Job failed, retrying",
            zap.Uint("job_id", job.ID),
            zap.Int("attempt", attempt),
            zap.Error(err),
        )
        
        // Exponential backoff
        if attempt < job.MaxAttempts {
            delay := time.Duration(attempt*attempt) * time.Second
            time.Sleep(delay)
        }
    }
    
    return fmt.Errorf("job failed after %d attempts", job.MaxAttempts)
}
```

### 2. Failure Recovery

```go
func (s *PollScheduler) recoverFailedJobs(ctx context.Context) error {
    // Get failed jobs that can be retried
    failedJobs, err := s.scheduleService.GetRetryableFailedJobs(ctx)
    if err != nil {
        return err
    }
    
    for _, job := range failedJobs {
        // Reset job for retry
        if err := s.scheduleService.ResetJob(ctx, job.ID); err != nil {
            s.logger.Error("Failed to reset job", zap.Uint("job_id", job.ID), zap.Error(err))
            continue
        }
        
        s.logger.Info("Job reset for retry", zap.Uint("job_id", job.ID))
    }
    
    return nil
}
```

## Testing

### 1. Schedule Testing

```go
func TestPollScheduling(t *testing.T) {
    // Setup test environment
    scheduler := setupTestScheduler(t)
    
    // Create poll with future start date
    startDate := time.Now().Add(1 * time.Hour)
    endDate := startDate.Add(24 * time.Hour)
    
    poll, err := pollService.CreatePoll(ctx, &CreatePollRequest{
        Title:     "Test Scheduled Poll",
        StartDate: &startDate,
        EndDate:   &endDate,
    })
    require.NoError(t, err)
    
    // Verify jobs were created
    jobs, err := scheduleService.GetPollJobs(ctx, poll.ID)
    require.NoError(t, err)
    assert.Len(t, jobs, 2) // activate and deactivate jobs
    
    // Test activation
    t.Run("Poll activation", func(t *testing.T) {
        // Simulate time passing
        scheduler.SetTime(startDate)
        scheduler.ProcessPendingJobs()
        
        // Verify poll is active
        updatedPoll, err := pollService.GetPollByID(ctx, poll.ID)
        require.NoError(t, err)
        assert.Equal(t, "active", updatedPoll.Status)
    })
    
    // Test deactivation
    t.Run("Poll deactivation", func(t *testing.T) {
        // Simulate time passing
        scheduler.SetTime(endDate)
        scheduler.ProcessPendingJobs()
        
        // Verify poll is ended
        updatedPoll, err := pollService.GetPollByID(ctx, poll.ID)
        require.NoError(t, err)
        assert.Equal(t, "ended", updatedPoll.Status)
    })
}
```

### 2. Failure Testing

```go
func TestScheduleFailureHandling(t *testing.T) {
    // Setup test with failing service
    scheduler := setupTestSchedulerWithFailingService(t)
    
    // Create job that will fail
    job := &ScheduleJob{
        JobType:       "activate",
        ScheduledTime: time.Now(),
        MaxAttempts:   3,
    }
    
    err := scheduler.ProcessJob(ctx, job)
    assert.Error(t, err)
    
    // Verify job was marked as failed after max attempts
    updatedJob, err := scheduleService.GetJob(ctx, job.ID)
    require.NoError(t, err)
    assert.Equal(t, "failed", updatedJob.Status)
    assert.Equal(t, 3, updatedJob.Attempts)
}
```
# Poll Module - API Endpoints

## Overview

The Poll module provides RESTful API endpoints for managing polls, options, votes, and analytics. All endpoints follow the project's multi-tenant architecture and response standards.

## Base URL

All poll endpoints are prefixed with `/api/v1/polls`

## Authentication

Most endpoints require authentication via JW<PERSON> token. Anonymous voting endpoints support both authenticated and unauthenticated access.

## API Flow Diagrams

### 1. Poll Creation Flow

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant AuthService
    participant PollService
    participant Database
    
    Client->>API: POST /api/v1/polls
    API->>AuthService: Validate JWT Token
    AuthService-->>API: User Context
    API->>PollService: CreatePoll(request)
    PollService->>Database: Insert poll record
    Database-->>PollService: Poll created
    PollService-->>API: Poll response
    API-->>Client: 201 Created
```

### 2. Voting Flow

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant PollService
    participant SessionService
    participant Database
    participant WebSocket
    
    Client->>API: POST /api/v1/polls/{id}/vote
    API->>PollService: ValidatePoll(id)
    PollService->>SessionService: ValidateSession()
    SessionService->>Database: Check duplicate vote
    Database-->>SessionService: Vote allowed
    SessionService->>Database: Record vote
    Database-->>SessionService: Vote recorded
    SessionService->>Database: Update vote counts
    Database-->>SessionService: Counts updated
    SessionService-->>API: Vote response
    API->>WebSocket: Broadcast vote update
    API-->>Client: 200 OK
```

### 3. Real-time Updates Flow

```mermaid
sequenceDiagram
    participant Client1
    participant Client2
    participant WebSocket
    participant PollService
    participant Database
    
    Client1->>WebSocket: Subscribe to poll updates
    Client2->>PollService: Submit vote
    PollService->>Database: Update vote counts
    Database-->>PollService: Counts updated
    PollService->>WebSocket: Broadcast update
    WebSocket->>Client1: Live vote update
    WebSocket->>Client2: Vote confirmation
```

## Endpoints

### Poll Management

#### Create Poll
```http
POST /api/v1/polls
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "What's your favorite programming language?",
  "description": "Help us understand developer preferences",
  "slug": "favorite-programming-language",
  "poll_type": "single_choice",
  "allow_anonymous": true,
  "start_date": "2024-01-01T00:00:00Z",
  "end_date": "2024-12-31T23:59:59Z",
  "options": [
    {
      "option_text": "JavaScript",
      "option_order": 1
    },
    {
      "option_text": "Python",
      "option_order": 2
    },
    {
      "option_text": "Go",
      "option_order": 3
    }
  ]
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "title": "What's your favorite programming language?",
    "description": "Help us understand developer preferences",
    "slug": "favorite-programming-language",
    "poll_type": "single_choice",
    "status": "draft",
    "allow_anonymous": true,
    "start_date": "2024-01-01T00:00:00Z",
    "end_date": "2024-12-31T23:59:59Z",
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z",
    "options": [
      {
        "id": 1,
        "option_text": "JavaScript",
        "option_order": 1,
        "vote_count": 0
      },
      {
        "id": 2,
        "option_text": "Python",
        "option_order": 2,
        "vote_count": 0
      },
      {
        "id": 3,
        "option_text": "Go",
        "option_order": 3,
        "vote_count": 0
      }
    ]
  }
}
```

#### Get Poll List
```http
GET /api/v1/polls?status=active&limit=10&cursor=eyJpZCI6MTA%3D
Authorization: Bearer {token}
```

Response:
```json
{
  "status": "success",
  "data": {
    "polls": [
      {
        "id": 1,
        "title": "What's your favorite programming language?",
        "slug": "favorite-programming-language",
        "poll_type": "single_choice",
        "status": "active",
        "total_votes": 150,
        "created_at": "2024-01-01T12:00:00Z"
      }
    ],
    "pagination": {
      "has_next": true,
      "next_cursor": "eyJpZCI6MjA%3D",
      "limit": 10
    }
  }
}
```

#### Get Poll Details
```http
GET /api/v1/polls/{id}
Authorization: Bearer {token} (optional for public polls)
```

Response:
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "title": "What's your favorite programming language?",
    "description": "Help us understand developer preferences",
    "slug": "favorite-programming-language",
    "poll_type": "single_choice",
    "status": "active",
    "allow_anonymous": true,
    "total_votes": 150,
    "unique_voters": 145,
    "start_date": "2024-01-01T00:00:00Z",
    "end_date": "2024-12-31T23:59:59Z",
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z",
    "options": [
      {
        "id": 1,
        "option_text": "JavaScript",
        "option_order": 1,
        "vote_count": 75,
        "vote_percentage": 50.0
      },
      {
        "id": 2,
        "option_text": "Python",
        "option_order": 2,
        "vote_count": 45,
        "vote_percentage": 30.0
      },
      {
        "id": 3,
        "option_text": "Go",
        "option_order": 3,
        "vote_count": 30,
        "vote_percentage": 20.0
      }
    ],
    "user_vote": {
      "option_id": 1,
      "voted_at": "2024-01-01T14:00:00Z"
    }
  }
}
```

#### Update Poll
```http
PUT /api/v1/polls/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "Updated: What's your favorite programming language?",
  "description": "Updated description",
  "status": "active"
}
```

#### Delete Poll
```http
DELETE /api/v1/polls/{id}
Authorization: Bearer {token}
```

### Voting

#### Submit Vote
```http
POST /api/v1/polls/{id}/vote
Authorization: Bearer {token} (optional for anonymous polls)
Content-Type: application/json

{
  "option_ids": [1],
  "session_id": "sess_123456789" // for anonymous voting
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "vote_id": 1,
    "poll_id": 1,
    "option_ids": [1],
    "voted_at": "2024-01-01T14:00:00Z",
    "updated_results": {
      "total_votes": 151,
      "options": [
        {
          "id": 1,
          "vote_count": 76,
          "vote_percentage": 50.3
        },
        {
          "id": 2,
          "vote_count": 45,
          "vote_percentage": 29.8
        },
        {
          "id": 3,
          "vote_count": 30,
          "vote_percentage": 19.9
        }
      ]
    }
  }
}
```

#### Get Vote Status
```http
GET /api/v1/polls/{id}/vote-status
Authorization: Bearer {token} (optional)
```

Response:
```json
{
  "status": "success",
  "data": {
    "has_voted": true,
    "vote": {
      "option_ids": [1],
      "voted_at": "2024-01-01T14:00:00Z"
    }
  }
}
```

### Poll Options

#### Add Option
```http
POST /api/v1/polls/{id}/options
Authorization: Bearer {token}
Content-Type: application/json

{
  "option_text": "TypeScript",
  "option_order": 4,
  "image_url": "https://example.com/typescript-logo.png"
}
```

#### Update Option
```http
PUT /api/v1/polls/{pollId}/options/{optionId}
Authorization: Bearer {token}
Content-Type: application/json

{
  "option_text": "Updated TypeScript",
  "option_order": 4
}
```

#### Delete Option
```http
DELETE /api/v1/polls/{pollId}/options/{optionId}
Authorization: Bearer {token}
```

### Analytics

#### Get Poll Analytics
```http
GET /api/v1/polls/{id}/analytics?period=daily&start_date=2024-01-01&end_date=2024-01-31
Authorization: Bearer {token}
```

Response:
```json
{
  "status": "success",
  "data": {
    "poll_id": 1,
    "period": "daily",
    "start_date": "2024-01-01",
    "end_date": "2024-01-31",
    "summary": {
      "total_votes": 150,
      "unique_voters": 145,
      "completion_rate": 96.7,
      "avg_time_to_vote": 45
    },
    "daily_stats": [
      {
        "date": "2024-01-01",
        "votes": 10,
        "unique_voters": 9,
        "page_views": 50
      },
      {
        "date": "2024-01-02",
        "votes": 15,
        "unique_voters": 14,
        "page_views": 75
      }
    ],
    "demographics": {
      "countries": [
        {"country": "US", "count": 60},
        {"country": "UK", "count": 30},
        {"country": "DE", "count": 25}
      ],
      "devices": {
        "desktop": 70,
        "mobile": 65,
        "tablet": 15
      }
    }
  }
}
```

#### Export Poll Results
```http
GET /api/v1/polls/{id}/export?format=csv
Authorization: Bearer {token}
```

Response: CSV file download with vote data

### Real-time WebSocket Events

#### Subscribe to Poll Updates
```javascript
// WebSocket connection
const ws = new WebSocket('ws://localhost:8080/ws/polls/1');

// Listen for vote updates
ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  if (data.type === 'vote_update') {
    updatePollDisplay(data.poll_results);
  }
};
```

#### WebSocket Message Types
- `vote_update`: New vote submitted
- `poll_status_change`: Poll status changed
- `option_added`: New option added
- `poll_ended`: Poll has ended

## Error Responses

### Common Error Codes

#### 400 Bad Request
```json
{
  "status": "error",
  "error": {
    "code": "INVALID_REQUEST",
    "message": "Invalid poll configuration",
    "details": {
      "field": "end_date",
      "message": "End date must be after start date"
    }
  }
}
```

#### 403 Forbidden
```json
{
  "status": "error",
  "error": {
    "code": "ALREADY_VOTED",
    "message": "User has already voted in this poll"
  }
}
```

#### 404 Not Found
```json
{
  "status": "error",
  "error": {
    "code": "POLL_NOT_FOUND",
    "message": "Poll not found or access denied"
  }
}
```

#### 422 Unprocessable Entity
```json
{
  "status": "error",
  "error": {
    "code": "POLL_ENDED",
    "message": "Cannot vote on ended poll"
  }
}
```

## Rate Limiting

- **Poll Creation**: 10 polls per hour per user
- **Voting**: 1 vote per poll per user/session
- **Analytics**: 100 requests per hour per user
- **General API**: 1000 requests per hour per user

## Caching Strategy

- **Poll Details**: Cached for 5 minutes
- **Vote Counts**: Cached for 1 minute
- **Analytics**: Cached for 15 minutes
- **Poll Lists**: Cached for 2 minutes

## Security Features

- **CSRF Protection**: Required for all POST/PUT/DELETE operations
- **Rate Limiting**: Per-user and per-IP limits
- **Input Validation**: Comprehensive validation for all inputs
- **SQL Injection Prevention**: Parameterized queries only
- **XSS Prevention**: Output sanitization
# Poll Module - Analytics

## Overview

The Poll Analytics system provides comprehensive insights into poll performance, user engagement, and voting patterns. It includes real-time metrics, historical analysis, and predictive insights to help optimize poll effectiveness.

## Analytics Architecture

```mermaid
graph TB
    subgraph "Data Collection"
        VoteEvents[Vote Events]
        ViewEvents[View Events]
        EngagementEvents[Engagement Events]
        UserEvents[User Events]
    end
    
    subgraph "Event Processing"
        EventQueue[Event Queue]
        EventProcessor[Event Processor]
        DataValidator[Data Validator]
    end
    
    subgraph "Analytics Engine"
        RealTimeProcessor[Real-time Processor]
        BatchProcessor[Batch Processor]
        MetricsCalculator[Metrics Calculator]
        TrendAnalyzer[Trend Analyzer]
    end
    
    subgraph "Storage Layer"
        AnalyticsDB[(Analytics DB)]
        TimeSeriesDB[(Time Series DB)]
        CacheLayer[Cache Layer]
    end
    
    subgraph "API Layer"
        AnalyticsAPI[Analytics API]
        ReportGenerator[Report Generator]
        ExportService[Export Service]
    end
    
    VoteEvents --> EventQueue
    ViewEvents --> EventQueue
    EngagementEvents --> EventQueue
    UserEvents --> EventQueue
    
    EventQueue --> EventProcessor
    EventProcessor --> DataValidator
    DataValidator --> RealTimeProcessor
    DataValidator --> BatchProcessor
    
    RealTimeProcessor --> MetricsCalculator
    BatchProcessor --> TrendAnalyzer
    
    MetricsCalculator --> AnalyticsDB
    TrendAnalyzer --> TimeSeriesDB
    
    AnalyticsDB --> CacheLayer
    TimeSeriesDB --> CacheLayer
    
    CacheLayer --> AnalyticsAPI
    AnalyticsAPI --> ReportGenerator
    AnalyticsAPI --> ExportService
```

## Key Metrics

### 1. Engagement Metrics

#### Vote Participation
- **Total Votes**: Absolute number of votes cast
- **Unique Voters**: Number of distinct users who voted
- **Participation Rate**: Percentage of viewers who voted
- **Completion Rate**: Percentage of users who completed voting

#### Time-based Metrics
- **Time to First Vote**: Average time from poll view to first vote
- **Vote Distribution**: Hourly/daily vote patterns
- **Peak Activity**: Time periods with highest engagement
- **Session Duration**: Average time spent on poll

### 2. Performance Metrics

#### Response Times
- **Vote Processing Time**: Time to process and record votes
- **Results Loading Time**: Time to load poll results
- **Real-time Update Latency**: WebSocket update delays
- **API Response Times**: REST endpoint performance

#### System Health
- **Error Rates**: Failed vote submissions
- **Cache Hit Ratio**: Cache effectiveness
- **Database Performance**: Query execution times
- **Connection Stability**: WebSocket connection drops

### 3. User Behavior Metrics

#### Voting Patterns
- **Option Preferences**: Most/least popular options
- **Vote Timing**: When users typically vote
- **Revision Behavior**: How often users change votes
- **Abandonment Rate**: Users who start but don't complete voting

#### Demographic Analysis
- **Geographic Distribution**: Votes by country/region
- **Device Breakdown**: Desktop vs mobile voting
- **Browser Analysis**: Browser-specific patterns
- **User Agent Tracking**: Platform preferences

## Data Collection

### Event Types

#### Vote Events
```json
{
  "event_type": "vote_cast",
  "timestamp": "2024-01-01T12:00:00Z",
  "poll_id": 1,
  "user_id": 123,
  "option_id": 2,
  "session_id": "sess_abc123",
  "metadata": {
    "user_agent": "Mozilla/5.0...",
    "ip_address": "***********",
    "referrer": "https://example.com/blog/post-1",
    "device_type": "desktop",
    "browser": "chrome",
    "country": "US",
    "response_time": 1.2
  }
}
```

#### View Events
```json
{
  "event_type": "poll_view",
  "timestamp": "2024-01-01T11:55:00Z",
  "poll_id": 1,
  "user_id": 123,
  "session_id": "sess_abc123",
  "metadata": {
    "page_url": "https://example.com/polls/favorite-language",
    "referrer": "https://example.com/blog",
    "view_duration": 45.7,
    "scroll_depth": 0.8,
    "device_type": "desktop"
  }
}
```

#### Engagement Events
```json
{
  "event_type": "option_hover",
  "timestamp": "2024-01-01T11:57:00Z",
  "poll_id": 1,
  "user_id": 123,
  "option_id": 2,
  "metadata": {
    "hover_duration": 2.3,
    "hover_count": 1,
    "before_vote": true
  }
}
```

### Data Pipeline

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant EventCollector
    participant Queue
    participant Processor
    participant Storage
    participant Analytics
    
    Client->>API: Submit Vote
    API->>EventCollector: Record Vote Event
    EventCollector->>Queue: Queue Event
    Queue->>Processor: Process Event
    Processor->>Storage: Store Raw Data
    Processor->>Analytics: Update Metrics
    Analytics-->>API: Updated Stats
    API-->>Client: Vote Confirmed
```

## Real-time Analytics

### Live Metrics Dashboard

```go
type RealTimeMetrics struct {
    PollID          uint      `json:"poll_id"`
    TotalVotes      int       `json:"total_votes"`
    UniqueVoters    int       `json:"unique_voters"`
    VotesPerMinute  float64   `json:"votes_per_minute"`
    ActiveSessions  int       `json:"active_sessions"`
    LastUpdated     time.Time `json:"last_updated"`
    
    OptionBreakdown []OptionMetrics `json:"option_breakdown"`
    GeographicData  []CountryStats  `json:"geographic_data"`
    DeviceStats     DeviceBreakdown `json:"device_stats"`
}

type OptionMetrics struct {
    OptionID       uint    `json:"option_id"`
    OptionText     string  `json:"option_text"`
    VoteCount      int     `json:"vote_count"`
    Percentage     float64 `json:"percentage"`
    TrendDirection string  `json:"trend_direction"` // up, down, stable
}

type CountryStats struct {
    Country    string  `json:"country"`
    VoteCount  int     `json:"vote_count"`
    Percentage float64 `json:"percentage"`
}

type DeviceBreakdown struct {
    Desktop int `json:"desktop"`
    Mobile  int `json:"mobile"`
    Tablet  int `json:"tablet"`
}
```

### Real-time Processing

```go
func (s *AnalyticsService) ProcessVoteEvent(ctx context.Context, event *VoteEvent) error {
    // Update real-time counters
    if err := s.updateRealTimeCounters(ctx, event); err != nil {
        return err
    }
    
    // Update option statistics
    if err := s.updateOptionStats(ctx, event); err != nil {
        return err
    }
    
    // Update geographic data
    if err := s.updateGeographicStats(ctx, event); err != nil {
        return err
    }
    
    // Update device statistics
    if err := s.updateDeviceStats(ctx, event); err != nil {
        return err
    }
    
    // Trigger real-time notifications
    s.notifyRealTimeUpdate(ctx, event.PollID)
    
    return nil
}
```

## Historical Analytics

### Time Series Data

```sql
-- Daily aggregation
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_votes,
    COUNT(DISTINCT user_id) as unique_voters,
    COUNT(DISTINCT voter_session) as anonymous_votes,
    AVG(TIMESTAMPDIFF(SECOND, poll_view_time, created_at)) as avg_time_to_vote
FROM poll_votes 
WHERE poll_id = ? 
    AND created_at >= ? 
    AND created_at <= ?
GROUP BY DATE(created_at)
ORDER BY date;

-- Hourly patterns
SELECT 
    HOUR(created_at) as hour,
    COUNT(*) as vote_count,
    AVG(COUNT(*)) OVER (ORDER BY HOUR(created_at) ROWS BETWEEN 2 PRECEDING AND 2 FOLLOWING) as moving_avg
FROM poll_votes 
WHERE poll_id = ? 
    AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY HOUR(created_at)
ORDER BY hour;
```

### Trend Analysis

```go
type TrendAnalysis struct {
    PollID        uint                `json:"poll_id"`
    Period        string              `json:"period"` // hourly, daily, weekly
    StartDate     time.Time           `json:"start_date"`
    EndDate       time.Time           `json:"end_date"`
    DataPoints    []TrendDataPoint    `json:"data_points"`
    Summary       TrendSummary        `json:"summary"`
    Predictions   []PredictionPoint   `json:"predictions"`
}

type TrendDataPoint struct {
    Timestamp       time.Time `json:"timestamp"`
    TotalVotes      int       `json:"total_votes"`
    UniqueVoters    int       `json:"unique_voters"`
    PageViews       int       `json:"page_views"`
    ConversionRate  float64   `json:"conversion_rate"`
}

type TrendSummary struct {
    TotalGrowth     float64 `json:"total_growth"`
    AverageDaily    float64 `json:"average_daily"`
    PeakHour        int     `json:"peak_hour"`
    BestDay         string  `json:"best_day"`
    TrendDirection  string  `json:"trend_direction"`
}
```

## Advanced Analytics

### Cohort Analysis

```go
type CohortAnalysis struct {
    PollID      uint                `json:"poll_id"`
    CohortType  string              `json:"cohort_type"` // daily, weekly, monthly
    Cohorts     []CohortData        `json:"cohorts"`
    RetentionMatrix [][]float64     `json:"retention_matrix"`
}

type CohortData struct {
    CohortDate      time.Time `json:"cohort_date"`
    InitialUsers    int       `json:"initial_users"`
    RetentionRates  []float64 `json:"retention_rates"`
}
```

### A/B Testing Analytics

```go
type ABTestResult struct {
    PollID          uint            `json:"poll_id"`
    TestName        string          `json:"test_name"`
    VariantA        TestVariant     `json:"variant_a"`
    VariantB        TestVariant     `json:"variant_b"`
    StatSignificance float64        `json:"statistical_significance"`
    Confidence      float64         `json:"confidence"`
    Winner          string          `json:"winner"` // A, B, or inconclusive
}

type TestVariant struct {
    Name            string  `json:"name"`
    Participants    int     `json:"participants"`
    Conversions     int     `json:"conversions"`
    ConversionRate  float64 `json:"conversion_rate"`
    ConfidenceInterval [2]float64 `json:"confidence_interval"`
}
```

### Predictive Analytics

```go
type PredictiveModel struct {
    PollID              uint                `json:"poll_id"`
    ModelType           string              `json:"model_type"`
    PredictionHorizon   time.Duration       `json:"prediction_horizon"`
    Predictions         []PredictionPoint   `json:"predictions"`
    Accuracy            float64             `json:"accuracy"`
    ConfidenceInterval  [2]float64          `json:"confidence_interval"`
}

type PredictionPoint struct {
    Timestamp       time.Time `json:"timestamp"`
    PredictedVotes  int       `json:"predicted_votes"`
    Probability     float64   `json:"probability"`
    Factors         []string  `json:"factors"`
}
```

## Analytics API

### Real-time Metrics Endpoint

```http
GET /api/v1/polls/{id}/analytics/realtime
Authorization: Bearer {token}
```

Response:
```json
{
  "status": "success",
  "data": {
    "poll_id": 1,
    "total_votes": 1247,
    "unique_voters": 1201,
    "votes_per_minute": 15.3,
    "active_sessions": 47,
    "last_updated": "2024-01-01T12:00:00Z",
    "option_breakdown": [
      {
        "option_id": 1,
        "option_text": "JavaScript",
        "vote_count": 623,
        "percentage": 50.0,
        "trend_direction": "up"
      },
      {
        "option_id": 2,
        "option_text": "Python",
        "vote_count": 374,
        "percentage": 30.0,
        "trend_direction": "stable"
      }
    ],
    "geographic_data": [
      {"country": "US", "vote_count": 450, "percentage": 36.1},
      {"country": "UK", "vote_count": 180, "percentage": 14.4}
    ],
    "device_stats": {
      "desktop": 747,
      "mobile": 450,
      "tablet": 50
    }
  }
}
```

### Historical Analytics Endpoint

```http
GET /api/v1/polls/{id}/analytics/historical?period=daily&start_date=2024-01-01&end_date=2024-01-31
Authorization: Bearer {token}
```

Response:
```json
{
  "status": "success",
  "data": {
    "poll_id": 1,
    "period": "daily",
    "start_date": "2024-01-01",
    "end_date": "2024-01-31",
    "data_points": [
      {
        "timestamp": "2024-01-01T00:00:00Z",
        "total_votes": 45,
        "unique_voters": 43,
        "page_views": 120,
        "conversion_rate": 35.8
      },
      {
        "timestamp": "2024-01-02T00:00:00Z",
        "total_votes": 67,
        "unique_voters": 64,
        "page_views": 180,
        "conversion_rate": 35.6
      }
    ],
    "summary": {
      "total_growth": 15.2,
      "average_daily": 42.3,
      "peak_hour": 14,
      "best_day": "2024-01-15",
      "trend_direction": "up"
    },
    "predictions": [
      {
        "timestamp": "2024-02-01T00:00:00Z",
        "predicted_votes": 75,
        "probability": 0.85,
        "factors": ["weekend_effect", "trending_topic"]
      }
    ]
  }
}
```

## Performance Optimization

### Data Aggregation Strategy

```go
// Pre-computed aggregations
type AggregationJob struct {
    PollID      uint
    Period      string // hourly, daily, weekly
    StartTime   time.Time
    EndTime     time.Time
    Metrics     map[string]interface{}
}

func (s *AnalyticsService) RunAggregationJob(ctx context.Context, job *AggregationJob) error {
    switch job.Period {
    case "hourly":
        return s.aggregateHourlyData(ctx, job)
    case "daily":
        return s.aggregateDailyData(ctx, job)
    case "weekly":
        return s.aggregateWeeklyData(ctx, job)
    default:
        return fmt.Errorf("unsupported period: %s", job.Period)
    }
}
```

### Caching Strategy

```go
type AnalyticsCache struct {
    redis   *redis.Client
    ttl     time.Duration
}

func (c *AnalyticsCache) GetRealTimeMetrics(ctx context.Context, pollID uint) (*RealTimeMetrics, error) {
    key := fmt.Sprintf("poll:analytics:realtime:%d", pollID)
    
    cached, err := c.redis.Get(ctx, key).Result()
    if err == nil {
        var metrics RealTimeMetrics
        if err := json.Unmarshal([]byte(cached), &metrics); err == nil {
            return &metrics, nil
        }
    }
    
    return nil, redis.Nil
}

func (c *AnalyticsCache) SetRealTimeMetrics(ctx context.Context, pollID uint, metrics *RealTimeMetrics) error {
    key := fmt.Sprintf("poll:analytics:realtime:%d", pollID)
    
    data, err := json.Marshal(metrics)
    if err != nil {
        return err
    }
    
    return c.redis.Set(ctx, key, data, c.ttl).Err()
}
```

## Reporting

### Automated Reports

```go
type ReportGenerator struct {
    analytics   *AnalyticsService
    emailService *EmailService
    scheduler   *cron.Cron
}

func (r *ReportGenerator) ScheduleReports() {
    // Daily reports
    r.scheduler.AddFunc("0 9 * * *", r.generateDailyReport)
    
    // Weekly reports
    r.scheduler.AddFunc("0 9 * * 1", r.generateWeeklyReport)
    
    // Monthly reports
    r.scheduler.AddFunc("0 9 1 * *", r.generateMonthlyReport)
}

func (r *ReportGenerator) generateDailyReport() {
    // Generate report for all active polls
    polls, err := r.analytics.GetActivePolls(context.Background())
    if err != nil {
        log.Error("Failed to get active polls", zap.Error(err))
        return
    }
    
    for _, poll := range polls {
        report, err := r.analytics.GenerateDailyReport(context.Background(), poll.ID)
        if err != nil {
            log.Error("Failed to generate daily report", zap.Uint("poll_id", poll.ID), zap.Error(err))
            continue
        }
        
        // Send email report
        if err := r.emailService.SendDailyReport(poll.CreatedBy, report); err != nil {
            log.Error("Failed to send daily report", zap.Uint("poll_id", poll.ID), zap.Error(err))
        }
    }
}
```

### Export Functionality

```go
func (s *AnalyticsService) ExportPollData(ctx context.Context, pollID uint, format string) ([]byte, error) {
    data, err := s.getFullPollData(ctx, pollID)
    if err != nil {
        return nil, err
    }
    
    switch format {
    case "csv":
        return s.exportToCSV(data)
    case "json":
        return s.exportToJSON(data)
    case "xlsx":
        return s.exportToExcel(data)
    default:
        return nil, fmt.Errorf("unsupported format: %s", format)
    }
}
```

## Privacy & Compliance

### Data Anonymization

```go
func (s *AnalyticsService) AnonymizeData(ctx context.Context, pollID uint) error {
    // Remove PII from analytics data
    query := `
        UPDATE poll_analytics 
        SET user_id = NULL, 
            voter_ip = NULL,
            user_agent = NULL 
        WHERE poll_id = ? 
            AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
    `
    
    _, err := s.db.ExecContext(ctx, query, pollID)
    return err
}
```

### GDPR Compliance

```go
func (s *AnalyticsService) DeleteUserData(ctx context.Context, userID uint) error {
    // Remove all user-specific analytics data
    queries := []string{
        "DELETE FROM poll_analytics WHERE user_id = ?",
        "UPDATE poll_votes SET user_id = NULL WHERE user_id = ?",
        "DELETE FROM poll_sessions WHERE user_id = ?",
    }
    
    for _, query := range queries {
        if _, err := s.db.ExecContext(ctx, query, userID); err != nil {
            return err
        }
    }
    
    return nil
}
```
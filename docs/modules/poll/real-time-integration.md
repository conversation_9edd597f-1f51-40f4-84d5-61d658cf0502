# Poll Module - Real-time Integration

## Overview

The Poll module integrates with the WebSocket system to provide real-time voting updates, live poll results, and instant feedback to users. This enables interactive polling experiences with immediate visual feedback.

## WebSocket Integration Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        WebClient[Web Client]
        MobileClient[Mobile Client]
    end
    
    subgraph "WebSocket Layer"
        WSServer[WebSocket Server]
        ConnectionPool[Connection Pool]
        EventDispatcher[Event Dispatcher]
    end
    
    subgraph "Poll Module"
        VoteService[Vote Service]
        PollService[Poll Service]
        EventPublisher[Event Publisher]
    end
    
    subgraph "Message Queue"
        RedisQueue[Redis Queue]
        EventProcessor[Event Processor]
    end
    
    subgraph "Database"
        VoteRepo[Vote Repository]
        PollRepo[Poll Repository]
    end
    
    WebClient --> WSServer
    MobileClient --> WSServer
    WSServer --> ConnectionPool
    ConnectionPool --> EventDispatcher
    
    VoteService --> EventPublisher
    PollService --> EventPublisher
    EventPublisher --> RedisQueue
    RedisQueue --> EventProcessor
    EventProcessor --> EventDispatcher
    
    VoteService --> VoteRepo
    PollService --> PollRepo
    
    EventDispatcher --> WSServer
```

## WebSocket Event Types

### 1. Vote Update Events

#### vote_cast
Triggered when a new vote is submitted.

```json
{
  "type": "vote_cast",
  "poll_id": 1,
  "timestamp": "2024-01-01T12:00:00Z",
  "data": {
    "option_id": 1,
    "vote_count": 76,
    "total_votes": 151,
    "percentage": 50.3
  }
}
```

#### vote_results_update
Triggered when vote counts are updated.

```json
{
  "type": "vote_results_update",
  "poll_id": 1,
  "timestamp": "2024-01-01T12:00:00Z",
  "data": {
    "total_votes": 151,
    "unique_voters": 146,
    "options": [
      {
        "id": 1,
        "vote_count": 76,
        "percentage": 50.3
      },
      {
        "id": 2,
        "vote_count": 45,
        "percentage": 29.8
      },
      {
        "id": 3,
        "vote_count": 30,
        "percentage": 19.9
      }
    ]
  }
}
```

### 2. Poll Status Events

#### poll_status_changed
Triggered when poll status changes (draft → active, active → ended, etc.).

```json
{
  "type": "poll_status_changed",
  "poll_id": 1,
  "timestamp": "2024-01-01T12:00:00Z",
  "data": {
    "old_status": "draft",
    "new_status": "active",
    "start_date": "2024-01-01T12:00:00Z"
  }
}
```

#### poll_ended
Triggered when poll reaches its end date or is manually ended.

```json
{
  "type": "poll_ended",
  "poll_id": 1,
  "timestamp": "2024-01-01T23:59:59Z",
  "data": {
    "end_date": "2024-01-01T23:59:59Z",
    "total_votes": 500,
    "unique_voters": 485,
    "winning_option": {
      "id": 1,
      "option_text": "JavaScript",
      "vote_count": 250,
      "percentage": 50.0
    }
  }
}
```

### 3. Poll Configuration Events

#### option_added
Triggered when a new option is added to an active poll.

```json
{
  "type": "option_added",
  "poll_id": 1,
  "timestamp": "2024-01-01T12:00:00Z",
  "data": {
    "option": {
      "id": 4,
      "option_text": "TypeScript",
      "option_order": 4,
      "vote_count": 0
    }
  }
}
```

#### option_removed
Triggered when an option is removed from a poll.

```json
{
  "type": "option_removed",
  "poll_id": 1,
  "timestamp": "2024-01-01T12:00:00Z",
  "data": {
    "option_id": 4,
    "redistributed_votes": 5
  }
}
```

## Connection Management

### Subscription Model

Clients subscribe to specific poll channels to receive updates:

```javascript
// Subscribe to poll updates
const pollChannel = `poll:${pollId}`;
ws.send(JSON.stringify({
  type: 'subscribe',
  channel: pollChannel
}));

// Subscribe to multiple polls
const channels = ['poll:1', 'poll:2', 'poll:3'];
ws.send(JSON.stringify({
  type: 'subscribe_multiple',
  channels: channels
}));
```

### Connection Lifecycle

```mermaid
sequenceDiagram
    participant Client
    participant WSServer
    participant ConnectionPool
    participant EventDispatcher
    participant PollService
    
    Client->>WSServer: WebSocket Connection
    WSServer->>ConnectionPool: Register Connection
    ConnectionPool-->>WSServer: Connection Registered
    
    Client->>WSServer: Subscribe to poll:1
    WSServer->>EventDispatcher: Add to poll:1 channel
    EventDispatcher-->>WSServer: Subscription Active
    
    PollService->>EventDispatcher: Broadcast vote update
    EventDispatcher->>WSServer: Send to poll:1 subscribers
    WSServer->>Client: Vote update event
    
    Client->>WSServer: Unsubscribe from poll:1
    WSServer->>EventDispatcher: Remove from poll:1 channel
    
    Client->>WSServer: Close Connection
    WSServer->>ConnectionPool: Cleanup Connection
```

## Event Publishing

### Vote Service Integration

```go
// In vote service
func (s *VoteService) SubmitVote(ctx context.Context, req *SubmitVoteRequest) (*Vote, error) {
    // Process vote
    vote, err := s.repository.CreateVote(ctx, req)
    if err != nil {
        return nil, err
    }
    
    // Update vote counts
    updatedCounts, err := s.updateVoteCounts(ctx, req.PollID)
    if err != nil {
        return nil, err
    }
    
    // Publish real-time event
    event := &VoteUpdateEvent{
        Type:      "vote_cast",
        PollID:    req.PollID,
        Timestamp: time.Now(),
        Data: VoteUpdateData{
            OptionID:   req.OptionID,
            VoteCount:  updatedCounts.OptionCounts[req.OptionID],
            TotalVotes: updatedCounts.TotalVotes,
            Percentage: updatedCounts.Percentages[req.OptionID],
        },
    }
    
    s.eventPublisher.Publish(ctx, event)
    
    return vote, nil
}
```

### Event Publisher Implementation

```go
type EventPublisher struct {
    redisClient *redis.Client
    logger      *zap.Logger
}

func (p *EventPublisher) Publish(ctx context.Context, event interface{}) error {
    eventData, err := json.Marshal(event)
    if err != nil {
        return err
    }
    
    channel := fmt.Sprintf("poll:%d", event.PollID)
    
    return p.redisClient.Publish(ctx, channel, eventData).Err()
}
```

## Client-Side Integration

### JavaScript WebSocket Client

```javascript
class PollWebSocketClient {
    constructor(pollId) {
        this.pollId = pollId;
        this.ws = null;
        this.subscribers = new Map();
        this.connect();
    }
    
    connect() {
        this.ws = new WebSocket(`ws://localhost:8080/ws`);
        
        this.ws.onopen = () => {
            console.log('Connected to poll WebSocket');
            this.subscribe(this.pollId);
        };
        
        this.ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
        };
        
        this.ws.onclose = () => {
            console.log('Disconnected from poll WebSocket');
            setTimeout(() => this.connect(), 1000); // Reconnect
        };
    }
    
    subscribe(pollId) {
        const message = {
            type: 'subscribe',
            channel: `poll:${pollId}`
        };
        this.ws.send(JSON.stringify(message));
    }
    
    handleMessage(data) {
        switch (data.type) {
            case 'vote_cast':
                this.handleVoteCast(data);
                break;
            case 'vote_results_update':
                this.handleResultsUpdate(data);
                break;
            case 'poll_status_changed':
                this.handleStatusChange(data);
                break;
            case 'poll_ended':
                this.handlePollEnded(data);
                break;
        }
    }
    
    handleVoteCast(data) {
        // Update UI with new vote
        this.updateOptionVoteCount(data.data.option_id, data.data.vote_count);
        this.updateTotalVotes(data.data.total_votes);
        this.animateVoteUpdate(data.data.option_id);
    }
    
    handleResultsUpdate(data) {
        // Update all option counts and percentages
        data.data.options.forEach(option => {
            this.updateOptionDisplay(option.id, option.vote_count, option.percentage);
        });
        this.updateTotalVotes(data.data.total_votes);
    }
    
    handleStatusChange(data) {
        // Update poll status display
        this.updatePollStatus(data.data.new_status);
        
        if (data.data.new_status === 'active') {
            this.enableVoting();
        } else if (data.data.new_status === 'ended') {
            this.disableVoting();
        }
    }
    
    handlePollEnded(data) {
        // Show final results
        this.disableVoting();
        this.showFinalResults(data.data);
        this.highlightWinningOption(data.data.winning_option);
    }
    
    // UI update methods
    updateOptionVoteCount(optionId, voteCount) {
        const element = document.querySelector(`#option-${optionId} .vote-count`);
        if (element) {
            element.textContent = voteCount;
        }
    }
    
    updateTotalVotes(totalVotes) {
        const element = document.querySelector('#total-votes');
        if (element) {
            element.textContent = totalVotes;
        }
    }
    
    animateVoteUpdate(optionId) {
        const element = document.querySelector(`#option-${optionId}`);
        if (element) {
            element.classList.add('vote-animation');
            setTimeout(() => {
                element.classList.remove('vote-animation');
            }, 500);
        }
    }
}

// Usage
const pollClient = new PollWebSocketClient(1);
```

### React Hook Example

```javascript
import { useState, useEffect, useRef } from 'react';

export const usePollWebSocket = (pollId) => {
    const [pollData, setPollData] = useState(null);
    const [isConnected, setIsConnected] = useState(false);
    const wsRef = useRef(null);
    
    useEffect(() => {
        const ws = new WebSocket(`ws://localhost:8080/ws`);
        wsRef.current = ws;
        
        ws.onopen = () => {
            setIsConnected(true);
            ws.send(JSON.stringify({
                type: 'subscribe',
                channel: `poll:${pollId}`
            }));
        };
        
        ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            handleWebSocketMessage(data);
        };
        
        ws.onclose = () => {
            setIsConnected(false);
        };
        
        return () => {
            ws.close();
        };
    }, [pollId]);
    
    const handleWebSocketMessage = (data) => {
        switch (data.type) {
            case 'vote_results_update':
                setPollData(prevData => ({
                    ...prevData,
                    options: data.data.options,
                    total_votes: data.data.total_votes,
                    unique_voters: data.data.unique_voters
                }));
                break;
            case 'poll_status_changed':
                setPollData(prevData => ({
                    ...prevData,
                    status: data.data.new_status
                }));
                break;
        }
    };
    
    return { pollData, isConnected };
};
```

## Performance Optimization

### Connection Pooling

```go
type ConnectionPool struct {
    connections map[string][]*websocket.Conn
    mutex       sync.RWMutex
}

func (p *ConnectionPool) AddConnection(channel string, conn *websocket.Conn) {
    p.mutex.Lock()
    defer p.mutex.Unlock()
    
    if p.connections[channel] == nil {
        p.connections[channel] = make([]*websocket.Conn, 0)
    }
    
    p.connections[channel] = append(p.connections[channel], conn)
}

func (p *ConnectionPool) BroadcastToChannel(channel string, message []byte) {
    p.mutex.RLock()
    defer p.mutex.RUnlock()
    
    connections := p.connections[channel]
    for _, conn := range connections {
        conn.WriteMessage(websocket.TextMessage, message)
    }
}
```

### Message Batching

```go
type MessageBatcher struct {
    messages chan *WebSocketMessage
    batchSize int
    flushInterval time.Duration
}

func (b *MessageBatcher) Start() {
    ticker := time.NewTicker(b.flushInterval)
    batch := make([]*WebSocketMessage, 0, b.batchSize)
    
    for {
        select {
        case msg := <-b.messages:
            batch = append(batch, msg)
            if len(batch) >= b.batchSize {
                b.flushBatch(batch)
                batch = batch[:0]
            }
        case <-ticker.C:
            if len(batch) > 0 {
                b.flushBatch(batch)
                batch = batch[:0]
            }
        }
    }
}
```

## Security Considerations

### Authentication

```javascript
// Include JWT token in WebSocket connection
const token = localStorage.getItem('jwt_token');
const ws = new WebSocket(`ws://localhost:8080/ws?token=${token}`);
```

### Rate Limiting

```go
type RateLimiter struct {
    connections map[string]*TokenBucket
    mutex       sync.RWMutex
}

func (r *RateLimiter) CheckLimit(connectionId string) bool {
    r.mutex.RLock()
    bucket, exists := r.connections[connectionId]
    r.mutex.RUnlock()
    
    if !exists {
        r.mutex.Lock()
        bucket = NewTokenBucket(10, time.Second) // 10 messages per second
        r.connections[connectionId] = bucket
        r.mutex.Unlock()
    }
    
    return bucket.Allow()
}
```

### Message Validation

```go
func (h *WebSocketHandler) ValidateMessage(msg *WebSocketMessage) error {
    // Validate message format
    if msg.Type == "" {
        return errors.New("message type is required")
    }
    
    // Validate channel access
    if strings.HasPrefix(msg.Channel, "poll:") {
        pollId := extractPollId(msg.Channel)
        if !h.pollService.CanAccessPoll(h.userCtx, pollId) {
            return errors.New("access denied to poll")
        }
    }
    
    return nil
}
```

## Error Handling

### Connection Recovery

```javascript
class ResilientWebSocketClient {
    constructor(url) {
        this.url = url;
        this.reconnectInterval = 1000;
        this.maxReconnectAttempts = 10;
        this.reconnectAttempts = 0;
        this.connect();
    }
    
    connect() {
        try {
            this.ws = new WebSocket(this.url);
            
            this.ws.onopen = () => {
                console.log('WebSocket connected');
                this.reconnectAttempts = 0;
                this.onConnected();
            };
            
            this.ws.onclose = () => {
                console.log('WebSocket disconnected');
                this.reconnect();
            };
            
            this.ws.onerror = (error) => {
                console.error('WebSocket error:', error);
            };
            
        } catch (error) {
            console.error('Failed to connect:', error);
            this.reconnect();
        }
    }
    
    reconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            setTimeout(() => {
                console.log(`Reconnecting... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
                this.connect();
            }, this.reconnectInterval * this.reconnectAttempts);
        }
    }
}
```

## Monitoring & Metrics

### WebSocket Metrics

```go
type WebSocketMetrics struct {
    ActiveConnections  prometheus.Gauge
    MessagesSent      prometheus.Counter
    MessagesReceived  prometheus.Counter
    ConnectionErrors  prometheus.Counter
    MessageLatency    prometheus.Histogram
}

func (m *WebSocketMetrics) RecordMessage(messageType string, processingTime time.Duration) {
    m.MessagesSent.Inc()
    m.MessageLatency.Observe(processingTime.Seconds())
}
```

### Health Checks

```go
func (h *WebSocketHandler) HealthCheck() error {
    // Check Redis connection
    if err := h.redisClient.Ping().Err(); err != nil {
        return fmt.Errorf("redis connection failed: %w", err)
    }
    
    // Check active connections
    activeConns := h.connectionPool.GetActiveCount()
    if activeConns > h.maxConnections {
        return fmt.Errorf("too many active connections: %d", activeConns)
    }
    
    return nil
}
```
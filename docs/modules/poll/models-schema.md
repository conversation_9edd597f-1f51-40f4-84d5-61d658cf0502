# Poll Module - Database Schema

## Overview

The Poll module uses a normalized database schema designed for multi-tenant isolation, performance, and scalability. All tables include tenant_id for proper data isolation and follow the project's MySQL 8 conventions.

## Database Tables

### 1. polls

Main poll entity table containing poll metadata and configuration.

```sql
CREATE TABLE IF NOT EXISTS polls (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    created_by INT UNSIGNED NOT NULL,
    
    -- Poll metadata
    title VARCHAR(255) NOT NULL,
    description TEXT,
    slug VARCHAR(255) NOT NULL,
    
    -- Poll configuration
    poll_type ENUM('single_choice', 'multiple_choice', 'ranked') NOT NULL DEFAULT 'single_choice',
    max_selections INT UNSIGNED DEFAULT 1,
    allow_anonymous BOOLEAN DEFAULT false,
    require_authentication BOOLEAN DEFAULT true,
    
    -- Scheduling
    start_date TIMESTAMP NULL,
    end_date TIMESTAMP NULL,
    
    -- Status and settings
    status ENUM('draft', 'active', 'scheduled', 'ended', 'archived', 'deleted') NOT NULL DEFAULT 'draft',
    is_featured BOOLEAN DEFAULT false,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_polls_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_polls_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_polls_created_by FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Unique Constraints
    UNIQUE KEY uk_polls_tenant_slug (tenant_id, slug),
    
    -- Indexes
    INDEX idx_polls_tenant_id (tenant_id),
    INDEX idx_polls_website_id (website_id),
    INDEX idx_polls_created_by (created_by),
    INDEX idx_polls_status (status),
    INDEX idx_polls_tenant_status (tenant_id, status),
    INDEX idx_polls_tenant_website (tenant_id, website_id),
    INDEX idx_polls_start_date (start_date),
    INDEX idx_polls_end_date (end_date),
    INDEX idx_polls_featured (is_featured)
);
```

### 2. poll_options

Poll answer options that users can vote for.

```sql
CREATE TABLE IF NOT EXISTS poll_options (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    poll_id INT UNSIGNED NOT NULL,
    
    -- Option content
    option_text TEXT NOT NULL,
    option_order INT UNSIGNED NOT NULL,
    
    -- Media support
    image_url VARCHAR(500),
    
    -- Vote tracking
    vote_count INT UNSIGNED DEFAULT 0,
    
    -- Status
    status ENUM('active', 'inactive', 'deleted') NOT NULL DEFAULT 'active',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_poll_options_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_poll_options_poll_id FOREIGN KEY (poll_id) REFERENCES polls(id) ON DELETE CASCADE,
    
    -- Unique Constraints
    UNIQUE KEY uk_poll_options_tenant_poll_order (tenant_id, poll_id, option_order),
    
    -- Indexes
    INDEX idx_poll_options_tenant_id (tenant_id),
    INDEX idx_poll_options_poll_id (poll_id),
    INDEX idx_poll_options_tenant_poll (tenant_id, poll_id),
    INDEX idx_poll_options_order (option_order),
    INDEX idx_poll_options_status (status),
    INDEX idx_poll_options_vote_count (vote_count)
);
```

### 3. poll_votes

Individual vote records for tracking user participation.

```sql
CREATE TABLE IF NOT EXISTS poll_votes (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    poll_id INT UNSIGNED NOT NULL,
    option_id INT UNSIGNED NOT NULL,
    
    -- Voter information
    user_id INT UNSIGNED NULL, -- NULL for anonymous votes
    voter_ip VARCHAR(45), -- IPv4/IPv6 support
    voter_session VARCHAR(255), -- Session identifier for anonymous votes
    
    -- Vote metadata
    vote_rank INT UNSIGNED NULL, -- For ranked polls
    vote_weight DECIMAL(3,2) DEFAULT 1.00, -- For weighted voting
    
    -- Tracking
    user_agent TEXT,
    referer VARCHAR(500),
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_poll_votes_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_poll_votes_poll_id FOREIGN KEY (poll_id) REFERENCES polls(id) ON DELETE CASCADE,
    CONSTRAINT fk_poll_votes_option_id FOREIGN KEY (option_id) REFERENCES poll_options(id) ON DELETE CASCADE,
    CONSTRAINT fk_poll_votes_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Unique Constraints (prevent duplicate votes)
    UNIQUE KEY uk_poll_votes_tenant_poll_user (tenant_id, poll_id, user_id),
    UNIQUE KEY uk_poll_votes_tenant_poll_session (tenant_id, poll_id, voter_session),
    
    -- Indexes
    INDEX idx_poll_votes_tenant_id (tenant_id),
    INDEX idx_poll_votes_poll_id (poll_id),
    INDEX idx_poll_votes_option_id (option_id),
    INDEX idx_poll_votes_user_id (user_id),
    INDEX idx_poll_votes_tenant_poll (tenant_id, poll_id),
    INDEX idx_poll_votes_voter_ip (voter_ip),
    INDEX idx_poll_votes_created_at (created_at)
);
```

### 4. poll_analytics

Aggregate analytics data for poll performance tracking.

```sql
CREATE TABLE IF NOT EXISTS poll_analytics (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    poll_id INT UNSIGNED NOT NULL,
    
    -- Analytics period
    date_recorded DATE NOT NULL,
    hour_recorded TINYINT UNSIGNED, -- 0-23 for hourly analytics
    
    -- Metrics
    total_votes INT UNSIGNED DEFAULT 0,
    unique_voters INT UNSIGNED DEFAULT 0,
    anonymous_votes INT UNSIGNED DEFAULT 0,
    authenticated_votes INT UNSIGNED DEFAULT 0,
    
    -- Engagement metrics
    page_views INT UNSIGNED DEFAULT 0,
    completion_rate DECIMAL(5,2) DEFAULT 0.00,
    avg_time_to_vote INT UNSIGNED DEFAULT 0, -- seconds
    
    -- Geographic data
    top_countries JSON DEFAULT (JSON_ARRAY()),
    device_types JSON DEFAULT (JSON_OBJECT()),
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_poll_analytics_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_poll_analytics_poll_id FOREIGN KEY (poll_id) REFERENCES polls(id) ON DELETE CASCADE,
    
    -- Unique Constraints
    UNIQUE KEY uk_poll_analytics_tenant_poll_date_hour (tenant_id, poll_id, date_recorded, hour_recorded),
    
    -- Indexes
    INDEX idx_poll_analytics_tenant_id (tenant_id),
    INDEX idx_poll_analytics_poll_id (poll_id),
    INDEX idx_poll_analytics_date_recorded (date_recorded),
    INDEX idx_poll_analytics_tenant_poll (tenant_id, poll_id),
    INDEX idx_poll_analytics_tenant_date (tenant_id, date_recorded)
);
```

### 5. poll_sessions

Session tracking for anonymous voting and fraud prevention.

```sql
CREATE TABLE IF NOT EXISTS poll_sessions (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    poll_id INT UNSIGNED NOT NULL,
    
    -- Session information
    session_id VARCHAR(255) NOT NULL,
    user_id INT UNSIGNED NULL,
    
    -- Tracking data
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    device_fingerprint VARCHAR(255),
    
    -- Session metadata
    votes_count INT UNSIGNED DEFAULT 0,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Status
    status ENUM('active', 'completed', 'flagged', 'blocked') NOT NULL DEFAULT 'active',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_poll_sessions_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_poll_sessions_poll_id FOREIGN KEY (poll_id) REFERENCES polls(id) ON DELETE CASCADE,
    CONSTRAINT fk_poll_sessions_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Unique Constraints
    UNIQUE KEY uk_poll_sessions_tenant_poll_session (tenant_id, poll_id, session_id),
    
    -- Indexes
    INDEX idx_poll_sessions_tenant_id (tenant_id),
    INDEX idx_poll_sessions_poll_id (poll_id),
    INDEX idx_poll_sessions_session_id (session_id),
    INDEX idx_poll_sessions_user_id (user_id),
    INDEX idx_poll_sessions_ip_address (ip_address),
    INDEX idx_poll_sessions_last_activity (last_activity),
    INDEX idx_poll_sessions_status (status)
);
```

## Key Design Principles

### 1. Multi-Tenant Isolation
- All tables include `tenant_id` with foreign key constraints
- Composite indexes starting with `tenant_id` for performance
- Unique constraints include `tenant_id` for proper isolation

### 2. Performance Optimization
- Strategic indexing on frequently queried columns
- Separate analytics table for heavy aggregation queries
- Vote count caching in poll_options table

### 3. Data Integrity
- Foreign key constraints with appropriate cascade behavior
- Unique constraints to prevent duplicate votes
- Enum fields for controlled status values

### 4. Scalability Features
- Separate session tracking for anonymous voting
- JSON fields for flexible metadata storage
- Efficient vote counting with cached aggregates

### 5. Security Considerations
- IP address tracking for fraud prevention
- Session-based anonymous voting
- Device fingerprinting support
- Vote weight support for future weighted voting

## Migration Files

Following the project's migration naming convention:

- `k_poll/1001_create_polls_table.up.sql`
- `k_poll/1002_create_poll_options_table.up.sql`
- `k_poll/1003_create_poll_votes_table.up.sql`
- `k_poll/1004_create_poll_analytics_table.up.sql`
- `k_poll/1005_create_poll_sessions_table.up.sql`

## Data Model Relationships

```
tenants (1) ──────────── (n) polls
websites (1) ──────────── (n) polls
users (1) ────────────── (n) polls (created_by)
users (1) ────────────── (n) poll_votes (user_id)

polls (1) ─────────────── (n) poll_options
polls (1) ─────────────── (n) poll_votes
polls (1) ─────────────── (n) poll_analytics
polls (1) ─────────────── (n) poll_sessions

poll_options (1) ──────── (n) poll_votes
```

## Status Enums

### Poll Status
- `draft`: Poll is being created/edited
- `active`: Poll is live and accepting votes
- `scheduled`: Poll is scheduled for future activation
- `ended`: Poll has reached its end date
- `archived`: Poll is archived but data preserved
- `deleted`: Poll is soft-deleted

### Poll Types
- `single_choice`: Users can select one option
- `multiple_choice`: Users can select multiple options
- `ranked`: Users can rank options in order of preference

### Session Status
- `active`: Session is active and can vote
- `completed`: Session has completed voting
- `flagged`: Session flagged for suspicious activity
- `blocked`: Session is blocked from voting
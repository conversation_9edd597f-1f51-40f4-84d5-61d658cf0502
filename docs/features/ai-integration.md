# AI Integration trong Blog API v3

## Tổng quan

Document này mô tả cách tích hợp AI vào hệ thống Blog API v3 để cung cấp các tính năng thông minh như AI-powered content generation, intelligent chatbots, automated web design, và content optimization.

## Kiến trúc AI Module

### C<PERSON>u trúc thư mục
```
internal/modules/ai/
├── models/
│   ├── ai_model.go              # AI model configurations
│   ├── ai_request.go            # Request tracking
│   ├── ai_response.go           # Response caching
│   ├── content_generation.go    # Content generation models
│   └── chat_session.go          # Chat session management
├── repositories/
│   ├── ai_model_repository.go   # AI model data access
│   ├── chat_repository.go       # Chat history
│   └── generation_repository.go # Content generation history
├── services/
│   ├── ai_service.go            # Core AI orchestration
│   ├── content_service.go       # Content generation
│   ├── chat_service.go          # Chat functionality
│   ├── design_service.go        # Web design generation
│   └── optimization_service.go  # Content optimization
├── handlers/
│   ├── ai_handler.go            # AI management endpoints
│   ├── content_handler.go       # Content generation endpoints
│   ├── chat_handler.go          # Chat endpoints
│   └── design_handler.go        # Design generation endpoints
├── providers/
│   ├── openai_provider.go       # OpenAI integration
│   ├── anthropic_provider.go    # Anthropic Claude integration
│   ├── gemini_provider.go       # Google Gemini integration
│   └── ollama_provider.go       # Local AI models
├── middleware/
│   ├── ai_quota.go              # AI usage quota management
│   ├── ai_cache.go              # AI response caching
│   └── ai_rate_limit.go         # AI-specific rate limiting
└── utils/
    ├── prompt_templates.go      # Prompt engineering templates
    ├── content_parser.go        # AI response parsing
    └── ai_metrics.go            # AI performance metrics
```

## Database Schema

### AI Models Configuration
```sql
-- AI Models và Configurations
CREATE TABLE ai_models (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    name VARCHAR(100) NOT NULL,
    provider ENUM('openai', 'anthropic', 'gemini', 'ollama', 'custom') NOT NULL,
    model_id VARCHAR(100) NOT NULL, -- gpt-4, claude-3-opus, etc.
    capabilities JSON DEFAULT (JSON_ARRAY()), -- ['text_generation', 'image_generation', 'chat']
    configuration JSON DEFAULT (JSON_OBJECT()),
    api_key_encrypted TEXT,
    status ENUM('active', 'inactive', 'error') DEFAULT 'active',
    usage_limits JSON DEFAULT (JSON_OBJECT()), -- daily/monthly limits
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    INDEX idx_tenant_provider (tenant_id, provider),
    INDEX idx_status (status)
);

-- AI Requests Tracking
CREATE TABLE ai_requests (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED,
    model_id INT UNSIGNED NOT NULL,
    request_type ENUM('content_generation', 'chat', 'design', 'optimization', 'analysis') NOT NULL,
    prompt_text TEXT NOT NULL,
    response_text LONGTEXT,
    tokens_used INT DEFAULT 0,
    processing_time_ms INT DEFAULT 0,
    cost_cents INT DEFAULT 0,
    status ENUM('pending', 'completed', 'failed', 'timeout') DEFAULT 'pending',
    error_message TEXT,
    metadata JSON DEFAULT (JSON_OBJECT()),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (model_id) REFERENCES ai_models(id) ON DELETE CASCADE,
    INDEX idx_tenant_type (tenant_id, request_type),
    INDEX idx_user_requests (user_id, created_at),
    INDEX idx_model_usage (model_id, created_at)
);

-- Chat Sessions
CREATE TABLE ai_chat_sessions (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED,
    title VARCHAR(255) NOT NULL,
    model_id INT UNSIGNED NOT NULL,
    context JSON DEFAULT (JSON_OBJECT()), -- conversation context
    system_prompt TEXT,
    status ENUM('active', 'archived', 'deleted') DEFAULT 'active',
    message_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (model_id) REFERENCES ai_models(id) ON DELETE CASCADE,
    INDEX idx_tenant_user (tenant_id, user_id),
    INDEX idx_status (status)
);

-- Chat Messages
CREATE TABLE ai_chat_messages (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    session_id INT UNSIGNED NOT NULL,
    role ENUM('user', 'assistant', 'system') NOT NULL,
    content TEXT NOT NULL,
    metadata JSON DEFAULT (JSON_OBJECT()),
    tokens_used INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (session_id) REFERENCES ai_chat_sessions(id) ON DELETE CASCADE,
    INDEX idx_session_messages (session_id, created_at)
);

-- Content Generation Templates
CREATE TABLE ai_content_templates (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    content_type ENUM('blog_post', 'article', 'social_media', 'email', 'product_description') NOT NULL,
    prompt_template TEXT NOT NULL,
    parameters JSON DEFAULT (JSON_ARRAY()), -- template parameters
    example_output TEXT,
    usage_count INT DEFAULT 0,
    status ENUM('active', 'draft', 'archived') DEFAULT 'active',
    created_by INT UNSIGNED,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_tenant_type (tenant_id, content_type),
    INDEX idx_status (status)
);

-- AI Generated Content
CREATE TABLE ai_generated_content (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED,
    template_id INT UNSIGNED,
    content_type ENUM('blog_post', 'article', 'social_media', 'email', 'product_description') NOT NULL,
    title VARCHAR(500),
    content LONGTEXT NOT NULL,
    metadata JSON DEFAULT (JSON_OBJECT()),
    quality_score DECIMAL(3,2), -- AI quality assessment
    used_in_post_id INT UNSIGNED, -- reference to blog post if used
    status ENUM('draft', 'approved', 'rejected', 'published') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (template_id) REFERENCES ai_content_templates(id) ON DELETE SET NULL,
    INDEX idx_tenant_type (tenant_id, content_type),
    INDEX idx_user_content (user_id, created_at),
    INDEX idx_status (status)
);

-- Web Design Templates
CREATE TABLE ai_web_designs (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    design_type ENUM('landing_page', 'blog_layout', 'product_page', 'portfolio', 'business_card') NOT NULL,
    html_content LONGTEXT,
    css_content LONGTEXT,
    js_content LONGTEXT,
    design_config JSON DEFAULT (JSON_OBJECT()), -- colors, fonts, layout preferences
    preview_image_url VARCHAR(500),
    usage_count INT DEFAULT 0,
    status ENUM('active', 'draft', 'archived') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_tenant_type (tenant_id, design_type),
    INDEX idx_status (status)
);
```

## Core AI Services

### 1. AI Service Interface
```go
// internal/modules/ai/services/interface.go
package services

import (
    "context"
    "github.com/tranthanhloi/wn-api-v3/internal/modules/ai/models"
)

type AIService interface {
    // Model Management
    CreateModel(ctx context.Context, req *models.CreateModelRequest) (*models.AIModel, error)
    GetModel(ctx context.Context, tenantID, modelID uint) (*models.AIModel, error)
    ListModels(ctx context.Context, tenantID uint) ([]*models.AIModel, error)
    UpdateModel(ctx context.Context, req *models.UpdateModelRequest) error
    DeleteModel(ctx context.Context, tenantID, modelID uint) error
    
    // Usage Tracking
    TrackUsage(ctx context.Context, req *models.UsageRequest) error
    GetUsageStats(ctx context.Context, tenantID uint, period string) (*models.UsageStats, error)
    CheckQuota(ctx context.Context, tenantID uint, requestType string) (*models.QuotaStatus, error)
}

type ContentGenerationService interface {
    // Content Generation
    GenerateContent(ctx context.Context, req *models.ContentGenerationRequest) (*models.GeneratedContent, error)
    GenerateFromTemplate(ctx context.Context, req *models.TemplateGenerationRequest) (*models.GeneratedContent, error)
    
    // Template Management
    CreateTemplate(ctx context.Context, req *models.CreateTemplateRequest) (*models.ContentTemplate, error)
    GetTemplate(ctx context.Context, tenantID, templateID uint) (*models.ContentTemplate, error)
    ListTemplates(ctx context.Context, tenantID uint, contentType string) ([]*models.ContentTemplate, error)
    
    // Content Optimization
    OptimizeContent(ctx context.Context, req *models.OptimizationRequest) (*models.OptimizedContent, error)
    AnalyzeContent(ctx context.Context, req *models.AnalysisRequest) (*models.ContentAnalysis, error)
}

type ChatService interface {
    // Chat Sessions
    CreateChatSession(ctx context.Context, req *models.CreateChatSessionRequest) (*models.ChatSession, error)
    GetChatSession(ctx context.Context, tenantID, sessionID uint) (*models.ChatSession, error)
    ListChatSessions(ctx context.Context, tenantID, userID uint) ([]*models.ChatSession, error)
    
    // Chat Messages
    SendMessage(ctx context.Context, req *models.ChatMessageRequest) (*models.ChatMessage, error)
    GetChatHistory(ctx context.Context, sessionID uint, limit int) ([]*models.ChatMessage, error)
    
    // Session Management
    ArchiveSession(ctx context.Context, tenantID, sessionID uint) error
    DeleteSession(ctx context.Context, tenantID, sessionID uint) error
}

type WebDesignService interface {
    // Design Generation
    GenerateDesign(ctx context.Context, req *models.DesignGenerationRequest) (*models.WebDesign, error)
    GenerateComponent(ctx context.Context, req *models.ComponentGenerationRequest) (*models.DesignComponent, error)
    
    // Design Management
    SaveDesign(ctx context.Context, req *models.SaveDesignRequest) (*models.WebDesign, error)
    GetDesign(ctx context.Context, tenantID, designID uint) (*models.WebDesign, error)
    ListDesigns(ctx context.Context, tenantID uint, designType string) ([]*models.WebDesign, error)
    
    // Design Optimization
    OptimizeDesign(ctx context.Context, req *models.DesignOptimizationRequest) (*models.OptimizedDesign, error)
    PreviewDesign(ctx context.Context, designID uint) (*models.DesignPreview, error)
}
```

### 2. AI Provider Interface
```go
// internal/modules/ai/providers/interface.go
package providers

import (
    "context"
    "github.com/tranthanhloi/wn-api-v3/internal/modules/ai/models"
)

type AIProvider interface {
    // Provider Info
    GetProviderInfo() *models.ProviderInfo
    
    // Text Generation
    GenerateText(ctx context.Context, req *models.TextGenerationRequest) (*models.TextGenerationResponse, error)
    GenerateChat(ctx context.Context, req *models.ChatCompletionRequest) (*models.ChatCompletionResponse, error)
    
    // Content Analysis
    AnalyzeContent(ctx context.Context, req *models.ContentAnalysisRequest) (*models.ContentAnalysisResponse, error)
    
    // Streaming Support
    GenerateTextStream(ctx context.Context, req *models.TextGenerationRequest) (<-chan *models.StreamResponse, error)
    
    // Cost Calculation
    CalculateCost(tokens int, modelType string) (costCents int, err error)
    
    // Model Validation
    ValidateModel(modelID string) error
    ListAvailableModels() []*models.ModelInfo
}

// Provider-specific implementations
type OpenAIProvider struct {
    apiKey string
    baseURL string
    client *openai.Client
}

type AnthropicProvider struct {
    apiKey string
    baseURL string
    client *anthropic.Client
}

type GeminiProvider struct {
    apiKey string
    client *genai.Client
}

type OllamaProvider struct {
    baseURL string
    client *ollama.Client
}
```

## API Endpoints

### 1. AI Model Management
```go
// POST /api/cms/v1/ai/models
// Create new AI model configuration
type CreateModelRequest struct {
    TenantID      uint                   `json:"tenant_id" validate:"required"`
    Name          string                 `json:"name" validate:"required,min=1,max=100"`
    Provider      string                 `json:"provider" validate:"required,oneof=openai anthropic gemini ollama"`
    ModelID       string                 `json:"model_id" validate:"required"`
    Capabilities  []string               `json:"capabilities" validate:"required"`
    Configuration map[string]interface{} `json:"configuration"`
    APIKey        string                 `json:"api_key,omitempty"`
    UsageLimits   map[string]interface{} `json:"usage_limits"`
}

// GET /api/cms/v1/ai/models
// List AI models for tenant
type ListModelsResponse struct {
    Models []AIModel `json:"models"`
    Total  int       `json:"total"`
}

// GET /api/cms/v1/ai/models/{id}
// Get specific AI model
type GetModelResponse struct {
    Model AIModel `json:"model"`
}

// PUT /api/cms/v1/ai/models/{id}
// Update AI model configuration
type UpdateModelRequest struct {
    Name          string                 `json:"name,omitempty"`
    Configuration map[string]interface{} `json:"configuration,omitempty"`
    APIKey        string                 `json:"api_key,omitempty"`
    UsageLimits   map[string]interface{} `json:"usage_limits,omitempty"`
    Status        string                 `json:"status,omitempty"`
}

// DELETE /api/cms/v1/ai/models/{id}
// Delete AI model
```

### 2. Content Generation APIs
```go
// POST /api/cms/v1/ai/content/generate
// Generate content using AI
type ContentGenerationRequest struct {
    TenantID    uint                   `json:"tenant_id" validate:"required"`
    UserID      uint                   `json:"user_id,omitempty"`
    ModelID     uint                   `json:"model_id" validate:"required"`
    ContentType string                 `json:"content_type" validate:"required,oneof=blog_post article social_media email product_description"`
    Prompt      string                 `json:"prompt" validate:"required,min=10"`
    Parameters  map[string]interface{} `json:"parameters"`
    Template    string                 `json:"template,omitempty"`
}

type GeneratedContentResponse struct {
    ID           uint                   `json:"id"`
    Title        string                 `json:"title"`
    Content      string                 `json:"content"`
    Metadata     map[string]interface{} `json:"metadata"`
    QualityScore float64                `json:"quality_score"`
    TokensUsed   int                    `json:"tokens_used"`
    CostCents    int                    `json:"cost_cents"`
}

// POST /api/cms/v1/ai/content/templates
// Create content template
type CreateTemplateRequest struct {
    TenantID        uint     `json:"tenant_id" validate:"required"`
    Name            string   `json:"name" validate:"required,min=1,max=255"`
    Description     string   `json:"description"`
    ContentType     string   `json:"content_type" validate:"required"`
    PromptTemplate  string   `json:"prompt_template" validate:"required"`
    Parameters      []string `json:"parameters"`
    ExampleOutput   string   `json:"example_output"`
}

// GET /api/cms/v1/ai/content/templates
// List content templates
type ListTemplatesResponse struct {
    Templates []ContentTemplate `json:"templates"`
    Total     int               `json:"total"`
}

// POST /api/cms/v1/ai/content/optimize
// Optimize existing content
type OptimizationRequest struct {
    TenantID       uint                   `json:"tenant_id" validate:"required"`
    ModelID        uint                   `json:"model_id" validate:"required"`
    Content        string                 `json:"content" validate:"required"`
    OptimizationType string               `json:"optimization_type" validate:"required,oneof=seo readability engagement tone"`
    TargetAudience string                 `json:"target_audience"`
    Parameters     map[string]interface{} `json:"parameters"`
}

type OptimizedContentResponse struct {
    OriginalContent   string                 `json:"original_content"`
    OptimizedContent  string                 `json:"optimized_content"`
    Improvements      []string               `json:"improvements"`
    SEOScore          float64                `json:"seo_score"`
    ReadabilityScore  float64                `json:"readability_score"`
    Suggestions       []string               `json:"suggestions"`
    Metadata          map[string]interface{} `json:"metadata"`
}
```

### 3. Chat APIs
```go
// POST /api/cms/v1/ai/chat/sessions
// Create new chat session
type CreateChatSessionRequest struct {
    TenantID     uint                   `json:"tenant_id" validate:"required"`
    UserID       uint                   `json:"user_id,omitempty"`
    Title        string                 `json:"title" validate:"required,min=1,max=255"`
    ModelID      uint                   `json:"model_id" validate:"required"`
    SystemPrompt string                 `json:"system_prompt"`
    Context      map[string]interface{} `json:"context"`
}

// GET /api/cms/v1/ai/chat/sessions
// List chat sessions
type ListChatSessionsResponse struct {
    Sessions []ChatSession `json:"sessions"`
    Total    int           `json:"total"`
}

// POST /api/cms/v1/ai/chat/sessions/{id}/messages
// Send message to chat session
type ChatMessageRequest struct {
    SessionID uint                   `json:"session_id" validate:"required"`
    Content   string                 `json:"content" validate:"required,min=1"`
    Metadata  map[string]interface{} `json:"metadata"`
}

type ChatMessageResponse struct {
    ID         uint                   `json:"id"`
    Role       string                 `json:"role"`
    Content    string                 `json:"content"`
    Metadata   map[string]interface{} `json:"metadata"`
    TokensUsed int                    `json:"tokens_used"`
    CreatedAt  time.Time              `json:"created_at"`
}

// GET /api/cms/v1/ai/chat/sessions/{id}/messages
// Get chat history
type ChatHistoryResponse struct {
    Messages []ChatMessage `json:"messages"`
    Total    int           `json:"total"`
}

// WebSocket: /api/cms/v1/ai/chat/sessions/{id}/stream
// Real-time chat streaming
```

### 4. Web Design APIs
```go
// POST /api/cms/v1/ai/design/generate
// Generate web design
type DesignGenerationRequest struct {
    TenantID      uint                   `json:"tenant_id" validate:"required"`
    UserID        uint                   `json:"user_id,omitempty"`
    ModelID       uint                   `json:"model_id" validate:"required"`
    DesignType    string                 `json:"design_type" validate:"required,oneof=landing_page blog_layout product_page portfolio business_card"`
    Description   string                 `json:"description" validate:"required,min=10"`
    Requirements  []string               `json:"requirements"`
    Style         string                 `json:"style"` // modern, minimalist, colorful, etc.
    Colors        []string               `json:"colors"`
    Layout        string                 `json:"layout"` // grid, flexbox, etc.
    Responsive    bool                   `json:"responsive" default:"true"`
    Accessibility bool                   `json:"accessibility" default:"true"`
}

type GeneratedDesignResponse struct {
    ID             uint                   `json:"id"`
    Name           string                 `json:"name"`
    HTMLContent    string                 `json:"html_content"`
    CSSContent     string                 `json:"css_content"`
    JSContent      string                 `json:"js_content"`
    DesignConfig   map[string]interface{} `json:"design_config"`
    PreviewURL     string                 `json:"preview_url"`
    TokensUsed     int                    `json:"tokens_used"`
    CostCents      int                    `json:"cost_cents"`
}

// POST /api/cms/v1/ai/design/components
// Generate design component
type ComponentGenerationRequest struct {
    TenantID      uint                   `json:"tenant_id" validate:"required"`
    ModelID       uint                   `json:"model_id" validate:"required"`
    ComponentType string                 `json:"component_type" validate:"required"` // header, footer, sidebar, card, etc.
    Description   string                 `json:"description" validate:"required"`
    Framework     string                 `json:"framework"` // react, vue, vanilla, etc.
    Styling       string                 `json:"styling"` // tailwind, css, styled-components, etc.
    Props         map[string]interface{} `json:"props"`
}

// GET /api/cms/v1/ai/design/templates
// List design templates
type ListDesignTemplatesResponse struct {
    Designs []WebDesign `json:"designs"`
    Total   int         `json:"total"`
}

// POST /api/cms/v1/ai/design/{id}/optimize
// Optimize existing design
type DesignOptimizationRequest struct {
    TenantID       uint     `json:"tenant_id" validate:"required"`
    ModelID        uint     `json:"model_id" validate:"required"`
    OptimizationType string `json:"optimization_type" validate:"required,oneof=performance accessibility seo mobile"`
    TargetDevice   string   `json:"target_device"` // mobile, tablet, desktop
    BrowserSupport []string `json:"browser_support"`
}

// GET /api/cms/v1/ai/design/{id}/preview
// Preview design
type DesignPreviewResponse struct {
    PreviewURL    string `json:"preview_url"`
    MobilePreview string `json:"mobile_preview"`
    TabletPreview string `json:"tablet_preview"`
    Screenshots   []string `json:"screenshots"`
}
```

## Integration với Blog Module

### 1. AI-Powered Blog Post Generation
```go
// Extend blog post creation with AI
// POST /api/cms/v1/blog/posts/generate
type AIBlogPostRequest struct {
    TenantID    uint     `json:"tenant_id" validate:"required"`
    UserID      uint     `json:"user_id" validate:"required"`
    ModelID     uint     `json:"model_id" validate:"required"`
    Topic       string   `json:"topic" validate:"required,min=3"`
    Keywords    []string `json:"keywords"`
    Tone        string   `json:"tone"` // professional, casual, friendly, etc.
    Length      string   `json:"length"` // short, medium, long
    Audience    string   `json:"audience"` // beginner, intermediate, expert
    Outline     bool     `json:"outline" default:"true"`
    SEOOptimize bool     `json:"seo_optimize" default:"true"`
    CategoryID  uint     `json:"category_id,omitempty"`
    Tags        []string `json:"tags"`
}

type AIBlogPostResponse struct {
    PostID       uint     `json:"post_id"`
    Title        string   `json:"title"`
    Content      string   `json:"content"`
    Excerpt      string   `json:"excerpt"`
    Outline      []string `json:"outline"`
    SEOKeywords  []string `json:"seo_keywords"`
    MetaTitle    string   `json:"meta_title"`
    MetaDescription string `json:"meta_description"`
    ReadingTime  int      `json:"reading_time_minutes"`
    QualityScore float64  `json:"quality_score"`
    Suggestions  []string `json:"suggestions"`
}
```

### 2. Content Enhancement
```go
// POST /api/cms/v1/blog/posts/{id}/enhance
// Enhance existing blog post with AI
type PostEnhancementRequest struct {
    TenantID       uint     `json:"tenant_id" validate:"required"`
    ModelID        uint     `json:"model_id" validate:"required"`
    EnhancementType string   `json:"enhancement_type" validate:"required,oneof=grammar seo readability engagement"`
    TargetAudience string   `json:"target_audience"`
    PreserveTone   bool     `json:"preserve_tone" default:"true"`
}

// POST /api/cms/v1/blog/posts/{id}/summarize
// Generate summary/excerpt
type PostSummaryRequest struct {
    TenantID   uint `json:"tenant_id" validate:"required"`
    ModelID    uint `json:"model_id" validate:"required"`
    Length     int  `json:"length" validate:"min=50,max=500"`
    Style      string `json:"style"` // bullet_points, paragraph, highlights
}
```

## Security và Rate Limiting

### 1. AI-Specific Security
```go
// AI Usage Quota per Plan
type AIQuotaConfig struct {
    DailyRequests   int     `json:"daily_requests"`
    MonthlyRequests int     `json:"monthly_requests"`
    DailyTokens     int     `json:"daily_tokens"`
    MonthlyTokens   int     `json:"monthly_tokens"`
    DailyCostCents  int     `json:"daily_cost_cents"`
    MonthlyCostCents int    `json:"monthly_cost_cents"`
    ModelAccess     []string `json:"model_access"`
    Features        []string `json:"features"` // content_generation, chat, design, etc.
}

// Plan-based AI features
var AIFeaturesByPlan = map[string]AIQuotaConfig{
    "free": {
        DailyRequests:    10,
        MonthlyRequests:  100,
        DailyTokens:      5000,
        MonthlyTokens:    50000,
        DailyCostCents:   100, // $1
        MonthlyCostCents: 1000, // $10
        ModelAccess:      []string{"gpt-3.5-turbo"},
        Features:         []string{"content_generation"},
    },
    "basic": {
        DailyRequests:    50,
        MonthlyRequests:  1000,
        DailyTokens:      25000,
        MonthlyTokens:    500000,
        DailyCostCents:   500, // $5
        MonthlyCostCents: 5000, // $50
        ModelAccess:      []string{"gpt-3.5-turbo", "gpt-4"},
        Features:         []string{"content_generation", "chat"},
    },
    "premium": {
        DailyRequests:    200,
        MonthlyRequests:  5000,
        DailyTokens:      100000,
        MonthlyTokens:    2000000,
        DailyCostCents:   2000, // $20
        MonthlyCostCents: 20000, // $200
        ModelAccess:      []string{"gpt-3.5-turbo", "gpt-4", "claude-3-opus"},
        Features:         []string{"content_generation", "chat", "design", "optimization"},
    },
}
```

### 2. Rate Limiting Middleware
```go
// pkg/http/middleware/ai_rate_limit.go
func AIRateLimitMiddleware(quotaService services.QuotaService) gin.HandlerFunc {
    return func(c *gin.Context) {
        tenantID := context.GetTenantID(c)
        requestType := getAIRequestType(c.Request.URL.Path)
        
        // Check quota
        quota, err := quotaService.CheckQuota(c, tenantID, requestType)
        if err != nil {
            c.JSON(500, gin.H{"error": "Failed to check quota"})
            c.Abort()
            return
        }
        
        if quota.Exceeded {
            c.Header("X-RateLimit-Limit", fmt.Sprintf("%d", quota.Limit))
            c.Header("X-RateLimit-Remaining", "0")
            c.Header("X-RateLimit-Reset", fmt.Sprintf("%d", quota.ResetTime))
            c.JSON(429, gin.H{
                "error": "AI quota exceeded",
                "quota": quota,
            })
            c.Abort()
            return
        }
        
        // Set quota headers
        c.Header("X-RateLimit-Limit", fmt.Sprintf("%d", quota.Limit))
        c.Header("X-RateLimit-Remaining", fmt.Sprintf("%d", quota.Remaining))
        c.Header("X-RateLimit-Reset", fmt.Sprintf("%d", quota.ResetTime))
        
        c.Next()
    }
}
```

## Performance Optimization

### 1. Caching Strategy
```go
// AI Response Caching
type AICache struct {
    redis *redis.Client
    ttl   time.Duration
}

func (c *AICache) GetCachedResponse(prompt string, modelID uint) (*models.CachedResponse, error) {
    key := fmt.Sprintf("ai_cache:%d:%s", modelID, hashPrompt(prompt))
    data, err := c.redis.Get(context.Background(), key).Result()
    if err == redis.Nil {
        return nil, nil // Cache miss
    }
    if err != nil {
        return nil, err
    }
    
    var response models.CachedResponse
    err = json.Unmarshal([]byte(data), &response)
    return &response, err
}

func (c *AICache) CacheResponse(prompt string, modelID uint, response *models.GeneratedContent) error {
    key := fmt.Sprintf("ai_cache:%d:%s", modelID, hashPrompt(prompt))
    
    cached := models.CachedResponse{
        Content:   response.Content,
        Metadata:  response.Metadata,
        CreatedAt: time.Now(),
    }
    
    data, err := json.Marshal(cached)
    if err != nil {
        return err
    }
    
    return c.redis.Set(context.Background(), key, data, c.ttl).Err()
}
```

### 2. Async Processing
```go
// Queue AI requests for processing
type AIRequestQueue struct {
    queue chan *models.AIRequest
    workers int
}

func (q *AIRequestQueue) ProcessAsync(req *models.AIRequest) error {
    select {
    case q.queue <- req:
        return nil
    default:
        return errors.New("queue is full")
    }
}

func (q *AIRequestQueue) worker(ctx context.Context) {
    for {
        select {
        case req := <-q.queue:
            // Process AI request
            result, err := q.processRequest(req)
            if err != nil {
                // Handle error, retry, or move to dead letter queue
                q.handleError(req, err)
            } else {
                // Update database with result
                q.updateResult(req, result)
            }
        case <-ctx.Done():
            return
        }
    }
}
```

## Monitoring và Analytics

### 1. AI Metrics
```go
// AI Usage Metrics
type AIMetrics struct {
    TenantID         uint    `json:"tenant_id"`
    ModelID          uint    `json:"model_id"`
    RequestType      string  `json:"request_type"`
    TotalRequests    int     `json:"total_requests"`
    SuccessfulRequests int   `json:"successful_requests"`
    FailedRequests   int     `json:"failed_requests"`
    TotalTokens      int     `json:"total_tokens"`
    TotalCostCents   int     `json:"total_cost_cents"`
    AverageResponseTime float64 `json:"average_response_time_ms"`
    QualityScore     float64 `json:"average_quality_score"`
    Period           string  `json:"period"` // daily, weekly, monthly
    Date             time.Time `json:"date"`
}

// GET /api/cms/v1/ai/metrics
type AIMetricsResponse struct {
    Metrics []AIMetrics `json:"metrics"`
    Summary struct {
        TotalRequests    int     `json:"total_requests"`
        TotalTokens      int     `json:"total_tokens"`
        TotalCostCents   int     `json:"total_cost_cents"`
        AverageQuality   float64 `json:"average_quality"`
        TopModels        []string `json:"top_models"`
        CostTrend        string   `json:"cost_trend"`
    } `json:"summary"`
}
```

### 2. Health Checks
```go
// GET /api/cms/v1/ai/health
type AIHealthResponse struct {
    Status string `json:"status"`
    Providers []struct {
        Name      string    `json:"name"`
        Status    string    `json:"status"`
        Latency   int       `json:"latency_ms"`
        LastCheck time.Time `json:"last_check"`
    } `json:"providers"`
    QueueStatus struct {
        Size      int `json:"size"`
        Processing int `json:"processing"`
        Failed    int `json:"failed"`
    } `json:"queue_status"`
}
```

## Configuration

### 1. Environment Variables
```env
# AI Service Configuration
AI_ENABLED=true
AI_DEFAULT_PROVIDER=openai
AI_MAX_CONCURRENT_REQUESTS=10
AI_REQUEST_TIMEOUT=30s
AI_CACHE_TTL=1h
AI_QUEUE_SIZE=1000

# Provider API Keys
OPENAI_API_KEY=sk-...
ANTHROPIC_API_KEY=sk-ant-...
GOOGLE_API_KEY=...
OLLAMA_BASE_URL=http://localhost:11434

# Rate Limiting
AI_RATE_LIMIT_ENABLED=true
AI_RATE_LIMIT_REQUESTS_PER_MINUTE=60
AI_RATE_LIMIT_TOKENS_PER_MINUTE=50000

# Caching
AI_CACHE_ENABLED=true
AI_CACHE_REDIS_URL=redis://localhost:6379
AI_CACHE_TTL=3600

# Queue Configuration
AI_QUEUE_ENABLED=true
AI_QUEUE_WORKERS=5
AI_QUEUE_REDIS_URL=redis://localhost:6379
```

### 2. Feature Flags
```go
// Feature flags for AI capabilities
type AIFeatureFlags struct {
    ContentGeneration bool `json:"content_generation" default:"true"`
    ChatSupport       bool `json:"chat_support" default:"true"`
    WebDesign         bool `json:"web_design" default:"false"`
    ContentOptimization bool `json:"content_optimization" default:"true"`
    ImageGeneration   bool `json:"image_generation" default:"false"`
    CodeGeneration    bool `json:"code_generation" default:"false"`
    Translation       bool `json:"translation" default:"false"`
    Summarization     bool `json:"summarization" default:"true"`
    SEOOptimization   bool `json:"seo_optimization" default:"true"`
    StreamingChat     bool `json:"streaming_chat" default:"true"`
}
```

## Testing

### 1. Unit Tests
```go
// AI Service Tests
func TestAIService_GenerateContent(t *testing.T) {
    // Mock AI provider
    mockProvider := &MockAIProvider{}
    mockProvider.On("GenerateText", mock.Anything, mock.Anything).Return(&models.TextGenerationResponse{
        Content: "Generated content",
        TokensUsed: 100,
    }, nil)
    
    service := NewAIService(mockProvider, mockRepo, mockCache)
    
    req := &models.ContentGenerationRequest{
        TenantID: 1,
        ModelID: 1,
        ContentType: "blog_post",
        Prompt: "Write about AI in blogging",
    }
    
    result, err := service.GenerateContent(context.Background(), req)
    assert.NoError(t, err)
    assert.Equal(t, "Generated content", result.Content)
    assert.Equal(t, 100, result.TokensUsed)
}
```

### 2. Integration Tests
```go
// Bruno API Tests
// ai-tests/bruno/AI/Content Generation/Generate Blog Post.bru
meta {
  name: Generate Blog Post
  type: http
  seq: 1
}

post {
  url: {{base_url}}/api/cms/v1/ai/content/generate
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "tenant_id": {{tenant_id}},
    "model_id": {{model_id}},
    "content_type": "blog_post",
    "prompt": "Write a comprehensive blog post about the benefits of AI in modern blogging platforms",
    "parameters": {
      "tone": "professional",
      "length": "medium",
      "seo_optimize": true
    }
  }
}

script:post-response {
  if (res.getStatus() === 200) {
    const body = res.getBody();
    if (body.success && body.data) {
      bru.setEnvVar("generated_content_id", body.data.id);
      bru.setEnvVar("generated_content", body.data.content);
    }
  }
}
```

## Deployment Considerations

### 1. Resource Requirements
```yaml
# Docker Compose for AI Services
version: '3.8'
services:
  ai-service:
    image: blog-api-v3:latest
    environment:
      - AI_ENABLED=true
      - AI_QUEUE_WORKERS=10
    resources:
      limits:
        memory: 2G
        cpus: 1.5
      reservations:
        memory: 1G
        cpus: 0.5
    depends_on:
      - redis
      - mysql
  
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
  
  ollama:
    image: ollama/ollama:latest
    volumes:
      - ollama_data:/root/.ollama
    ports:
      - "11434:11434"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
```

### 2. Monitoring Setup
```yaml
# Prometheus metrics for AI
# /metrics endpoint will expose:
ai_requests_total{tenant_id, model_id, request_type, status}
ai_request_duration_seconds{tenant_id, model_id, request_type}
ai_tokens_used_total{tenant_id, model_id}
ai_cost_cents_total{tenant_id, model_id}
ai_queue_size{queue_type}
ai_provider_health{provider_name}
```

## Future Enhancements

### 1. Advanced Features
- **Multi-modal AI**: Support for image, video, and audio generation
- **Fine-tuning**: Custom model training on tenant data
- **AI Agents**: Autonomous AI agents for content management
- **Voice Integration**: Voice-to-text and text-to-speech
- **Real-time Collaboration**: AI-powered collaborative editing

### 2. Performance Optimizations
- **Edge Computing**: Deploy AI models closer to users
- **Model Quantization**: Reduce model size for faster inference
- **Batch Processing**: Batch multiple requests for efficiency
- **Predictive Caching**: Pre-generate common content types
- **Auto-scaling**: Dynamic scaling based on AI usage

### 3. Advanced Analytics
- **Content Performance Prediction**: Predict content success
- **User Behavior Analysis**: AI-powered user insights
- **Trend Detection**: Identify trending topics and keywords
- **Competitive Analysis**: AI-powered competitor monitoring
- **ROI Tracking**: Measure AI feature ROI

---

## Conclusion

Việc tích hợp AI vào Blog API v3 sẽ mang lại giá trị lớn cho users thông qua:

1. **Automated Content Creation**: Giảm thời gian tạo content xuống 80%
2. **Enhanced User Experience**: AI chat support và personalization
3. **Improved SEO**: AI-powered content optimization
4. **Design Automation**: Tự động tạo và optimize web design
5. **Cost Efficiency**: Giảm chi phí content creation và maintenance

Implementation này cung cấp foundation mạnh mẽ cho AI features với scalability, security, và performance optimization được tích hợp sẵn.
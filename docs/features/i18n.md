# Internationalization (i18n) - T<PERSON><PERSON> liệu Tiếng Việt

## Tổng quan

Hệ thống Internationalization (i18n) cung cấp hỗ trợ đa ngôn ngữ toàn diện cho Blog API v3, cho phép localization của UI messages, content, và API responses. Hệ thống được thiết kế để dễ dàng thêm ngôn ngữ mới và quản lý translations.

## Mục tiêu

- **Multi-language Support**: Hỗ trợ nhiều ngôn ngữ cho toàn bộ hệ thống
- **Dynamic Language Switching**: Thay đổi ngôn ngữ real-time
- **Translation Management**: Quản lý translations centralized
- **Content Localization**: Localize database content
- **Fallback Support**: Graceful fallback khi translation không có
- **RTL Support**: Hỗ trợ Right-to-Left languages

## Kiến trúc hệ thống

### I18n Architecture Overview

```mermaid
flowchart TD
    A[I18n System] --> B[Language Detection]
    A --> C[Translation Engine]
    A --> D[Content Localization]
    A --> E[Template Rendering]
    A --> F[API Localization]
    
    B --> B1[Accept-Language Header]
    B --> B2[Query Parameter]
    B --> B3[User Preference]
    B --> B4[Default Language]
    
    C --> C1[Message Translation]
    C --> C2[Pluralization]
    C --> C3[Date/Time Formatting]
    C --> C4[Number Formatting]
    
    D --> D1[Database Content]
    D --> D2[Media Localization]
    D --> D3[URL Localization]
    
    E --> E1[Template Variables]
    E --> E2[Dynamic Content]
    E --> E3[Static Content]
    
    F --> F1[Error Messages]
    F --> F2[Validation Messages]
    F --> F3[Response Messages]
```

### Components

#### Language Detection
- **HTTP Headers**: Accept-Language parsing
- **Query Parameters**: ?lang=en URL parameter  
- **User Preferences**: Stored user language choice
- **Domain/Subdomain**: Language-specific domains

#### Translation Engine
- **Message Translation**: UI text localization
- **Pluralization**: Language-specific plural rules
- **Interpolation**: Variable substitution trong messages
- **Formatting**: Date, time, number formatting

#### Content Localization
- **Database Content**: Multi-language content storage
- **URL Localization**: Language-specific URLs
- **Media Localization**: Localized images và assets

## Language Configuration

### Supported Languages
```yaml
languages:
  default: "en"
  fallback: "en"
  supported:
    - code: "en"
      name: "English"
      native_name: "English"
      direction: "ltr"
      flag: "🇺🇸"
      enabled: true
      
    - code: "vi"
      name: "Vietnamese"
      native_name: "Tiếng Việt"
      direction: "ltr"
      flag: "🇻🇳"
      enabled: true
      
    - code: "ja"
      name: "Japanese"
      native_name: "日本語"
      direction: "ltr"
      flag: "🇯🇵"
      enabled: true
      
    - code: "ar"
      name: "Arabic"
      native_name: "العربية"
      direction: "rtl"
      flag: "🇸🇦"
      enabled: true
      
    - code: "zh"
      name: "Chinese"
      native_name: "中文"
      direction: "ltr"
      flag: "🇨🇳"
      enabled: false  # Disabled but configured
```

### I18n Package Structure
```
pkg/i18n/
├── i18n.go                 # Main i18n package
├── detector.go            # Language detection
├── translator.go          # Translation engine
├── formatter.go           # Date/number formatting
├── pluralizer.go          # Pluralization rules
└── loader.go              # Translation file loader

locales/
├── en/
│   ├── common.json        # Common translations
│   ├── errors.json        # Error messages
│   ├── validation.json    # Validation messages
│   └── modules/
│       ├── auth.json      # Auth module translations
│       ├── blog.json      # Blog module translations
│       └── seo.json       # SEO module translations
├── vi/
│   ├── common.json
│   ├── errors.json
│   └── ...
└── ja/
    ├── common.json
    ├── errors.json
    └── ...
```

## Translation Files Structure

### Common Translations
```json
// locales/en/common.json
{
  "welcome": "Welcome to our blog",
  "hello_user": "Hello, {{.Name}}!",
  "items_count": {
    "zero": "No items",
    "one": "1 item", 
    "other": "{{.Count}} items"
  },
  "navigation": {
    "home": "Home",
    "blog": "Blog",
    "about": "About",
    "contact": "Contact"
  },
  "actions": {
    "save": "Save",
    "cancel": "Cancel",
    "delete": "Delete",
    "edit": "Edit",
    "create": "Create",
    "update": "Update"
  },
  "time": {
    "just_now": "Just now",
    "minutes_ago": "{{.Count}} minutes ago",
    "hours_ago": "{{.Count}} hours ago",
    "days_ago": "{{.Count}} days ago"
  }
}
```

```json
// locales/vi/common.json
{
  "welcome": "Chào mừng đến với blog của chúng tôi",
  "hello_user": "Xin chào, {{.Name}}!",
  "items_count": {
    "zero": "Không có mục nào",
    "other": "{{.Count}} mục"
  },
  "navigation": {
    "home": "Trang chủ",
    "blog": "Blog",
    "about": "Giới thiệu", 
    "contact": "Liên hệ"
  },
  "actions": {
    "save": "Lưu",
    "cancel": "Hủy",
    "delete": "Xóa",
    "edit": "Sửa",
    "create": "Tạo",
    "update": "Cập nhật"
  },
  "time": {
    "just_now": "Vừa xong",
    "minutes_ago": "{{.Count}} phút trước",
    "hours_ago": "{{.Count}} giờ trước", 
    "days_ago": "{{.Count}} ngày trước"
  }
}
```

### Error Messages
```json
// locales/en/errors.json
{
  "validation": {
    "required": "{{.Field}} is required",
    "email": "{{.Field}} must be a valid email",
    "min_length": "{{.Field}} must be at least {{.Min}} characters",
    "max_length": "{{.Field}} must not exceed {{.Max}} characters"
  },
  "auth": {
    "unauthorized": "Authentication required",
    "forbidden": "Insufficient permissions",
    "invalid_credentials": "Invalid email or password",
    "token_expired": "Token has expired",
    "account_locked": "Account has been locked"
  },
  "general": {
    "not_found": "Resource not found",
    "internal_error": "Internal server error",
    "bad_request": "Invalid request data",
    "rate_limited": "Too many requests, please try again later"
  }
}
```

### Module-specific Translations
```json
// locales/en/modules/blog.json
{
  "post": {
    "created": "Post created successfully",
    "updated": "Post updated successfully",
    "deleted": "Post deleted successfully",
    "published": "Post published successfully",
    "status": {
      "draft": "Draft",
      "published": "Published",
      "archived": "Archived"
    }
  },
  "comment": {
    "added": "Comment added successfully",
    "moderation_required": "Your comment is pending moderation",
    "approved": "Comment approved",
    "rejected": "Comment rejected"
  },
  "category": {
    "created": "Category created successfully",
    "posts_count": "{{.Count}} posts in this category"
  }
}
```

## I18n Package Implementation

### Core I18n Interface
```go
package i18n

import (
    "context"
    "time"
)

type I18n interface {
    // Translation
    T(key string, params ...interface{}) string
    TWithContext(ctx context.Context, key string, params ...interface{}) string
    TPlural(key string, count int, params ...interface{}) string
    
    // Language management
    SetLanguage(lang string) error
    GetLanguage() string
    GetSupportedLanguages() []Language
    
    // Formatting
    FormatDate(date time.Time, format string) string
    FormatNumber(number float64) string
    FormatCurrency(amount float64, currency string) string
}

type Language struct {
    Code        string `json:"code"`
    Name        string `json:"name"`
    NativeName  string `json:"native_name"`
    Direction   string `json:"direction"` // ltr, rtl
    Flag        string `json:"flag"`
    Enabled     bool   `json:"enabled"`
}

type Translator struct {
    currentLang string
    fallbackLang string
    translations map[string]map[string]interface{}
    formatters   map[string]Formatter
}
```

### Language Detection
```go
type LanguageDetector struct {
    supportedLangs []string
    defaultLang    string
}

func (d *LanguageDetector) DetectFromRequest(c *gin.Context) string {
    // 1. Check query parameter
    if lang := c.Query("lang"); lang != "" && d.isSupported(lang) {
        return lang
    }
    
    // 2. Check user preference from database
    if userID := c.GetString("user_id"); userID != "" {
        if lang := d.getUserLanguage(userID); lang != "" && d.isSupported(lang) {
            return lang
        }
    }
    
    // 3. Parse Accept-Language header
    if lang := d.parseAcceptLanguage(c.GetHeader("Accept-Language")); lang != "" {
        return lang
    }
    
    // 4. Return default language
    return d.defaultLang
}

func (d *LanguageDetector) parseAcceptLanguage(header string) string {
    // Parse Accept-Language header
    // Example: "en-US,en;q=0.9,vi;q=0.8"
    languages := parseAcceptLanguageHeader(header)
    
    for _, lang := range languages {
        // Check exact match first
        if d.isSupported(lang.Code) {
            return lang.Code
        }
        
        // Check language family (en-US -> en)
        if primary := strings.Split(lang.Code, "-")[0]; d.isSupported(primary) {
            return primary
        }
    }
    
    return ""
}
```

### Translation Engine
```go
func (t *Translator) T(key string, params ...interface{}) string {
    return t.TWithLang(t.currentLang, key, params...)
}

func (t *Translator) TWithLang(lang, key string, params ...interface{}) string {
    // Get translation from current language
    if translation := t.getTranslation(lang, key); translation != "" {
        return t.interpolate(translation, params...)
    }
    
    // Fallback to default language
    if lang != t.fallbackLang {
        if translation := t.getTranslation(t.fallbackLang, key); translation != "" {
            return t.interpolate(translation, params...)
        }
    }
    
    // Return key if no translation found
    return key
}

func (t *Translator) TPlural(key string, count int, params ...interface{}) string {
    pluralKey := t.getPluralKey(key, count, t.currentLang)
    return t.T(pluralKey, append([]interface{}{count}, params...)...)
}

func (t *Translator) interpolate(template string, params ...interface{}) string {
    if len(params) == 0 {
        return template
    }
    
    // Support both named parameters và positional parameters
    switch params[0].(type) {
    case map[string]interface{}:
        return t.interpolateNamed(template, params[0].(map[string]interface{}))
    default:
        return t.interpolatePositional(template, params...)
    }
}
```

### Pluralization Rules
```go
type PluralRule func(count int) string

var pluralRules = map[string]PluralRule{
    "en": func(count int) string {
        if count == 0 {
            return "zero"
        } else if count == 1 {
            return "one"
        } else {
            return "other"
        }
    },
    
    "vi": func(count int) string {
        if count == 0 {
            return "zero"
        } else {
            return "other"
        }
    },
    
    "ja": func(count int) string {
        return "other" // Japanese doesn't have plural forms
    },
    
    "ar": func(count int) string {
        if count == 0 {
            return "zero"
        } else if count == 1 {
            return "one"
        } else if count == 2 {
            return "two"
        } else if count >= 3 && count <= 10 {
            return "few"
        } else if count >= 11 && count <= 99 {
            return "many"
        } else {
            return "other"
        }
    },
}
```

## Content Localization

### Database Models for Localized Content

#### Localized Post Model
```go
type Post struct {
    ID          uint32    `gorm:"primarykey" json:"id"`
    Slug        string    `gorm:"uniqueIndex;not null" json:"slug"`
    AuthorID    uint32    `gorm:"not null" json:"author_id"`
    Status      string    `gorm:"size:20;default:'draft'" json:"status"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Localized content
    Translations []PostTranslation `gorm:"foreignKey:PostID" json:"translations,omitempty"`
}

type PostTranslation struct {
    ID          uint32    `gorm:"primarykey" json:"id"`
    PostID      uint32    `gorm:"not null;index" json:"post_id"`
    Language    string    `gorm:"size:5;not null" json:"language"`
    Title       string    `gorm:"size:255;not null" json:"title"`
    Content     string    `gorm:"type:longtext" json:"content"`
    Excerpt     string    `gorm:"type:text" json:"excerpt"`
    MetaTitle   string    `gorm:"size:70" json:"meta_title"`
    MetaDesc    string    `gorm:"size:160" json:"meta_description"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Unique constraint: one translation per post per language
    // gorm:"uniqueIndex:idx_post_language,priority:1"
}
```

#### Category Localization
```go
type Category struct {
    ID          uint32    `gorm:"primarykey" json:"id"`
    Slug        string    `gorm:"uniqueIndex;not null" json:"slug"`
    Color       string    `gorm:"size:7" json:"color"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    Translations []CategoryTranslation `gorm:"foreignKey:CategoryID" json:"translations,omitempty"`
}

type CategoryTranslation struct {
    ID          uint32    `gorm:"primarykey" json:"id"`
    CategoryID  uint32    `gorm:"not null;index" json:"category_id"`
    Language    string    `gorm:"size:5;not null" json:"language"`
    Name        string    `gorm:"size:100;not null" json:"name"`
    Description string    `gorm:"type:text" json:"description"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}
```

### Content Repository with I18n Support
```go
type PostRepository struct {
    db   *gorm.DB
    i18n I18n
}

func (r *PostRepository) GetBySlugWithLang(slug, lang string) (*Post, error) {
    var post Post
    
    err := r.db.Preload("Translations", "language = ?", lang).
        Where("slug = ?", slug).
        First(&post).Error
        
    if err != nil {
        return nil, err
    }
    
    // If no translation in requested language, try fallback
    if len(post.Translations) == 0 {
        err = r.db.Preload("Translations", "language = ?", r.i18n.GetFallbackLanguage()).
            Where("slug = ?", slug).
            First(&post).Error
    }
    
    return &post, err
}

func (r *PostRepository) ListWithLang(lang string, cursor string, limit int) ([]*Post, string, error) {
    var posts []*Post
    
    query := r.db.Preload("Translations", "language = ?", lang).
        Where("status = ?", "published")
    
    // Apply cursor pagination
    if cursor != "" {
        query = query.Where("id > ?", cursor)
    }
    
    err := query.Limit(limit + 1).Find(&posts).Error
    if err != nil {
        return nil, "", err
    }
    
    // Calculate next cursor
    var nextCursor string
    if len(posts) > limit {
        nextCursor = fmt.Sprintf("%d", posts[limit].ID)
        posts = posts[:limit]
    }
    
    return posts, nextCursor, nil
}
```

## Middleware Integration

### I18n Middleware
```go
func I18nMiddleware(i18nService I18n) gin.HandlerFunc {
    return func(c *gin.Context) {
        // Detect language
        detector := NewLanguageDetector(i18nService.GetSupportedLanguages())
        lang := detector.DetectFromRequest(c)
        
        // Set language for this request
        i18nService.SetLanguage(lang)
        
        // Store in context for use in handlers
        c.Set("language", lang)
        c.Set("i18n", i18nService)
        
        // Set Content-Language header
        c.Header("Content-Language", lang)
        
        c.Next()
    }
}
```

### Usage in Handlers
```go
func (h *PostHandler) GetPost(c *gin.Context) {
    slug := c.Param("slug")
    lang := c.GetString("language")
    i18n := c.MustGet("i18n").(I18n)
    
    post, err := h.postService.GetBySlugWithLang(slug, lang)
    if err != nil {
        errorMsg := i18n.T("errors.general.not_found")
        resp := response.ErrorResponse(404, errorMsg, "NOT_FOUND", c.Request.URL.Path, nil)
        c.JSON(resp.Status.Code, resp)
        return
    }
    
    resp := response.SuccessResponse(post, c.Request.URL.Path)
    c.JSON(http.StatusOK, resp)
}
```

## URL Localization

### Language-specific URLs

#### Subdirectory Approach
```
https://yourblog.com/en/posts/my-post-title
https://yourblog.com/vi/posts/tieu-de-bai-viet
https://yourblog.com/ja/posts/私の投稿タイトル
```

#### Subdomain Approach  
```
https://en.yourblog.com/posts/my-post-title
https://vi.yourblog.com/posts/tieu-de-bai-viet
https://ja.yourblog.com/posts/私の投稿タイトル
```

#### Domain Approach
```
https://yourblog.com/posts/my-post-title     (English)
https://yourblog.vn/posts/tieu-de-bai-viet   (Vietnamese)
https://yourblog.jp/posts/私の投稿タイトル      (Japanese)
```

### Route Localization
```go
// Route registration with language prefix
func (r *Router) setupLocalizedRoutes() {
    for _, lang := range r.supportedLanguages {
        group := r.engine.Group("/" + lang.Code)
        group.Use(I18nMiddleware(r.i18n))
        
        // Localized routes
        group.GET("/posts/:slug", r.handlers.Post.Get)
        group.GET("/categories/:slug", r.handlers.Category.Get)
        group.GET("/tags/:slug", r.handlers.Tag.Get)
    }
    
    // Default language routes (no prefix)
    defaultGroup := r.engine.Group("")
    defaultGroup.Use(I18nMiddleware(r.i18n))
    defaultGroup.GET("/posts/:slug", r.handlers.Post.Get)
    // ...
}
```

## API Response Localization

### Localized Error Responses
```go
func ValidationErrorResponse(c *gin.Context, details []ValidationError) *Response {
    i18n := c.MustGet("i18n").(I18n)
    path := c.Request.URL.Path
    
    localizedDetails := make([]response.Detail, len(details))
    for i, detail := range details {
        localizedDetails[i] = response.Detail{
            Field:   detail.Field,
            Message: i18n.T(detail.MessageKey, detail.Params),
            Code:    detail.Code,
        }
    }
    
    message := i18n.T("errors.validation.failed")
    
    return &Response{
        Status: Status{
            Code:      422,
            Message:   message,
            Success:   false,
            ErrorCode: "VALIDATION_ERROR",
            Path:      path,
            Timestamp: time.Now().UTC().Format(time.RFC3339),
            Details:   localizedDetails,
        },
        Data: nil,
    }
}
```

### Localized Success Messages
```go
func (h *PostHandler) CreatePost(c *gin.Context) {
    i18n := c.MustGet("i18n").(I18n)
    
    // ... create post logic
    
    successMsg := i18n.T("blog.post.created")
    resp := response.SuccessResponse(post, c.Request.URL.Path)
    resp.Status.Message = successMsg
    
    c.JSON(http.StatusCreated, resp)
}
```

## Date and Number Formatting

### Locale-specific Formatting
```go
type Formatter interface {
    FormatDate(date time.Time, format string) string
    FormatNumber(number float64) string
    FormatCurrency(amount float64, currency string) string
    FormatRelativeTime(date time.Time) string
}

type LocaleFormatter struct {
    locale string
    timezone string
}

func (f *LocaleFormatter) FormatDate(date time.Time, format string) string {
    switch f.locale {
    case "en":
        return date.Format("January 2, 2006")
    case "vi":
        return date.Format("02/01/2006")
    case "ja":
        return date.Format("2006年1月2日")
    default:
        return date.Format(time.RFC3339)
    }
}

func (f *LocaleFormatter) FormatRelativeTime(date time.Time) string {
    now := time.Now()
    diff := now.Sub(date)
    
    if diff < time.Minute {
        return f.i18n.T("time.just_now")
    } else if diff < time.Hour {
        minutes := int(diff.Minutes())
        return f.i18n.TPlural("time.minutes_ago", minutes, map[string]interface{}{
            "Count": minutes,
        })
    } else if diff < 24*time.Hour {
        hours := int(diff.Hours())
        return f.i18n.TPlural("time.hours_ago", hours, map[string]interface{}{
            "Count": hours,
        })
    } else {
        days := int(diff.Hours() / 24)
        return f.i18n.TPlural("time.days_ago", days, map[string]interface{}{
            "Count": days,
        })
    }
}
```

## Configuration Management

### I18n Configuration
```yaml
# config/i18n.yml
i18n:
  enabled: true
  default_language: "en"
  fallback_language: "en"
  
  detection:
    methods: ["query", "user_preference", "header", "default"]
    query_param: "lang"
    
  storage:
    type: "file" # file, database, redis
    path: "./locales"
    cache_enabled: true
    cache_ttl: "1h"
    
  url_strategy: "subdirectory" # subdirectory, subdomain, domain
  
  formatting:
    timezone: "UTC"
    date_format: "2006-01-02"
    time_format: "15:04:05"
    
  rtl_languages: ["ar", "he", "ur"]
```

### Environment Variables
```bash
# I18n Configuration
I18N_ENABLED=true
I18N_DEFAULT_LANGUAGE=en
I18N_FALLBACK_LANGUAGE=en
I18N_LOCALES_PATH=./locales
I18N_CACHE_ENABLED=true
I18N_URL_STRATEGY=subdirectory
```

## Admin Interface

### Translation Management API

#### List Translations
```http
GET /cms/v1/i18n/translations?language=en&namespace=common
```

#### Update Translation
```http
PUT /cms/v1/i18n/translations/{key}
Content-Type: application/json

{
  "en": "Hello, World!",
  "vi": "Xin chào, Thế giới!",
  "ja": "こんにちは、世界！"
}
```

#### Export Translations
```http
GET /cms/v1/i18n/export?language=en&format=json
```

#### Import Translations
```http
POST /cms/v1/i18n/import
Content-Type: multipart/form-data

file: translations.json
language: en
```

### Translation Statistics
```http
GET /cms/v1/i18n/stats
```

**Response:**
```json
{
  "success": true,
  "data": {
    "languages": [
      {
        "code": "en",
        "name": "English",
        "total_keys": 1250,
        "translated_keys": 1250,
        "completion_rate": 100.0
      },
      {
        "code": "vi", 
        "name": "Vietnamese",
        "total_keys": 1250,
        "translated_keys": 980,
        "completion_rate": 78.4
      }
    ],
    "missing_translations": [
      {
        "key": "blog.post.draft_saved",
        "missing_languages": ["vi", "ja"]
      }
    ]
  }
}
```

## Best Practices

### Translation Keys
- **Hierarchical Structure**: Use nested keys như `blog.post.created`
- **Descriptive Names**: Clear, descriptive key names
- **Consistency**: Consistent naming conventions
- **Modularity**: Group translations by modules

### Content Localization
- **Complete Translations**: Always provide fallback content
- **Cultural Adaptation**: Adapt content cho local culture
- **Image Localization**: Use culturally appropriate images
- **Currency và Dates**: Format appropriately cho region

### Performance
- **Translation Caching**: Cache frequently used translations
- **Lazy Loading**: Load translations on demand
- **Minification**: Minify translation files
- **CDN**: Serve translations from CDN

### Maintenance
- **Translation Validation**: Validate translation completeness
- **Regular Updates**: Keep translations up to date
- **Professional Translation**: Use professional translators
- **Community Contributions**: Allow community translations

## RTL (Right-to-Left) Support

### RTL Languages Configuration
```yaml
rtl_languages:
  - code: "ar"
    name: "Arabic"
    direction: "rtl"
  - code: "he"
    name: "Hebrew"
    direction: "rtl"
  - code: "ur"
    name: "Urdu"
    direction: "rtl"
```

### CSS và Layout
```css
/* RTL-specific styles */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .sidebar {
  right: 0;
  left: auto;
}

[dir="rtl"] .breadcrumb {
  direction: rtl;
}

/* Use logical properties */
.content {
  margin-inline-start: 20px;
  padding-inline-end: 15px;
  border-inline-start: 1px solid #ccc;
}
```

## Testing I18n

### Unit Tests
```go
func TestTranslation(t *testing.T) {
    i18n := NewI18n("en", "en")
    i18n.LoadTranslations("./testdata/locales")
    
    // Test basic translation
    result := i18n.T("common.welcome")
    assert.Equal(t, "Welcome to our blog", result)
    
    // Test interpolation
    result = i18n.T("common.hello_user", map[string]interface{}{
        "Name": "John",
    })
    assert.Equal(t, "Hello, John!", result)
    
    // Test pluralization
    result = i18n.TPlural("common.items_count", 5)
    assert.Equal(t, "5 items", result)
}
```

### Integration Tests
```go
func TestI18nMiddleware(t *testing.T) {
    router := setupTestRouter()
    
    // Test English
    req := httptest.NewRequest("GET", "/posts/test-post", nil)
    req.Header.Set("Accept-Language", "en-US,en;q=0.9")
    w := httptest.NewRecorder()
    router.ServeHTTP(w, req)
    
    assert.Equal(t, "en", w.Header().Get("Content-Language"))
    
    // Test Vietnamese
    req = httptest.NewRequest("GET", "/posts/test-post?lang=vi", nil)
    w = httptest.NewRecorder()
    router.ServeHTTP(w, req)
    
    assert.Equal(t, "vi", w.Header().Get("Content-Language"))
}
```

## Migration và Deployment

### Translation File Management
```bash
# Export current translations
go run cmd/i18n export --language=en --output=./backup/

# Import new translations
go run cmd/i18n import --file=./new-translations.json --language=vi

# Validate translations
go run cmd/i18n validate --check-missing --check-unused

# Generate translation keys from code
go run cmd/i18n extract --source=./internal --output=./locales/keys.json
```

### Database Migration cho Localized Content
```sql
-- Migration for localized posts
CREATE TABLE post_translations (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    post_id BIGINT UNSIGNED NOT NULL,
    language VARCHAR(5) NOT NULL,
    title VARCHAR(255) NOT NULL,
    content LONGTEXT,
    excerpt TEXT,
    meta_title VARCHAR(70),
    meta_description VARCHAR(160),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY idx_post_language (post_id, language),
    FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE
);
```

## Security Considerations

### I18n Security
- **Input Validation**: Validate language codes
- **XSS Prevention**: Escape translated content
- **File Security**: Secure translation file access
- **Injection Prevention**: Prevent translation injection attacks

### Best Practices
- **Whitelist Languages**: Only allow configured languages
- **Sanitize Input**: Clean user-provided translations
- **Access Control**: Restrict translation management access
- **Audit Logging**: Log translation changes

## Tài liệu liên quan

- [Response Standard](../api/response-standard.md)
- [Frontend API](../api/frontend-api.md)
- [Website Module](../internal/modules/website.md)
- [SEO Module](../modules/seo.md)
- [Configuration Guide](../architecture/configuration.md)
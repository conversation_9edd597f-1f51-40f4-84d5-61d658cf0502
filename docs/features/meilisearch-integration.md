# MeiliSearch Integration trong Blog API v3

## Tổng quan

Document này hướng dẫn cách tích hợp MeiliSearch vào Blog API v3 để cung cấp full-text search capabilities với performance cao, typo-tolerance, và instant search experience.

## Tại sao chọn MeiliSearch?

### Ưu điểm của MeiliSearch
- **Ultra-fast Search**: Sub-millisecond response time
- **Typo Tolerance**: Tự động sửa lỗi chính tả khi search
- **Instant Search**: Real-time search khi user typing
- **Faceted Search**: Filter theo categories, tags, dates
- **Highlighting**: Highlight search terms trong results
- **Multi-language Support**: Hỗ trợ nhiều ngôn ngữ
- **Easy Integration**: REST API đơn giản
- **Self-hosted**: Full control over data
- **Developer-friendly**: Extensive documentation và SDKs

### So sánh với alternatives
| Feature | MeiliSearch | Elasticsearch | Algolia |
|---------|-------------|---------------|---------|
| Setup Complexity | Low | High | N/A (SaaS) |
| Performance | Excellent | Good | Excellent |
| Typo Tolerance | Built-in | Complex config | Built-in |
| Pricing | Free | Free tier limited | Expensive |
| Multi-tenancy | Good | Excellent | Good |
| Real-time | Yes | Yes | Yes |

## Architecture Overview

### System Components
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Blog API v3   │    │   MeiliSearch   │    │   Search UI     │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ Search      │ │◄──►│ │ Indexes     │ │    │ │ Instant     │ │
│ │ Module      │ │    │ │ - Posts     │ │    │ │ Search      │ │
│ │             │ │    │ │ - Users     │ │    │ │ Interface   │ │
│ └─────────────┘ │    │ │ - Tenants   │ │    │ └─────────────┘ │
│                 │    │ └─────────────┘ │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ Index       │ │    │ │ Search      │ │    │ │ Filter      │ │
│ │ Sync        │ │    │ │ Engine      │ │    │ │ Facets      │ │
│ │ Service     │ │    │ │             │ │    │ │             │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Data Flow
1. **Write Path**: Database changes → Index sync → MeiliSearch update
2. **Read Path**: User search → API → MeiliSearch → Results
3. **Admin Path**: Index management → MeiliSearch configuration

## Installation và Setup

### 1. MeiliSearch Server Setup

#### Docker Compose
```yaml
# docker-compose.yml
version: '3.8'
services:
  meilisearch:
    image: getmeili/meilisearch:v1.5
    container_name: meilisearch
    ports:
      - "7700:7700"
    environment:
      - MEILI_MASTER_KEY=${MEILI_MASTER_KEY}
      - MEILI_ENV=production
      - MEILI_DB_PATH=/meili_data
      - MEILI_HTTP_ADDR=0.0.0.0:7700
      - MEILI_NO_ANALYTICS=true
      - MEILI_LOG_LEVEL=INFO
    volumes:
      - meilisearch_data:/meili_data
    restart: unless-stopped
    networks:
      - blogapi_network

volumes:
  meilisearch_data:
    driver: local

networks:
  blogapi_network:
    driver: bridge
```

#### Environment Variables
```env
# .env
MEILI_HOST=http://localhost:7700
MEILI_MASTER_KEY=your-master-key-here
MEILI_SEARCH_KEY=your-search-key-here
MEILI_ADMIN_KEY=your-admin-key-here
MEILI_INDEX_PREFIX=blogapi_
MEILI_BATCH_SIZE=1000
MEILI_SYNC_INTERVAL=30s
MEILI_ENABLE_TYPO_TOLERANCE=true
MEILI_ENABLE_FACETED_SEARCH=true
```

### 2. Go Client Installation
```bash
go get github.com/meilisearch/meilisearch-go
```

### 3. Project Structure
```
internal/
├── modules/
│   └── search/
│       ├── models/
│       │   ├── search_request.go
│       │   ├── search_response.go
│       │   └── search_document.go
│       ├── repositories/
│       │   ├── search_repository.go
│       │   └── meili_repository.go
│       ├── services/
│       │   ├── search_service.go
│       │   ├── index_service.go
│       │   └── sync_service.go
│       ├── handlers/
│       │   ├── search_handler.go
│       │   └── admin_handler.go
│       ├── middleware/
│       │   ├── search_cache.go
│       │   └── search_rate_limit.go
│       └── routes.go
├── search/
│   ├── client.go
│   ├── config.go
│   ├── indexes/
│   │   ├── post_index.go
│   │   ├── user_index.go
│   │   └── tenant_index.go
│   └── sync/
│       ├── sync_manager.go
│       └── event_handler.go
```

## MeiliSearch Client Configuration

### 1. Client Setup
```go
// internal/search/client.go
package search

import (
    "context"
    "fmt"
    "time"
    
    "github.com/meilisearch/meilisearch-go"
    "github.com/tranthanhloi/wn-api-v3/internal/config"
    "github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

type Client struct {
    client *meilisearch.Client
    config *SearchConfig
    logger utils.Logger
}

type SearchConfig struct {
    Host            string        `json:"host"`
    MasterKey       string        `json:"master_key"`
    SearchKey       string        `json:"search_key"`
    AdminKey        string        `json:"admin_key"`
    IndexPrefix     string        `json:"index_prefix"`
    BatchSize       int           `json:"batch_size"`
    SyncInterval    time.Duration `json:"sync_interval"`
    RequestTimeout  time.Duration `json:"request_timeout"`
    EnableTypoTolerance bool      `json:"enable_typo_tolerance"`
    EnableFacetedSearch bool      `json:"enable_faceted_search"`
}

func NewClient(cfg *SearchConfig, logger utils.Logger) (*Client, error) {
    client := meilisearch.NewClient(meilisearch.ClientConfig{
        Host:    cfg.Host,
        APIKey:  cfg.MasterKey,
        Timeout: cfg.RequestTimeout,
    })
    
    // Test connection
    if err := client.Health(); err != nil {
        return nil, fmt.Errorf("failed to connect to MeiliSearch: %w", err)
    }
    
    return &Client{
        client: client,
        config: cfg,
        logger: logger,
    }, nil
}

func (c *Client) GetIndex(name string) *meilisearch.Index {
    indexName := c.config.IndexPrefix + name
    return c.client.Index(indexName)
}

func (c *Client) CreateIndex(name string, primaryKey string) error {
    indexName := c.config.IndexPrefix + name
    
    _, err := c.client.CreateIndex(&meilisearch.IndexConfig{
        Uid:        indexName,
        PrimaryKey: primaryKey,
    })
    
    if err != nil {
        return fmt.Errorf("failed to create index %s: %w", indexName, err)
    }
    
    c.logger.Info("Created MeiliSearch index", utils.Fields{
        "index": indexName,
        "primary_key": primaryKey,
    })
    
    return nil
}

func (c *Client) DeleteIndex(name string) error {
    indexName := c.config.IndexPrefix + name
    
    _, err := c.client.DeleteIndex(indexName)
    if err != nil {
        return fmt.Errorf("failed to delete index %s: %w", indexName, err)
    }
    
    c.logger.Info("Deleted MeiliSearch index", utils.Fields{
        "index": indexName,
    })
    
    return nil
}

func (c *Client) GetStats() (*meilisearch.Stats, error) {
    return c.client.GetStats()
}

func (c *Client) IsHealthy() error {
    return c.client.Health()
}
```

### 2. Index Configuration
```go
// internal/search/indexes/post_index.go
package indexes

import (
    "context"
    "encoding/json"
    "fmt"
    "time"
    
    "github.com/meilisearch/meilisearch-go"
    "github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
    "github.com/tranthanhloi/wn-api-v3/internal/search"
)

type PostIndex struct {
    client *search.Client
    index  *meilisearch.Index
}

type PostDocument struct {
    ID          string    `json:"id"`
    TenantID    uint      `json:"tenant_id"`
    WebsiteID   uint      `json:"website_id"`
    Title       string    `json:"title"`
    Content     string    `json:"content"`
    Excerpt     string    `json:"excerpt"`
    Slug        string    `json:"slug"`
    Status      string    `json:"status"`
    Author      string    `json:"author"`
    AuthorID    uint      `json:"author_id"`
    Categories  []string  `json:"categories"`
    Tags        []string  `json:"tags"`
    PublishedAt *time.Time `json:"published_at"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    ReadingTime int       `json:"reading_time"`
    ViewCount   int       `json:"view_count"`
    LikeCount   int       `json:"like_count"`
    CommentCount int      `json:"comment_count"`
    SEOScore    float64   `json:"seo_score"`
    Language    string    `json:"language"`
    Featured    bool      `json:"featured"`
}

func NewPostIndex(client *search.Client) (*PostIndex, error) {
    index := client.GetIndex("posts")
    
    postIndex := &PostIndex{
        client: client,
        index:  index,
    }
    
    // Configure index settings
    if err := postIndex.configureIndex(); err != nil {
        return nil, fmt.Errorf("failed to configure post index: %w", err)
    }
    
    return postIndex, nil
}

func (p *PostIndex) configureIndex() error {
    // Set searchable attributes
    searchableAttributes := []string{
        "title",
        "content",
        "excerpt",
        "author",
        "categories",
        "tags",
    }
    
    _, err := p.index.UpdateSearchableAttributes(&searchableAttributes)
    if err != nil {
        return fmt.Errorf("failed to set searchable attributes: %w", err)
    }
    
    // Set filterable attributes
    filterableAttributes := []string{
        "tenant_id",
        "website_id",
        "status",
        "author_id",
        "categories",
        "tags",
        "published_at",
        "created_at",
        "featured",
        "language",
    }
    
    _, err = p.index.UpdateFilterableAttributes(&filterableAttributes)
    if err != nil {
        return fmt.Errorf("failed to set filterable attributes: %w", err)
    }
    
    // Set sortable attributes
    sortableAttributes := []string{
        "published_at",
        "created_at",
        "updated_at",
        "view_count",
        "like_count",
        "seo_score",
        "title",
    }
    
    _, err = p.index.UpdateSortableAttributes(&sortableAttributes)
    if err != nil {
        return fmt.Errorf("failed to set sortable attributes: %w", err)
    }
    
    // Set displayed attributes
    displayedAttributes := []string{
        "id",
        "tenant_id",
        "website_id",
        "title",
        "excerpt",
        "slug",
        "status",
        "author",
        "author_id",
        "categories",
        "tags",
        "published_at",
        "reading_time",
        "view_count",
        "featured",
        "language",
    }
    
    _, err = p.index.UpdateDisplayedAttributes(&displayedAttributes)
    if err != nil {
        return fmt.Errorf("failed to set displayed attributes: %w", err)
    }
    
    // Configure ranking rules
    rankingRules := []string{
        "words",
        "typo",
        "proximity",
        "attribute",
        "sort",
        "exactness",
        "featured:desc",
        "seo_score:desc",
        "published_at:desc",
    }
    
    _, err = p.index.UpdateRankingRules(&rankingRules)
    if err != nil {
        return fmt.Errorf("failed to set ranking rules: %w", err)
    }
    
    // Configure typo tolerance
    typoTolerance := meilisearch.TypoTolerance{
        Enabled: true,
        MinWordSizeForTypos: meilisearch.MinWordSizeForTypos{
            OneTypo:  5,
            TwoTypos: 9,
        },
        DisableOnWords:      []string{},
        DisableOnAttributes: []string{},
    }
    
    _, err = p.index.UpdateTypoTolerance(&typoTolerance)
    if err != nil {
        return fmt.Errorf("failed to set typo tolerance: %w", err)
    }
    
    // Configure faceting
    faceting := meilisearch.Faceting{
        MaxValuesPerFacet: 100,
        SortFacetValuesBy: map[string]string{
            "categories": "alpha",
            "tags":       "count",
            "author":     "alpha",
        },
    }
    
    _, err = p.index.UpdateFaceting(&faceting)
    if err != nil {
        return fmt.Errorf("failed to set faceting: %w", err)
    }
    
    // Configure pagination
    pagination := meilisearch.Pagination{
        MaxTotalHits: 10000,
    }
    
    _, err = p.index.UpdatePagination(&pagination)
    if err != nil {
        return fmt.Errorf("failed to set pagination: %w", err)
    }
    
    return nil
}

func (p *PostIndex) AddDocuments(posts []*models.BlogPost) error {
    documents := make([]PostDocument, len(posts))
    
    for i, post := range posts {
        documents[i] = p.postToDocument(post)
    }
    
    _, err := p.index.AddDocuments(documents)
    if err != nil {
        return fmt.Errorf("failed to add documents: %w", err)
    }
    
    return nil
}

func (p *PostIndex) UpdateDocuments(posts []*models.BlogPost) error {
    documents := make([]PostDocument, len(posts))
    
    for i, post := range posts {
        documents[i] = p.postToDocument(post)
    }
    
    _, err := p.index.UpdateDocuments(documents)
    if err != nil {
        return fmt.Errorf("failed to update documents: %w", err)
    }
    
    return nil
}

func (p *PostIndex) DeleteDocuments(postIDs []string) error {
    _, err := p.index.DeleteDocuments(postIDs)
    if err != nil {
        return fmt.Errorf("failed to delete documents: %w", err)
    }
    
    return nil
}

func (p *PostIndex) Search(ctx context.Context, request *SearchRequest) (*SearchResponse, error) {
    searchRequest := &meilisearch.SearchRequest{
        Query:                request.Query,
        Limit:                request.Limit,
        Offset:               request.Offset,
        AttributesToRetrieve: request.AttributesToRetrieve,
        AttributesToHighlight: request.AttributesToHighlight,
        Filter:               request.Filter,
        Sort:                 request.Sort,
        Facets:               request.Facets,
        HighlightPreTag:      "<mark>",
        HighlightPostTag:     "</mark>",
        MatchingStrategy:     "last",
        ShowMatchesPosition:  request.ShowMatchesPosition,
    }
    
    result, err := p.index.Search(request.Query, searchRequest)
    if err != nil {
        return nil, fmt.Errorf("search failed: %w", err)
    }
    
    // Convert result to our response format
    response := &SearchResponse{
        Hits:           result.Hits,
        Query:          result.Query,
        Limit:          result.Limit,
        Offset:         result.Offset,
        EstimatedTotalHits: result.EstimatedTotalHits,
        ProcessingTimeMs: result.ProcessingTimeMs,
        FacetDistribution: result.FacetDistribution,
    }
    
    return response, nil
}

func (p *PostIndex) postToDocument(post *models.BlogPost) PostDocument {
    // Extract categories and tags
    categories := make([]string, len(post.Categories))
    for i, cat := range post.Categories {
        categories[i] = cat.Name
    }
    
    tags := make([]string, len(post.Tags))
    for i, tag := range post.Tags {
        tags[i] = tag.Name
    }
    
    return PostDocument{
        ID:           fmt.Sprintf("post_%d", post.ID),
        TenantID:     post.TenantID,
        WebsiteID:    post.WebsiteID,
        Title:        post.Title,
        Content:      post.Content,
        Excerpt:      post.Excerpt,
        Slug:         post.Slug,
        Status:       post.Status,
        Author:       post.Author.Name,
        AuthorID:     post.AuthorID,
        Categories:   categories,
        Tags:         tags,
        PublishedAt:  post.PublishedAt,
        CreatedAt:    post.CreatedAt,
        UpdatedAt:    post.UpdatedAt,
        ReadingTime:  post.ReadingTime,
        ViewCount:    post.ViewCount,
        LikeCount:    post.LikeCount,
        CommentCount: post.CommentCount,
        SEOScore:     post.SEOScore,
        Language:     post.Language,
        Featured:     post.Featured,
    }
}

func (p *PostIndex) GetStats() (*meilisearch.StatsIndex, error) {
    return p.index.GetStats()
}

func (p *PostIndex) ClearAll() error {
    _, err := p.index.DeleteAllDocuments()
    return err
}
```

## Search Models

### 1. Search Request/Response Models
```go
// internal/modules/search/models/search_request.go
package models

import (
    "time"
)

type SearchRequest struct {
    Query                string   `json:"query" form:"q"`
    Limit                int64    `json:"limit" form:"limit" validate:"min=1,max=100"`
    Offset               int64    `json:"offset" form:"offset" validate:"min=0"`
    AttributesToRetrieve []string `json:"attributes_to_retrieve,omitempty"`
    AttributesToHighlight []string `json:"attributes_to_highlight,omitempty"`
    Filter               string   `json:"filter,omitempty"`
    Sort                 []string `json:"sort,omitempty"`
    Facets               []string `json:"facets,omitempty"`
    ShowMatchesPosition  bool     `json:"show_matches_position,omitempty"`
    
    // Filtering options
    TenantID    uint      `json:"tenant_id,omitempty"`
    WebsiteID   uint      `json:"website_id,omitempty"`
    Status      string    `json:"status,omitempty"`
    AuthorID    uint      `json:"author_id,omitempty"`
    Categories  []string  `json:"categories,omitempty"`
    Tags        []string  `json:"tags,omitempty"`
    Language    string    `json:"language,omitempty"`
    Featured    *bool     `json:"featured,omitempty"`
    DateFrom    *time.Time `json:"date_from,omitempty"`
    DateTo      *time.Time `json:"date_to,omitempty"`
}

func (r *SearchRequest) BuildFilter() string {
    var filters []string
    
    if r.TenantID != 0 {
        filters = append(filters, fmt.Sprintf("tenant_id = %d", r.TenantID))
    }
    
    if r.WebsiteID != 0 {
        filters = append(filters, fmt.Sprintf("website_id = %d", r.WebsiteID))
    }
    
    if r.Status != "" {
        filters = append(filters, fmt.Sprintf("status = %s", r.Status))
    }
    
    if r.AuthorID != 0 {
        filters = append(filters, fmt.Sprintf("author_id = %d", r.AuthorID))
    }
    
    if len(r.Categories) > 0 {
        categoryFilter := strings.Join(r.Categories, " OR ")
        filters = append(filters, fmt.Sprintf("categories IN [%s]", categoryFilter))
    }
    
    if len(r.Tags) > 0 {
        tagFilter := strings.Join(r.Tags, " OR ")
        filters = append(filters, fmt.Sprintf("tags IN [%s]", tagFilter))
    }
    
    if r.Language != "" {
        filters = append(filters, fmt.Sprintf("language = %s", r.Language))
    }
    
    if r.Featured != nil {
        filters = append(filters, fmt.Sprintf("featured = %t", *r.Featured))
    }
    
    if r.DateFrom != nil {
        filters = append(filters, fmt.Sprintf("published_at >= %d", r.DateFrom.Unix()))
    }
    
    if r.DateTo != nil {
        filters = append(filters, fmt.Sprintf("published_at <= %d", r.DateTo.Unix()))
    }
    
    // Always filter by published status for public search
    if r.Status == "" {
        filters = append(filters, "status = published")
    }
    
    return strings.Join(filters, " AND ")
}

func (r *SearchRequest) SetDefaults() {
    if r.Limit == 0 {
        r.Limit = 20
    }
    if r.Limit > 100 {
        r.Limit = 100
    }
    
    if r.AttributesToHighlight == nil {
        r.AttributesToHighlight = []string{"title", "content", "excerpt"}
    }
    
    if r.Sort == nil {
        r.Sort = []string{"published_at:desc"}
    }
}

type SearchResponse struct {
    Hits               []interface{} `json:"hits"`
    Query              string        `json:"query"`
    Limit              int64         `json:"limit"`
    Offset             int64         `json:"offset"`
    EstimatedTotalHits int64         `json:"estimated_total_hits"`
    ProcessingTimeMs   int64         `json:"processing_time_ms"`
    FacetDistribution  map[string]map[string]int64 `json:"facet_distribution,omitempty"`
    
    // Pagination metadata
    Page       int64 `json:"page"`
    TotalPages int64 `json:"total_pages"`
    HasMore    bool  `json:"has_more"`
}

func (r *SearchResponse) CalculatePagination() {
    if r.Limit > 0 {
        r.Page = (r.Offset / r.Limit) + 1
        r.TotalPages = (r.EstimatedTotalHits + r.Limit - 1) / r.Limit
        r.HasMore = r.Offset + r.Limit < r.EstimatedTotalHits
    }
}

type AutocompleteRequest struct {
    Query     string `json:"query" form:"q" validate:"required,min=1"`
    Limit     int64  `json:"limit" form:"limit" validate:"min=1,max=20"`
    TenantID  uint   `json:"tenant_id,omitempty"`
    WebsiteID uint   `json:"website_id,omitempty"`
}

type AutocompleteResponse struct {
    Suggestions []Suggestion `json:"suggestions"`
    Query       string       `json:"query"`
    ProcessingTimeMs int64    `json:"processing_time_ms"`
}

type Suggestion struct {
    Text     string `json:"text"`
    Type     string `json:"type"` // "post", "category", "tag", "author"
    Category string `json:"category,omitempty"`
    Count    int64  `json:"count"`
}

type SearchStats struct {
    TotalQueries        int64             `json:"total_queries"`
    QueriesLast24h      int64             `json:"queries_last_24h"`
    AverageResponseTime float64           `json:"average_response_time_ms"`
    PopularQueries      []PopularQuery    `json:"popular_queries"`
    PopularFilters      []PopularFilter   `json:"popular_filters"`
    IndexStats          map[string]IndexStat `json:"index_stats"`
}

type PopularQuery struct {
    Query       string `json:"query"`
    Count       int64  `json:"count"`
    LastQueried time.Time `json:"last_queried"`
}

type PopularFilter struct {
    Filter string `json:"filter"`
    Count  int64  `json:"count"`
}

type IndexStat struct {
    DocumentCount int64 `json:"document_count"`
    IndexSize     int64 `json:"index_size_bytes"`
    LastUpdated   time.Time `json:"last_updated"`
}
```

## Search Service Implementation

### 1. Search Service
```go
// internal/modules/search/services/search_service.go
package services

import (
    "context"
    "fmt"
    "strings"
    "time"
    
    "github.com/tranthanhloi/wn-api-v3/internal/modules/search/models"
    "github.com/tranthanhloi/wn-api-v3/internal/search"
    "github.com/tranthanhloi/wn-api-v3/internal/search/indexes"
    "github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

type SearchService struct {
    client    *search.Client
    postIndex *indexes.PostIndex
    userIndex *indexes.UserIndex
    logger    utils.Logger
}

func NewSearchService(client *search.Client, logger utils.Logger) (*SearchService, error) {
    postIndex, err := indexes.NewPostIndex(client)
    if err != nil {
        return nil, fmt.Errorf("failed to create post index: %w", err)
    }
    
    userIndex, err := indexes.NewUserIndex(client)
    if err != nil {
        return nil, fmt.Errorf("failed to create user index: %w", err)
    }
    
    return &SearchService{
        client:    client,
        postIndex: postIndex,
        userIndex: userIndex,
        logger:    logger,
    }, nil
}

func (s *SearchService) SearchPosts(ctx context.Context, req *models.SearchRequest) (*models.SearchResponse, error) {
    startTime := time.Now()
    
    // Set defaults and build filter
    req.SetDefaults()
    req.Filter = req.BuildFilter()
    
    // Perform search
    response, err := s.postIndex.Search(ctx, req)
    if err != nil {
        s.logger.Error("Post search failed", utils.Fields{
            "query": req.Query,
            "error": err.Error(),
        })
        return nil, fmt.Errorf("post search failed: %w", err)
    }
    
    // Calculate pagination
    response.CalculatePagination()
    
    // Log search metrics
    s.logger.Info("Post search completed", utils.Fields{
        "query":          req.Query,
        "results":        response.EstimatedTotalHits,
        "processing_time": time.Since(startTime).Milliseconds(),
        "tenant_id":      req.TenantID,
    })
    
    return response, nil
}

func (s *SearchService) SearchUsers(ctx context.Context, req *models.SearchRequest) (*models.SearchResponse, error) {
    startTime := time.Now()
    
    req.SetDefaults()
    req.Filter = req.BuildFilter()
    
    response, err := s.userIndex.Search(ctx, req)
    if err != nil {
        s.logger.Error("User search failed", utils.Fields{
            "query": req.Query,
            "error": err.Error(),
        })
        return nil, fmt.Errorf("user search failed: %w", err)
    }
    
    response.CalculatePagination()
    
    s.logger.Info("User search completed", utils.Fields{
        "query":          req.Query,
        "results":        response.EstimatedTotalHits,
        "processing_time": time.Since(startTime).Milliseconds(),
        "tenant_id":      req.TenantID,
    })
    
    return response, nil
}

func (s *SearchService) GlobalSearch(ctx context.Context, req *models.SearchRequest) (*models.GlobalSearchResponse, error) {
    // Perform parallel searches
    postsChan := make(chan *models.SearchResponse, 1)
    usersChan := make(chan *models.SearchResponse, 1)
    errorsChan := make(chan error, 2)
    
    // Search posts
    go func() {
        postReq := *req
        postReq.Limit = 10 // Limit for global search
        response, err := s.SearchPosts(ctx, &postReq)
        if err != nil {
            errorsChan <- err
            return
        }
        postsChan <- response
    }()
    
    // Search users
    go func() {
        userReq := *req
        userReq.Limit = 5 // Limit for global search
        response, err := s.SearchUsers(ctx, &userReq)
        if err != nil {
            errorsChan <- err
            return
        }
        usersChan <- response
    }()
    
    // Collect results
    var postsResponse *models.SearchResponse
    var usersResponse *models.SearchResponse
    var searchErr error
    
    for i := 0; i < 2; i++ {
        select {
        case err := <-errorsChan:
            searchErr = err
        case posts := <-postsChan:
            postsResponse = posts
        case users := <-usersChan:
            usersResponse = users
        case <-ctx.Done():
            return nil, ctx.Err()
        }
    }
    
    if searchErr != nil {
        return nil, searchErr
    }
    
    return &models.GlobalSearchResponse{
        Query:     req.Query,
        Posts:     postsResponse,
        Users:     usersResponse,
        TotalTime: time.Since(startTime).Milliseconds(),
    }, nil
}

func (s *SearchService) Autocomplete(ctx context.Context, req *models.AutocompleteRequest) (*models.AutocompleteResponse, error) {
    startTime := time.Now()
    
    if req.Limit == 0 {
        req.Limit = 10
    }
    
    // Build suggestions from multiple sources
    suggestions := []models.Suggestion{}
    
    // Get title suggestions from posts
    titleSuggestions, err := s.getTitleSuggestions(ctx, req)
    if err != nil {
        s.logger.Error("Failed to get title suggestions", utils.Fields{
            "error": err.Error(),
        })
    } else {
        suggestions = append(suggestions, titleSuggestions...)
    }
    
    // Get tag suggestions
    tagSuggestions, err := s.getTagSuggestions(ctx, req)
    if err != nil {
        s.logger.Error("Failed to get tag suggestions", utils.Fields{
            "error": err.Error(),
        })
    } else {
        suggestions = append(suggestions, tagSuggestions...)
    }
    
    // Get category suggestions
    categorySuggestions, err := s.getCategorySuggestions(ctx, req)
    if err != nil {
        s.logger.Error("Failed to get category suggestions", utils.Fields{
            "error": err.Error(),
        })
    } else {
        suggestions = append(suggestions, categorySuggestions...)
    }
    
    // Limit results
    if len(suggestions) > int(req.Limit) {
        suggestions = suggestions[:req.Limit]
    }
    
    response := &models.AutocompleteResponse{
        Suggestions:      suggestions,
        Query:            req.Query,
        ProcessingTimeMs: time.Since(startTime).Milliseconds(),
    }
    
    return response, nil
}

func (s *SearchService) getTitleSuggestions(ctx context.Context, req *models.AutocompleteRequest) ([]models.Suggestion, error) {
    searchReq := &models.SearchRequest{
        Query:     req.Query,
        Limit:     req.Limit / 2,
        TenantID:  req.TenantID,
        WebsiteID: req.WebsiteID,
        AttributesToRetrieve: []string{"title"},
    }
    
    response, err := s.postIndex.Search(ctx, searchReq)
    if err != nil {
        return nil, err
    }
    
    suggestions := make([]models.Suggestion, 0, len(response.Hits))
    for _, hit := range response.Hits {
        if hitMap, ok := hit.(map[string]interface{}); ok {
            if title, ok := hitMap["title"].(string); ok {
                suggestions = append(suggestions, models.Suggestion{
                    Text: title,
                    Type: "post",
                    Count: 1,
                })
            }
        }
    }
    
    return suggestions, nil
}

func (s *SearchService) getTagSuggestions(ctx context.Context, req *models.AutocompleteRequest) ([]models.Suggestion, error) {
    // Implementation for tag suggestions
    // This would query a separate tags index or use faceted search
    return []models.Suggestion{}, nil
}

func (s *SearchService) getCategorySuggestions(ctx context.Context, req *models.AutocompleteRequest) ([]models.Suggestion, error) {
    // Implementation for category suggestions
    return []models.Suggestion{}, nil
}

func (s *SearchService) GetSearchStats(ctx context.Context, tenantID uint) (*models.SearchStats, error) {
    // Get stats from MeiliSearch
    stats, err := s.client.GetStats()
    if err != nil {
        return nil, fmt.Errorf("failed to get MeiliSearch stats: %w", err)
    }
    
    // Convert to our stats format
    indexStats := make(map[string]models.IndexStat)
    for indexName, indexStat := range stats.Indexes {
        indexStats[indexName] = models.IndexStat{
            DocumentCount: int64(indexStat.NumberOfDocuments),
            IndexSize:     int64(indexStat.FieldDistribution["_content"]),
            LastUpdated:   indexStat.CreatedAt,
        }
    }
    
    return &models.SearchStats{
        IndexStats: indexStats,
        // Additional stats would be populated from analytics
    }, nil
}

func (s *SearchService) HealthCheck(ctx context.Context) error {
    return s.client.IsHealthy()
}
```

## Index Synchronization

### 1. Sync Service
```go
// internal/search/sync/sync_service.go
package sync

import (
    "context"
    "fmt"
    "sync"
    "time"
    
    "github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
    "github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
    "github.com/tranthanhloi/wn-api-v3/internal/search"
    "github.com/tranthanhloi/wn-api-v3/internal/search/indexes"
    "github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

type SyncService struct {
    client          *search.Client
    postIndex       *indexes.PostIndex
    postRepo        repositories.BlogPostRepository
    syncInterval    time.Duration
    batchSize       int
    logger          utils.Logger
    stopCh          chan struct{}
    wg              sync.WaitGroup
    lastSyncTime    time.Time
    mu              sync.RWMutex
}

func NewSyncService(
    client *search.Client,
    postRepo repositories.BlogPostRepository,
    logger utils.Logger,
) (*SyncService, error) {
    postIndex, err := indexes.NewPostIndex(client)
    if err != nil {
        return nil, fmt.Errorf("failed to create post index: %w", err)
    }
    
    return &SyncService{
        client:       client,
        postIndex:    postIndex,
        postRepo:     postRepo,
        syncInterval: 30 * time.Second,
        batchSize:    1000,
        logger:       logger,
        stopCh:       make(chan struct{}),
    }, nil
}

func (s *SyncService) Start(ctx context.Context) {
    s.logger.Info("Starting MeiliSearch sync service")
    
    s.wg.Add(1)
    go s.syncWorker(ctx)
    
    // Initial full sync
    if err := s.FullSync(ctx); err != nil {
        s.logger.Error("Initial full sync failed", utils.Fields{
            "error": err.Error(),
        })
    }
}

func (s *SyncService) Stop() {
    s.logger.Info("Stopping MeiliSearch sync service")
    close(s.stopCh)
    s.wg.Wait()
}

func (s *SyncService) syncWorker(ctx context.Context) {
    defer s.wg.Done()
    
    ticker := time.NewTicker(s.syncInterval)
    defer ticker.Stop()
    
    for {
        select {
        case <-ctx.Done():
            return
        case <-s.stopCh:
            return
        case <-ticker.C:
            if err := s.IncrementalSync(ctx); err != nil {
                s.logger.Error("Incremental sync failed", utils.Fields{
                    "error": err.Error(),
                })
            }
        }
    }
}

func (s *SyncService) FullSync(ctx context.Context) error {
    s.logger.Info("Starting full sync")
    startTime := time.Now()
    
    // Clear existing index
    if err := s.postIndex.ClearAll(); err != nil {
        return fmt.Errorf("failed to clear index: %w", err)
    }
    
    // Sync all posts in batches
    offset := 0
    totalSynced := 0
    
    for {
        posts, err := s.postRepo.GetBatch(ctx, offset, s.batchSize)
        if err != nil {
            return fmt.Errorf("failed to get posts batch: %w", err)
        }
        
        if len(posts) == 0 {
            break
        }
        
        // Filter published posts only
        publishedPosts := make([]*models.BlogPost, 0, len(posts))
        for _, post := range posts {
            if post.Status == models.PostStatusPublished {
                publishedPosts = append(publishedPosts, post)
            }
        }
        
        if len(publishedPosts) > 0 {
            if err := s.postIndex.AddDocuments(publishedPosts); err != nil {
                return fmt.Errorf("failed to add documents: %w", err)
            }
            totalSynced += len(publishedPosts)
        }
        
        offset += len(posts)
        
        // Prevent infinite loop
        if len(posts) < s.batchSize {
            break
        }
    }
    
    s.mu.Lock()
    s.lastSyncTime = time.Now()
    s.mu.Unlock()
    
    s.logger.Info("Full sync completed", utils.Fields{
        "total_synced": totalSynced,
        "duration":     time.Since(startTime).String(),
    })
    
    return nil
}

func (s *SyncService) IncrementalSync(ctx context.Context) error {
    s.mu.RLock()
    since := s.lastSyncTime
    s.mu.RUnlock()
    
    if since.IsZero() {
        return s.FullSync(ctx)
    }
    
    s.logger.Debug("Starting incremental sync", utils.Fields{
        "since": since.Format(time.RFC3339),
    })
    
    // Get updated posts
    updatedPosts, err := s.postRepo.GetUpdatedSince(ctx, since)
    if err != nil {
        return fmt.Errorf("failed to get updated posts: %w", err)
    }
    
    if len(updatedPosts) == 0 {
        s.logger.Debug("No updates found")
        return nil
    }
    
    // Process updates
    var toUpdate []*models.BlogPost
    var toDelete []string
    
    for _, post := range updatedPosts {
        if post.Status == models.PostStatusPublished {
            toUpdate = append(toUpdate, post)
        } else {
            // Delete from index if not published
            toDelete = append(toDelete, fmt.Sprintf("post_%d", post.ID))
        }
    }
    
    // Update documents
    if len(toUpdate) > 0 {
        if err := s.postIndex.UpdateDocuments(toUpdate); err != nil {
            return fmt.Errorf("failed to update documents: %w", err)
        }
    }
    
    // Delete documents
    if len(toDelete) > 0 {
        if err := s.postIndex.DeleteDocuments(toDelete); err != nil {
            return fmt.Errorf("failed to delete documents: %w", err)
        }
    }
    
    s.mu.Lock()
    s.lastSyncTime = time.Now()
    s.mu.Unlock()
    
    s.logger.Info("Incremental sync completed", utils.Fields{
        "updated": len(toUpdate),
        "deleted": len(toDelete),
    })
    
    return nil
}

func (s *SyncService) SyncPost(ctx context.Context, post *models.BlogPost) error {
    if post.Status == models.PostStatusPublished {
        return s.postIndex.UpdateDocuments([]*models.BlogPost{post})
    } else {
        return s.postIndex.DeleteDocuments([]string{fmt.Sprintf("post_%d", post.ID)})
    }
}

func (s *SyncService) GetLastSyncTime() time.Time {
    s.mu.RLock()
    defer s.mu.RUnlock()
    return s.lastSyncTime
}

func (s *SyncService) GetSyncStatus() map[string]interface{} {
    s.mu.RLock()
    defer s.mu.RUnlock()
    
    return map[string]interface{}{
        "last_sync_time": s.lastSyncTime,
        "sync_interval":  s.syncInterval.String(),
        "batch_size":     s.batchSize,
        "is_running":     true,
    }
}
```

## HTTP Handlers

### 1. Search Handler
```go
// internal/modules/search/handlers/search_handler.go
package handlers

import (
    "net/http"
    "strconv"
    "time"
    
    "github.com/gin-gonic/gin"
    "github.com/tranthanhloi/wn-api-v3/internal/modules/search/models"
    "github.com/tranthanhloi/wn-api-v3/internal/modules/search/services"
    "github.com/tranthanhloi/wn-api-v3/pkg/http/response"
    "github.com/tranthanhloi/wn-api-v3/pkg/utils"
    "github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

type SearchHandler struct {
    searchService *services.SearchService
    validator     validator.Validator
    logger        utils.Logger
}

func NewSearchHandler(
    searchService *services.SearchService,
    validator validator.Validator,
    logger utils.Logger,
) *SearchHandler {
    return &SearchHandler{
        searchService: searchService,
        validator:     validator,
        logger:        logger,
    }
}

// SearchPosts handles POST /search/posts
func (h *SearchHandler) SearchPosts(c *gin.Context) {
    var req models.SearchRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        response.BadRequest(c, "Invalid request format", err)
        return
    }
    
    // Validate request
    if err := h.validator.Validate(&req); err != nil {
        response.ValidationError(c, err)
        return
    }
    
    // Get tenant context
    tenantID, exists := c.Get("tenant_id")
    if exists {
        req.TenantID = tenantID.(uint)
    }
    
    // Get website context
    websiteID, exists := c.Get("website_id")
    if exists {
        req.WebsiteID = websiteID.(uint)
    }
    
    // Perform search
    result, err := h.searchService.SearchPosts(c.Request.Context(), &req)
    if err != nil {
        h.logger.Error("Post search failed", utils.Fields{
            "error": err.Error(),
            "query": req.Query,
        })
        response.InternalServerError(c, "Search failed", err)
        return
    }
    
    response.Success(c, "Posts retrieved successfully", result)
}

// SearchPostsGET handles GET /search/posts
func (h *SearchHandler) SearchPostsGET(c *gin.Context) {
    var req models.SearchRequest
    if err := c.ShouldBindQuery(&req); err != nil {
        response.BadRequest(c, "Invalid query parameters", err)
        return
    }
    
    // Get tenant context
    tenantID, exists := c.Get("tenant_id")
    if exists {
        req.TenantID = tenantID.(uint)
    }
    
    // Perform search
    result, err := h.searchService.SearchPosts(c.Request.Context(), &req)
    if err != nil {
        response.InternalServerError(c, "Search failed", err)
        return
    }
    
    response.Success(c, "Posts retrieved successfully", result)
}

// SearchUsers handles POST /search/users
func (h *SearchHandler) SearchUsers(c *gin.Context) {
    var req models.SearchRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        response.BadRequest(c, "Invalid request format", err)
        return
    }
    
    if err := h.validator.Validate(&req); err != nil {
        response.ValidationError(c, err)
        return
    }
    
    tenantID, exists := c.Get("tenant_id")
    if exists {
        req.TenantID = tenantID.(uint)
    }
    
    result, err := h.searchService.SearchUsers(c.Request.Context(), &req)
    if err != nil {
        response.InternalServerError(c, "Search failed", err)
        return
    }
    
    response.Success(c, "Users retrieved successfully", result)
}

// GlobalSearch handles POST /search/global
func (h *SearchHandler) GlobalSearch(c *gin.Context) {
    var req models.SearchRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        response.BadRequest(c, "Invalid request format", err)
        return
    }
    
    if err := h.validator.Validate(&req); err != nil {
        response.ValidationError(c, err)
        return
    }
    
    tenantID, exists := c.Get("tenant_id")
    if exists {
        req.TenantID = tenantID.(uint)
    }
    
    result, err := h.searchService.GlobalSearch(c.Request.Context(), &req)
    if err != nil {
        response.InternalServerError(c, "Search failed", err)
        return
    }
    
    response.Success(c, "Global search completed", result)
}

// Autocomplete handles GET /search/autocomplete
func (h *SearchHandler) Autocomplete(c *gin.Context) {
    var req models.AutocompleteRequest
    if err := c.ShouldBindQuery(&req); err != nil {
        response.BadRequest(c, "Invalid query parameters", err)
        return
    }
    
    if err := h.validator.Validate(&req); err != nil {
        response.ValidationError(c, err)
        return
    }
    
    tenantID, exists := c.Get("tenant_id")
    if exists {
        req.TenantID = tenantID.(uint)
    }
    
    result, err := h.searchService.Autocomplete(c.Request.Context(), &req)
    if err != nil {
        response.InternalServerError(c, "Autocomplete failed", err)
        return
    }
    
    response.Success(c, "Autocomplete suggestions retrieved", result)
}

// GetSearchStats handles GET /search/stats
func (h *SearchHandler) GetSearchStats(c *gin.Context) {
    tenantID, exists := c.Get("tenant_id")
    if !exists {
        response.Unauthorized(c, "Tenant context required")
        return
    }
    
    stats, err := h.searchService.GetSearchStats(c.Request.Context(), tenantID.(uint))
    if err != nil {
        response.InternalServerError(c, "Failed to get search stats", err)
        return
    }
    
    response.Success(c, "Search stats retrieved", stats)
}

// HealthCheck handles GET /search/health
func (h *SearchHandler) HealthCheck(c *gin.Context) {
    if err := h.searchService.HealthCheck(c.Request.Context()); err != nil {
        response.ServiceUnavailable(c, "Search service unhealthy", err)
        return
    }
    
    response.Success(c, "Search service healthy", gin.H{
        "status":    "healthy",
        "timestamp": time.Now().UTC(),
    })
}
```

## API Endpoints

### 1. Routes Configuration
```go
// internal/modules/search/routes.go
package search

import (
    "github.com/gin-gonic/gin"
    "gorm.io/gorm"
    
    "github.com/tranthanhloi/wn-api-v3/internal/modules/search/handlers"
    "github.com/tranthanhloi/wn-api-v3/internal/modules/search/services"
    "github.com/tranthanhloi/wn-api-v3/internal/search"
    "github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
    "github.com/tranthanhloi/wn-api-v3/pkg/utils"
    "github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

func RegisterRoutes(router *gin.RouterGroup, db *gorm.DB, validator validator.Validator, logger utils.Logger) {
    // Initialize search client
    searchConfig := &search.SearchConfig{
        Host:                os.Getenv("MEILI_HOST"),
        MasterKey:           os.Getenv("MEILI_MASTER_KEY"),
        SearchKey:           os.Getenv("MEILI_SEARCH_KEY"),
        IndexPrefix:         os.Getenv("MEILI_INDEX_PREFIX"),
        BatchSize:           1000,
        SyncInterval:        30 * time.Second,
        RequestTimeout:      10 * time.Second,
        EnableTypoTolerance: true,
        EnableFacetedSearch: true,
    }
    
    searchClient, err := search.NewClient(searchConfig, logger)
    if err != nil {
        logger.Error("Failed to initialize search client", utils.Fields{
            "error": err.Error(),
        })
        return
    }
    
    // Initialize service
    searchService, err := services.NewSearchService(searchClient, logger)
    if err != nil {
        logger.Error("Failed to initialize search service", utils.Fields{
            "error": err.Error(),
        })
        return
    }
    
    // Initialize handler
    searchHandler := handlers.NewSearchHandler(searchService, validator, logger)
    
    // Public search routes
    public := router.Group("/search")
    public.Use(middleware.OptionalTenantMiddleware()) // Optional tenant context
    public.Use(middleware.RateLimitMiddleware(100, 60)) // 100 req/min
    
    public.GET("/posts", searchHandler.SearchPostsGET)
    public.POST("/posts", searchHandler.SearchPosts)
    public.GET("/autocomplete", searchHandler.Autocomplete)
    public.GET("/health", searchHandler.HealthCheck)
    
    // Authenticated search routes
    authenticated := router.Group("/search")
    authenticated.Use(middleware.AuthenticationMiddleware())
    authenticated.Use(middleware.TenantIsolationMiddleware())
    
    authenticated.POST("/users", searchHandler.SearchUsers)
    authenticated.POST("/global", searchHandler.GlobalSearch)
    authenticated.GET("/stats", searchHandler.GetSearchStats)
    
    // Admin routes
    admin := router.Group("/admin/search")
    admin.Use(middleware.AuthenticationMiddleware())
    admin.Use(middleware.RequirePermission("search:admin"))
    
    adminHandler := handlers.NewSearchAdminHandler(searchService, validator, logger)
    admin.GET("/indexes", adminHandler.ListIndexes)
    admin.POST("/indexes/:name/reindex", adminHandler.ReindexIndex)
    admin.GET("/indexes/:name/stats", adminHandler.GetIndexStats)
    admin.DELETE("/indexes/:name", adminHandler.DeleteIndex)
    admin.POST("/sync/full", adminHandler.FullSync)
    admin.POST("/sync/incremental", adminHandler.IncrementalSync)
    admin.GET("/sync/status", adminHandler.GetSyncStatus)
}
```

### 2. API Documentation
```yaml
# API Endpoints Documentation

# Public Search Endpoints
GET /api/cms/v1/search/posts
  description: Search published posts
  parameters:
    - name: q
      in: query
      required: true
      type: string
    - name: limit
      in: query
      type: integer
      default: 20
    - name: offset
      in: query
      type: integer
      default: 0
    - name: categories
      in: query
      type: array
    - name: tags
      in: query
      type: array
    - name: sort
      in: query
      type: string
      default: "published_at:desc"

POST /api/cms/v1/search/posts
  description: Advanced post search with filters
  body:
    query: string
    limit: integer
    offset: integer
    categories: array
    tags: array
    author_id: integer
    date_from: string
    date_to: string
    sort: array
    facets: array

GET /api/cms/v1/search/autocomplete
  description: Get search suggestions
  parameters:
    - name: q
      in: query
      required: true
      type: string
    - name: limit
      in: query
      type: integer
      default: 10

# Authenticated Search Endpoints
POST /api/cms/v1/search/users
  description: Search users (requires authentication)
  headers:
    Authorization: Bearer {token}
  body:
    query: string
    limit: integer
    offset: integer

POST /api/cms/v1/search/global
  description: Search across all content types
  headers:
    Authorization: Bearer {token}
  body:
    query: string
    limit: integer

GET /api/cms/v1/search/stats
  description: Get search statistics
  headers:
    Authorization: Bearer {token}

# Admin Search Endpoints
GET /api/cms/v1/admin/search/indexes
  description: List all search indexes
  headers:
    Authorization: Bearer {admin_token}

POST /api/cms/v1/admin/search/indexes/{name}/reindex
  description: Reindex specific index
  headers:
    Authorization: Bearer {admin_token}

GET /api/cms/v1/admin/search/sync/status
  description: Get sync status
  headers:
    Authorization: Bearer {admin_token}

POST /api/cms/v1/admin/search/sync/full
  description: Trigger full sync
  headers:
    Authorization: Bearer {admin_token}
```

## Frontend Integration

### 1. JavaScript Client
```javascript
// public/js/search-client.js
class SearchClient {
    constructor(baseURL, options = {}) {
        this.baseURL = baseURL;
        this.options = {
            timeout: 10000,
            retries: 3,
            ...options
        };
        this.cache = new Map();
    }
    
    async searchPosts(query, options = {}) {
        const params = new URLSearchParams({
            q: query,
            limit: options.limit || 20,
            offset: options.offset || 0,
            ...options.filters
        });
        
        const cacheKey = `posts:${params.toString()}`;
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }
        
        try {
            const response = await fetch(`${this.baseURL}/search/posts?${params}`);
            const data = await response.json();
            
            if (data.success) {
                this.cache.set(cacheKey, data.data);
                return data.data;
            } else {
                throw new Error(data.error || 'Search failed');
            }
        } catch (error) {
            console.error('Search error:', error);
            throw error;
        }
    }
    
    async autocomplete(query, options = {}) {
        if (!query || query.length < 2) {
            return { suggestions: [] };
        }
        
        const params = new URLSearchParams({
            q: query,
            limit: options.limit || 10
        });
        
        try {
            const response = await fetch(`${this.baseURL}/search/autocomplete?${params}`);
            const data = await response.json();
            
            if (data.success) {
                return data.data;
            } else {
                throw new Error(data.error || 'Autocomplete failed');
            }
        } catch (error) {
            console.error('Autocomplete error:', error);
            return { suggestions: [] };
        }
    }
    
    async globalSearch(query, options = {}) {
        try {
            const response = await fetch(`${this.baseURL}/search/global`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${options.token}`
                },
                body: JSON.stringify({
                    query: query,
                    limit: options.limit || 20
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                return data.data;
            } else {
                throw new Error(data.error || 'Global search failed');
            }
        } catch (error) {
            console.error('Global search error:', error);
            throw error;
        }
    }
    
    clearCache() {
        this.cache.clear();
    }
}

// Usage example
const searchClient = new SearchClient('http://localhost:8080/api/cms/v1');

// Search posts
searchClient.searchPosts('javascript tutorial', {
    limit: 10,
    filters: {
        categories: ['programming', 'web-development'],
        tags: ['javascript', 'tutorial']
    }
}).then(results => {
    console.log('Search results:', results);
}).catch(error => {
    console.error('Search failed:', error);
});

// Autocomplete
searchClient.autocomplete('java').then(suggestions => {
    console.log('Suggestions:', suggestions);
});
```

### 2. React Search Component
```jsx
// components/Search/SearchBox.jsx
import React, { useState, useEffect, useCallback } from 'react';
import { debounce } from 'lodash';

const SearchBox = ({ onSearch, onSuggestionSelect, placeholder = "Search..." }) => {
    const [query, setQuery] = useState('');
    const [suggestions, setSuggestions] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [showSuggestions, setShowSuggestions] = useState(false);
    
    const searchClient = new SearchClient('/api/cms/v1');
    
    // Debounced autocomplete
    const debouncedAutocomplete = useCallback(
        debounce(async (searchQuery) => {
            if (searchQuery.length < 2) {
                setSuggestions([]);
                return;
            }
            
            setIsLoading(true);
            try {
                const result = await searchClient.autocomplete(searchQuery);
                setSuggestions(result.suggestions || []);
            } catch (error) {
                console.error('Autocomplete error:', error);
                setSuggestions([]);
            } finally {
                setIsLoading(false);
            }
        }, 300),
        []
    );
    
    useEffect(() => {
        debouncedAutocomplete(query);
    }, [query, debouncedAutocomplete]);
    
    const handleInputChange = (e) => {
        const value = e.target.value;
        setQuery(value);
        setShowSuggestions(true);
    };
    
    const handleSearch = async (searchQuery = query) => {
        if (!searchQuery.trim()) return;
        
        setShowSuggestions(false);
        setIsLoading(true);
        
        try {
            const results = await searchClient.searchPosts(searchQuery);
            onSearch(results);
        } catch (error) {
            console.error('Search error:', error);
        } finally {
            setIsLoading(false);
        }
    };
    
    const handleSuggestionClick = (suggestion) => {
        setQuery(suggestion.text);
        setShowSuggestions(false);
        onSuggestionSelect(suggestion);
        handleSearch(suggestion.text);
    };
    
    const handleKeyDown = (e) => {
        if (e.key === 'Enter') {
            handleSearch();
        } else if (e.key === 'Escape') {
            setShowSuggestions(false);
        }
    };
    
    return (
        <div className="search-box">
            <div className="search-input-container">
                <input
                    type="text"
                    value={query}
                    onChange={handleInputChange}
                    onKeyDown={handleKeyDown}
                    placeholder={placeholder}
                    className="search-input"
                />
                <button
                    onClick={() => handleSearch()}
                    disabled={isLoading}
                    className="search-button"
                >
                    {isLoading ? 'Searching...' : 'Search'}
                </button>
            </div>
            
            {showSuggestions && suggestions.length > 0 && (
                <div className="suggestions-dropdown">
                    {suggestions.map((suggestion, index) => (
                        <div
                            key={index}
                            onClick={() => handleSuggestionClick(suggestion)}
                            className="suggestion-item"
                        >
                            <span className="suggestion-text">{suggestion.text}</span>
                            <span className="suggestion-type">{suggestion.type}</span>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

export default SearchBox;
```

## Performance Optimization

### 1. Caching Strategy
```go
// pkg/cache/search_cache.go
package cache

import (
    "context"
    "encoding/json"
    "fmt"
    "time"
    
    "github.com/redis/go-redis/v9"
)

type SearchCache struct {
    client *redis.Client
    ttl    time.Duration
}

func NewSearchCache(redisClient *redis.Client, ttl time.Duration) *SearchCache {
    return &SearchCache{
        client: redisClient,
        ttl:    ttl,
    }
}

func (c *SearchCache) Get(ctx context.Context, key string) ([]byte, error) {
    cacheKey := fmt.Sprintf("search:%s", key)
    
    val, err := c.client.Get(ctx, cacheKey).Result()
    if err == redis.Nil {
        return nil, nil // Cache miss
    }
    if err != nil {
        return nil, err
    }
    
    return []byte(val), nil
}

func (c *SearchCache) Set(ctx context.Context, key string, value interface{}) error {
    cacheKey := fmt.Sprintf("search:%s", key)
    
    data, err := json.Marshal(value)
    if err != nil {
        return err
    }
    
    return c.client.Set(ctx, cacheKey, data, c.ttl).Err()
}

func (c *SearchCache) Delete(ctx context.Context, key string) error {
    cacheKey := fmt.Sprintf("search:%s", key)
    return c.client.Del(ctx, cacheKey).Err()
}

func (c *SearchCache) ClearAll(ctx context.Context) error {
    keys, err := c.client.Keys(ctx, "search:*").Result()
    if err != nil {
        return err
    }
    
    if len(keys) > 0 {
        return c.client.Del(ctx, keys...).Err()
    }
    
    return nil
}
```

### 2. Search Middleware
```go
// internal/modules/search/middleware/cache_middleware.go
package middleware

import (
    "crypto/md5"
    "encoding/hex"
    "encoding/json"
    "fmt"
    "net/http"
    "strings"
    "time"
    
    "github.com/gin-gonic/gin"
    "github.com/tranthanhloi/wn-api-v3/pkg/cache"
    "github.com/tranthanhloi/wn-api-v3/pkg/http/response"
)

func SearchCacheMiddleware(searchCache *cache.SearchCache, ttl time.Duration) gin.HandlerFunc {
    return func(c *gin.Context) {
        // Only cache GET requests
        if c.Request.Method != http.MethodGet {
            c.Next()
            return
        }
        
        // Skip cache for authenticated requests
        if c.GetHeader("Authorization") != "" {
            c.Next()
            return
        }
        
        // Generate cache key
        cacheKey := generateCacheKey(c)
        
        // Try to get from cache
        cachedData, err := searchCache.Get(c.Request.Context(), cacheKey)
        if err == nil && cachedData != nil {
            var cachedResponse interface{}
            if err := json.Unmarshal(cachedData, &cachedResponse); err == nil {
                c.Header("X-Cache", "HIT")
                c.Header("X-Cache-TTL", ttl.String())
                response.Success(c, "Search results (cached)", cachedResponse)
                c.Abort()
                return
            }
        }
        
        // Continue with request
        c.Next()
        
        // Cache successful responses
        if c.Writer.Status() == http.StatusOK {
            // This would require custom response writer to capture response
            // Implementation depends on your response package
        }
    }
}

func generateCacheKey(c *gin.Context) string {
    // Include path, query parameters, and tenant context
    keyData := fmt.Sprintf("%s:%s:%s", 
        c.Request.URL.Path, 
        c.Request.URL.RawQuery,
        c.GetString("tenant_id"),
    )
    
    hash := md5.Sum([]byte(keyData))
    return hex.EncodeToString(hash[:])
}
```

## Monitoring và Analytics

### 1. Search Analytics
```go
// internal/modules/search/analytics/search_analytics.go
package analytics

import (
    "context"
    "time"
    
    "github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

type SearchAnalytics struct {
    logger utils.Logger
    // Could integrate with analytics service
}

func NewSearchAnalytics(logger utils.Logger) *SearchAnalytics {
    return &SearchAnalytics{
        logger: logger,
    }
}

func (a *SearchAnalytics) TrackSearch(ctx context.Context, event *SearchEvent) {
    // Log search event
    a.logger.Info("Search performed", utils.Fields{
        "query":            event.Query,
        "tenant_id":        event.TenantID,
        "user_id":          event.UserID,
        "results_count":    event.ResultsCount,
        "processing_time":  event.ProcessingTime,
        "filters":          event.Filters,
        "page":             event.Page,
        "timestamp":        event.Timestamp,
    })
    
    // Could send to analytics service
    // analyticsService.TrackEvent("search", event)
}

func (a *SearchAnalytics) TrackClick(ctx context.Context, event *ClickEvent) {
    a.logger.Info("Search result clicked", utils.Fields{
        "query":      event.Query,
        "result_id":  event.ResultID,
        "position":   event.Position,
        "tenant_id":  event.TenantID,
        "user_id":    event.UserID,
        "timestamp":  event.Timestamp,
    })
}

type SearchEvent struct {
    Query          string            `json:"query"`
    TenantID       uint              `json:"tenant_id"`
    UserID         uint              `json:"user_id"`
    ResultsCount   int64             `json:"results_count"`
    ProcessingTime int64             `json:"processing_time_ms"`
    Filters        map[string]string `json:"filters"`
    Page           int               `json:"page"`
    Timestamp      time.Time         `json:"timestamp"`
}

type ClickEvent struct {
    Query     string    `json:"query"`
    ResultID  string    `json:"result_id"`
    Position  int       `json:"position"`
    TenantID  uint      `json:"tenant_id"`
    UserID    uint      `json:"user_id"`
    Timestamp time.Time `json:"timestamp"`
}
```

### 2. Health Monitoring
```go
// internal/modules/search/monitoring/health_monitor.go
package monitoring

import (
    "context"
    "time"
    
    "github.com/tranthanhloi/wn-api-v3/internal/search"
    "github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

type HealthMonitor struct {
    client   *search.Client
    logger   utils.Logger
    interval time.Duration
}

func NewHealthMonitor(client *search.Client, logger utils.Logger) *HealthMonitor {
    return &HealthMonitor{
        client:   client,
        logger:   logger,
        interval: 30 * time.Second,
    }
}

func (h *HealthMonitor) Start(ctx context.Context) {
    ticker := time.NewTicker(h.interval)
    defer ticker.Stop()
    
    for {
        select {
        case <-ctx.Done():
            return
        case <-ticker.C:
            h.checkHealth(ctx)
        }
    }
}

func (h *HealthMonitor) checkHealth(ctx context.Context) {
    startTime := time.Now()
    
    err := h.client.IsHealthy()
    responseTime := time.Since(startTime)
    
    if err != nil {
        h.logger.Error("MeiliSearch health check failed", utils.Fields{
            "error":         err.Error(),
            "response_time": responseTime.Milliseconds(),
        })
    } else {
        h.logger.Debug("MeiliSearch health check passed", utils.Fields{
            "response_time": responseTime.Milliseconds(),
        })
    }
    
    // Could send metrics to monitoring system
    // metrics.RecordHealthCheck("meilisearch", err == nil, responseTime)
}
```

## Deployment

### 1. Docker Configuration
```dockerfile
# Dockerfile.meilisearch
FROM getmeili/meilisearch:v1.5

# Set environment variables
ENV MEILI_ENV=production
ENV MEILI_DB_PATH=/meili_data
ENV MEILI_HTTP_ADDR=0.0.0.0:7700
ENV MEILI_NO_ANALYTICS=true
ENV MEILI_LOG_LEVEL=INFO

# Create data directory
RUN mkdir -p /meili_data

# Expose port
EXPOSE 7700

# Volume for data persistence
VOLUME ["/meili_data"]

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:7700/health || exit 1

# Run MeiliSearch
CMD ["meilisearch"]
```

### 2. Kubernetes Deployment
```yaml
# k8s/meilisearch-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: meilisearch
  labels:
    app: meilisearch
spec:
  replicas: 1
  selector:
    matchLabels:
      app: meilisearch
  template:
    metadata:
      labels:
        app: meilisearch
    spec:
      containers:
      - name: meilisearch
        image: getmeili/meilisearch:v1.5
        ports:
        - containerPort: 7700
        env:
        - name: MEILI_MASTER_KEY
          valueFrom:
            secretKeyRef:
              name: meilisearch-secret
              key: master-key
        - name: MEILI_ENV
          value: "production"
        - name: MEILI_DB_PATH
          value: "/meili_data"
        - name: MEILI_HTTP_ADDR
          value: "0.0.0.0:7700"
        volumeMounts:
        - name: data
          mountPath: /meili_data
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 7700
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 7700
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: data
        persistentVolumeClaim:
          claimName: meilisearch-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: meilisearch-service
spec:
  selector:
    app: meilisearch
  ports:
  - port: 7700
    targetPort: 7700
  type: ClusterIP
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: meilisearch-pvc
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
```

## Testing

### 1. Unit Tests
```go
// internal/modules/search/services/search_service_test.go
package services

import (
    "context"
    "testing"
    
    . "github.com/onsi/ginkgo/v2"
    . "github.com/onsi/gomega"
    "github.com/stretchr/testify/mock"
    
    "github.com/tranthanhloi/wn-api-v3/internal/modules/search/models"
    "github.com/tranthanhloi/wn-api-v3/internal/tests/mocks"
)

var _ = Describe("SearchService", func() {
    var (
        service     *SearchService
        mockClient  *mocks.MockSearchClient
        mockIndex   *mocks.MockPostIndex
    )
    
    BeforeEach(func() {
        mockClient = new(mocks.MockSearchClient)
        mockIndex = new(mocks.MockPostIndex)
        service = &SearchService{
            client:    mockClient,
            postIndex: mockIndex,
        }
    })
    
    Describe("SearchPosts", func() {
        Context("with valid request", func() {
            It("should return search results", func() {
                // Arrange
                req := &models.SearchRequest{
                    Query:    "test query",
                    Limit:    20,
                    TenantID: 1,
                }
                
                expectedResponse := &models.SearchResponse{
                    Hits:               []interface{}{},
                    Query:              "test query",
                    EstimatedTotalHits: 10,
                    ProcessingTimeMs:   50,
                }
                
                mockIndex.On("Search", mock.Anything, mock.Anything).
                    Return(expectedResponse, nil)
                
                // Act
                result, err := service.SearchPosts(context.Background(), req)
                
                // Assert
                Expect(err).NotTo(HaveOccurred())
                Expect(result).NotTo(BeNil())
                Expect(result.Query).To(Equal("test query"))
                Expect(result.EstimatedTotalHits).To(Equal(int64(10)))
                
                mockIndex.AssertExpectations(GinkgoT())
            })
        })
        
        Context("with search error", func() {
            It("should return error", func() {
                // Arrange
                req := &models.SearchRequest{
                    Query: "test query",
                }
                
                mockIndex.On("Search", mock.Anything, mock.Anything).
                    Return(nil, errors.New("search failed"))
                
                // Act
                result, err := service.SearchPosts(context.Background(), req)
                
                // Assert
                Expect(err).To(HaveOccurred())
                Expect(result).To(BeNil())
                Expect(err.Error()).To(ContainSubstring("search failed"))
            })
        })
    })
})
```

### 2. Integration Tests
```go
// internal/modules/search/integration_test.go
package search

import (
    "context"
    "testing"
    
    . "github.com/onsi/ginkgo/v2"
    . "github.com/onsi/gomega"
    
    "github.com/tranthanhloi/wn-api-v3/internal/modules/search/models"
    "github.com/tranthanhloi/wn-api-v3/internal/modules/search/services"
    "github.com/tranthanhloi/wn-api-v3/internal/search"
    "github.com/tranthanhloi/wn-api-v3/internal/tests/helpers"
)

var _ = Describe("Search Integration", func() {
    var (
        searchService *services.SearchService
        testHelper    *helpers.TestHelper
    )
    
    BeforeEach(func() {
        // Setup test MeiliSearch instance
        searchConfig := &search.SearchConfig{
            Host:        "http://localhost:7700",
            MasterKey:   "test-master-key",
            IndexPrefix: "test_",
        }
        
        client, err := search.NewClient(searchConfig, logger)
        Expect(err).NotTo(HaveOccurred())
        
        searchService, err = services.NewSearchService(client, logger)
        Expect(err).NotTo(HaveOccurred())
        
        testHelper = helpers.NewTestHelper(db)
    })
    
    AfterEach(func() {
        // Clean up test indexes
        testHelper.CleanupSearchIndexes()
    })
    
    Describe("Post Search", func() {
        BeforeEach(func() {
            // Seed test data
            testHelper.SeedPosts([]string{
                "JavaScript Tutorial for Beginners",
                "Advanced React Patterns",
                "Node.js Best Practices",
                "TypeScript Deep Dive",
                "Vue.js Component Design",
            })
        })
        
        It("should find posts by title", func() {
            req := &models.SearchRequest{
                Query: "JavaScript",
                Limit: 10,
            }
            
            result, err := searchService.SearchPosts(context.Background(), req)
            
            Expect(err).NotTo(HaveOccurred())
            Expect(result.EstimatedTotalHits).To(BeNumerically(">=", 1))
            Expect(result.Hits).NotTo(BeEmpty())
        })
        
        It("should handle typos", func() {
            req := &models.SearchRequest{
                Query: "Javscript", // Typo in JavaScript
                Limit: 10,
            }
            
            result, err := searchService.SearchPosts(context.Background(), req)
            
            Expect(err).NotTo(HaveOccurred())
            Expect(result.EstimatedTotalHits).To(BeNumerically(">=", 1))
        })
        
        It("should filter by tenant", func() {
            req := &models.SearchRequest{
                Query:    "React",
                TenantID: 1,
                Limit:    10,
            }
            
            result, err := searchService.SearchPosts(context.Background(), req)
            
            Expect(err).NotTo(HaveOccurred())
            // Verify results are filtered by tenant
        })
    })
})
```

## Best Practices

### 1. Index Management
- **Separate indexes** cho different content types
- **Regular cleanup** của expired documents
- **Monitor index size** và performance
- **Backup index configuration** và settings

### 2. Performance Optimization
- **Cache frequent searches** với Redis
- **Paginate results** để avoid large responses
- **Use faceted search** cho filtering
- **Optimize searchable attributes**

### 3. Security
- **API key management** với proper permissions
- **Tenant isolation** trong search results
- **Rate limiting** cho search endpoints
- **Input validation** để prevent injection

### 4. Monitoring
- **Track search performance** và usage
- **Monitor index health** và sync status
- **Alert on search failures**
- **Analytics** cho search behavior

---

## Conclusion

MeiliSearch integration cung cấp powerful search capabilities cho Blog API v3:

1. **Ultra-fast Search**: Sub-millisecond response time
2. **Typo Tolerance**: Tự động fix typos trong search queries
3. **Faceted Search**: Advanced filtering options
4. **Real-time Sync**: Automatic index updates
5. **Multi-tenant Support**: Proper data isolation
6. **Rich APIs**: Comprehensive REST endpoints
7. **Frontend Ready**: JavaScript client và React components

Implementation này cung cấp foundation mạnh mẽ cho advanced search features với scalability, performance, và user experience được tối ưu hóa.
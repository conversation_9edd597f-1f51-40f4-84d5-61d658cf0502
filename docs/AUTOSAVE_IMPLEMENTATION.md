# Blog Post Auto-save Implementation

## Overview
Implemented automatic draft saving functionality for blog posts (task-159: "Tự động lưu nháp bài viết") to prevent data loss during content creation.

## Features
- **Automatic Draft Saving**: Saves blog post content automatically as users type
- **Conflict Detection**: Detects when multiple users edit the same post
- **Version Tracking**: Maintains version numbers for each auto-save
- **Conflict Resolution**: Three strategies: keep_saved, use_autosave, merge
- **Cleanup**: Automatic removal of old auto-saves after 7 days

## API Endpoints

### Individual Post Auto-save
- `POST /api/cms/v1/blog/posts/:id/autosave` - Save draft automatically
- `GET /api/cms/v1/blog/posts/:id/autosave` - Retrieve auto-save
- `DELETE /api/cms/v1/blog/posts/:id/autosave` - Delete auto-save
- `GET /api/cms/v1/blog/posts/:id/autosave/status` - Get auto-save status
- `POST /api/cms/v1/blog/posts/:id/autosave/restore` - <PERSON><PERSON> from auto-save

### Global Auto-save Management
- `POST /api/cms/v1/blog/autosave/resolve-conflict` - Resolve conflicts
- `GET /api/cms/v1/blog/autosave/conflicted` - Get all conflicted auto-saves

## Database Schema
```sql
CREATE TABLE blog_post_autosaves (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    post_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    
    -- Auto-saved content
    title VARCHAR(255),
    content LONGTEXT,
    excerpt TEXT,
    featured_image VARCHAR(500),
    
    -- Metadata
    version INT UNSIGNED NOT NULL DEFAULT 1,
    is_conflicted BOOLEAN DEFAULT FALSE,
    last_saved_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Unique: one auto-save per post per user
    UNIQUE KEY uk_blog_post_autosaves_post_user (post_id, user_id)
);
```

## Client-Side Implementation (Recommended)
```javascript
// Example client-side implementation with debouncing
let autoSaveTimer;
const AUTOSAVE_DELAY = 2000; // 2 seconds

function autoSave() {
    clearTimeout(autoSaveTimer);
    autoSaveTimer = setTimeout(async () => {
        const data = {
            title: document.getElementById('title').value,
            content: document.getElementById('content').value,
            excerpt: document.getElementById('excerpt').value,
            featured_image: document.getElementById('featured_image').value
        };
        
        try {
            const response = await fetch(`/api/cms/v1/blog/posts/${postId}/autosave`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`,
                    'X-Tenant-ID': tenantId,
                    'X-Website-ID': websiteId
                },
                body: JSON.stringify(data)
            });
            
            if (response.ok) {
                updateAutoSaveStatus('Saved');
            }
        } catch (error) {
            console.error('Auto-save failed:', error);
        }
    }, AUTOSAVE_DELAY);
}

// Attach to input events
document.querySelectorAll('input, textarea').forEach(element => {
    element.addEventListener('input', autoSave);
});
```

## Conflict Resolution

### Detection
- Conflicts are detected when a different user has edited the post after the last auto-save
- The `is_conflicted` flag is set to true when conflicts are detected

### Resolution Options
1. **keep_saved**: Discard auto-save, keep the current saved version
2. **use_autosave**: Replace current content with auto-saved version
3. **merge**: Keep both for manual resolution

## Testing
Run the test script to verify functionality:
```bash
./test_autosave.sh
```

## Migration
The database migration is located at:
`internal/database/migrations/g_blog/609_create_blog_post_autosaves_table.up.sql`

Run migration:
```bash
make migrate-up MODULE=g_blog
```

## Future Enhancements
- Real-time collaboration indicators
- Diff view for conflicts
- Compression for large content
- WebSocket notifications for conflicts
- Auto-save history/timeline view
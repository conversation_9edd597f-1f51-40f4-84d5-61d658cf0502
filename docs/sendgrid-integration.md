# SendGrid Email Integration

This document describes the SendGrid email integration implemented in the Blog API v3 notification system.

## Overview

The Blog API v3 now supports SendGrid as an email provider alongside SMTP and mock providers. This integration allows for reliable email delivery with advanced features like tracking, analytics, and scalability.

## Configuration

### Environment Variables

Add the following environment variables to your `.env` file:

```bash
# Email provider selection
EMAIL_PROVIDER=sendgrid

# SendGrid Configuration
SENDGRID_API_KEY=your_sendgrid_api_key_here
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=Your App Name

# Email defaults
DEFAULT_FROM_EMAIL=<EMAIL>
DEFAULT_FROM_NAME=Blog API v3
EMAIL_ENABLE_TRACKING=true
EMAIL_RETRY_ATTEMPTS=3
EMAIL_RETRY_DELAY=300
```

### SendGrid Setup

1. **Create SendGrid Account**: Sign up at [SendGrid](https://sendgrid.com/)
2. **Get API Key**: 
   - Go to Settings > API Keys
   - Create a new API key with "Full Access" or "Mail Send" permissions
   - Copy the API key to your environment variables
3. **Verify Sender**: Add and verify your sender email address in SendGrid
4. **Domain Authentication** (Optional but recommended): Set up domain authentication for better deliverability

## Features

### Supported Email Types
- ✅ Plain text emails
- ✅ HTML emails  
- ✅ Mixed content (auto-detection)

### Tracking & Metadata
- ✅ Custom tracking metadata
- ✅ Tenant-specific tracking
- ✅ Recipient tracking
- ✅ Notification tracking
- ✅ Click and open tracking (when enabled in SendGrid)

### Error Handling
- ✅ Comprehensive error logging
- ✅ Status code validation
- ✅ Retry mechanisms
- ✅ Fallback to mock provider on configuration errors

## Usage

### Basic Email Sending

The SendGrid provider is automatically selected based on the `EMAIL_PROVIDER` environment variable:

```go
// The notification service will automatically use SendGrid
notificationService.SendNotification(ctx, notificationRequest)
```

### Direct Provider Usage

```go
provider := services.NewSendGridProvider(
    apiKey,
    fromName,
    fromEmail,
    logger,
)

recipient := &models.NotificationRecipient{
    RecipientAddress: "<EMAIL>",
    RecipientType:    models.RecipientTypeEmail,
}

metadata := map[string]interface{}{
    "tracking_id":     "unique-tracking-id",
    "tenant_id":       1,
    "notification_id": 123,
}

err := provider.SendEmail(recipient, "Subject", "Email content", metadata)
```

## Testing

### Manual Testing

Run the test script to verify your SendGrid integration:

```bash
# Make sure your environment variables are set
export SENDGRID_API_KEY="your_api_key"
export SENDGRID_FROM_EMAIL="<EMAIL>"
export SENDGRID_FROM_NAME="Your App"

# Run the test
go run test_sendgrid.go
```

### Integration Testing

The notification system includes built-in testing endpoints:

```bash
# Test email sending via API
POST /api/v1/auth/test-email
Content-Type: application/json

{
  "to": "<EMAIL>",
  "subject": "Test Email",
  "content": "This is a test email",
  "template_id": 1
}
```

## Provider Comparison

| Feature | SMTP | SendGrid | Mock |
|---------|------|----------|------|
| Reliability | ⚠️ Medium | ✅ High | ❌ Test Only |
| Scalability | ⚠️ Limited | ✅ High | ❌ N/A |
| Tracking | ❌ No | ✅ Yes | ❌ No |
| Analytics | ❌ No | ✅ Yes | ❌ No |
| Templates | ❌ No | ✅ Yes | ❌ No |
| Deliverability | ⚠️ Variable | ✅ High | ❌ N/A |
| Cost | ✅ Free | 💰 Paid | ✅ Free |

## Troubleshooting

### Common Issues

1. **API Key Invalid**
   - Verify your API key is correct
   - Check API key permissions include "Mail Send"

2. **Sender Verification**
   - Ensure `SENDGRID_FROM_EMAIL` is verified in SendGrid
   - Check sender authentication status

3. **Rate Limiting**
   - SendGrid has rate limits based on your plan
   - Implement appropriate retry logic

4. **Domain Authentication**
   - Set up domain authentication for better deliverability
   - Avoid using generic domains like gmail.com

### Debug Logging

The SendGrid integration includes comprehensive logging at multiple levels:

**Log Levels:**
- `INFO`: Provider initialization, email attempts, successful deliveries
- `DEBUG`: Content type detection, API client initialization, tracking metadata
- `WARN`: Configuration issues, empty subjects/content
- `ERROR`: Authentication, authorization, rate limiting, network issues

Enable debug logging to troubleshoot issues:

```bash
LOG_LEVEL=debug
```

**Log Categories:**

```bash
# SendGrid provider initialization
grep "Initialized SendGrid email provider" ./logs/app.log

# Email sending attempts
grep "Attempting to send email via SendGrid" ./logs/app.log

# Successful deliveries  
grep "Email sent successfully via SendGrid" ./logs/app.log

# Error categorization
grep "sendgrid send failed" ./logs/app.log

# Configuration validation
grep "SendGrid API key" ./logs/app.log

# Content type detection
grep "Detected.*content type" ./logs/app.log
```

**Structured Log Fields:**
- `provider`: Always "sendgrid"
- `recipient`: Email address
- `tenant_id`: Tenant identifier
- `message_id`: SendGrid message ID
- `status_code`: HTTP response code
- `error_type`: Categorized error (authentication, rate_limit, network, etc.)
- `content_type`: html or text
- `has_tracking`: Whether metadata was included

## Security Considerations

1. **API Key Security**
   - Never commit API keys to version control
   - Use environment variables or secure secret management
   - Rotate API keys regularly

2. **Email Content**
   - Sanitize user-generated content
   - Validate email addresses
   - Implement rate limiting

3. **Tracking Data**
   - Be mindful of privacy regulations
   - Allow users to opt-out of tracking
   - Secure metadata storage

## Migration from SMTP

To migrate from SMTP to SendGrid:

1. Set up SendGrid account and get API key
2. Update environment variables:
   ```bash
   EMAIL_PROVIDER=sendgrid  # Change from 'smtp'
   SENDGRID_API_KEY=your_key
   SENDGRID_FROM_EMAIL=your_email
   SENDGRID_FROM_NAME=your_name
   ```
3. Restart the application
4. Test email functionality
5. Monitor logs for any issues

The migration is seamless as the same notification service interface is used.
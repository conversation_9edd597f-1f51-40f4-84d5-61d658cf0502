# Notification Webhook Handlers Documentation

## Overview

The notification module supports webhooks from various email, SMS, and push notification providers to track delivery status, bounces, complaints, and user engagement (opens, clicks, etc.).

## Supported Providers

### Email Providers
- **SendGrid** - Full support for delivery status, bounces, opens, clicks, spam reports
- **Mailgun** - Full support for delivery status, bounces, opens, clicks, complaints
- **AWS SES** - Full support via SNS topics for delivery, bounces, complaints

### SMS Providers
- **Twilio** - Full support for SMS delivery status and errors

### Push Notification Providers
- **FCM** (Firebase Cloud Messaging) - Custom webhook support for Android/Web push
- **APNS** (Apple Push Notification Service) - Custom webhook support for iOS push
- **AWS SNS** - Generic notification delivery tracking

## Webhook Endpoints

All webhook endpoints are publicly accessible but require the `X-Tenant-ID` header to identify the tenant.

### Email Webhooks
- `POST /api/webhooks/notifications/sendgrid` - SendGrid events
- `POST /api/webhooks/notifications/mailgun` - Mailgun events
- `POST /api/webhooks/notifications/ses` - AWS SES events (via SNS)

### SMS Webhooks
- `POST /api/webhooks/notifications/twilio` - Twilio SMS status

### Push Webhooks
- `POST /api/webhooks/notifications/fcm` - FCM custom events
- `POST /api/webhooks/notifications/apns` - APNS custom events
- `POST /api/webhooks/notifications/sns` - AWS SNS generic events

## Configuration

### Environment Variables

#### SendGrid
```bash
SENDGRID_WEBHOOK_VERIFICATION_KEY=your_verification_key  # Optional, for signature verification
```

#### Mailgun
```bash
MAILGUN_WEBHOOK_SIGNING_KEY=your_signing_key  # Optional, for signature verification
```

#### Twilio
```bash
TWILIO_AUTH_TOKEN=your_auth_token  # Required for signature verification
```

### Webhook URL Configuration

Configure your webhook URLs in each provider's dashboard:

#### SendGrid
1. Go to Settings > Mail Settings > Event Webhook
2. Set HTTP Post URL: `https://your-domain.com/api/webhooks/notifications/sendgrid`
3. Add custom header: `X-Tenant-ID: YOUR_TENANT_ID`
4. Select events to track
5. Enable signature verification (optional)

#### Mailgun
1. Go to Webhooks in your domain settings
2. Add webhook URL: `https://your-domain.com/api/webhooks/notifications/mailgun`
3. Add custom header: `X-Tenant-ID: YOUR_TENANT_ID`
4. Select event types

#### AWS SES
1. Create SNS topic for SES notifications
2. Subscribe HTTP/HTTPS endpoint: `https://your-domain.com/api/webhooks/notifications/ses`
3. Configure SES to send notifications to SNS topic
4. Add custom header in SNS subscription: `X-Tenant-ID: YOUR_TENANT_ID`

#### Twilio
1. Go to your messaging service or phone number settings
2. Set Status Callback URL: `https://your-domain.com/api/webhooks/notifications/twilio`
3. Add custom parameter: `X-Tenant-ID=YOUR_TENANT_ID`

## Webhook Event Processing

### Event Storage
All webhook events are stored in the `notification_webhook_events` table before processing:
- Raw payload is preserved
- Events are processed asynchronously
- Failed events can be reprocessed

### Event Mapping
Provider events are mapped to internal notification log event types:
- `delivered` - Message successfully delivered
- `bounced` - Hard bounce (permanent failure)
- `failed` - Soft bounce or temporary failure
- `opened` - Email/notification opened
- `clicked` - Link clicked
- `complaint` - Spam complaint
- `unsubscribed` - User unsubscribed

### Recipient Status Updates
Webhook events automatically update recipient status:
- Delivered → `delivered`
- Failed/Bounced → `failed`
- Opened → `opened`
- Clicked → `clicked`
- Unsubscribed → `unsubscribed`

## Webhook Management API

Authenticated endpoints for viewing and managing webhook events:

### List Webhook Events
```
GET /api/notification-webhooks/events
```

Query parameters:
- `page` (int) - Page number (default: 1)
- `limit` (int) - Items per page (default: 20, max: 100)
- `provider` (string) - Filter by provider
- `event_type` (string) - Filter by event type
- `message_id` (string) - Filter by message ID
- `recipient_address` (string) - Filter by recipient
- `processed` (bool) - Filter by processed status
- `start_date` (RFC3339) - Filter by date range
- `end_date` (RFC3339) - Filter by date range

### Get Webhook Event
```
GET /api/notification-webhooks/events/:id
```

### Reprocess Webhook Event
```
POST /api/notification-webhooks/events/:id/reprocess
```

### Reprocess Failed Events
```
POST /api/notification-webhooks/events/reprocess-failed
```

Query parameters:
- `since` (RFC3339) - Reprocess events since this date (default: last 24 hours)

## Security

### Signature Verification
Most providers support webhook signature verification:
- **SendGrid**: HMAC-SHA256 signature in `X-Twilio-Email-Event-Webhook-Signature` header
- **Mailgun**: HMAC-SHA256 signature in webhook payload
- **Twilio**: HMAC-SHA256 signature in `X-Twilio-Signature` header
- **AWS SNS/SES**: RSA-SHA1 signature with certificate validation

### Best Practices
1. Always use HTTPS for webhook endpoints
2. Enable signature verification when available
3. Use tenant-specific webhook URLs or headers
4. Monitor webhook processing for failures
5. Implement retry logic for failed processing
6. Set up alerts for high bounce/complaint rates

## Testing Webhooks

### Local Development
Use ngrok or similar tools to expose local webhook endpoints:
```bash
ngrok http 8080
```

### Test Webhook Events
Each provider typically offers webhook testing tools:
- SendGrid: Event Webhook Test
- Mailgun: Webhook Test
- Twilio: Webhook Debugger

### Manual Testing
Send test webhook events using curl:
```bash
# SendGrid test event
curl -X POST https://your-domain.com/api/webhooks/notifications/sendgrid \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: 1" \
  -d '[{
    "email": "<EMAIL>",
    "timestamp": **********,
    "event": "delivered",
    "sg_event_id": "test-event-id",
    "sg_message_id": "test-message-id"
  }]'
```

## Error Handling

### Common Issues
1. **Missing Tenant ID**: Ensure `X-Tenant-ID` header is included
2. **Invalid Signature**: Check webhook signing keys in environment
3. **Processing Failures**: Check logs and use reprocess endpoints
4. **Rate Limiting**: Implement proper queue processing

### Monitoring
- Monitor webhook event processing rates
- Set up alerts for processing failures
- Track bounce and complaint rates
- Monitor webhook endpoint availability
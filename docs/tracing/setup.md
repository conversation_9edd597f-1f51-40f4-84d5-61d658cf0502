# Distributed Tracing Setup Guide

## Overview

This guide provides comprehensive setup instructions for distributed tracing in the WN API v3 project using Jaeger and OpenTelemetry SDK.

## Prerequisites

- Go 1.23.2 or higher
- Docker and Docker Compose
- MySQL 8.0+
- Redis (for caching)

## Infrastructure Setup

### 1. Jaeger Infrastructure

Add the following to your `docker-compose.yml`:

```yaml
version: '3.8'

services:
  # Jaeger
  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - wn-network

  # Your existing services...
  app:
    depends_on:
      - jaeger
    environment:
      - JAEGER_AGENT_HOST=jaeger
      - JAEGER_AGENT_PORT=6831
      - JAEGER_SAMPLER_TYPE=const
      - JAEGER_SAMPLER_PARAM=1
      - JAEGER_REPORTER_LOG_SPANS=true
    networks:
      - wn-network

networks:
  wn-network:
    driver: bridge
```

### 2. Environment Variables

Add these environment variables to your `.env` file:

```env
# Distributed Tracing Configuration
TRACING_ENABLED=true
TRACING_SERVICE_NAME=wn-api-v3
TRACING_SERVICE_VERSION=1.0.0

# Jaeger Configuration
JAEGER_AGENT_HOST=localhost
JAEGER_AGENT_PORT=6831
JAEGER_SAMPLER_TYPE=const
JAEGER_SAMPLER_PARAM=1
JAEGER_REPORTER_LOG_SPANS=true
JAEGER_REPORTER_FLUSH_INTERVAL=1s

# OpenTelemetry Configuration
OTEL_EXPORTER_JAEGER_ENDPOINT=http://localhost:14268/api/traces
OTEL_RESOURCE_ATTRIBUTES=service.name=wn-api-v3,service.version=1.0.0
OTEL_PROPAGATORS=tracecontext,baggage

# Sampling Configuration
OTEL_TRACES_SAMPLER=always_on
OTEL_TRACES_SAMPLER_ARG=1.0
```

### 3. Go Dependencies

Add these dependencies to your `go.mod`:

```go
require (
    go.opentelemetry.io/otel v1.21.0
    go.opentelemetry.io/otel/trace v1.21.0
    go.opentelemetry.io/otel/sdk v1.21.0
    go.opentelemetry.io/otel/exporters/jaeger v1.17.0
    go.opentelemetry.io/otel/exporters/otlp/otlptrace v1.21.0
    go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracehttp v1.21.0
    go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp v0.46.0
    go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin v0.46.0
    go.opentelemetry.io/contrib/instrumentation/gorm.io/gorm/otelgorm v0.46.0
    go.opentelemetry.io/otel/semconv/v1.21.0
)
```

## Application Setup

### 1. Tracing Package Structure

Create the following package structure:

```
pkg/
└── tracing/
    ├── config.go          # Configuration management
    ├── tracer.go          # Tracer initialization
    ├── span.go            # Span utilities
    ├── context.go         # Context propagation
    ├── middleware.go      # HTTP middleware
    ├── database.go        # Database tracing
    ├── external.go        # External service tracing
    └── metrics.go         # Custom metrics
```

### 2. Configuration Setup

Create `pkg/tracing/config.go`:

```go
package tracing

import (
    "os"
    "strconv"
    "time"
)

type Config struct {
    Enabled             bool
    ServiceName         string
    ServiceVersion      string
    JaegerAgentHost     string
    JaegerAgentPort     string
    SamplerType         string
    SamplerParam        float64
    ReporterLogSpans    bool
    ReporterFlushInterval time.Duration
    OTLPEndpoint        string
}

func LoadConfig() *Config {
    enabled, _ := strconv.ParseBool(os.Getenv("TRACING_ENABLED"))
    samplerParam, _ := strconv.ParseFloat(os.Getenv("JAEGER_SAMPLER_PARAM"), 64)
    reporterLogSpans, _ := strconv.ParseBool(os.Getenv("JAEGER_REPORTER_LOG_SPANS"))
    flushInterval, _ := time.ParseDuration(os.Getenv("JAEGER_REPORTER_FLUSH_INTERVAL"))
    
    return &Config{
        Enabled:             enabled,
        ServiceName:         getEnvOrDefault("TRACING_SERVICE_NAME", "wn-api-v3"),
        ServiceVersion:      getEnvOrDefault("TRACING_SERVICE_VERSION", "1.0.0"),
        JaegerAgentHost:     getEnvOrDefault("JAEGER_AGENT_HOST", "localhost"),
        JaegerAgentPort:     getEnvOrDefault("JAEGER_AGENT_PORT", "6831"),
        SamplerType:         getEnvOrDefault("JAEGER_SAMPLER_TYPE", "const"),
        SamplerParam:        samplerParam,
        ReporterLogSpans:    reporterLogSpans,
        ReporterFlushInterval: flushInterval,
        OTLPEndpoint:        getEnvOrDefault("OTEL_EXPORTER_JAEGER_ENDPOINT", "http://localhost:14268/api/traces"),
    }
}

func getEnvOrDefault(key, defaultValue string) string {
    if value := os.Getenv(key); value != "" {
        return value
    }
    return defaultValue
}
```

### 3. Tracer Initialization

Create `pkg/tracing/tracer.go`:

```go
package tracing

import (
    "context"
    "fmt"
    "log"

    "go.opentelemetry.io/otel"
    "go.opentelemetry.io/otel/attribute"
    "go.opentelemetry.io/otel/exporters/jaeger"
    "go.opentelemetry.io/otel/propagation"
    "go.opentelemetry.io/otel/sdk/resource"
    "go.opentelemetry.io/otel/sdk/trace"
    "go.opentelemetry.io/otel/semconv/v1.21.0/semconv"
)

var tracer trace.Tracer

func Initialize(config *Config) func() {
    if !config.Enabled {
        log.Println("Tracing is disabled")
        return func() {}
    }

    // Create Jaeger exporter
    exp, err := jaeger.New(jaeger.WithCollectorEndpoint(jaeger.WithEndpoint(config.OTLPEndpoint)))
    if err != nil {
        log.Fatal("Failed to create Jaeger exporter:", err)
    }

    // Create resource with service information
    res, err := resource.New(context.Background(),
        resource.WithAttributes(
            semconv.ServiceName(config.ServiceName),
            semconv.ServiceVersion(config.ServiceVersion),
        ),
    )
    if err != nil {
        log.Fatal("Failed to create resource:", err)
    }

    // Create trace provider
    tp := trace.NewTracerProvider(
        trace.WithBatcher(exp),
        trace.WithResource(res),
        trace.WithSampler(trace.AlwaysSample()),
    )

    // Register trace provider
    otel.SetTracerProvider(tp)

    // Set global propagator
    otel.SetTextMapPropagator(propagation.NewCompositeTextMapPropagator(
        propagation.TraceContext{},
        propagation.Baggage{},
    ))

    tracer = tp.Tracer(config.ServiceName)

    log.Printf("Tracing initialized for service: %s", config.ServiceName)

    return func() {
        if err := tp.Shutdown(context.Background()); err != nil {
            log.Printf("Error shutting down tracer provider: %v", err)
        }
    }
}

func GetTracer() trace.Tracer {
    return tracer
}
```

### 4. Main Application Integration

Update your `main.go`:

```go
package main

import (
    "context"
    "log"
    "net/http"
    "os"
    "os/signal"
    "syscall"
    "time"

    "github.com/gin-gonic/gin"
    "github.com/tranthanhloi/wn-api-v3/pkg/tracing"
)

func main() {
    // Load tracing configuration
    tracingConfig := tracing.LoadConfig()
    
    // Initialize tracing
    tracingShutdown := tracing.Initialize(tracingConfig)
    defer tracingShutdown()

    // Create Gin router
    router := gin.Default()

    // Add tracing middleware
    router.Use(tracing.GinMiddleware())

    // Setup your routes...
    setupRoutes(router)

    // Start server
    srv := &http.Server{
        Addr:    ":8080",
        Handler: router,
    }

    // Graceful shutdown
    go func() {
        if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
            log.Fatalf("Server failed to start: %v", err)
        }
    }()

    // Wait for interrupt signal
    quit := make(chan os.Signal, 1)
    signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
    <-quit

    log.Println("Shutting down server...")

    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()

    if err := srv.Shutdown(ctx); err != nil {
        log.Fatal("Server forced to shutdown:", err)
    }

    log.Println("Server exited")
}
```

## Verification

### 1. Start Services

```bash
# Start Jaeger and other services
docker-compose up -d

# Start the application
go run cmd/server/main.go
```

### 2. Access Jaeger UI

Open your browser and navigate to: `http://localhost:16686`

### 3. Generate Test Traces

Make some API calls to generate traces:

```bash
# Test endpoint
curl -X GET http://localhost:8080/health

# Check traces in Jaeger UI
# Should see traces for your service
```

### 4. Health Check

Verify Jaeger is receiving traces:

```bash
# Check Jaeger health
curl http://localhost:16686/

# Check if traces are being sent
# Look for logs indicating spans are being exported
```

## Production Considerations

### 1. Sampling Configuration

For production, adjust sampling rates:

```env
# Production sampling - only sample 1% of traces
JAEGER_SAMPLER_TYPE=probabilistic
JAEGER_SAMPLER_PARAM=0.01
```

### 2. Resource Limits

Configure appropriate resource limits:

```yaml
# docker-compose.yml
services:
  jaeger:
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
```

### 3. Storage Backend

For production, use a persistent storage backend:

```yaml
# docker-compose.yml
services:
  jaeger:
    environment:
      - SPAN_STORAGE_TYPE=elasticsearch
      - ES_SERVER_URLS=http://elasticsearch:9200
```

## Next Steps

1. Review [Integration Guide](./integration.md) for module-specific integration
2. Check [Performance Tuning](./performance.md) for optimization strategies
3. See [Best Practices](./best-practices.md) for recommended patterns
4. Review [Troubleshooting](./troubleshooting.md) for common issues

## References

- [OpenTelemetry Go Documentation](https://opentelemetry.io/docs/instrumentation/go/)
- [Jaeger Documentation](https://www.jaegertracing.io/docs/)
- [Gin OpenTelemetry Integration](https://github.com/open-telemetry/opentelemetry-go-contrib/tree/main/instrumentation/github.com/gin-gonic/gin/otelgin)
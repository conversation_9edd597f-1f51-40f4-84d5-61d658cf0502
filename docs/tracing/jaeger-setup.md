# Jaeger Tracing Setup - Blog API v3

## Overview

This document provides a comprehensive guide for setting up and using Jaeger distributed tracing in the Blog API v3 project.

## Architecture

The tracing system consists of several components:

- **Tracer**: OpenTelemetry-based tracer with <PERSON><PERSON><PERSON> exporter
- **Collector**: Jaeger collector for receiving traces
- **Storage**: In-memory storage for development (can be configured for production)
- **Query Service**: Jaeger query service for trace visualization
- **UI**: Jaeger UI for viewing traces

## Quick Start

### 1. Start Infrastructure

```bash
# Start all services including <PERSON><PERSON><PERSON>
docker-compose up -d

# Check if <PERSON><PERSON><PERSON> is running
docker-compose ps jaeger
```

### 2. Configure Environment

```bash
# Copy tracing environment variables
cp .env.tracing .env

# Or set environment variables directly
export TRACING_ENABLED=true
export JAEGER_ENDPOINT=http://localhost:14268/api/traces
export TRACING_SAMPLING=always
export TRACING_SAMPLE_RATE=1.0
```

### 3. Initialize Tracing in Application

```go
package main

import (
    "log"
    "github.com/tranthanhloi/wn-api-v3/internal/tracing"
)

func main() {
    // Initialize tracing
    if err := tracing.Initialize(); err != nil {
        log.Fatalf("Failed to initialize tracing: %v", err)
    }
    defer tracing.ShutdownWithTimeout(30 * time.Second)
    
    // Your application code here
}
```

### 4. Add Tracing Middleware

```go
package main

import (
    "github.com/gin-gonic/gin"
    "github.com/tranthanhloi/wn-api-v3/internal/tracing"
)

func main() {
    router := gin.Default()
    
    // Add tracing middleware
    router.Use(tracing.TracingMiddleware())
    
    // Add API key tracing
    router.Use(tracing.APIKeyTracingMiddleware())
    
    // Your routes here
}
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `TRACING_ENABLED` | Enable/disable tracing | `false` |
| `TRACING_SERVICE_NAME` | Service name for traces | `blog-api-v3` |
| `TRACING_ENVIRONMENT` | Environment (dev/prod) | `development` |
| `JAEGER_ENDPOINT` | Jaeger collector endpoint | `http://localhost:14268/api/traces` |
| `TRACING_SAMPLING` | Sampling strategy | `traceidratio` |
| `TRACING_SAMPLE_RATE` | Sample rate (0.0-1.0) | `0.1` |
| `TRACING_DEBUG` | Enable debug logging | `false` |

### Development Configuration

```bash
# .env.tracing for development
TRACING_ENABLED=true
TRACING_ENVIRONMENT=development
TRACING_DEBUG=true
TRACING_SAMPLING=always
TRACING_SAMPLE_RATE=1.0
JAEGER_ENDPOINT=http://localhost:14268/api/traces
```

### Production Configuration

```bash
# .env.tracing for production
TRACING_ENABLED=true
TRACING_ENVIRONMENT=production
TRACING_DEBUG=false
TRACING_SAMPLING=traceidratio
TRACING_SAMPLE_RATE=0.1
JAEGER_ENDPOINT=http://jaeger:14268/api/traces
```

## Usage Examples

### 1. Basic Span Creation

```go
func handleRequest(c *gin.Context) {
    ctx := c.Request.Context()
    
    // Create a span
    spanCtx, span := tracing.CreateSpan(ctx, "user.get")
    defer span.End()
    
    // Set attributes
    span.SetAttribute("user.id", userID)
    span.SetAttribute("component", "user_handler")
    
    // Your logic here
    
    // Set status
    span.SetStatus(tracing.SpanStatusOK, "User retrieved")
}
```

### 2. Database Operations

```go
func getUserFromDB(ctx context.Context, userID string) (*User, error) {
    spanCtx, span := tracing.CreateSpan(ctx, "db.users.select")
    defer span.End()
    
    span.SetAttribute("db.operation", "select")
    span.SetAttribute("db.table", "users")
    span.SetAttribute("db.query", "SELECT * FROM users WHERE id = ?")
    span.SetAttribute("user.id", userID)
    
    // Database query
    user, err := db.QueryUser(spanCtx, userID)
    if err != nil {
        span.RecordError(err)
        span.SetStatus(tracing.SpanStatusError, err.Error())
        return nil, err
    }
    
    span.SetAttribute("db.result_count", 1)
    span.SetStatus(tracing.SpanStatusOK, "Query completed")
    return user, nil
}
```

### 3. External Service Calls

```go
func callExternalAPI(ctx context.Context, url string) (*Response, error) {
    spanCtx, span := tracing.CreateSpan(ctx, "http.client.get")
    defer span.End()
    
    span.SetAttribute("http.method", "GET")
    span.SetAttribute("http.url", url)
    span.SetAttribute("component", "http-client")
    
    // Make HTTP request
    resp, err := http.Get(url)
    if err != nil {
        span.RecordError(err)
        span.SetStatus(tracing.SpanStatusError, err.Error())
        return nil, err
    }
    
    span.SetAttribute("http.status_code", resp.StatusCode)
    span.SetStatus(tracing.SpanStatusOK, "HTTP request completed")
    return resp, nil
}
```

### 4. Service Layer Tracing

```go
func (s *UserService) CreateUser(ctx context.Context, userData *CreateUserRequest) (*User, error) {
    spanCtx, span := tracing.CreateSpan(ctx, "user_service.create")
    defer span.End()
    
    span.SetAttribute("service.layer", "business")
    span.SetAttribute("service.operation", "create_user")
    span.SetAttribute("user.email", userData.Email)
    
    // Validation
    if err := s.validateUser(spanCtx, userData); err != nil {
        span.RecordError(err)
        span.SetStatus(tracing.SpanStatusError, "Validation failed")
        return nil, err
    }
    
    // Create user in database
    user, err := s.userRepo.Create(spanCtx, userData)
    if err != nil {
        span.RecordError(err)
        span.SetStatus(tracing.SpanStatusError, "Database error")
        return nil, err
    }
    
    span.SetAttribute("user.id", user.ID)
    span.SetStatus(tracing.SpanStatusOK, "User created successfully")
    return user, nil
}
```

## Accessing Jaeger UI

### Local Development

1. Start the services: `docker-compose up -d`
2. Open browser: `http://localhost:16686`
3. Select service: `blog-api-v3`
4. View traces and analyze performance

### Key Features

- **Trace Timeline**: View request flow across services
- **Service Map**: Understand service dependencies
- **Error Analysis**: Identify failed operations
- **Performance Metrics**: Analyze response times
- **Span Details**: View attributes and events

## Best Practices

### 1. Span Naming

```go
// Good: Descriptive and hierarchical
"user_service.create_user"
"db.users.insert"
"http.client.get"
"cache.redis.get"

// Bad: Too generic
"operation"
"database"
"request"
```

### 2. Attribute Standards

```go
// Use standard OpenTelemetry attributes
span.SetAttribute("http.method", "GET")
span.SetAttribute("http.status_code", 200)
span.SetAttribute("db.operation", "select")
span.SetAttribute("db.table", "users")

// Add business context
span.SetAttribute("tenant.id", tenantID)
span.SetAttribute("user.id", userID)
span.SetAttribute("component", "user_handler")
```

### 3. Error Handling

```go
if err != nil {
    // Record the error
    span.RecordError(err)
    
    // Set error status
    span.SetStatus(tracing.SpanStatusError, err.Error())
    
    // Add error attributes
    span.SetAttribute("error.type", "validation_error")
    span.SetAttribute("error.message", err.Error())
    
    return err
}
```

### 4. Performance Monitoring

```go
startTime := time.Now()
defer func() {
    duration := time.Since(startTime)
    span.SetAttribute("duration_ms", float64(duration.Nanoseconds())/1000000.0)
}()
```

## Module Integration

### API Key Module

```go
// In API key handlers
func (h *APIKeyHandler) CreateAPIKey(c *gin.Context) {
    ctx := c.Request.Context()
    
    spanCtx, span := tracing.CreateSpan(ctx, "apikey.create")
    defer span.End()
    
    span.SetAttribute("component", "apikey_handler")
    span.SetAttribute("operation", "create")
    
    // Add tenant context
    if tenantID, exists := c.Get("tenant_id"); exists {
        span.SetAttribute("tenant.id", tenantID)
    }
    
    // Your handler logic
}
```

### User Module

```go
// In user service
func (s *UserService) GetUser(ctx context.Context, id uint) (*User, error) {
    spanCtx, span := tracing.CreateSpan(ctx, "user_service.get")
    defer span.End()
    
    span.SetAttribute("service.layer", "business")
    span.SetAttribute("user.id", id)
    
    // Service logic
}
```

## Troubleshooting

### Common Issues

1. **Traces not appearing**
   - Check `TRACING_ENABLED=true`
   - Verify Jaeger endpoint
   - Check sampling configuration

2. **High memory usage**
   - Reduce sample rate
   - Increase batch size
   - Check for span leaks

3. **Missing spans**
   - Ensure context propagation
   - Check middleware order
   - Verify span creation

### Debug Commands

```bash
# Check tracing status
curl http://localhost:8080/tracing/info

# Get tracing metrics
curl http://localhost:8080/tracing/metrics

# Test trace creation
curl -H "X-Trace-Debug: true" http://localhost:8080/api/v1/users
```

## Monitoring and Alerts

### Key Metrics

- Trace volume per service
- Error rate by operation
- P95/P99 latency
- Sampling effectiveness

### Health Checks

```go
// Add to health check endpoint
func healthCheck(c *gin.Context) {
    status := map[string]interface{}{
        "tracing": tracing.HealthCheck(),
        "status":  "healthy",
    }
    c.JSON(200, status)
}
```

## Production Considerations

### 1. Sampling

```bash
# Use ratio-based sampling in production
TRACING_SAMPLING=traceidratio
TRACING_SAMPLE_RATE=0.1  # 10% of traces
```

### 2. Resource Limits

```bash
# Configure batch settings
TRACING_BATCH_SIZE=200
TRACING_MAX_EXPORT_BATCH_SIZE=1024
TRACING_MAX_QUEUE_SIZE=4096
```

### 3. Security

```bash
# Use authentication if needed
JAEGER_USER=admin
JAEGER_PASSWORD=secret
```

## Integration with Other Tools

### Prometheus Metrics

```go
// Add tracing metrics to Prometheus
import "github.com/prometheus/client_golang/prometheus"

var (
    tracingSpansTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "tracing_spans_total",
            Help: "Total number of spans created",
        },
        []string{"service", "operation"},
    )
)
```

### Logging Integration

```go
// Add trace context to logs
func logWithTrace(ctx context.Context, msg string) {
    traceID := tracing.GetTraceID(ctx)
    spanID := tracing.GetSpanID(ctx)
    
    log.Printf("[trace_id=%s span_id=%s] %s", traceID, spanID, msg)
}
```

## Conclusion

The Jaeger tracing setup provides comprehensive observability for the Blog API v3 application. Follow the examples and best practices to ensure effective tracing implementation across all modules.

For more information, see:
- [OpenTelemetry Documentation](https://opentelemetry.io/docs/)
- [Jaeger Documentation](https://www.jaegertracing.io/docs/)
- [Tracing Best Practices](./best-practices.md)
# Troubleshooting Guide

## Overview

This guide provides solutions for common distributed tracing issues in the WN API v3 project.

## Common Issues

### 1. Traces Not Appearing in Jaeger

**Symptoms:**
- No traces visible in Jaeger UI
- Spans created but not exported
- Application running but no tracing data

**Troubleshooting Steps:**

#### Check Jaeger Service Status
```bash
# Check if <PERSON><PERSON><PERSON> is running
docker ps | grep jaeger

# Check J<PERSON><PERSON> logs
docker logs jaeger-container-name

# Test Jaeger endpoint
curl http://localhost:16686/api/services
```

#### Verify Configuration
```go
// Add debug logging to tracer initialization
func Initialize(config *Config) func() {
    log.Printf("Initializing tracing with config: %+v", config)
    
    if !config.Enabled {
        log.Println("Tracing is disabled - check TRACING_ENABLED environment variable")
        return func() {}
    }
    
    // Create exporter with debug logging
    exp, err := jaeger.New(jaeger.WithCollectorEndpoint(
        jaeger.WithEndpoint(config.OTLPEndpoint),
        jaeger.WithHTTPClient(&http.Client{
            Transport: &debugTransport{
                base: http.DefaultTransport,
            },
        }),
    ))
    if err != nil {
        log.Fatal("Failed to create Jaeger exporter:", err)
    }
    
    log.Printf("Jaeger exporter created successfully, endpoint: %s", config.OTLPEndpoint)
    
    // ... rest of initialization
}

// Debug transport to log HTTP requests
type debugTransport struct {
    base http.RoundTripper
}

func (t *debugTransport) RoundTrip(req *http.Request) (*http.Response, error) {
    log.Printf("Sending traces to: %s", req.URL.String())
    
    resp, err := t.base.RoundTrip(req)
    if err != nil {
        log.Printf("Error sending traces: %v", err)
        return resp, err
    }
    
    log.Printf("Traces sent successfully, status: %d", resp.StatusCode)
    return resp, nil
}
```

#### Check Environment Variables
```bash
# Verify tracing configuration
echo "TRACING_ENABLED: $TRACING_ENABLED"
echo "JAEGER_AGENT_HOST: $JAEGER_AGENT_HOST"
echo "JAEGER_AGENT_PORT: $JAEGER_AGENT_PORT"
echo "OTEL_EXPORTER_JAEGER_ENDPOINT: $OTEL_EXPORTER_JAEGER_ENDPOINT"
```

#### Test Span Creation
```go
// Add test endpoint to verify span creation
func TestTracingHandler(c *gin.Context) {
    ctx := c.Request.Context()
    
    // Create a test span
    ctx, span := tracing.StartSpan(ctx, "test.operation")
    defer span.End()
    
    span.SetAttributes(
        attribute.String("test.id", "123"),
        attribute.String("test.type", "manual"),
    )
    
    log.Printf("Test span created with trace ID: %s", span.SpanContext().TraceID())
    
    c.JSON(200, gin.H{
        "message": "Test span created",
        "trace_id": span.SpanContext().TraceID().String(),
    })
}
```

### 2. Spans Not Linking Together

**Symptoms:**
- Spans appear as separate traces
- No parent-child relationships
- Broken trace continuity

**Solutions:**

#### Context Propagation
```go
// Ensure context is properly propagated
func HandlerWithProperContext(c *gin.Context) {
    ctx := c.Request.Context()
    
    // Start span with proper context
    ctx, span := tracing.StartSpan(ctx, "handler.operation")
    defer span.End()
    
    // Pass context to service layer
    result, err := service.ProcessRequest(ctx, request)
    if err != nil {
        tracing.RecordError(span, err, "service processing failed")
        c.JSON(500, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(200, result)
}

// Service layer must accept and use context
func (s *Service) ProcessRequest(ctx context.Context, req *Request) (*Response, error) {
    // Create child span from context
    ctx, span := tracing.StartSpan(ctx, "service.process_request")
    defer span.End()
    
    // Continue propagating context
    return s.repository.Save(ctx, req)
}
```

#### Check Middleware Order
```go
// Ensure tracing middleware is applied first
func setupRoutes(router *gin.Engine) {
    // Tracing middleware MUST be first
    router.Use(tracing.GinMiddleware())
    router.Use(tracing.CustomSpanMiddleware())
    
    // Other middleware after tracing
    router.Use(cors.Default())
    router.Use(auth.Middleware())
    
    // Routes
    api := router.Group("/api/v1")
    {
        tenant.RegisterRoutes(api)
    }
}
```

### 3. High Memory Usage

**Symptoms:**
- Application memory usage growing over time
- Out of memory errors
- Slow application performance

**Solutions:**

#### Implement Span Limits
```go
// Add span limits to prevent memory leaks
func Initialize(config *Config) func() {
    // Create resource with limits
    res, err := resource.New(context.Background(),
        resource.WithAttributes(
            semconv.ServiceName(config.ServiceName),
            semconv.ServiceVersion(config.ServiceVersion),
        ),
        resource.WithSpanLimits(trace.SpanLimits{
            AttributeValueLengthLimit:   1024,
            AttributeCountLimit:         50,
            EventCountLimit:            10,
            LinkCountLimit:             10,
            AttributePerEventCountLimit: 10,
            AttributePerLinkCountLimit:  10,
        }),
    )
    
    // ... rest of initialization
}
```

#### Monitor Memory Usage
```go
// Add memory monitoring
func monitorMemoryUsage() {
    ticker := time.NewTicker(1 * time.Minute)
    defer ticker.Stop()
    
    for range ticker.C {
        var m runtime.MemStats
        runtime.ReadMemStats(&m)
        
        log.Printf("Memory usage: Alloc=%dKB, TotalAlloc=%dKB, Sys=%dKB, NumGC=%d",
            m.Alloc/1024, m.TotalAlloc/1024, m.Sys/1024, m.NumGC)
        
        // Force GC if memory usage is high
        if m.Alloc > 100*1024*1024 { // 100MB
            runtime.GC()
            log.Println("Forced garbage collection due to high memory usage")
        }
    }
}
```

### 4. Slow Export Performance

**Symptoms:**
- High span export latency
- Export timeouts
- Spans being dropped

**Solutions:**

#### Optimize Batch Configuration
```go
// Tune batch processor settings
func Initialize(config *Config) func() {
    // Optimized batch settings
    batchOptions := []trace.BatchSpanProcessorOption{
        trace.WithMaxExportBatchSize(1000),        // Larger batches
        trace.WithBatchTimeout(2 * time.Second),   // Faster batching
        trace.WithMaxExportBatchSize(2000),        // Higher max batch size
        trace.WithExportTimeout(10 * time.Second), // Reasonable timeout
    }
    
    bsp := trace.NewBatchSpanProcessor(exp, batchOptions...)
    
    // ... rest of initialization
}
```

#### Add Export Monitoring
```go
// Monitor export performance
type ExportMetrics struct {
    mu             sync.RWMutex
    exportCount    int64
    exportErrors   int64
    exportDuration time.Duration
}

func (m *ExportMetrics) RecordExport(duration time.Duration, err error) {
    m.mu.Lock()
    defer m.mu.Unlock()
    
    m.exportCount++
    m.exportDuration += duration
    
    if err != nil {
        m.exportErrors++
        log.Printf("Export error: %v", err)
    }
}

func (m *ExportMetrics) GetStats() (count int64, errors int64, avgDuration time.Duration) {
    m.mu.RLock()
    defer m.mu.RUnlock()
    
    if m.exportCount > 0 {
        avgDuration = m.exportDuration / time.Duration(m.exportCount)
    }
    
    return m.exportCount, m.exportErrors, avgDuration
}
```

### 5. Missing Database Traces

**Symptoms:**
- HTTP traces visible but no database operations
- Database spans not created
- Incomplete transaction traces

**Solutions:**

#### Verify GORM Plugin Registration
```go
// Check GORM plugin is properly registered
func NewMySQLConnection(config *Config) (*gorm.DB, error) {
    db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
    if err != nil {
        return nil, err
    }
    
    // Check if plugin is registered
    if err := db.Use(tracing.NewGormTracer()); err != nil {
        log.Printf("Failed to register GORM tracing plugin: %v", err)
        return nil, err
    }
    
    log.Println("GORM tracing plugin registered successfully")
    return db, nil
}
```

#### Test Database Tracing
```go
// Add test endpoint for database tracing
func TestDatabaseTracing(c *gin.Context) {
    ctx := c.Request.Context()
    
    // Create test span
    ctx, span := tracing.StartSpan(ctx, "test.database_operation")
    defer span.End()
    
    // Test database operation with context
    var count int64
    if err := db.WithContext(ctx).Model(&models.Tenant{}).Count(&count).Error; err != nil {
        tracing.RecordError(span, err, "database query failed")
        c.JSON(500, gin.H{"error": err.Error()})
        return
    }
    
    span.SetAttributes(attribute.Int64("tenant.count", count))
    c.JSON(200, gin.H{
        "message": "Database operation traced",
        "count":   count,
    })
}
```

### 6. Context Propagation Issues

**Symptoms:**
- Spans not connected across services
- Missing trace context in downstream calls
- Broken distributed traces

**Solutions:**

#### HTTP Client Context Propagation
```go
// Ensure context is propagated in HTTP calls
func MakeHTTPRequest(ctx context.Context, url string) (*http.Response, error) {
    req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
    if err != nil {
        return nil, err
    }
    
    // OpenTelemetry will automatically inject trace context
    client := &http.Client{
        Transport: otelhttp.NewTransport(http.DefaultTransport),
    }
    
    return client.Do(req)
}
```

#### Service-to-Service Context
```go
// Propagate context between services
func (s *TenantService) CreateTenant(ctx context.Context, req *CreateTenantRequest) (*models.Tenant, error) {
    ctx, span := tracing.StartSpan(ctx, "tenant_service.create_tenant")
    defer span.End()
    
    // Validate with context
    if err := s.validator.Validate(ctx, req); err != nil {
        return nil, err
    }
    
    // Create in database with context
    tenant, err := s.repository.Create(ctx, req)
    if err != nil {
        return nil, err
    }
    
    // Notify other services with context
    if err := s.notificationService.NotifyTenantCreated(ctx, tenant); err != nil {
        // Log error but don't fail the operation
        log.Printf("Failed to send notification: %v", err)
    }
    
    return tenant, nil
}
```

### 7. Sampling Issues

**Symptoms:**
- Too many or too few traces
- Inconsistent sampling behavior
- Important traces being dropped

**Solutions:**

#### Debug Sampling Decisions
```go
// Add sampling debug information
type DebuggingSampler struct {
    base trace.Sampler
}

func (s *DebuggingSampler) ShouldSample(ctx context.Context, traceID trace.TraceID, name string, spanKind trace.SpanKind, attributes []attribute.KeyValue, links []trace.Link) trace.SamplingResult {
    result := s.base.ShouldSample(ctx, traceID, name, spanKind, attributes, links)
    
    log.Printf("Sampling decision for %s: %v", name, result.Decision)
    
    return result
}

func (s *DebuggingSampler) Description() string {
    return fmt.Sprintf("DebuggingSampler{%s}", s.base.Description())
}
```

#### Implement Head-Based Sampling
```go
// Ensure important traces are always sampled
type PrioritySampler struct {
    defaultSampler trace.Sampler
    alwaysSample   map[string]bool
}

func NewPrioritySampler(defaultSampler trace.Sampler) *PrioritySampler {
    return &PrioritySampler{
        defaultSampler: defaultSampler,
        alwaysSample: map[string]bool{
            "user.login":       true,
            "payment.process":  true,
            "tenant.create":    true,
            "error.handler":    true,
        },
    }
}

func (s *PrioritySampler) ShouldSample(ctx context.Context, traceID trace.TraceID, name string, spanKind trace.SpanKind, attributes []attribute.KeyValue, links []trace.Link) trace.SamplingResult {
    // Always sample important operations
    if s.alwaysSample[name] {
        return trace.SamplingResult{
            Decision: trace.RecordAndSample,
        }
    }
    
    // Check for error conditions
    for _, attr := range attributes {
        if attr.Key == "error" && attr.Value.AsBool() {
            return trace.SamplingResult{
                Decision: trace.RecordAndSample,
            }
        }
    }
    
    return s.defaultSampler.ShouldSample(ctx, traceID, name, spanKind, attributes, links)
}
```

## Debugging Tools

### 1. Trace Validation

```go
// Validate trace completeness
func ValidateTrace(traceID trace.TraceID) error {
    // Query Jaeger for trace
    trace, err := jaegerClient.GetTrace(traceID.String())
    if err != nil {
        return fmt.Errorf("failed to get trace: %w", err)
    }
    
    // Check for missing spans
    expectedSpans := []string{
        "http.request",
        "service.process",
        "database.query",
    }
    
    foundSpans := make(map[string]bool)
    for _, span := range trace.Spans {
        foundSpans[span.OperationName] = true
    }
    
    for _, expected := range expectedSpans {
        if !foundSpans[expected] {
            return fmt.Errorf("missing span: %s", expected)
        }
    }
    
    return nil
}
```

### 2. Health Check Endpoint

```go
// Add tracing health check endpoint
func TracingHealthCheck(c *gin.Context) {
    ctx := c.Request.Context()
    
    // Test span creation
    ctx, span := tracing.StartSpan(ctx, "health.tracing_check")
    defer span.End()
    
    // Check export functionality
    exportError := testExport(ctx)
    
    health := gin.H{
        "tracing_enabled": true,
        "span_created":    true,
        "export_working":  exportError == nil,
    }
    
    if exportError != nil {
        health["export_error"] = exportError.Error()
        c.JSON(500, health)
        return
    }
    
    c.JSON(200, health)
}

func testExport(ctx context.Context) error {
    // Create a test span and force flush
    ctx, span := tracing.StartSpan(ctx, "health.export_test")
    span.SetAttributes(attribute.String("test", "export"))
    span.End()
    
    // Force flush to test export
    if tp, ok := otel.GetTracerProvider().(*trace.TracerProvider); ok {
        ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
        defer cancel()
        
        return tp.ForceFlush(ctx)
    }
    
    return nil
}
```

### 3. Trace Debugging

```go
// Add debug logging for traces
func DebugTrace(ctx context.Context, message string) {
    span := trace.SpanFromContext(ctx)
    if span.IsRecording() {
        traceID := span.SpanContext().TraceID()
        spanID := span.SpanContext().SpanID()
        
        log.Printf("DEBUG [%s:%s] %s", traceID.String(), spanID.String(), message)
    }
}

// Usage in service methods
func (s *TenantService) CreateTenant(ctx context.Context, req *CreateTenantRequest) (*models.Tenant, error) {
    ctx, span := tracing.StartSpan(ctx, "tenant_service.create_tenant")
    defer span.End()
    
    DebugTrace(ctx, "Starting tenant creation")
    
    // ... business logic ...
    
    DebugTrace(ctx, "Tenant created successfully")
    return tenant, nil
}
```

## Performance Debugging

### 1. Slow Trace Detection

```go
// Monitor slow traces
func MonitorSlowTraces(threshold time.Duration) {
    // Custom span processor to detect slow traces
    type SlowTraceProcessor struct {
        threshold time.Duration
    }
    
    func (p *SlowTraceProcessor) OnStart(parent context.Context, s trace.ReadWriteSpan) {
        // Store start time
        s.SetAttributes(attribute.Int64("internal.start_time", time.Now().UnixNano()))
    }
    
    func (p *SlowTraceProcessor) OnEnd(s trace.ReadOnlySpan) {
        // Calculate duration
        if startTimeAttr, ok := s.Attributes()["internal.start_time"]; ok {
            startTime := time.Unix(0, startTimeAttr.AsInt64())
            duration := time.Since(startTime)
            
            if duration > p.threshold {
                log.Printf("SLOW TRACE: %s took %v (threshold: %v)", 
                    s.Name(), duration, p.threshold)
            }
        }
    }
}
```

### 2. Memory Leak Detection

```go
// Detect memory leaks in tracing
func MonitorTracingMemory() {
    ticker := time.NewTicker(30 * time.Second)
    defer ticker.Stop()
    
    var lastAlloc uint64
    
    for range ticker.C {
        var m runtime.MemStats
        runtime.ReadMemStats(&m)
        
        if lastAlloc > 0 {
            growth := m.Alloc - lastAlloc
            if growth > 10*1024*1024 { // 10MB growth
                log.Printf("WARNING: Memory growth detected: %d bytes", growth)
                
                // Print GC stats
                log.Printf("GC: NumGC=%d, PauseTotal=%v", 
                    m.NumGC, time.Duration(m.PauseTotalNs))
            }
        }
        
        lastAlloc = m.Alloc
    }
}
```

## Common Error Messages

### 1. "connection refused"
- **Cause**: Jaeger service not running
- **Solution**: Start Jaeger with `docker-compose up jaeger`

### 2. "context deadline exceeded"
- **Cause**: Export timeout too short
- **Solution**: Increase export timeout in configuration

### 3. "too many attributes"
- **Cause**: Span attribute limits exceeded
- **Solution**: Implement attribute filtering

### 4. "span not found"
- **Cause**: Context not properly propagated
- **Solution**: Check context passing between functions

### 5. "sampling rate too high"
- **Cause**: Performance impact from 100% sampling
- **Solution**: Reduce sampling rate for production

## Emergency Procedures

### 1. Disable Tracing
```go
// Emergency tracing disable
func EmergencyDisableTracing() {
    // Set global no-op tracer
    otel.SetTracerProvider(trace.NewNoopTracerProvider())
    log.Println("EMERGENCY: Tracing disabled")
}
```

### 2. Flush All Spans
```go
// Emergency flush before shutdown
func EmergencyFlush() {
    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()
    
    if tp, ok := otel.GetTracerProvider().(*trace.TracerProvider); ok {
        if err := tp.ForceFlush(ctx); err != nil {
            log.Printf("Emergency flush failed: %v", err)
        }
    }
}
```

## Next Steps

1. Review [Best Practices](./best-practices.md) for preventive measures
2. Set up monitoring alerts for common issues
3. Implement health checks for tracing components
4. Create runbooks for operational procedures

## References

- [OpenTelemetry Troubleshooting](https://opentelemetry.io/docs/reference/specification/troubleshooting/)
- [Jaeger Troubleshooting](https://www.jaegertracing.io/docs/1.35/troubleshooting/)
- [Go Tracing Best Practices](https://go.dev/doc/diagnostics)
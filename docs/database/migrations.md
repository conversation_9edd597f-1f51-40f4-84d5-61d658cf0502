# Database Migrations

## Overview

The Blog API v3 uses [golang-migrate/migrate](https://github.com/golang-migrate/migrate) for database schema version control. This tool provides sequential migration execution, rollback capabilities, and atomic migrations with proper transaction handling.

## Migration System Architecture

### Directory Structure

```
internal/database/migrations/
├── a_tenant/          # Tenant module (001-099)
├── b_website/         # Website module (101-199)  
├── c_user/            # User module (201-299)
├── d_auth/            # Auth module (301-399)
├── e_rbac/            # RBAC module (401-499)
├── f_onboarding/      # Onboarding module (501-599)
├── g_blog/            # Blog module (601-699)
└── h_seo/             # SEO module (701-799)
```

### Migration File Naming Convention

- Format: `{version}_{description}.{direction}.sql`
- Version: 3-digit sequential number (e.g., `001`, `002`)
- Description: Snake case description of the migration
- Direction: Either `up` or `down`
- Example: `001_create_tenants_table.up.sql`

## Installation

### Install golang-migrate CLI

```bash
# macOS
brew install golang-migrate

# Linux (download binary)
curl -L https://github.com/golang-migrate/migrate/releases/download/v4.17.0/migrate.linux-amd64.tar.gz | tar xvz
sudo mv migrate /usr/local/bin/

# Using Go
go install -tags 'mysql' github.com/golang-migrate/migrate/v4/cmd/migrate@latest
```

## Migration Examples

### Basic Table Creation

For table creation examples, see the actual migration files:

- **Basic Table**: `internal/database/migrations/a_tenant/001_create_tenants_table.up.sql`
- **Down Migration**: `internal/database/migrations/a_tenant/001_create_tenants_table.down.sql`

### Adding Columns

For column addition examples, see:

- **Adding Features**: `internal/database/migrations/a_tenant/002_add_tenant_features.up.sql`
- **Rollback**: `internal/database/migrations/a_tenant/002_add_tenant_features.down.sql`

### Creating Relationships

For foreign key relationship examples, see:

- **User Table**: `internal/database/migrations/c_user/201_create_users_table.up.sql`
- **Complex Relationships**: `internal/database/migrations/e_rbac/404_create_rbac_user_roles_table.up.sql`

## CLI Usage

### Basic Commands

```bash
# Run all pending migrations
migrate -path internal/database/migrations -database "mysql://user:password@tcp(localhost:3306)/dbname" up

# Rollback last migration
migrate -path internal/database/migrations -database "mysql://user:password@tcp(localhost:3306)/dbname" down 1

# Check current version
migrate -path internal/database/migrations -database "mysql://user:password@tcp(localhost:3306)/dbname" version

# Create new migration
migrate create -ext sql -dir internal/database/migrations -seq create_posts_table
```

### Environment Variables

```bash
# Set database URL as environment variable
export DATABASE_URL="mysql://user:password@tcp(localhost:3306)/dbname"

# Use with migrate CLI
migrate -path internal/database/migrations -database "$DATABASE_URL" up
```

## Module-Specific Migrations

### Migration Numbering by Module

- **Tenant (001-099)**: Core tenant management
- **Website (101-199)**: Website configuration
- **User (201-299)**: User management and profiles
- **Auth (301-399)**: Authentication and security
- **RBAC (401-499)**: Role-based access control
- **Onboarding (501-599)**: User onboarding flows
- **Blog (601-699)**: Blog content management
- **SEO (701-799)**: SEO optimization features

### Migration Dependencies

Always consider module dependencies when creating migrations:

```
Dependency Order:
1. Tenant (001-099)     # Core foundation
2. Website (101-199)    # Depends on tenant
3. User (201-299)       # Depends on tenant
4. Auth (301-399)       # Depends on user
5. RBAC (401-499)       # Depends on user
6. Blog (601-699)       # Depends on user, website
```

## Best Practices

### Migration Design

1. **One Change Per Migration**: Each migration should focus on a single change
2. **Always Write Down Migrations**: Every `.up.sql` should have a corresponding `.down.sql`
3. **Test Both Directions**: Test both up and down migrations thoroughly
4. **Use Transactions**: Wrap migrations in transactions when possible
5. **Data Safety**: Always backup before running migrations in production

### Naming Conventions

- Use sequential numbering within module ranges
- Use descriptive names: `create_users_table`, `add_index_posts_published_at`
- Keep names concise but clear
- Use snake_case for consistency

### Database Constraints

For examples of proper constraint usage, see:

- **Foreign Key Constraints**: `internal/database/migrations/g_blog/603_create_blog_posts_table.up.sql`
- **Check Constraints**: `internal/database/migrations/c_user/201_create_users_table.up.sql`
- **Unique Constraints**: `internal/database/migrations/a_tenant/001_create_tenants_table.up.sql`

## Makefile Integration

```makefile
# Migration commands
migrate-create: ## Create new migration file
	@read -p "Migration name: " name; \
	migrate create -ext sql -dir internal/database/migrations -seq $$name

migrate-up: ## Run all pending migrations
	migrate -path internal/database/migrations -database "$(DATABASE_URL)" up

migrate-down: ## Rollback last migration
	migrate -path internal/database/migrations -database "$(DATABASE_URL)" down 1

migrate-status: ## Show current migration version
	migrate -path internal/database/migrations -database "$(DATABASE_URL)" version

.PHONY: migrate-create migrate-up migrate-down migrate-status
```

## Production Deployment

### Pre-Migration Checklist

1. **Backup Database**: Always create a backup before migrations
2. **Check Dependencies**: Ensure all dependencies are met
3. **Test in Staging**: Run migrations in staging environment first
4. **Monitor Performance**: Consider impact on large tables

### Migration Workflow

```bash
# 1. Backup database
mysqldump -u user -p database > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. Check current version
make migrate-status

# 3. Run migrations
make migrate-up

# 4. Verify completion
make migrate-status
```

## Troubleshooting

### Common Issues

1. **Dirty State**: Migration partially failed
   - Use `migrate force <version>` to reset state
   - Manual cleanup may be required

2. **Foreign Key Constraints**: Failed constraint validation
   - Check data integrity before migration
   - Consider disabling foreign key checks temporarily

3. **Lock Timeouts**: Long-running migrations on large tables
   - Use `pt-online-schema-change` for large table modifications
   - Schedule during low-traffic periods

### Recovery Procedures

```bash
# Check if database is in dirty state
migrate -path internal/database/migrations -database "$DATABASE_URL" version

# Force to specific version (use with caution)
migrate -path internal/database/migrations -database "$DATABASE_URL" force <version>

# Manual cleanup and retry
# Fix issues manually then retry migration
```

## Related Documentation

- [Database Models](./models.md)
- [Seeding System](./seeding.md)
- [Development Setup](../development/setup.md)
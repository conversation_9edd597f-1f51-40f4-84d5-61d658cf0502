# Entity Relationship Diagram (ERD) - Database Schema by <PERSON><PERSON><PERSON>

## Tổng quan

Tài liệu nà<PERSON> mô tả complete Entity Relationship Diagram cho Blog API v3 database schema, đư<PERSON>c tổ chức theo modules với prefix groups để dễ quản lý và hiểu.

## Database Overview

### Database Statistics
- **Total Tables**: 45+ tables
- **Engine**: InnoDB
- **Charset**: utf8mb4_unicode_ci
- **Primary Keys**: INT UNSIGNED AUTO_INCREMENT
- **Foreign Keys**: Cascading deletes where appropriate
- **Indexes**: Optimized for multi-tenant queries

## Module Organization

Database được tổ chức theo các module groups với module-based prefix naming convention. For complete table definitions, see the corresponding migration files in `internal/database/migrations/`.

### Core System Modules
- **Tenant Management**: `internal/database/migrations/a_tenant/`
- **Website Management**: `internal/database/migrations/b_website/`
- **User Management**: `internal/database/migrations/c_user/`
- **Authentication System**: `internal/database/migrations/d_auth/`
- **RBAC System**: `internal/database/migrations/e_rbac/`

### Content Modules
- **Blog System**: `internal/database/migrations/g_blog/`
- **Media Management**: `internal/database/migrations/h_media/`
- **SEO Features**: `internal/database/migrations/i_seo/`

### Communication Modules
- **Notification System**: `internal/database/migrations/j_notification/`
- **Email System**: `internal/database/migrations/k_email/`
- **Socket Communication**: `internal/database/migrations/l_socket/`

### Feature Modules
- **Payment Processing**: `internal/database/migrations/m_payment/`
- **API Management**: `internal/database/migrations/n_api/`
- **User Onboarding**: `internal/database/migrations/f_onboarding/`

## 1. Core System - Tenant & User Management

### Core Tenant & User Management

The core system manages tenants, websites, users, and authentication. For complete table schemas, see:

#### Core Tables
- **Tenants**: `internal/database/migrations/a_tenant/001_create_tenants_table.up.sql`
- **Websites**: `internal/database/migrations/b_website/101_create_websites_table.up.sql`
- **Users**: `internal/database/migrations/c_user/201_create_users_table.up.sql`
- **User Profiles**: `internal/database/migrations/c_user/202_create_user_profiles_table.up.sql`
- **User Preferences**: `internal/database/migrations/c_user/203_create_user_preferences_table.up.sql`
- **User Social Links**: `internal/database/migrations/c_user/204_create_user_social_links_table.up.sql`
- **Tenant Memberships**: `internal/database/migrations/c_user/205_create_tenant_memberships_table.up.sql`
- **User Invitations**: `internal/database/migrations/c_user/206_create_user_invitations_table.up.sql`

#### Authentication Tables
- **Sessions**: `internal/database/migrations/d_auth/301_create_auth_sessions_table.up.sql`
- **Tokens**: `internal/database/migrations/d_auth/302_create_auth_tokens_table.up.sql`
- **Password Resets**: `internal/database/migrations/d_auth/303_create_auth_password_resets_table.up.sql`
- **Login Attempts**: `internal/database/migrations/d_auth/304_create_auth_login_attempts_table.up.sql`
- **Token Blacklist**: `internal/database/migrations/d_auth/305_create_auth_token_blacklist_table.up.sql`
- **Password History**: `internal/database/migrations/d_auth/306_create_auth_password_history_table.up.sql`
- **OAuth Providers**: `internal/database/migrations/d_auth/307_create_auth_oauth_providers_table.up.sql`
- **OAuth Connections**: `internal/database/migrations/d_auth/308_create_auth_oauth_connections_table.up.sql`

#### Key Architecture Features
- **Global User Identity**: Single `users` table for cross-tenant identity
- **Website-scoped Sessions**: Each session is tied to a specific website
- **Context-aware Role Assignments**: RBAC user roles support website context
- **Tenant Isolation**: All tenant-specific data is properly isolated with foreign key constraints

## 2. RBAC System - Role-Based Access Control

The RBAC system provides role-based access control with context-aware permissions. For complete table schemas, see:

#### RBAC Tables
- **Roles**: `internal/database/migrations/e_rbac/401_create_rbac_roles_table.up.sql`
- **Permissions**: `internal/database/migrations/e_rbac/402_create_rbac_permissions_table.up.sql`
- **Role Permissions**: `internal/database/migrations/e_rbac/403_create_rbac_role_permissions_table.up.sql`
- **User Roles**: `internal/database/migrations/e_rbac/404_create_rbac_user_roles_table.up.sql`
- **Permission Groups**: `internal/database/migrations/e_rbac/405_create_rbac_permission_groups_table.up.sql`

#### Key Features
- **Context-aware Permissions**: All permissions are scoped to specific website contexts
- **Role Hierarchy**: Supports role inheritance and hierarchical structures
- **Temporal Assignments**: Role assignments with expiration dates
- **Permission Caching**: Optimized for fast permission lookups
- **Flexible Assignment**: Users can have multiple roles per website context

## 3. Blog Content System

The blog content system manages posts, categories, tags, and authors with hierarchical organization. For complete table schemas, see:

#### Blog Tables
- **Categories**: `internal/database/migrations/g_blog/601_create_blog_categories_table.up.sql`
- **Tags**: `internal/database/migrations/g_blog/602_create_blog_tags_table.up.sql`
- **Posts**: `internal/database/migrations/g_blog/603_create_blog_posts_table.up.sql`
- **Post Tags**: `internal/database/migrations/g_blog/604_create_blog_post_tags_table.up.sql`
- **Authors**: `internal/database/migrations/g_blog/605_create_blog_authors_table.up.sql`
- **Post Schedules**: `internal/database/migrations/g_blog/606_create_blog_post_schedules_table.up.sql`
- **Post Revisions**: `internal/database/migrations/g_blog/607_create_blog_post_revisions_table.up.sql`
- **Comments**: `internal/database/migrations/g_blog/608_create_blog_comments_table.up.sql`

#### Key Features
- **Hierarchical Categories**: Nested set model for category organization
- **Content Management**: Support for drafts, published, and archived states
- **SEO Optimization**: Built-in SEO fields and scoring
- **Multi-Author Support**: Author profiles with social links
- **Post Scheduling**: Automated publishing with scheduling
- **Content Revisions**: Version control for post content
- **Comment System**: Nested comments with moderation

## Core Entity Definitions

For complete entity definitions with all fields, constraints, and indexes, see the corresponding migration files:

### 1. Tenant & User Management

#### Core Entities
- **User**: `internal/database/migrations/c_user/201_create_users_table.up.sql` - Global user identity
- **Tenant**: `internal/database/migrations/a_tenant/001_create_tenants_table.up.sql` - Tenant organization
- **Website**: `internal/database/migrations/b_website/101_create_websites_table.up.sql` - Website management
- **Tenant Membership**: `internal/database/migrations/c_user/205_create_tenant_memberships_table.up.sql` - User-tenant relationships

### 2. RBAC System

#### RBAC Entities
- **Role**: `internal/database/migrations/e_rbac/401_create_rbac_roles_table.up.sql` - Role definitions
- **Permission**: `internal/database/migrations/e_rbac/402_create_rbac_permissions_table.up.sql` - Permission definitions
- **User Role**: `internal/database/migrations/e_rbac/404_create_rbac_user_roles_table.up.sql` - User role assignments

### 3. Blog Content System

#### Content Entities
- **Category**: `internal/database/migrations/g_blog/601_create_blog_categories_table.up.sql` - Hierarchical categories
- **Tag**: `internal/database/migrations/g_blog/602_create_blog_tags_table.up.sql` - Content tags
- **Post**: `internal/database/migrations/g_blog/603_create_blog_posts_table.up.sql` - Blog posts with SEO
- **Author**: `internal/database/migrations/g_blog/605_create_blog_authors_table.up.sql` - Author profiles

## 4. Media Management System

The media management system handles file uploads, storage, and organization. For complete table schemas, see:

#### Media Tables
- **Media Files**: `internal/database/migrations/h_media/801_create_media_files_table.up.sql`
- **Media Folders**: `internal/database/migrations/h_media/802_create_media_folders_table.up.sql`
- **Media Thumbnails**: `internal/database/migrations/h_media/803_create_media_thumbnails_table.up.sql`
- **Media Tags**: `internal/database/migrations/h_media/804_create_media_tags_table.up.sql`

#### Key Features
- **Multi-Storage Support**: Local, S3, MinIO, and CDN integration
- **Automatic Optimization**: Image compression and thumbnail generation
- **Hierarchical Organization**: Folder-based file organization
- **Metadata Extraction**: EXIF and file metadata support
- **Access Control**: Public/private file access control

## 5. Notification System

The notification system provides template-based messaging with multi-channel delivery support. For complete table schemas, see:

#### Notification Tables
- **Templates**: `internal/database/migrations/j_notification/901_create_notification_templates_table.up.sql`
- **Messages**: `internal/database/migrations/j_notification/902_create_notification_messages_table.up.sql`
- **Logs**: `internal/database/migrations/j_notification/903_create_notification_logs_table.up.sql`

#### Key Features
- **Template-Based System**: Reusable notification templates with variable substitution
- **Multi-Channel Delivery**: Email, SMS, push notifications, and Slack integration
- **Priority Levels**: Low, normal, high, and urgent priority classifications
- **Scheduled Delivery**: Support for delayed and scheduled notifications
- **Delivery Tracking**: Complete audit trail of notification delivery status
- **Website Isolation**: All notifications are properly scoped to website contexts

## 6. Payment System

The payment system handles subscriptions, transactions, and billing with support for multiple payment providers. For complete table schemas, see:

#### Payment Tables
- **Plans**: `internal/database/migrations/m_payment/1301_create_payment_plans_table.up.sql`
- **Customers**: `internal/database/migrations/m_payment/1302_create_payment_customers_table.up.sql`
- **Subscriptions**: `internal/database/migrations/m_payment/1303_create_payment_subscriptions_table.up.sql`
- **Transactions**: `internal/database/migrations/m_payment/1304_create_payment_transactions_table.up.sql`
- **Invoices**: `internal/database/migrations/m_payment/1305_create_payment_invoices_table.up.sql`

#### Key Features
- **Multi-Provider Support**: Stripe, PayPal, Square, and other payment processors
- **Subscription Management**: Recurring billing with trial periods and cancellation handling
- **Invoice Generation**: Automated invoice creation with tax calculation
- **Payment Tracking**: Complete transaction history and status monitoring
- **Currency Support**: Multi-currency pricing and billing
- **Website Isolation**: Payment processing scoped to specific websites

## 7. SEO Management System

The SEO system provides comprehensive search engine optimization tools including meta tag management, redirects, and sitemap generation. For complete table schemas, see:

#### SEO Tables
- **Meta Tags**: `internal/database/migrations/i_seo/1001_create_seo_meta_tags_table.up.sql`
- **Redirects**: `internal/database/migrations/i_seo/1002_create_seo_redirects_table.up.sql`
- **Sitemaps**: `internal/database/migrations/i_seo/1003_create_seo_sitemaps_table.up.sql`
- **Audits**: `internal/database/migrations/i_seo/1004_create_seo_audits_table.up.sql`
- **Keywords**: `internal/database/migrations/i_seo/1005_create_seo_keywords_table.up.sql`

#### Key Features
- **Meta Tag Management**: Title, description, keywords, and Open Graph tags
- **Redirect Management**: 301, 302, 307, and 308 redirects with hit tracking
- **Sitemap Generation**: Automated XML sitemap generation for different content types
- **SEO Scoring**: Automated SEO score calculation for content
- **Structured Data**: JSON-LD structured data support
- **Website Isolation**: All SEO data properly scoped to website contexts

## 8. Real-time Communication System

The real-time communication system enables WebSocket connections, chat rooms, and live messaging. For complete table schemas, see:

#### Socket Tables
- **Connections**: `internal/database/migrations/l_socket/1201_create_socket_connections_table.up.sql`
- **Rooms**: `internal/database/migrations/l_socket/1202_create_socket_rooms_table.up.sql`
- **Room Members**: `internal/database/migrations/l_socket/1203_create_socket_room_members_table.up.sql`
- **Messages**: `internal/database/migrations/l_socket/1204_create_socket_messages_table.up.sql`

#### Key Features
- **WebSocket Management**: Connection tracking with heartbeat monitoring
- **Room-Based Communication**: Public, private, and broadcast rooms
- **Message Types**: Text, image, file, and system messages
- **Role-Based Access**: Member, moderator, and admin roles within rooms
- **Connection Limits**: Configurable maximum connections per room
- **Website Isolation**: All real-time communication scoped to website contexts

## 9. Analytics & Activity Tracking

The analytics system provides comprehensive tracking of user behavior, system activities, and performance metrics. For complete table schemas, see:

#### Analytics Tables
- **Events**: `internal/database/migrations/o_analytics/1501_create_analytics_events_table.up.sql`
- **Page Views**: `internal/database/migrations/o_analytics/1502_create_analytics_page_views_table.up.sql`
- **Activity Logs**: `internal/database/migrations/o_analytics/1503_create_system_activity_logs_table.up.sql`
- **Reports**: `internal/database/migrations/o_analytics/1504_create_analytics_reports_table.up.sql`

#### Key Features
- **Event Tracking**: Page views, clicks, downloads, and custom events
- **User Behavior Analysis**: Session tracking with duration and referrer data
- **Activity Logging**: Complete audit trail of system activities
- **Performance Metrics**: Response times, error rates, and usage statistics
- **Privacy Compliance**: IP anonymization and data retention policies
- **Website Isolation**: All analytics data properly scoped to website contexts

## 10. Comments & Engagement System

The comments system enables nested discussions on blog posts with moderation and engagement features. For complete table schemas, see:

#### Comment Tables
- **Comments**: `internal/database/migrations/g_blog/608_create_blog_comments_table.up.sql`
- **Comment Likes**: `internal/database/migrations/g_blog/609_create_blog_comment_likes_table.up.sql`

#### Key Features
- **Nested Comments**: Hierarchical comment structure with parent-child relationships
- **Comment Moderation**: Pending, approved, rejected, and spam status workflow
- **Engagement Metrics**: Like and dislike counts with user tracking
- **Comment Pinning**: Ability to pin important comments
- **IP Tracking**: Comment origin tracking for moderation purposes
- **Website Isolation**: All comments properly scoped to website contexts

## 11. System Configuration & Settings

The system configuration module handles website settings, plugin configurations, and system maintenance. For complete table schemas, see:

#### System Tables
- **Website Settings**: `internal/database/migrations/b_website/102_create_website_settings_table.up.sql`
- **Plugin Configs**: `internal/database/migrations/p_system/1601_create_system_plugin_configs_table.up.sql`
- **Migrations**: `internal/database/migrations/p_system/1602_create_system_migrations_table.up.sql`
- **Seeder Logs**: `internal/database/migrations/p_system/1603_create_system_seeder_logs_table.up.sql`

#### Key Features
- **Dynamic Settings**: Key-value configuration system with type validation
- **Plugin Management**: Configurable plugin settings per website
- **Migration Tracking**: Database schema version control
- **Seeding Logs**: Audit trail of data seeding operations
- **Public/Private Settings**: Configurable visibility for settings
- **Website Isolation**: All configurations properly scoped to website contexts

## Database Relationships Summary

### Module-Based Relationships

#### Core System Relationships
1. **tenant_organizations → website_sites** (1:N)
   - One tenant can have multiple websites
   - Website belongs to exactly one tenant

2. **website_sites → user_accounts** (1:N)
   - Users are scoped to specific websites
   - Multi-tenant isolation through website_id

3. **user_accounts → auth_sessions** (1:N)
   - Users can have multiple active sessions
   - Session management per website context

#### RBAC Relationships
4. **website_sites → rbac_roles** (1:N)
   - Roles are website-specific
   - Each website defines its own role hierarchy

5. **user_accounts → rbac_user_roles** (N:M)
   - Users can have multiple roles per website
   - Role assignments are website-scoped

#### Content Relationships
6. **website_sites → blog_posts** (1:N)
   - Posts belong to specific website
   - Content isolation per website

7. **blog_posts → blog_tags** (N:M via blog_post_tags)
   - Posts can have multiple tags
   - Tags can be used by multiple posts

8. **blog_categories → blog_posts** (1:N with hierarchy)
   - Categories use nested set model
   - Posts belong to one primary category

#### Feature Module Relationships
9. **website_sites → media_files** (1:N)
   - Media files are website-scoped
   - Organized in folder hierarchy

10. **website_sites → notification_messages** (1:N)
    - Notifications are website-specific
    - Template-based notification system

11. **website_sites → payment_transactions** (1:N)
    - Payment processing per website
    - Subscription management per tenant

### Key Constraints

1. **Tenant Isolation**
   - All content tables include website_id
   - Foreign keys ensure data isolation

2. **User Management**
   - Global users with tenant-specific memberships
   - Role assignments are website-scoped

3. **Content Hierarchy**
   - Nested categories with lft/rgt boundaries
   - Post status workflow enforcement

4. **Referential Integrity**
   - Cascade deletes for dependent data
   - Restrict deletes for critical references

## Index Strategy by Module

The database uses a comprehensive indexing strategy optimized for multi-tenant queries and performance. For complete index definitions, see the corresponding migration files.

### Core System Indexes
- **Tenant Organizations**: Unique constraints on slug and email, status indexing
- **Website Sites**: Domain uniqueness, tenant-scoped slugs, tenant relationship indexing
- **User Accounts**: Email uniqueness per website, website and status indexing

### Content Module Indexes
- **Blog Posts**: Website-scoped slug uniqueness, status and publication date indexing, full-text search
- **Blog Categories**: Website-scoped slug uniqueness, nested set model indexing
- **Blog Tags**: Website-scoped slug uniqueness, website relationship indexing

### Feature Module Indexes
- **Media Files**: Website relationship, folder path, and MIME type indexing
- **Notification Messages**: Website and user relationships, status and scheduling indexing
- **Payment Transactions**: Website and customer relationships, status indexing

### Performance Optimization Indexes
- **Multi-tenant Query Optimization**: Composite indexes for efficient website-scoped queries
- **Session Management**: Active session tracking with expiration indexing
- **Analytics**: Time-based partitioning with website and date indexing

## Data Size Estimates by Module

### Storage Projections by Website Type

#### Small Website (< 10K users)
- **Total Database Size**: 50MB - 200MB
- **Core System**: 1MB - 10MB (users, auth, RBAC)
- **Content**: 5MB - 50MB (blog posts, categories, tags)
- **Media**: 10MB - 100MB (files, thumbnails)
- **Analytics**: 20MB - 50MB (events, page views)
- **Notifications**: 5MB - 20MB (messages, templates)

#### Medium Website (10K - 100K users)
- **Total Database Size**: 500MB - 5GB
- **Core System**: 10MB - 100MB
- **Content**: 50MB - 500MB
- **Media**: 200MB - 2GB
- **Analytics**: 100MB - 1GB
- **Notifications**: 50MB - 500MB

#### Large Website (100K - 1M users)
- **Total Database Size**: 5GB - 50GB
- **Core System**: 100MB - 1GB
- **Content**: 500MB - 5GB
- **Media**: 2GB - 20GB
- **Analytics**: 1GB - 10GB
- **Notifications**: 500MB - 5GB

#### Enterprise (1M+ users)
- **Total Database Size**: 50GB+
- **Requires**: Sharding, archiving, and distributed storage strategies

### Module Growth Patterns
- **Core System**: Linear growth with user registration
- **Content**: Moderate growth based on publishing frequency
- **Media**: High growth, largest storage consumer
- **Analytics**: Exponential growth, requires data archiving
- **Notifications**: High volume, short retention periods
- **Payment**: Low volume, long retention requirements

## Performance Considerations

### Query Optimization
1. **Always filter by website_id first**
2. **Use covering indexes for common queries**
3. **Implement query result caching**
4. **Use read replicas for reporting**

### Scaling Strategies
1. **Horizontal sharding by website_id**
2. **Separate read/write databases**
3. **Archive old data to cold storage**
4. **Implement connection pooling**

### Maintenance
1. **Regular index optimization**
2. **Partition large tables by date**
3. **Automated backup strategies**
4. **Monitor slow query logs**
# WebSocket Real-time Notifications

This document describes how to use the WebSocket real-time notification system in the Blog API v3.

## Overview

The notification system supports multiple channels including real-time WebSocket notifications through Socket.IO and plain WebSocket connections.

## Configuration

### Socket.IO Provider (Recommended)

```json
{
  "notification": {
    "socket_provider": "socketio",
    "socketio_config": {
      "server_url": "http://localhost:3000",
      "namespace": "/notifications"
    }
  }
}
```

### WebSocket Provider

```json
{
  "notification": {
    "socket_provider": "websocket",
    "socketio_config": {
      "server_url": "ws://localhost:3000"
    }
  }
}
```

### Mock Provider (Development/Testing)

```json
{
  "notification": {
    "socket_provider": "mock"
  }
}
```

## Usage

### Sending Notifications

```go
// Create notification
notification := &models.Notification{
    TenantID: 1,
    Type:     "user_mention",
    Subject:  "You were mentioned",
    Channel:  models.ChannelSocket,
    Priority: models.PriorityHigh,
}

// Create recipient
recipient := &models.NotificationRecipient{
    NotificationID:   notification.ID,
    RecipientType:    "user",
    RecipientAddress: "<EMAIL>",
    UserID:           &userID,
}

// Send through notification service
err := notificationService.SendNotification(ctx, notification.ID)
```

### Client-side Connection (JavaScript)

#### Socket.IO Client

```javascript
import io from 'socket.io-client';

const socket = io('http://localhost:3000/notifications', {
  transports: ['websocket']
});

// Listen for user-specific notifications
socket.on('user_notification', (data) => {
  console.log('Received notification:', data);
  // Handle notification in UI
  showNotification(data.title, data.content);
});

// Listen for tenant-wide notifications
socket.on('tenant_notification', (data) => {
  console.log('Received tenant notification:', data);
  // Handle tenant notification
  showTenantNotification(data.title, data.content);
});

// Join user room
socket.emit('join_room', `user_${userId}`);

// Join tenant room
socket.emit('join_room', `tenant_${tenantId}`);
```

#### Plain WebSocket Client

```javascript
const ws = new WebSocket('ws://localhost:3000');

ws.onopen = function() {
  console.log('WebSocket connected');
  
  // Join rooms
  ws.send(JSON.stringify({
    type: 'join_room',
    room: `user_${userId}`
  }));
};

ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('Received notification:', data);
  showNotification(data.title, data.content);
};
```

## Event Types

### User Notifications
- **Event**: `user_notification`
- **Room**: `user_{user_id}`
- **Trigger**: User-specific notifications (mentions, direct messages, etc.)

### Tenant Notifications
- **Event**: `tenant_notification`
- **Room**: `tenant_{tenant_id}`
- **Trigger**: Tenant-wide announcements, system messages

## Notification Payload

```json
{
  "id": 123,
  "type": "user_mention",
  "title": "You were mentioned",
  "content": "John mentioned you in a comment",
  "priority": "high",
  "timestamp": **********,
  "metadata": {
    "notification_id": 123,
    "recipient_id": 456,
    "tenant_id": 1,
    "type": "user_mention",
    "room": "user_456"
  },
  "read": false,
  "tenant_id": 1,
  "room": "user_456",
  "event": "user_notification"
}
```

## Error Handling

The system includes automatic reconnection logic:

- Connection failures are logged and retried automatically
- Failed notifications are queued and sent when connection is restored
- Health checks monitor connection status
- Graceful fallback to other notification channels if WebSocket fails

## Monitoring

Logs include detailed information for monitoring:

```
INFO Socket.IO notification sent successfully
  provider=socket.io
  server_url=http://localhost:3000
  namespace=/notifications
  event=user_notification
  room=user_123
  recipient_type=user
  recipient_id=123
  notification_id=456
  subject=You were mentioned
  type=user_mention
  priority=high
```

## Development and Testing

Use the mock provider for development:

```go
provider := NewMockSocketProvider(logger)
err := provider.SendNotification(recipient, notification, content, metadata)
// Always succeeds, logs notification details
```

## Security Considerations

- Authenticate Socket.IO connections with JWT tokens
- Validate user permissions before joining rooms
- Encrypt sensitive notification content
- Rate limit notification sending per user/tenant
- Monitor for suspicious connection patterns
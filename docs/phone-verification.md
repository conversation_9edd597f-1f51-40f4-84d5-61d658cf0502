# Phone Verification System

## Overview

The phone verification system allows users to verify their phone numbers using SMS verification codes. This system is built with security and rate limiting in mind to prevent abuse.

## Features

- **SMS Code Generation**: 6-digit verification codes sent via SMS
- **Rate Limiting**: Maximum 3 resend attempts with 5-minute intervals
- **Daily Limits**: Maximum 10 verification attempts per phone number per day
- **Code Expiry**: Verification codes expire after 15 minutes
- **Twilio Integration**: SMS sending through Twilio provider
- **Multi-tenant Support**: Fully tenant-scoped verification system

## API Endpoints

### Send Verification Code
```
POST /api/v1/user/phone/send-verification
```

Request body:
```json
{
  "user_id": 123,
  "phone": "+**********"
}
```

### Verify Phone Number
```
POST /api/v1/user/phone/verify
```

Request body:
```json
{
  "phone": "+**********",
  "code": "123456"
}
```

### Resend Verification Code
```
POST /api/v1/user/phone/resend-verification
```

Request body:
```json
{
  "phone": "+**********"
}
```

### Get Verification Status
```
GET /api/v1/user/phone/verification-status/{userId}
GET /api/v1/user/phone/verification-status (current user)
```

### Get Token Statistics
```
GET /api/v1/user/phone/token-stats/{userId}
GET /api/v1/user/phone/token-stats (current user)
```

## Configuration

### Environment Variables

```bash
# Twilio Configuration
TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_FROM_NUMBER=+**********
```

### Service Configuration

The phone verification service can be configured with:

```go
config := &PhoneVerificationConfig{
    CodeLength:         6,      // Length of verification code
    CodeExpiryMinutes:  15,     // Code expiry time in minutes
    MaxResendAttempts:  3,      // Maximum resend attempts
    ResendIntervalMins: 5,      // Minimum interval between resends
    MaxDailyAttempts:   10,     // Maximum daily attempts per phone
    SMSProvider:        "twilio", // SMS provider name
    SMSTemplate:        "Your verification code is: {{code}}. This code will expire in {{expiry}} minutes.",
}
```

## Database Schema

### phone_verification_tokens table

```sql
CREATE TABLE phone_verification_tokens (
    id INT UNSIGNED PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    phone VARCHAR(50) NOT NULL,
    verification_code VARCHAR(10) NOT NULL,
    code_hash VARCHAR(255) NOT NULL,
    is_used BOOLEAN DEFAULT FALSE,
    used_at TIMESTAMP NULL,
    resend_count INT UNSIGNED DEFAULT 0,
    last_resent_at TIMESTAMP NULL,
    provider VARCHAR(50),
    provider_message_id VARCHAR(255),
    provider_response JSON,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## Security Considerations

1. **Code Storage**: Verification codes are hashed using SHA-256 before storage
2. **Rate Limiting**: Prevents brute force attacks and SMS bombing
3. **Token Invalidation**: All previous tokens are invalidated when a new one is created
4. **Phone Format**: Only E.164 formatted phone numbers are accepted
5. **Tenant Isolation**: All tokens are scoped to specific tenants

## Error Handling

Common error responses:

- `400 Bad Request`: Invalid phone number format or verification code
- `404 Not Found`: User or active verification not found
- `429 Too Many Requests`: Rate limit or daily limit exceeded
- `500 Internal Server Error`: SMS provider failure or system error

## Usage Example

```go
// Send verification code
req := &models.CreatePhoneVerificationTokenRequest{
    UserID: 123,
    Phone:  "+**********",
}
token, err := phoneVerificationService.CreateVerificationToken(ctx, tenantID, req)

// Verify phone
verifyReq := &models.VerifyPhoneRequest{
    Phone: "+**********",
    Code:  "123456",
}
result, err := phoneVerificationService.VerifyPhone(ctx, tenantID, verifyReq)
```

## Monitoring and Cleanup

The system includes automatic cleanup of expired tokens:

```go
// Cleanup expired tokens (should be run periodically)
deletedCount, err := phoneVerificationService.CleanupExpiredTokens(ctx)
```

## Future Enhancements

1. Support for multiple SMS providers (AWS SNS, Nexmo, etc.)
2. Voice call verification as fallback
3. Custom verification code length per tenant
4. Webhook notifications for verification events
5. Advanced fraud detection and prevention
# Blog Post Templates System

## Overview
The Blog Post Templates system (task-160: "Template cho các loại bài viết") provides reusable templates for different types of blog posts, streamlining content creation and ensuring consistency across posts.

## Features
- **Multiple Template Types**: Standard, Tutorial, Review, News Article, Case Study, Event, and more
- **Dynamic Fields**: Customizable fields with validation for each template type
- **Multi-scope Support**: System, Tenant, Website, and User-level templates
- **Template Variables**: Support for placeholder variables in content
- **SEO Templates**: Built-in SEO title and description templates
- **Usage Tracking**: Track popular templates and usage statistics
- **Template Duplication**: Clone and customize existing templates

## Template Types

### 1. Standard Blog Post
- Basic template for regular blog posts
- Fields: title, excerpt, introduction, main content, conclusion

### 2. Tutorial / How-To Guide
- Step-by-step guide template
- Fields: task, prerequisites, steps, troubleshooting, resources

### 3. Product Review
- Comprehensive review template
- Fields: product name, rating, pros/cons, performance, pricing, verdict

### 4. News Article
- Breaking news template
- Fields: headline, lead, location, date, background, impact

### 5. Case Study
- Business case study template  
- Fields: client name, challenge, solution, results, metrics, testimonial

### 6. Event Announcement
- Event promotion template
- Fields: event details, agenda, speakers, registration info

## API Endpoints

### Template Management
- `GET /api/cms/v1/blog/templates` - List all templates
- `GET /api/cms/v1/blog/templates/:id` - Get template by ID
- `GET /api/cms/v1/blog/templates/slug/:slug` - Get template by slug
- `POST /api/cms/v1/blog/templates` - Create new template
- `PUT /api/cms/v1/blog/templates/:id` - Update template
- `DELETE /api/cms/v1/blog/templates/:id` - Delete template

### Template Discovery
- `GET /api/cms/v1/blog/templates/accessible` - Get templates accessible to user
- `GET /api/cms/v1/blog/templates/featured` - Get featured templates
- `GET /api/cms/v1/blog/templates/popular` - Get popular templates

### Template Operations
- `POST /api/cms/v1/blog/templates/:id/duplicate` - Duplicate a template
- `POST /api/cms/v1/blog/templates/create-post` - Create post from template

## Database Schema

```sql
CREATE TABLE blog_post_templates (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED DEFAULT NULL,
    website_id INT UNSIGNED DEFAULT NULL,
    created_by INT UNSIGNED NOT NULL,
    
    -- Basic Information
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL DEFAULT 'standard',
    scope VARCHAR(20) NOT NULL DEFAULT 'user',
    
    -- Template Configuration
    icon VARCHAR(100),
    color VARCHAR(7),
    is_active BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    
    -- Template Content
    title_template VARCHAR(500),
    content_template LONGTEXT,
    excerpt_template TEXT,
    structure JSON DEFAULT (JSON_OBJECT()),
    
    -- Default Settings
    default_type VARCHAR(20),
    default_status VARCHAR(20),
    default_category_id INT UNSIGNED,
    default_tags JSON,
    default_allow_comments BOOLEAN DEFAULT TRUE,
    
    -- SEO Templates
    seo_title_template VARCHAR(500),
    seo_description_template TEXT,
    seo_keywords_template TEXT,
    
    -- Usage Statistics
    usage_count INT UNSIGNED DEFAULT 0,
    last_used_at TIMESTAMP NULL
);
```

## Template Structure

Templates use a dynamic field structure defined in JSON:

```json
{
  "sections": [
    {
      "id": "basic-info",
      "title": "Basic Information",
      "description": "Essential post details",
      "order": 1,
      "collapsible": false,
      "collapsed": false,
      "fields": [
        {
          "name": "title",
          "label": "Post Title",
          "type": "text",
          "required": true,
          "placeholder": "Enter your post title",
          "options": [],
          "default": "",
          "validation": "",
          "help_text": "Choose a compelling title"
        }
      ]
    }
  ],
  "metadata": {}
}
```

### Field Types
- `text` - Single line text input
- `textarea` - Multi-line text input
- `number` - Numeric input
- `date` - Date picker
- `select` - Dropdown selection
- `image` - Image URL input

## Using Templates

### Creating a Post from Template

```json
POST /api/cms/v1/blog/templates/create-post
{
  "template_id": 1,
  "field_values": {
    "title": "How to Build a REST API",
    "task": "Build a REST API with Go",
    "introduction": "In this tutorial...",
    "prerequisites": "Basic Go knowledge...",
    "steps": "1. Set up the project...",
    "conclusion": "You've now built..."
  },
  "category_id": 5,
  "tag_ids": [1, 2, 3],
  "status": "draft"
}
```

### Template Variables

Templates support variable placeholders using `{{variable_name}}` syntax:

```
Title Template: "How to {{task}}"
Content Template: "# {{title}}\n\n{{introduction}}"
```

## Template Scopes

### System Templates
- Available to all tenants
- Created by administrators
- Cannot be modified by users

### Tenant Templates  
- Available to all users within a tenant
- Created by tenant administrators

### Website Templates
- Available to users of a specific website
- Created by website administrators

### User Templates
- Private templates for individual users
- Created by users for personal use

## Best Practices

1. **Naming Convention**: Use clear, descriptive names for templates
2. **Field Validation**: Add appropriate validation rules for fields
3. **Help Text**: Provide helpful descriptions for complex fields
4. **Default Values**: Set sensible defaults where applicable
5. **SEO Templates**: Include SEO templates for better search visibility
6. **Version Control**: Duplicate templates before major changes

## Migration

Run the migration to create the templates table:
```bash
make migrate-up MODULE=g_blog
```

## Seeding Default Templates

Default templates are provided via seeder:
```bash
go run cmd/seeder/main.go --seed=blog_post_templates
```

## Client-Side Implementation

### Template Selection UI
```javascript
// Fetch available templates
const templates = await fetch('/api/cms/v1/blog/templates/accessible');

// Display template picker
templates.forEach(template => {
  // Render template card with icon, name, description
});
```

### Dynamic Form Generation
```javascript
// Generate form fields based on template structure
template.structure.sections.forEach(section => {
  section.fields.forEach(field => {
    // Create appropriate input based on field.type
    // Apply validation rules
    // Show help text
  });
});
```

### Template Preview
```javascript
// Preview rendered template with field values
const preview = processTemplate(template.content_template, fieldValues);
```

## Future Enhancements

1. **Template Versioning**: Track template changes over time
2. **Template Marketplace**: Share templates between tenants
3. **AI-Powered Templates**: Generate templates based on content type
4. **Template Analytics**: Detailed usage and performance metrics
5. **Conditional Fields**: Show/hide fields based on other field values
6. **Rich Field Types**: Add WYSIWYG editor, file upload, etc.